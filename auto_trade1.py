#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ETF自动交易脚本

结合realtime_signal.py的信号计算和citic_trader的交易接口，实现自动交易功能
"""

import os
import sys
import time
import json
import logging
import requests
import argparse
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, Tuple
from colorama import init, Fore, Style
from pytdx.hq import TdxHq_API
import pickle

# 导入自定义模块
from realtime_signal import RealTimeSignalCalculator, TDX_SERVERS
from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger, format_stock_code
from citic_trader.query import QueryClient

# 初始化colorama
init(autoreset=True)

# 配置参数
BASE_ETF = '518880'  # 黄金ETF
TARGET_ETF = '518850'  # 黄金ETF联接
# TRADE_ETF = '518850'  # 要交易的ETF代码，这里是黄金ETF
WINDOW = 80  # 使用过去120分钟的数据
STD_DEV_MULT = 5  # 标准差倍数
MAX_POS_SIZE = 1.0  # 最大持仓比例
CHECK_INTERVAL = 1  # 检查信号间隔(秒)
TRADE_VOLUME = 100  # 交易数量(默认100股)
COOKIES_FILE = "cookies.json"  # cookies文件路径

class AutoTrader:
    """自动交易类，结合信号计算和交易接口"""
    
    def __init__(self, base_etf: str, target_etf: str, 
                 window: int = 120, std_dev_mult: float = 1.2, 
                 max_pos_size: float = 1.0, trade_volume: int = 100,
                 cookies_file: str = "cookies.json"):
        """
        初始化自动交易类
        
        Args:
            base_etf: 基准ETF代码
            target_etf: 目标ETF代码(用于计算信号)
            window: 滚动窗口大小
            std_dev_mult: 标准差倍数
            max_pos_size: 最大持仓比例
            trade_volume: 交易数量
            cookies_file: cookies文件路径
        """
        self.base_etf = base_etf
        self.target_etf = target_etf
        self.window = window
        self.std_dev_mult = std_dev_mult
        self.max_pos_size = max_pos_size
        self.trade_volume = trade_volume
        self.cookies_file = cookies_file
        
        # 设置日志
        self.logger = setup_logger(log_level=logging.INFO)
        self.logger.info("初始化自动交易系统...")
        
        # 初始化API和交易客户端
        self.api = TdxHq_API(heartbeat=True)
        self.connected = False
        self.session = None
        self.trade_client = None
        self.query_client = None
        self.signal_calculator = None
        
        # 交易状态
        self.last_signal = 0  # 上一次的信号: 1(买入), -1(卖出), 0(不操作)
        # self.position = 0  # 当前持仓: 正数表示多头, 负数表示空头, 0表示无持仓
        self.last_trade_time = None  # 上次交易时间
        # self.stock_info = None  # 股票信息缓存
        
        # 新增：ETF信息缓存
        self.base_etf_info = None
        self.target_etf_info = None
        
        # 账户信息
        self.account_info = None  # 账户持仓和资金信息
        self.available_cash = 0  # 可用资金
        self.max_buy_qty = 0  # 最大可买入数量
        # self.max_sell_qty = 0  # 最大可卖出数量
        
        # 新增：两个ETF的持仓和可卖出数量
        self.etf_positions = {self.base_etf: 0, self.target_etf: 0}
        self.etf_can_sell_qty = {self.base_etf: 0, self.target_etf: 0}
        
        # 交易记录
        self.trade_records = []

    def _is_trading_time(self) -> bool:
        """
        判断当前是否是交易时间（周一至周五，上午 9:30-11:30，下午 13:00-15:00）
        
        Returns:
            bool: 如果是交易时间，返回 True，否则返回 False
        """
        now = datetime.now()
        
        # 检查是否是周末 (周一=0, 周日=6)
        if now.weekday() >= 5: 
            return False
        
        current_time_hm = now.hour * 100 + now.minute
        
        # 检查上午交易时间
        if (current_time_hm >= 930 and current_time_hm <= 1130):
            return True
        
        # 检查下午交易时间
        if (current_time_hm >= 1300 and current_time_hm <= 1500):
            return True
            
        return False

    def _wait_until_trading_time(self) -> bool:
        """
        如果当前不是交易时间，则休眠直到下一个交易时间点（上午开盘、下午开盘）。
        如果已过当天所有交易时间，返回 False 信号结束程序。
        Returns:
            bool: 如果成功休眠到下一个交易时间点或正在交易时间，返回 True；
                  如果已过当天所有交易时间，返回 False。
        """
        now = datetime.now()
        
        # 1. 检查是否是周末
        if now.weekday() >= 5: # Saturday or Sunday
            self.logger.info("当前是周末。")
            next_monday_930 = now + timedelta(days=(7 - now.weekday())) # Go to next Monday
            next_monday_930 = next_monday_930.replace(hour=9, minute=30, second=0, microsecond=0)
            
            wait_seconds = (next_monday_930 - now).total_seconds()
            self.logger.info(f"休眠 {wait_seconds:.0f} 秒直到下周一 {next_monday_930.strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(wait_seconds) # Add a buffer
            return True # After sleep, it will be a trading day, so continue

        # 2. 检查交易时段
        current_time_hm = now.hour * 100 + now.minute
        
        # 交易时间段
        # 9:30 - 11:30 (上午)
        # 13:00 - 15:00 (下午)

        if (current_time_hm >= 930 and current_time_hm <= 1130) or \
           (current_time_hm >= 1300 and current_time_hm <= 1500):
            # 已经在交易时间内，直接返回 True
            return True

        # 3. 非交易时间，需要等待或结束
        # 3.1. 盘前 (00:00 - 09:29)
        if current_time_hm < 930:
            next_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
            wait_seconds = (next_open - now).total_seconds()
            self.logger.info(f"非交易时间（盘前），休眠 {wait_seconds:.0f} 秒直到 {next_open.strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(wait_seconds)
            return True

        # 3.2. 午休 (11:31 - 12:59)
        elif current_time_hm > 1130 and current_time_hm < 1300:
            next_open = now.replace(hour=13, minute=0, second=0, microsecond=0)
            wait_seconds = (next_open - now).total_seconds()
            self.logger.info(f"非交易时间（午休），休眠 {wait_seconds:.0f} 秒直到 {next_open.strftime('%Y-%m-%d %H:%M:%S')}")
            time.sleep(wait_seconds)
            return True

        # 3.3. 盘后 (15:01 - 23:59)
        elif current_time_hm > 1500:
            self.logger.info("已过当日交易时间，程序将退出。")
            return False # Signal to terminate the script

        # Fallback for unexpected cases, though the above conditions should cover all
        self.logger.warning("未覆盖的时间段，短暂休眠后重试。")
        time.sleep(60)
        return True   
    def connect_tdx(self) -> bool:
        """
        连接到TDX服务器
        
        Returns:
            bool: 是否连接成功
        """
        for server in TDX_SERVERS:
            try:
                self.connected = self.api.connect(server['ip'], server['port'])
                if self.connected:
                    self.logger.info(f"成功连接到服务器: {server['ip']}:{server['port']}")
                    return True
            except Exception as e:
                self.logger.error(f"连接服务器 {server['ip']}:{server['port']} 失败: {str(e)}")
        
        self.logger.error("所有服务器连接失败!")
        return False
    
    def load_cookies(self) -> Dict[str, str]:
        """
        从文件加载cookies
        
        可以支持两种格式：
        1. JSON格式的字典：{"key1": "value1", "key2": "value2"}
        2. 原始字符串格式： "key1=value1; key2=value2"
        
        Returns:
            Dict: cookies字典
        """
        try:
            if not os.path.exists(self.cookies_file):
                self.logger.error(f"Cookies文件不存在: {self.cookies_file}")
                return {}
                
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
                # 尝试解析为JSON格式
                try:
                    cookies = json.loads(content)
                    self.logger.info(f"成功从 {self.cookies_file} 加载JSON格式的cookies")
                    return cookies
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析为原始字符串格式
                    self.logger.info(f"尝试从 {self.cookies_file} 加载原始字符串格式的cookies")
                    cookies = {}
                    parts = content.split(';')
                    for part in parts:
                        if '=' in part:
                            key, value = part.split('=', 1)
                            cookies[key.strip()] = value.strip()
                    if cookies:
                        self.logger.info(f"成功解析原始字符串格式的cookies")
                        return cookies
                    else:
                        self.logger.error(f"无法解析cookies文件内容为有效的JSON或原始字符串格式: {self.cookies_file}")
                        return {}
                
        except Exception as e:
            self.logger.error(f"加载cookies失败: {e}")
            return {}
    
    def setup_trade_client(self) -> bool:
        """
        设置交易客户端
        
        Returns:
            bool: 是否设置成功
        """
        try:
            # 加载cookies
            cookies = self.load_cookies()
            if not cookies:
                self.logger.error("未找到有效的cookies，无法初始化交易客户端")
                return False
            
            # 创建会话并添加cookies
            self.session = requests.Session()
            # 禁用代理
            self.session.trust_env = False
            self.session.proxies = {'http': None, 'https': None}
            for key, value in cookies.items():
                self.session.cookies.set(key, value)
            
            # 设置用户信息 (这里需要根据实际情况修改)
            user_info = {
                "account_id": "",  # 从cookies中获取或手动设置
                "stockholder_code": "",  # 从交易接口获取或手动设置
                "psw_session": "",  # 从cookies中获取或手动设置
                "app_info": {
                    "_appver": "7.0.20",
                    "_osVer": "Windows1064",
                    "_buildh5ver": "************"
                }
            }
            
            # 创建交易客户端
            self.trade_client = TradeClient(self.session, user_info, self.logger)
            # 创建查询客户端
            self.query_client = QueryClient(self.session, user_info, self.logger)
            self.logger.info("交易客户端和查询客户端创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设置交易客户端失败: {e}")
            return False
    
    def setup_signal_calculator(self) -> bool:
        """
        设置信号计算器
        
        Returns:
            bool: 是否设置成功
        """
        try:
            # 创建信号计算器
            self.signal_calculator = RealTimeSignalCalculator(
                base_etf=self.base_etf,
                target_etf=self.target_etf,
                window=self.window,
                std_dev_mult=self.std_dev_mult,
                max_pos_size=self.max_pos_size
            )
            
            # 设置API
            self.signal_calculator.set_api(self.api, self.connected)
            self.logger.info("信号计算器创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设置信号计算器失败: {e}")
            return False
    
    def _get_etf_info(self, etf_code: str) -> bool:
        """
        获取指定ETF的信息并缓存
        
        Args:
            etf_code: ETF代码
            
        Returns:
            bool: 是否获取成功
        """
        try:
            if not self.trade_client:
                self.logger.error("交易客户端未初始化")
                return False
                
            self.logger.info(f"获取ETF信息: {etf_code}")
            result = self.trade_client.get_stock_info(format_stock_code(etf_code))
            
            if result.get("status") == "success":
                if etf_code == self.base_etf:
                    self.base_etf_info = result
                elif etf_code == self.target_etf:
                    self.target_etf_info = result
                self.logger.info(f"成功获取ETF信息: {etf_code}, 名称: {result['data'].get('name', '')}, 价格: {result['data'].get('price', '0')}")
                return True
            else:
                self.logger.error(f"获取ETF信息失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            self.logger.error(f"获取ETF信息过程中出错: {e}")
            return False
    
    def get_account_info(self) -> bool:
        """
        获取账户持仓和资金信息
        
        Returns:
            bool: 是否获取成功
        """
        try:
            if not self.query_client:
                self.logger.error("查询客户端未初始化")
                return False
                
            # 获取账户信息（新版接口）
            self.logger.info("获取账户持仓和资金信息...")
            result = self.query_client.query_account()
            
            if result.get("status") == "success":
                self.account_info = result
                
                # 更新可用资金
                self.available_cash = float(result["data"]["funds"].get("available_cash", 0))
                
                # 清空旧的持仓信息
                self.etf_positions = {self.base_etf: 0, self.target_etf: 0}
                self.etf_can_sell_qty = {self.base_etf: 0, self.target_etf: 0}
                
                # 更新两个ETF的持仓
                for position in result["data"]["positions"]:
                    code = position["code"]
                    if code == self.base_etf or code == self.target_etf:
                        self.etf_positions[code] = int(position.get("hold_num", 0))
                        self.etf_can_sell_qty[code] = int(position.get("can_use", 0))
                
                self.logger.info(f"账户信息获取成功")
                self.logger.info(f"可用资金: {self.available_cash}元")
                self.logger.info(f"当前 {self.base_etf} 持仓: {self.etf_positions[self.base_etf]}份, 可卖出: {self.etf_can_sell_qty[self.base_etf]}份")
                self.logger.info(f"当前 {self.target_etf} 持仓: {self.etf_positions[self.target_etf]}份, 可卖出: {self.etf_can_sell_qty[self.target_etf]}份")
                
                return True
            else:
                self.logger.error(f"获取账户信息失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            self.logger.error(f"获取账户信息过程中出错: {e}")
            return False
    
    def calculate_trade_volume(self, signal: int, etf_code: str, current_price: float) -> int:
        """
        计算交易数量
        
        考虑手续费（最低0.1元）和最小购买单位100份的限制
        
        Args:
            signal: 交易信号 (1: 买入, -1: 卖出)
            etf_code: 交易的ETF代码
            current_price: 当前价格
            
        Returns:
            int: 交易数量
        """
        # 手续费率和最低手续费
        commission_rate = 0.00005  # 0.03%
        min_commission = 0.1  # 最低0.1元
        

        if signal == 1:    
            # 买入
            # 先计算可用资金减去手续费后能买多少份
            max_qty = int((self.available_cash / (1 + commission_rate)) / current_price) // 100 * 100
            
            # 如果最大可买入数量为0，则无法交易
            if max_qty <= 0:
                self.logger.info(f"可用资金不足，无法买入 {etf_code}")
                return 0
            
            # 计算手续费
            commission = max(current_price * max_qty * commission_rate, min_commission)
            
            # 检查总成本是否超过可用资金
            total_cost = current_price * max_qty + commission
            if total_cost > self.available_cash:
                # 调整购买数量
                max_qty = int((self.available_cash - min_commission) / (current_price * (1 + commission_rate))) // 100 * 100
                if max_qty <= 0:
                    self.logger.info(f"考虑手续费后可用资金不足，无法买入 {etf_code}")
                    return 0
            
            return max_qty
            
        elif signal == -1:  # 卖出信号
            # 返回指定ETF的当前持仓数量（全部卖出）
            return self.etf_can_sell_qty.get(etf_code, 0)
        
        return 0  # 不操作
    
    def verify_trade_result(self, etf_code: str, signal: int, trade_volume: int, current_price: float) -> bool:
        """
        验证交易结果
        
        在交易后获取最新账户信息，验证持仓和资金变化是否符合预期
        
        Args:
            etf_code: 交易的ETF代码
            signal: 交易信号 (1: 买入, -1: 卖出)
            trade_volume: 交易数量
            current_price: 交易价格
            
        Returns:
            bool: 验证是否通过
        """
        # 获取交易前的持仓和资金信息
        old_position = self.etf_positions.get(etf_code, 0)
        old_cash = self.available_cash # 可用资金是全局的
        
        # 获取最新账户信息
        if not self.get_account_info():
            self.logger.error("无法获取最新账户信息，验证失败")
            return False
        
        # 手续费率和最低手续费
        commission_rate = 0.00005  # 0.03%
        min_commission = 0.1  # 最低0.1元
        
        # 计算预期的持仓和资金变化
        expected_position = old_position
        expected_cash = old_cash
        
        if signal == 1:  # 买入
            expected_position += trade_volume
            commission = max(current_price * trade_volume * commission_rate, min_commission)
            expected_cash -= (current_price * trade_volume + commission)
        elif signal == -1:  # 卖出
            expected_position -= trade_volume
            commission = max(current_price * trade_volume * commission_rate, min_commission)
            expected_cash += (current_price * trade_volume - commission)
        
        # 验证持仓变化
        position_diff = abs(self.etf_positions.get(etf_code, 0) - expected_position)
        if position_diff > 0:
            self.logger.warning(f"ETF {etf_code} 持仓验证失败: 预期 {expected_position}, 实际 {self.etf_positions.get(etf_code, 0)}, 差异 {position_diff}")
            return False
        
        # 验证资金变化（允许一定误差）
        cash_diff = abs(self.available_cash - expected_cash)
        cash_tolerance = 1.0  # 允许1元的误差
        if cash_diff > cash_tolerance:
            self.logger.warning(f"资金验证失败: 预期 {expected_cash:.2f}, 实际 {self.available_cash:.2f}, 差异 {cash_diff:.2f}")
            return False
        
        self.logger.info(f"ETF {etf_code} 交易结果验证通过")
        return True
    
    def execute_trade(self, signal: int) -> bool:
        """
        执行交易
        
        Args:
            signal: 交易信号 (1: 买入TARGET_ETF并卖出BASE_ETF, -1: 买入BASE_ETF并卖出TARGET_ETF, 0: 不操作)
            
        Returns:
            bool: 交易是否成功
        """
        if signal == 0:
            return True  # 不操作，直接返回成功
            
        if not self.trade_client:
            self.logger.error("交易客户端未初始化")
            return False
        

        
        trade_success = True

        if signal == 1:  # 买入TARGET_ETF并卖出BASE_ETF
            self.logger.info(f"检测到买入信号: 买入 {self.target_etf}, 卖出 {self.base_etf}")
            
            # 1. 卖出BASE_ETF
            _, base_etf_bid, _, _ = self.signal_calculator.get_latest_price(self.base_etf)
            if base_etf_bid is None or base_etf_bid <= 0:
                self.logger.error(f"无法获取BASE ETF ({self.base_etf}) 的实时盘口价格或价格无效，无法卖出。")
                return False
            
            sell_base_price = base_etf_bid / 10.0
            sell_base_volume = self.calculate_trade_volume(-1, self.base_etf, sell_base_price)
            
            if sell_base_volume > 0:
                self.logger.info(f"执行卖出: {self.base_etf}, 价格: {sell_base_price}, 数量: {sell_base_volume}")
                result = self.trade_client.sell(format_stock_code(self.base_etf), sell_base_price, sell_base_volume, self.base_etf_info, fok=True)
                
                if result.get("status") == "success":
                    trade_record = {
                        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "action": "卖出",
                        "code": self.base_etf,
                        "price": sell_base_price,
                        "volume": sell_base_volume,
                        "amount": sell_base_price * sell_base_volume,
                        "contract_no": result.get("contract_no", ""),
                        "entrust_no": result.get("entrust_no", "")
                    }
                    self.trade_records.append(trade_record)
                    self.logger.info(f"卖出 {self.base_etf} 成功: 合同编号 {result.get('contract_no', '')}, 委托编号 {result.get('entrust_no', '')}")
                    if not self.verify_trade_result(self.base_etf, -1, sell_base_volume, sell_base_price):
                        self.logger.warning(f"{self.base_etf} 交易结果验证失败，请检查账户状态")
                        trade_success = False
                else:
                    self.logger.error(f"卖出 {self.base_etf} 失败: {result.get('message', '未知错误')}")
                    trade_success = False
            else:
                self.logger.info(f"无可卖出的 {self.base_etf}，跳过卖出操作")

                
            # 2. 买入TARGET_ETF
            _, _, target_etf_ask, _ = self.signal_calculator.get_latest_price(self.target_etf)
            if target_etf_ask is None or target_etf_ask <= 0:
                self.logger.error(f"无法获取TARGET ETF ({self.target_etf}) 的实时盘口价格或价格无效，无法买入。")
                return False
            
            buy_target_price = target_etf_ask / 10.0
            buy_target_volume = self.calculate_trade_volume(1, self.target_etf, buy_target_price)
            
            if buy_target_volume > 0:
                self.logger.info(f"执行买入: {self.target_etf}, 价格: {buy_target_price}, 数量: {buy_target_volume}")
                result = self.trade_client.buy(format_stock_code(self.target_etf), buy_target_price, buy_target_volume, self.target_etf_info, fok=True)
                
                if result.get("status") == "success":
                    trade_record = {
                        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "action": "买入",
                        "code": self.target_etf,
                        "price": buy_target_price,
                        "volume": buy_target_volume,
                        "amount": buy_target_price * buy_target_volume,
                        "contract_no": result.get("contract_no", ""),
                        "entrust_no": result.get("entrust_no", "")
                    }
                    self.trade_records.append(trade_record)
                    self.logger.info(f"买入 {self.target_etf} 成功: 合同编号 {result.get('contract_no', '')}, 委托编号 {result.get('entrust_no', '')}")
                    if not self.verify_trade_result(self.target_etf, 1, buy_target_volume, buy_target_price):
                        self.logger.warning(f"{self.target_etf} 交易结果验证失败，请检查账户状态")
                        trade_success = False
                else:
                    self.logger.error(f"买入 {self.target_etf} 失败: {result.get('message', '未知错误')}")
                    trade_success = False
            else:
                self.logger.info(f"可用资金不足或计算买入数量为0，跳过买入 {self.target_etf} 操作")
                trade_success = False # If we can't buy, this signal isn't fully executed
            
        elif signal == -1:  # 卖出TARGET_ETF并买入BASE_ETF
            self.logger.info(f"检测到卖出信号: 卖出 {self.target_etf}, 买入 {self.base_etf}")
            
            # 1. 卖出TARGET_ETF
            _, target_etf_bid, _, _ = self.signal_calculator.get_latest_price(self.target_etf)
            if target_etf_bid is None or target_etf_bid <= 0:
                self.logger.error(f"无法获取TARGET ETF ({self.target_etf}) 的实时盘口价格或价格无效，无法卖出。")
                return False
            
            sell_target_price = target_etf_bid / 10.0
            sell_target_volume = self.calculate_trade_volume(-1, self.target_etf, sell_target_price)
            
            if sell_target_volume > 0:
                self.logger.info(f"执行卖出: {self.target_etf}, 价格: {sell_target_price}, 数量: {sell_target_volume}")
                result = self.trade_client.sell(format_stock_code(self.target_etf), sell_target_price, sell_target_volume, self.target_etf_info, fok=True)
                
                if result.get("status") == "success":
                    trade_record = {
                        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "action": "卖出",
                        "code": self.target_etf,
                        "price": sell_target_price,
                        "volume": sell_target_volume,
                        "amount": sell_target_price * sell_target_volume,
                        "contract_no": result.get("contract_no", ""),
                        "entrust_no": result.get("entrust_no", "")
                    }
                    self.trade_records.append(trade_record)
                    self.logger.info(f"卖出 {self.target_etf} 成功: 合同编号 {result.get('contract_no', '')}, 委托编号 {result.get('entrust_no', '')}")
                    if not self.verify_trade_result(self.target_etf, -1, sell_target_volume, sell_target_price):
                        self.logger.warning(f"{self.target_etf} 交易结果验证失败，请检查账户状态")
                        trade_success = False
                else:
                    self.logger.error(f"卖出 {self.target_etf} 失败: {result.get('message', '未知错误')}")
                    trade_success = False
            else:
                self.logger.info(f"无可卖出的 {self.target_etf}，跳过卖出操作")

                
            # 2. 买入BASE_ETF
            _, _, base_etf_ask, _ = self.signal_calculator.get_latest_price(self.base_etf)
            if base_etf_ask is None or base_etf_ask <= 0:
                self.logger.error(f"无法获取BASE ETF ({self.base_etf}) 的实时盘口价格或价格无效，无法买入。")
                return False
            
            buy_base_price = base_etf_ask / 10.0
            buy_base_volume = self.calculate_trade_volume(1, self.base_etf, buy_base_price)
            
            if buy_base_volume > 0:
                self.logger.info(f"执行买入: {self.base_etf}, 价格: {buy_base_price}, 数量: {buy_base_volume}")
                result = self.trade_client.buy(format_stock_code(self.base_etf), buy_base_price, buy_base_volume, self.base_etf_info, fok=True)
                
                if result.get("status") == "success":
                    trade_record = {
                        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "action": "买入",
                        "code": self.base_etf,
                        "price": buy_base_price,
                        "volume": buy_base_volume,
                        "amount": buy_base_price * buy_base_volume,
                        "contract_no": result.get("contract_no", ""),
                        "entrust_no": result.get("entrust_no", "")
                    }
                    self.trade_records.append(trade_record)
                    self.logger.info(f"买入 {self.base_etf} 成功: 合同编号 {result.get('contract_no', '')}, 委托编号 {result.get('entrust_no', '')}")
                    if not self.verify_trade_result(self.base_etf, 1, buy_base_volume, buy_base_price):
                        self.logger.warning(f"{self.base_etf} 交易结果验证失败，请检查账户状态")
                        trade_success = False
                else:
                    self.logger.error(f"买入 {self.base_etf} 失败: {result.get('message', '未知错误')}")
                    trade_success = False
            else:
                self.logger.info(f"可用资金不足或计算买入数量为0，跳过买入 {self.base_etf} 操作")
                trade_success = False # If we can't buy, this signal isn't fully executed

        return trade_success
    
    def run(self, check_interval: int = 5) -> None:
        """
        运行自动交易系统
        
        Args:
            check_interval: 检查信号的间隔时间(秒)
        """
        self.logger.info("启动自动交易系统...")
        
        # 连接TDX服务器
        if not self.connect_tdx():
            self.logger.error("无法连接到TDX服务器，程序退出")
            return
        
        # 设置交易客户端
        if not self.setup_trade_client():
            self.logger.error("无法设置交易客户端，程序退出")
            return
        
        # 设置信号计算器
        if not self.setup_signal_calculator():
            self.logger.error("无法设置信号计算器，程序退出")
            return
        
        # 获取交易ETF信息
        if not self._get_etf_info(self.base_etf):
            self.logger.error(f"无法获取基准ETF ({self.base_etf}) 信息，程序退出")
            return
            
        if not self._get_etf_info(self.target_etf):
            self.logger.error(f"无法获取目标ETF ({self.target_etf}) 信息，程序退出")
            return
        
        # 获取账户持仓和资金信息
        if not self.get_account_info():
            self.logger.error("无法获取账户信息，程序退出")
            return
        
        # 初始化数据
        self.logger.info("初始化历史数据...")
        data = self.signal_calculator.update_data()
        if data is None:
            self.logger.error("无法获取历史数据，程序退出")
            return
        
        self.logger.info(f"自动交易系统启动成功，开始监控交易信号，每 {check_interval} 秒检查一次...")
        print(Fore.GREEN + Style.BRIGHT + "=" * 60)
        print(Fore.GREEN + Style.BRIGHT + f"自动交易系统已启动")
        print(Fore.GREEN + Style.BRIGHT + f"基准ETF: {self.base_etf}, 目标ETF: {self.target_etf}")
        print(Fore.GREEN + Style.BRIGHT + f"窗口大小: {self.window}分钟, 标准差倍数: {self.std_dev_mult}")
        print(Fore.GREEN + Style.BRIGHT + f"交易数量: {self.trade_volume}股, 检查间隔: {check_interval}秒")
        print(Fore.GREEN + Style.BRIGHT + "=" * 60)
        
        try:
            # 主循环
            while True:
                # 检查并等待交易时间
                if not self._wait_until_trading_time():
                    self.logger.info("已过当日交易时间，程序结束。")
                    break # Exit the main loop
                # 计算信号
                signal_data = self.signal_calculator.calculate_signal()
                if signal_data is None:
                    self.logger.error("计算信号失败，将在下次循环重试")
                    time.sleep(check_interval)
                    continue
                
                # 显示信号
                self.signal_calculator.display_signal(signal_data)
                
                # 获取交易信号
                current_signal = signal_data['trade_signal']
                
                # 如果信号变化，执行交易
                if current_signal != 0:
                    self.logger.info(f"检测到信号变化: {self.last_signal} -> {current_signal}")
                    
                    # 执行交易
                    if self.execute_trade(current_signal):
                        self.last_signal = current_signal
                    else:
                        self.logger.error("交易执行失败")
                
                # 等待下一次检查
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("用户中断，程序退出")
            print(Fore.YELLOW + "\n自动交易系统已停止")
        except Exception as e:
            self.logger.error(f"运行过程中出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
        finally:
            # 断开连接
            if self.connected:
                self.api.disconnect()
                self.logger.info("已断开TDX服务器连接")
            
            # 保存交易记录
            if self.trade_records:
                try:
                    with open('trade_records.json', 'w', encoding='utf-8') as f:
                        json.dump(self.trade_records, f, indent=2, ensure_ascii=False)
                    self.logger.info("交易记录已保存到 trade_records.json")
                except Exception as e:
                    self.logger.error(f"保存交易记录失败: {e}")
            
            # 保存cookies
            if self.session:
                try:
                    with open(self.cookies_file, 'w', encoding='utf-8') as f:
                        json.dump(self.session.cookies.get_dict(), f, indent=2)
                    self.logger.info(f"Cookies已保存到 {self.cookies_file}")
                except Exception as e:
                    self.logger.error(f"保存cookies失败: {e}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ETF自动交易系统')
    
    parser.add_argument('--target', type=str, default=TARGET_ETF,
                        help=f'目标ETF代码 (默认: {TARGET_ETF})')
    # parser.add_argument('--trade', type=str, default=TRADE_ETF,
    #                     help=f'交易ETF代码 (默认: {TRADE_ETF})')
    parser.add_argument('--window', type=int, default=WINDOW,
                        help=f'滚动窗口大小 (默认: {WINDOW}分钟)')
    parser.add_argument('--std', type=float, default=STD_DEV_MULT,
                        help=f'标准差倍数 (默认: {STD_DEV_MULT})')
    parser.add_argument('--max-pos', type=float, default=MAX_POS_SIZE,
                        help=f'最大持仓比例 (默认: {MAX_POS_SIZE})')
    parser.add_argument('--volume', type=int, default=TRADE_VOLUME,
                        help=f'交易数量 (默认: {TRADE_VOLUME}股)')
    parser.add_argument('--interval', type=int, default=CHECK_INTERVAL,
                        help=f'检查信号间隔 (默认: {CHECK_INTERVAL}秒)')
    parser.add_argument('--cookies', type=str, default=COOKIES_FILE,
                        help=f'cookies文件路径 (默认: {COOKIES_FILE})')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 创建自动交易系统
    trader = AutoTrader(
        base_etf=BASE_ETF,
        target_etf=args.target,
        window=args.window,
        std_dev_mult=args.std,
        max_pos_size=args.max_pos,
        trade_volume=args.volume,
        cookies_file=args.cookies
    )
    
    # 运行自动交易系统
    trader.run(check_interval=args.interval)

if __name__ == "__main__":
    main() 