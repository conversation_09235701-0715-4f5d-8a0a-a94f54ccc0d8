import os
import sys
import time
import pandas as pd
import numpy as np
import argparse
from datetime import datetime, timedelta
from colorama import init, Fore, Style
from pytdx.hq import TdxHq_API
import statsmodels.api as sm

# 初始化colorama
init(autoreset=True)

# 配置参数
BASE_ETF = '518880'  # 黄金ETF
TARGET_ETF = '518890'  # 可以根据需要更改为其他ETF
WINDOW = 120  # 使用过去120分钟的数据
STD_DEV_MULT = 1.2  # 标准差倍数
MAX_POS_SIZE = 1.0  # 最大持仓比例
COMMISSION_RATE = 0.0001  # 佣金率
SLIPPAGE_RATE = 0.000  # 滑点率

# TDX服务器列表 (更新了更多可用服务器)
TDX_SERVERS = [
    {'ip': '**************', 'port': 7709},  # 内蒙
    {'ip': '***************', 'port': 7709},   # 北京联通
    {'ip': '**************', 'port': 7709},   # 上海移动
    {'ip': '*************', 'port': 7709}, # 广州电信
]

class RealTimeSignalCalculator:
    """实时交易信号计算器"""
    
    def __init__(self, base_etf, target_etf, window=120, std_dev_mult=1.2, max_pos_size=1.0):
        """
        初始化实时信号计算器
        
        Args:
            base_etf: 基准ETF代码
            target_etf: 目标ETF代码
            window: 滚动窗口大小
            std_dev_mult: 标准差倍数
            max_pos_size: 最大持仓比例
        """
        self.base_etf = base_etf
        self.target_etf = target_etf
        self.window = window
        self.std_dev_mult = std_dev_mult
        self.max_pos_size = max_pos_size
        self.api = None  # 将在外部设置
        self.connected = False
        
        # 数据缓存
        self.data_cache = None
        self.last_minute = None
        self.last_signal = None
        self.last_params = None
    
    def set_api(self, api, connected=True):
        """设置API实例"""
        self.api = api
        self.connected = connected
    
    def connect(self):
        """连接到TDX服务器 (备用方法，推荐使用set_api)"""
        if self.api is None:
            self.api = TdxHq_API(heartbeat=True)
            
        for server in TDX_SERVERS:
            try:
                self.connected = self.api.connect(server['ip'], server['port'])
                if self.connected:
                    print(Fore.GREEN + f"成功连接到服务器: {server['ip']}:{server['port']}")
                    return True
            except Exception as e:
                print(Fore.RED + f"连接服务器 {server['ip']}:{server['port']} 失败: {str(e)}")
        
        print(Fore.RED + Style.BRIGHT + "所有服务器连接失败!")
        return False
    
    def disconnect(self):
        """断开连接"""
        if self.connected and self.api:
            self.api.disconnect()
            self.connected = False
            print(Fore.YELLOW + "已断开服务器连接")
    
    def get_etf_market_code(self, etf_code):
        """
        获取ETF的市场代码
        
        Returns:
            0: 深市
            1: 沪市
        """
        if etf_code.startswith('5'):
            return 1  # 沪市
        else:
            return 0  # 深市
    
    def get_latest_price(self, etf_code):
        """
        获取ETF的最新价格
        
        Args:
            etf_code: ETF代码
            
        Returns:
            float: 最新价格
            float: 买一价
            float: 卖一价
            datetime: 时间
        """
        market_code = self.get_etf_market_code(etf_code)
        try:
            # 获取ETF的实时行情
            quote = self.api.get_security_quotes([(market_code, etf_code)])
            if not quote or len(quote) == 0:
                print(Fore.RED + f"获取 {etf_code} 最新价格失败!")
                return None, None, None, None
            
            # 提取最新价格、买一价、卖一价和时间
            latest_price = quote[0]['price']
            bid_price = quote[0]['bid1']  # 买一价
            ask_price = quote[0]['ask1']  # 卖一价
            latest_time = datetime.now().replace(microsecond=0)
            
            return latest_price, bid_price, ask_price, latest_time
            
        except Exception as e:
            print(Fore.RED + f"获取 {etf_code} 最新价格时出错: {str(e)}")
            return None, None, None, None
    
    def get_latest_quotes(self):
        """
        获取基准ETF和目标ETF的最新盘口数据
        
        Returns:
            dict: 包含基准ETF和目标ETF的盘口数据
        """
        base_price, base_bid, base_ask, _ = self.get_latest_price(self.base_etf)
        target_price, target_bid, target_ask, current_time = self.get_latest_price(self.target_etf)
        
        if base_price is None or target_price is None:
            print(Fore.RED + "获取盘口数据失败!")
            return None
        
        quotes_data = {
            'time': current_time,
            'base_etf': self.base_etf,
            'target_etf': self.target_etf,
            'base_price': base_price,
            'base_bid': base_bid,
            'base_ask': base_ask,
            'target_price': target_price,
            'target_bid': target_bid,
            'target_ask': target_ask,
            'price_ratio': target_price / base_price,
            'bid_ratio': target_bid / base_price,  # 使用目标ETF买一价计算
            'ask_ratio': target_ask / base_price   # 使用目标ETF卖一价计算
        }
        
        return quotes_data
    
    def get_minute_data(self, etf_code, count=1):
        """
        获取ETF的最近N分钟K线数据
        
        Args:
            etf_code: ETF代码
            count: 获取的K线数量
            
        Returns:
            DataFrame: 分钟K线数据
        """
        market_code = self.get_etf_market_code(etf_code)
        try:
            # 获取分钟K线数据
            minute_data = self.api.get_security_bars(8, market_code, etf_code, 0, count)
            
            if not minute_data:
                print(Fore.RED + f"获取 {etf_code} 分钟数据失败!")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(minute_data)
            # 重命名列
            df.rename(columns={'datetime': 'time', 'close': etf_code}, inplace=True)
            # 设置时间索引
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)
            
            # 只保留收盘价列
            return df[[etf_code]]
            
        except Exception as e:
            print(Fore.RED + f"获取 {etf_code} 分钟数据时出错: {str(e)}")
            return None
    
    def get_initial_data(self):
        """
        获取初始历史数据（只在首次运行时调用）
        
        Returns:
            DataFrame: 合并后的历史分钟数据
        """
        print(Fore.CYAN + f"首次获取 {self.window} 分钟历史数据...")
        
        market_code_base = self.get_etf_market_code(self.base_etf)
        market_code_target = self.get_etf_market_code(self.target_etf)
        
        try:
            # 获取基准ETF和目标ETF的历史分钟数据
            base_data = self.api.get_security_bars(8, market_code_base, self.base_etf, 0, self.window)
            target_data = self.api.get_security_bars(8, market_code_target, self.target_etf, 0, self.window)
            
            if not base_data or not target_data:
                print(Fore.RED + "获取历史数据失败!")
                return None
            
            # 转换为DataFrame
            base_df = pd.DataFrame(base_data)
            target_df = pd.DataFrame(target_data)
            
            # 重命名列
            base_df.rename(columns={'datetime': 'time', 'close': self.base_etf}, inplace=True)
            target_df.rename(columns={'datetime': 'time', 'close': self.target_etf}, inplace=True)
            
            # 设置时间索引
            base_df['time'] = pd.to_datetime(base_df['time'])
            target_df['time'] = pd.to_datetime(target_df['time'])
            base_df.set_index('time', inplace=True)
            target_df.set_index('time', inplace=True)
            
            # 只保留收盘价列
            base_df = base_df[[self.base_etf]]
            target_df = target_df[[self.target_etf]]
            
            # 合并数据
            merged_df = pd.concat([base_df, target_df], axis=1)
            merged_df = merged_df.dropna()
            
            print(Fore.GREEN + f"成功获取历史数据，共 {len(merged_df)} 条记录")
            
            return merged_df
            
        except Exception as e:
            print(Fore.RED + f"获取历史数据时出错: {str(e)}")
            return None
    
    def update_data(self):
        """
        更新数据，只获取最新价格并追加到缓存
        
        Returns:
            DataFrame: 更新后的数据
        """
        current_time = datetime.now().replace(microsecond=0)
        current_minute = current_time.replace(second=0)
        
        # 初始化数据
        if self.data_cache is None:
            self.data_cache = self.get_initial_data()
            if self.data_cache is None:
                return None
            self.last_minute = current_minute
            return self.data_cache
        
        # 如果分钟没变，使用缓存数据
        if current_minute == self.last_minute:
            return self.data_cache
        
        # 分钟变化，获取最新价格
        print(Fore.CYAN + "分钟变化，获取最新价格...")
        
        base_price, _, _, _ = self.get_latest_price(self.base_etf)
        target_price, _, _, _ = self.get_latest_price(self.target_etf)
        
        if base_price is None or target_price is None:
            print(Fore.RED + "获取最新价格失败!")
            return self.data_cache  # 返回旧数据
        
        # 创建新的数据点
        new_data = pd.DataFrame({
            self.base_etf: [base_price],
            self.target_etf: [target_price]
        }, index=[current_minute])
        
        # 更新数据缓存：添加新数据，删除最老的数据
        self.data_cache = pd.concat([self.data_cache, new_data])
        if len(self.data_cache) > self.window:
            self.data_cache = self.data_cache.iloc[-self.window:]
        
        self.last_minute = current_minute
        
        print(Fore.GREEN + f"数据已更新，最新时间: {current_minute}")
        return self.data_cache
    
    def calculate_ols_signals(self, data):
        """
        使用OLS计算交易信号
        
        Args:
            data: 价格数据DataFrame
            
        Returns:
            dict: 交易信号和相关参数
        """
        current_time = datetime.now().replace(microsecond=0)
        current_minute = current_time.replace(second=0)
        
        # 如果是同一分钟内的请求且已经计算过回归参数，获取最新盘口数据重新计算信号
        if current_minute == self.last_minute and self.last_signal is not None:
            print(Fore.CYAN + "同一分钟内使用缓存的回归参数，但获取最新盘口数据...")
            # 获取最新盘口数据
            quotes = self.get_latest_quotes()
            if quotes is None:
                print(Fore.RED + "获取盘口数据失败，使用上次的信号结果")
                return self.last_signal
            
            # 使用缓存的回归参数
            beta = self.last_signal['ma']
            residual_std = self.last_signal['residual_std']
            
            # 计算交易区间（使用缓存的回归参数但基于最新价格）
            upper_bound = (beta * quotes['base_price'] + self.std_dev_mult * residual_std) / quotes['base_price']
            lower_bound = (beta * quotes['base_price'] - self.std_dev_mult * residual_std) / quotes['base_price']
            
            # 计算考虑手续费的交易边界
            trading_upper = upper_bound * (1 + COMMISSION_RATE)**2 * (1 + SLIPPAGE_RATE)
            trading_lower = lower_bound / (1 + COMMISSION_RATE)**2 * (1 - SLIPPAGE_RATE)
            
            # 确定交易信号（使用卖一价与上界比较，买一价与下界比较）
            if quotes['bid_ratio'] > trading_upper:  # 用卖一价与上界比较
                trade_signal = -1  # 卖出信号
            elif quotes['ask_ratio'] < trading_lower:  # 用买一价与下界比较
                trade_signal = 1   # 买入信号
            else:
                trade_signal = 0   # 不操作
            
            # 创建结果字典
            signal_data = {
                'time': current_time,
                'trade_signal': trade_signal,
                'base_etf': self.base_etf,
                'target_etf': self.target_etf,
                'base_price': quotes['base_price'],
                'target_price': quotes['target_price'],
                'price_ratio': quotes['price_ratio'],
                'ask_ratio': quotes['ask_ratio'],  # 卖一价比率
                'bid_ratio': quotes['bid_ratio'],  # 买一价比率
                'ma': beta,  # 对应于PairsStrategy中的beta
                'upper_bound': trading_upper,
                'lower_bound': trading_lower,
                'residual_std': residual_std
            }
            
            # 缓存结果，但保持回归参数不变
            self.last_signal = signal_data
            
            return signal_data
        
        try:
            # 准备数据
            X = data[self.base_etf].values
            y = data[self.target_etf].values
            
            # 添加常数项
            X = X
            
            # 执行OLS回归
            model = sm.OLS(y, X)
            results = model.fit()
            
            # 获取回归系数
            #const = results.params[0]
            beta = results.params[0]
            
            # 计算预测值和残差
            y_pred =  beta * data[self.base_etf].values
            residuals = y - y_pred
            
            # 计算残差标准差
            residual_std = np.std(residuals)
            
            # 获取最新盘口数据
            quotes = self.get_latest_quotes()
            if quotes is None:
                # 无法获取盘口数据时，使用K线数据最后一行
                print(Fore.YELLOW + "无法获取盘口数据，使用K线数据最后一行计算信号")
                price_ratio = data[self.target_etf].iloc[-1] / data[self.base_etf].iloc[-1]
                
                # 计算交易区间
                upper_bound = (beta * data[self.base_etf].iloc[-1] + self.std_dev_mult * residual_std) / data[self.base_etf].iloc[-1]
                lower_bound = (beta * data[self.base_etf].iloc[-1] - self.std_dev_mult * residual_std) / data[self.base_etf].iloc[-1]
                
                # 计算考虑手续费的交易边界
                trading_upper = upper_bound * (1 + COMMISSION_RATE)**2 * (1 + SLIPPAGE_RATE)
                trading_lower = lower_bound / (1 + COMMISSION_RATE)**2 * (1 - SLIPPAGE_RATE)
                
                # 确定交易信号
                if price_ratio > trading_upper:
                    trade_signal = -1  # 卖出信号
                elif price_ratio < trading_lower:
                    trade_signal = 1   # 买入信号
                else:
                    trade_signal = 0   # 不操作
                
                # 创建结果字典
                signal_data = {
                    'time': current_time,
                    'trade_signal': trade_signal,
                    'base_etf': self.base_etf,
                    'target_etf': self.target_etf,
                    'base_price': data[self.base_etf].iloc[-1],
                    'target_price': data[self.target_etf].iloc[-1],
                    'price_ratio': price_ratio,
                    'ma': beta,  # 对应于PairsStrategy中的beta
                    'upper_bound': trading_upper,
                    'lower_bound': trading_lower,
                    'residual_std': residual_std
                }
            else:
                # 使用盘口数据计算信号
                # 计算交易区间
                upper_bound = (beta * quotes['base_price'] + self.std_dev_mult * residual_std) / quotes['base_price']
                lower_bound = (beta * quotes['base_price'] - self.std_dev_mult * residual_std) / quotes['base_price']
                
                # 计算考虑手续费的交易边界
                trading_upper = upper_bound * (1 + COMMISSION_RATE)**2 * (1 + SLIPPAGE_RATE)
                trading_lower = lower_bound / (1 + COMMISSION_RATE)**2 * (1 - SLIPPAGE_RATE)
                
                # 确定交易信号（使用卖一价与上界比较，买一价与下界比较）
                if quotes['ask_ratio'] > trading_upper:  # 用卖一价与上界比较
                    trade_signal = -1  # 卖出信号
                elif quotes['bid_ratio'] < trading_lower:  # 用买一价与下界比较
                    trade_signal = 1   # 买入信号
                else:
                    trade_signal = 0   # 不操作
                
                # 创建结果字典
                signal_data = {
                    'time': current_time,
                    'trade_signal': trade_signal,
                    'base_etf': self.base_etf,
                    'target_etf': self.target_etf,
                    'base_price': quotes['base_price'],
                    'target_price': quotes['target_price'],
                    'price_ratio': quotes['price_ratio'],
                    'ask_ratio': quotes['ask_ratio'],  # 卖一价比率
                    'bid_ratio': quotes['bid_ratio'],  # 买一价比率
                    'ma': beta,  # 对应于PairsStrategy中的beta
                    'upper_bound': trading_upper,
                    'lower_bound': trading_lower,
                    'residual_std': residual_std
                }
            
            # 缓存结果
            self.last_signal = signal_data
            
            return signal_data
            
        except Exception as e:
            print(Fore.RED + f"OLS信号计算出错: {str(e)}")
            import traceback
            print(Fore.RED + traceback.format_exc())
            return None
    
    def calculate_signal(self):
        """
        计算交易信号
            
        Returns:
            dict: 交易信号和相关信息
        """
        # 获取/更新数据
        data = self.update_data()
        if data is None:
            print(Fore.RED + "无法计算信号: 数据获取失败")
            return None
        
        # 使用OLS计算信号
        signal_data = self.calculate_ols_signals(data)
        if signal_data is None:
            print(Fore.RED + "无法计算信号: OLS计算失败")
            return None
        
        return signal_data
    
    def display_signal(self, signal_data):
        """
        显示交易信号
        
        Args:
            signal_data: 计算出的信号数据
        """
        if signal_data is None:
            return
        
        # 打印分隔线
        print("\n" + "="*60)
        
        # 打印标题
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(Fore.CYAN + Style.BRIGHT + f"交易信号计算时间: {now}")
        print(Fore.CYAN + f"基于 {self.base_etf} 和 {self.target_etf} 的配对交易策略")
        
        # 打印当前价格
        print(f"\n当前价格:")
        print(f"{self.base_etf} (基准ETF): {Fore.YELLOW}{signal_data['base_price']:.4f}")
        print(f"{self.target_etf} (目标ETF): {Fore.YELLOW}{signal_data['target_price']:.4f}")
        print(f"价格比率: {Fore.YELLOW}{signal_data['price_ratio']:.4f}")
        
        # 如果存在买卖价比率，也打印出来
        if 'ask_ratio' in signal_data and 'bid_ratio' in signal_data:
            print(f"买一价比率: {Fore.YELLOW}{signal_data['bid_ratio']:.4f}")
            print(f"卖一价比率: {Fore.YELLOW}{signal_data['ask_ratio']:.4f}")
        
        # 打印交易区间
        print(f"\n交易区间:")
        print(f"Beta系数: {Fore.YELLOW}{signal_data['ma']:.4f}")
        print(f"残差标准差: {Fore.YELLOW}{signal_data.get('residual_std', 0):.4f}")
        print(f"上边界: {Fore.YELLOW}{signal_data['upper_bound']:.4f} {'(与卖一价比较)' if 'ask_ratio' in signal_data else ''}")
        print(f"下边界: {Fore.YELLOW}{signal_data['lower_bound']:.4f} {'(与买一价比较)' if 'bid_ratio' in signal_data else ''}")
        
        # 打印交易信号
        signal = signal_data['trade_signal']
        signal_text = ""
        signal_color = Fore.WHITE
        
        if signal == 1:  # 买入信号
            signal_text = "买入 " + self.target_etf
            signal_color = Fore.GREEN
        elif signal == -1:  # 卖出信号
            signal_text = "卖出 " + self.target_etf
            signal_color = Fore.RED
        else:  # 不操作
            signal_text = "不操作"
            signal_color = Fore.YELLOW
        
        print("\n" + "="*60)
        print(signal_color + Style.BRIGHT + f"交易信号: {signal_text}")
        print("="*60 + "\n")

def list_available_etfs(api):
    """
    列出可用的ETF
    
    Args:
        api: TdxHq_API实例
    """
    print(Fore.CYAN + Style.BRIGHT + "\n可用ETF列表:")
    print("="*60)
    
    # 查询深市ETF
    print(Fore.CYAN + "\n深圳ETF:")
    sz_etfs = api.get_security_list(0, 1)  # 市场代码0表示深市，起始位置1
    count = 0
    for etf in sz_etfs:
        if etf['code'].startswith('159'):  # 深市ETF代码通常以159开头
            print(f"{Fore.GREEN}{etf['code']} - {etf['name']}")
            count += 1
            # 每5个ETF换行显示
            if count % 5 == 0:
                print()
    
    # 查询沪市ETF
    print(Fore.CYAN + "\n上海ETF:")
    sh_etfs = api.get_security_list(1, 1)  # 市场代码1表示沪市，起始位置1
    count = 0
    for etf in sh_etfs:
        if etf['code'].startswith('51'):  # 沪市ETF代码通常以51开头
            print(f"{Fore.YELLOW}{etf['code']} - {etf['name']}")
            count += 1
            # 每5个ETF换行显示
            if count % 5 == 0:
                print()
    
    print("\n" + "="*60)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='ETF配对交易实时信号计算程序')
    
    parser.add_argument('--target', type=str, default=TARGET_ETF,
                        help=f'目标ETF代码 (默认: {TARGET_ETF})')
    parser.add_argument('--window', type=int, default=WINDOW,
                        help=f'滚动窗口大小 (默认: {WINDOW}分钟)')
    parser.add_argument('--std', type=float, default=STD_DEV_MULT,
                        help=f'标准差倍数 (默认: {STD_DEV_MULT})')
    parser.add_argument('--max-pos', type=float, default=MAX_POS_SIZE,
                        help=f'最大持仓比例 (默认: {MAX_POS_SIZE})')
    parser.add_argument('--interval', type=int, default=1,
                        help='自动刷新间隔,单位秒 (默认: 60秒, 0表示不自动刷新)')
    parser.add_argument('--list-etfs', action='store_true',
                        help='列出可用的ETF')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    print(Fore.CYAN + Style.BRIGHT + "ETF配对交易实时信号计算程序")
    print(Fore.CYAN + "基于pytdx实时数据源")
    
    # 创建TDX API实例
    api = TdxHq_API()
    calculator = None  # 初始化为None，防止引用错误
    connected = False
    
    try:
        # 连接到服务器
        for server in TDX_SERVERS:
            try:
                print(Fore.YELLOW + f"尝试连接服务器: {server['ip']}:{server['port']}...")
                connected = api.connect(server['ip'], server['port'])
                if connected:
                    print(Fore.GREEN + f"成功连接到服务器: {server['ip']}:{server['port']}")
                    break
            except Exception as e:
                print(Fore.RED + f"连接服务器 {server['ip']}:{server['port']} 失败: {str(e)}")
        
        if not connected:
            print(Fore.RED + Style.BRIGHT + "所有服务器连接失败!")
            print(Fore.RED + "无法获取实时数据，程序退出")
            return
        
        # 如果指定了列出ETF选项
        if args.list_etfs:
            list_available_etfs(api)
            api.disconnect()
            return
        
        print(Fore.CYAN + f"基准ETF: {BASE_ETF}，目标ETF: {args.target}")
        print(Fore.CYAN + f"窗口大小: {args.window}分钟，标准差倍数: {args.std}，最大持仓比例: {args.max_pos}")
        
        # 创建实时信号计算器
        calculator = RealTimeSignalCalculator(
            base_etf=BASE_ETF,
            target_etf=args.target,
            window=args.window,
            std_dev_mult=args.std,
            max_pos_size=args.max_pos
        )
        
        # 设置API连接
        calculator.set_api(api, connected=connected)
        
        # 第一次计算信号
        print(Fore.CYAN + "正在计算交易信号...")
        signal_data = calculator.calculate_signal()
        
        # 如果计算信号失败，退出程序
        if signal_data is None:
            print(Fore.RED + "无法计算交易信号，程序退出")
            return
            
        # 显示信号
        calculator.display_signal(signal_data)
        
        # 根据命令行参数决定是否自动刷新
        if args.interval > 0:
            print(Fore.CYAN + f"开始监控，每 {args.interval} 秒刷新一次，按Ctrl+C停止...")
            try:
                while True:
                    time.sleep(args.interval)
                    signal_data = calculator.calculate_signal()
                    if signal_data is None:
                        print(Fore.RED + "获取数据失败，停止监控")
                        break
                    calculator.display_signal(signal_data)
            except KeyboardInterrupt:
                print(Fore.YELLOW + "\n监控已停止")
        else:
            # 询问是否持续监控
            while True:
                choice = input(Fore.CYAN + "是否继续监控实时信号? (y/n): ").strip().lower()
                if choice == 'n':
                    break
                elif choice == 'y':
                    interval = int(input(Fore.CYAN + "请输入刷新间隔(秒): ").strip())
                    print(Fore.CYAN + f"开始监控，每 {interval} 秒刷新一次，按Ctrl+C停止...")
                    
                    try:
                        while True:
                            time.sleep(interval)
                            signal_data = calculator.calculate_signal()
                            if signal_data is None:
                                print(Fore.RED + "获取数据失败，停止监控")
                                break
                            calculator.display_signal(signal_data)
                    except KeyboardInterrupt:
                        print(Fore.YELLOW + "\n监控已停止")
                else:
                    print(Fore.RED + "无效的输入，请输入 'y' 或 'n'")
    
    except Exception as e:
        print(Fore.RED + f"程序运行出错: {str(e)}")
        import traceback
        print(Fore.RED + traceback.format_exc())
    
    finally:
        # 断开连接
        if calculator is not None and connected:
            calculator.disconnect()
        elif api and connected:
            api.disconnect()
            print(Fore.YELLOW + "已断开服务器连接")
        print(Fore.CYAN + "程序结束")

if __name__ == "__main__":
    main() 