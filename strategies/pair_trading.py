"""
配对交易策略

基于两个相关资产之间的价格差异计算交易信号
"""

from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
import logging
from statsmodels.regression.linear_model import OLS
import statsmodels.api as sm

from .base_strategy import BaseStrategy

class PairTradingStrategy(BaseStrategy):
    """
    配对交易策略
    
    基于两个相关资产之间的价格差异计算交易信号，当价格差异超过历史标准差的一定倍数时产生交易信号
    """
    
    def __init__(self, params: Dict[str, Any] = None, logger: logging.Logger = None):
        """
        初始化配对交易策略
        
        Args:
            params: 策略参数字典，包含以下键值：
                - base_code: 基准资产代码
                - target_code: 目标资产代码
                - window: 滚动窗口大小
                - std_dev_mult: 标准差倍数
                - max_pos_size: 最大持仓比例
            logger: 日志记录器
        """
        # 设置默认参数
        self.default_params = {
            "base_code": "518880",  # 默认基准资产：黄金ETF
            "target_code": "518850",  # 默认目标资产：黄金ETF联接
            "trade_code": "518850",  # 实际交易的资产代码
            "window": 120,  # 使用过去120个数据点
            "std_dev_mult": 1.2,  # 标准差倍数
            "max_pos_size": 1.0    # 最大持仓比例
        }
        
        # 合并用户参数和默认参数
        merged_params = self.default_params.copy()
        if params:
            merged_params.update(params)
        
        # 调用父类构造函数
        super().__init__(merged_params, logger)
    
    def _initialize(self):
        """
        初始化策略特定的变量
        """
        self.base_code = self.params["base_code"]
        self.target_code = self.params["target_code"]
        self.trade_code = self.params["trade_code"]
        self.window = self.params["window"]
        self.std_dev_mult = self.params["std_dev_mult"]
        self.max_pos_size = self.params["max_pos_size"]
        
        # 初始化价格存储
        self.base_prices = []
        self.target_prices = []
        self.trade_prices = []
        
        # 初始化计算结果
        self.spreads = []
        self.mean = 0
        self.std_dev = 0
        self.z_scores = []
        self.hedge_ratio = 1.0  # 对冲比率
        
        self.logger.info(f"配对交易策略初始化完成: {self.base_code} - {self.target_code}")
    
    def update_data(self, market_data: Dict[str, Any]) -> None:
        """
        更新市场数据
        
        Args:
            market_data: 新的市场数据，必须包含base_price和target_price
        """
        # 提取价格数据
        base_price = market_data.get("base_price")
        target_price = market_data.get("target_price")
        trade_price = market_data.get("trade_price", target_price)  # 如果没有提供交易价格，默认使用目标价格
        
        if base_price is None or target_price is None:
            self.logger.warning("市场数据中缺少价格信息")
            return
        
        # 添加到价格列表
        self.base_prices.append(base_price)
        self.target_prices.append(target_price)
        self.trade_prices.append(trade_price)
        
        # 调用父类方法保存原始数据
        super().update_data(market_data)
        
        # 计算技术指标
        self._calculate_indicators()
    
    def _calculate_indicators(self) -> None:
        """
        计算配对交易所需的技术指标
        """
        # 如果数据不足，无法计算指标
        if len(self.base_prices) < 2 or len(self.target_prices) < 2:
            return
        
        # 将价格列表转换为numpy数组
        base_array = np.array(self.base_prices)
        target_array = np.array(self.target_prices)
        
        # 计算对冲比率（简单线性回归）
        try:
            X = sm.add_constant(base_array)  # 添加常数项
            model = OLS(target_array, X).fit()
            if len(model.params) > 1: # 检查斜率系数是否存在
                self.hedge_ratio = model.params[1]  # 回归系数作为对冲比率
            else:
                self.hedge_ratio = 1.0 # 如果只返回常数项，使用默认比率
                self.logger.warning(f"对冲比率回归模型只返回常数项，使用默认比率1.0。")
        except Exception as e:
            self.logger.warning(f"计算对冲比率失败: {e}")
            # 如果计算失败，使用默认比率
            self.hedge_ratio = 1.0
        
        # 计算价差
        self.spreads = target_array - self.hedge_ratio * base_array
        
        # 如果数据足够，计算均值和标准差
        if len(self.spreads) >= self.window:
            # 使用滚动窗口
            recent_spreads = self.spreads[-self.window:]
            self.mean = np.mean(recent_spreads)
            self.std_dev = np.std(recent_spreads)
            
            # 计算最新的z-score
            latest_spread = self.spreads[-1]
            latest_z_score = (latest_spread - self.mean) / self.std_dev if self.std_dev > 0 else 0
            self.z_scores.append(latest_z_score)
        else:
            # 数据不足，使用所有数据
            self.mean = np.mean(self.spreads)
            self.std_dev = np.std(self.spreads)
            latest_z_score = (self.spreads[-1] - self.mean) / self.std_dev if self.std_dev > 0 else 0
            self.z_scores.append(latest_z_score)
        
        # 保存指标到数据历史
        self.add_indicator("hedge_ratio", [self.hedge_ratio])
        self.add_indicator("mean", [self.mean])
        self.add_indicator("std_dev", [self.std_dev])
        self.add_indicator("z_score", [latest_z_score])
    
    def calculate_signal(self, market_data: Dict[str, Any]) -> int:
        """
        计算交易信号
        
        Args:
            market_data: 市场数据字典
            
        Returns:
            int: 交易信号 1(买入), -1(卖出), 0(不操作)
        """
        # 检查是否有足够的数据
        if len(self.z_scores) == 0:
            self.logger.warning("没有足够的数据计算信号")
            return 0
        
        # 获取最新的z-score
        z_score = self.z_scores[-1]
        
        # 根据z-score和持仓状态计算信号
        if z_score <= -self.std_dev_mult and self.current_position <= 0:
            # 如果z-score小于负标准差倍数，且没有多头持仓，则买入
            signal = 1
        elif z_score >= self.std_dev_mult and self.current_position >= 0:
            # 如果z-score大于正标准差倍数，且没有空头持仓，则卖出
            signal = -1
        elif abs(z_score) < 0.5 and self.current_position != 0:
            # 如果z-score接近0，且有持仓，则平仓
            signal = 0
        else:
            # 其他情况维持当前持仓
            signal = 0
        
        # 考虑最大持仓比例
        if signal != 0:
            signal = int(signal * self.max_pos_size)
        
        # 记录信号
        self.logger.info(f"计算得到信号: {signal}, z-score: {z_score:.4f}, 均值: {self.mean:.4f}, 标准差: {self.std_dev:.4f}")
        
        return signal
    
    def get_latest_info(self) -> Dict[str, Any]:
        """
        获取最新的策略信息
        
        Returns:
            Dict: 包含最新指标和价格的字典
        """
        return {
            "hedge_ratio": self.hedge_ratio,
            "mean": self.mean,
            "std_dev": self.std_dev,
            "z_score": self.z_scores[-1] if self.z_scores else None,
            "base_price": self.base_prices[-1] if self.base_prices else None,
            "target_price": self.target_prices[-1] if self.target_prices else None,
            "trade_price": self.trade_prices[-1] if self.trade_prices else None,
            "signal": self.signal_history[-1] if self.signal_history else None,
            "position": self.current_position
        } 