"""
性能分析器

分析交易策略的性能指标，包括收益率、夏普比率、最大回撤等
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.figure import Figure


class PerformanceAnalyzer:
    """
    性能分析器
    
    分析交易策略的性能指标，包括收益率、夏普比率、最大回撤等
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化性能分析器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化性能指标
        self.metrics = {}
        
        self.logger.info("性能分析器初始化完成")
    
    def calculate_returns(self, equity_curve: pd.Series) -> pd.Series:
        """
        计算收益率序列
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            
        Returns:
            pd.Series: 收益率序列
        """
        # 计算日收益率
        returns = equity_curve.pct_change().fillna(0)
        return returns
    
    def calculate_metrics(self, equity_curve: pd.Series, risk_free_rate: float = 0.03) -> Dict[str, float]:
        """
        计算性能指标
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            risk_free_rate: 无风险利率，年化，默认3%
            
        Returns:
            Dict[str, float]: 性能指标字典
        """
        if len(equity_curve) < 2:
            self.logger.warning("权益曲线数据点不足，无法计算性能指标")
            return {}
            
        # 计算日收益率
        returns = self.calculate_returns(equity_curve)
        
        # 计算累计收益率
        total_return = (equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1
        
        # 计算年化收益率（假设252个交易日）
        days = (equity_curve.index[-1] - equity_curve.index[0]).days
        annual_return = (1 + total_return) ** (252 / max(days, 1)) - 1
        
        # 计算波动率（年化）
        volatility = returns.std() * np.sqrt(252)
        
        # 计算夏普比率
        daily_risk_free = (1 + risk_free_rate) ** (1 / 252) - 1
        excess_returns = returns - daily_risk_free
        sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # 计算最大回撤
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.cummax()
        drawdowns = (cumulative_returns / running_max) - 1
        max_drawdown = drawdowns.min()
        
        # 计算卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 计算索提诺比率
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 and downside_returns.std() > 0 else 1e-6
        sortino_ratio = (annual_return - risk_free_rate) / downside_deviation
        
        # 计算胜率
        win_days = (returns > 0).sum()
        total_days = len(returns)
        win_rate = win_days / total_days if total_days > 0 else 0
        
        # 计算盈亏比
        avg_win = returns[returns > 0].mean() if len(returns[returns > 0]) > 0 else 0
        avg_loss = abs(returns[returns < 0].mean()) if len(returns[returns < 0]) > 0 else 1e-6
        profit_loss_ratio = avg_win / avg_loss if avg_loss != 0 else 0
        
        # 汇总指标
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'start_date': equity_curve.index[0],
            'end_date': equity_curve.index[-1],
            'trading_days': len(equity_curve)
        }
        
        self.metrics = metrics
        self.logger.info("计算性能指标完成")
        
        return metrics
    
    def plot_equity_curve(self, equity_curve: pd.Series, benchmark: Optional[pd.Series] = None) -> Figure:
        """
        绘制权益曲线图
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            benchmark: 基准曲线，格式为带有日期索引的Series，可选
            
        Returns:
            Figure: Matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制策略权益曲线
        ax.plot(equity_curve.index, equity_curve, label='Strategy', color='blue', linewidth=2)
        
        # 绘制基准曲线（如果有）
        if benchmark is not None and len(benchmark) > 0:
            # 确保基准曲线与权益曲线有相同的起点
            benchmark = benchmark / benchmark.iloc[0] * equity_curve.iloc[0]
            ax.plot(benchmark.index, benchmark, label='Benchmark', color='gray', linewidth=1.5, linestyle='--')
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 设置标题和标签
        ax.set_title('Equity Curve', fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Value', fontsize=12)
        
        # 添加图例
        ax.legend()
        
        # 格式化日期轴
        fig.autofmt_xdate()
        
        # 添加性能指标文本
        if self.metrics:
            metrics_text = (
                f"Total Return: {self.metrics['total_return']:.2%}\n"
                f"Annual Return: {self.metrics['annual_return']:.2%}\n"
                f"Sharpe Ratio: {self.metrics['sharpe_ratio']:.2f}\n"
                f"Max Drawdown: {self.metrics['max_drawdown']:.2%}\n"
                f"Win Rate: {self.metrics['win_rate']:.2%}"
            )
            ax.text(0.02, 0.02, metrics_text, transform=ax.transAxes, 
                   verticalalignment='bottom', horizontalalignment='left',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        return fig
    
    def plot_drawdown(self, equity_curve: pd.Series) -> Figure:
        """
        绘制回撤图
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            
        Returns:
            Figure: Matplotlib图形对象
        """
        # 计算回撤序列
        returns = self.calculate_returns(equity_curve)
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.cummax()
        drawdowns = (cumulative_returns / running_max) - 1
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制回撤曲线
        ax.fill_between(drawdowns.index, drawdowns, 0, color='red', alpha=0.3)
        ax.plot(drawdowns.index, drawdowns, color='red', linewidth=1)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 设置标题和标签
        ax.set_title('Drawdown', fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Drawdown', fontsize=12)
        
        # 格式化日期轴
        fig.autofmt_xdate()
        
        # 添加最大回撤标注
        if len(drawdowns) > 0:
            max_dd = drawdowns.min()
            max_dd_date = drawdowns.idxmin()
            ax.annotate(f'Max Drawdown: {max_dd:.2%}', 
                       xy=(max_dd_date, max_dd),
                       xytext=(max_dd_date, max_dd / 2),
                       arrowprops=dict(facecolor='black', shrink=0.05),
                       horizontalalignment='right')
        
        plt.tight_layout()
        
        return fig
    
    def plot_monthly_returns(self, equity_curve: pd.Series) -> Figure:
        """
        绘制月度收益率热图
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            
        Returns:
            Figure: Matplotlib图形对象
        """
        # 计算日收益率
        returns = self.calculate_returns(equity_curve)
        
        # 计算月度收益率
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        
        # 创建月度收益率矩阵（年x月）
        monthly_returns_matrix = monthly_returns.groupby([monthly_returns.index.year, monthly_returns.index.month]).first().unstack()
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 绘制热图
        cmap = plt.cm.RdYlGn  # 红黄绿色图，负值为红色，正值为绿色
        heatmap = ax.pcolor(monthly_returns_matrix, cmap=cmap, edgecolors='white', linewidths=2)
        
        # 设置坐标轴
        ax.set_xticks(np.arange(monthly_returns_matrix.shape[1]) + 0.5)
        ax.set_yticks(np.arange(monthly_returns_matrix.shape[0]) + 0.5)
        
        # 设置坐标轴标签
        month_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        ax.set_xticklabels(month_labels[:monthly_returns_matrix.shape[1]])
        ax.set_yticklabels(monthly_returns_matrix.index.astype(str))
        
        # 添加颜色条
        cbar = plt.colorbar(heatmap)
        cbar.set_label('Monthly Return')
        
        # 在每个单元格中添加收益率文本
        for i in range(monthly_returns_matrix.shape[0]):
            for j in range(monthly_returns_matrix.shape[1]):
                if not np.isnan(monthly_returns_matrix.iloc[i, j]):
                    value = monthly_returns_matrix.iloc[i, j]
                    text_color = 'white' if abs(value) > 0.1 else 'black'  # 根据背景色调整文本颜色
                    ax.text(j + 0.5, i + 0.5, f'{value:.2%}',
                           ha='center', va='center', color=text_color)
        
        # 设置标题
        ax.set_title('Monthly Returns', fontsize=14)
        
        plt.tight_layout()
        
        return fig
    
    def generate_report(self, equity_curve: pd.Series, benchmark: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        生成完整的性能报告
        
        Args:
            equity_curve: 权益曲线，格式为带有日期索引的Series
            benchmark: 基准曲线，格式为带有日期索引的Series，可选
            
        Returns:
            Dict[str, Any]: 性能报告字典，包含指标和图表
        """
        # 计算性能指标
        metrics = self.calculate_metrics(equity_curve)
        
        # 生成图表
        equity_fig = self.plot_equity_curve(equity_curve, benchmark)
        drawdown_fig = self.plot_drawdown(equity_curve)
        monthly_returns_fig = self.plot_monthly_returns(equity_curve)
        
        # 计算相对于基准的指标（如果有基准）
        benchmark_metrics = {}
        if benchmark is not None and len(benchmark) > 0:
            benchmark_returns = benchmark.pct_change().fillna(0)
            
            # 计算基准的累计收益率
            benchmark_total_return = (benchmark.iloc[-1] / benchmark.iloc[0]) - 1
            
            # 计算基准的年化收益率
            days = (benchmark.index[-1] - benchmark.index[0]).days
            benchmark_annual_return = (1 + benchmark_total_return) ** (252 / max(days, 1)) - 1
            
            # 计算基准的波动率
            benchmark_volatility = benchmark_returns.std() * np.sqrt(252)
            
            # 计算基准的最大回撤
            benchmark_cumulative_returns = (1 + benchmark_returns).cumprod()
            benchmark_running_max = benchmark_cumulative_returns.cummax()
            benchmark_drawdowns = (benchmark_cumulative_returns / benchmark_running_max) - 1
            benchmark_max_drawdown = benchmark_drawdowns.min()
            
            # 计算超额收益
            excess_return = metrics['annual_return'] - benchmark_annual_return
            
            # 计算信息比率
            tracking_error = (returns - benchmark_returns).std() * np.sqrt(252)
            information_ratio = excess_return / tracking_error if tracking_error > 0 else 0
            
            # 计算贝塔系数
            covariance = np.cov(returns, benchmark_returns)[0, 1]
            variance = np.var(benchmark_returns)
            beta = covariance / variance if variance > 0 else 0
            
            # 计算阿尔法系数
            alpha = metrics['annual_return'] - (0.03 + beta * (benchmark_annual_return - 0.03))
            
            benchmark_metrics = {
                'benchmark_total_return': benchmark_total_return,
                'benchmark_annual_return': benchmark_annual_return,
                'benchmark_volatility': benchmark_volatility,
                'benchmark_max_drawdown': benchmark_max_drawdown,
                'excess_return': excess_return,
                'information_ratio': information_ratio,
                'beta': beta,
                'alpha': alpha
            }
        
        # 合并所有指标
        all_metrics = {**metrics, **benchmark_metrics}
        
        # 生成报告
        report = {
            'metrics': all_metrics,
            'equity_curve': equity_fig,
            'drawdown': drawdown_fig,
            'monthly_returns': monthly_returns_fig
        }
        
        self.logger.info("生成性能报告完成")
        
        return report
    
    def save_report(self, report: Dict[str, Any], output_dir: str):
        """
        保存性能报告到文件
        
        Args:
            report: 性能报告字典
            output_dir: 输出目录
        """
        import os
        import json
        from matplotlib.backends.backend_pdf import PdfPages
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存指标到JSON文件
        metrics_file = os.path.join(output_dir, 'performance_metrics.json')
        with open(metrics_file, 'w') as f:
            # 将日期转换为字符串
            metrics = report['metrics'].copy()
            for key, value in metrics.items():
                if isinstance(value, (datetime, pd.Timestamp)):
                    metrics[key] = value.strftime('%Y-%m-%d')
            
            json.dump(metrics, f, indent=4)
        
        # 保存图表到PDF文件
        figures_file = os.path.join(output_dir, 'performance_figures.pdf')
        with PdfPages(figures_file) as pdf:
            pdf.savefig(report['equity_curve'])
            pdf.savefig(report['drawdown'])
            pdf.savefig(report['monthly_returns'])
        
        # 单独保存每个图表为PNG文件
        report['equity_curve'].savefig(os.path.join(output_dir, 'equity_curve.png'))
        report['drawdown'].savefig(os.path.join(output_dir, 'drawdown.png'))
        report['monthly_returns'].savefig(os.path.join(output_dir, 'monthly_returns.png'))
        
        self.logger.info(f"性能报告已保存到目录: {output_dir}")
        
        # 关闭图表以释放内存
        plt.close(report['equity_curve'])
        plt.close(report['drawdown'])
        plt.close(report['monthly_returns'])