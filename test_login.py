#!/usr/bin/env python3
"""
测试登录功能

使用方法:
python test_login.py <密码>
"""

import asyncio
import json
import sys
import os
from citic_trader.login import CiticLogin, login_sync


def load_cookies():
    """加载cookies文件"""
    cookies_file = "cookies.json"
    if os.path.exists(cookies_file):
        try:
            with open(cookies_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载cookies失败: {e}")
    return {}


def save_cookies(cookies):
    """保存cookies到文件"""
    try:
        with open("cookies.json", 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        print("cookies已保存到 cookies.json")
    except Exception as e:
        print(f"保存cookies失败: {e}")


async def test_login_async(password: str):
    """异步测试登录"""
    print("=== 异步登录测试 ===")
    
    # 加载现有cookies
    cookies = load_cookies()
    if cookies:
        print(f"加载了 {len(cookies)} 个cookies")
    
    async with CiticLogin() as login_manager:
        try:
            # 测试tradeprepare
            print("1. 测试获取交易准备信息...")
            login_manager.set_cookies(cookies)
            prepare_result = await login_manager.trade_prepare()
            print("✓ tradeprepare成功")
            print(f"  - 加密方法: {prepare_result.get('encrypt_method')}")
            print(f"  - 时间种子: {prepare_result.get('timeseed')[:20]}...")
            print(f"  - 需要检查: {prepare_result.get('needcheck')}")
            
            # 测试完整登录
            print("\n2. 测试完整登录流程...")
            login_result, updated_cookies = await login_manager.login(password, cookies)
            print("✓ 登录成功!")
            
            # 显示结果
            if 'psw_session' in login_result:
                session = login_result['psw_session']
                print(f"  - 会话信息: {session[:50]}...")
            
            # 保存更新的cookies
            if updated_cookies:
                save_cookies(updated_cookies)
            
            return True
            
        except Exception as e:
            print(f"✗ 登录失败: {e}")
            return False


def test_login_sync_func(password: str):
    """同步测试登录"""
    print("\n=== 同步登录测试 ===")
    
    # 加载现有cookies
    cookies = load_cookies()
    
    try:
        result, updated_cookies = login_sync(password, cookies)
        print("✓ 同步登录成功!")
        
        # 显示结果
        if 'psw_session' in result:
            session = result['psw_session']
            print(f"  - 会话信息: {session[:50]}...")
        
        # 保存更新的cookies
        if updated_cookies:
            save_cookies(updated_cookies)
        
        return True
        
    except Exception as e:
        print(f"✗ 同步登录失败: {e}")
        return False


def test_encrypt_only():
    """仅测试加密功能"""
    print("\n=== 加密功能测试 ===")
    
    from citic_trader.encrypt import encrypt_password, TimeSeedHelper
    
    # 模拟配置数据
    test_config = {
        "timeseed": "17485820861926175275",
        "key": "C0CEE4B6914866965BE0D3D3F155D85FA296CBBF13956EDBB32146C8C61E36B164BDD8F399CFEF37B60AB650BABBB1F62EFA679119B375E0E1A0272D7CD84DD6DE8D2C618753A7C2D79AEFBC2249F23A8797A4AC885CE795CBA8DB27F8BEBCC0DADCC155ED216BAE6A2923A98E45A7CD1BE1F330CB1B66054B2FC9A117522633"
    }
    
    test_password = "123456"
    
    try:
        # 测试时间种子处理
        ts_helper = TimeSeedHelper(test_config["timeseed"])
        time_seed = ts_helper.get()
        time_code = ts_helper.get_time_code()
        
        print(f"时间种子: {time_seed}")
        print(f"时间码: {time_code}")
        
        # 测试密码加密
        encrypted = encrypt_password(test_config, test_password)
        print(f"加密结果长度: {len(encrypted)}")
        print(f"加密结果前100字符: {encrypted[:100]}")
        
        print("✓ 加密功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 加密功能测试失败: {e}")
        return False


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_login.py <密码>")
        print("或者: python test_login.py --encrypt-only  # 仅测试加密")
        sys.exit(1)
    
    if sys.argv[1] == "--encrypt-only":
        test_encrypt_only()
        return
    
    password = sys.argv[1]
    
    print("中信建投登录功能测试")
    print("=" * 50)
    
    # 测试加密功能
    encrypt_ok = test_encrypt_only()
    
    if encrypt_ok:
        # 测试异步登录
        async_ok = await test_login_async(password)
        
        # 测试同步登录
        sync_ok = test_login_sync_func(password)
        
        print("\n" + "=" * 50)
        print("测试结果汇总:")
        print(f"  加密功能: {'✓' if encrypt_ok else '✗'}")
        print(f"  异步登录: {'✓' if async_ok else '✗'}")
        print(f"  同步登录: {'✓' if sync_ok else '✗'}")
        
        if async_ok or sync_ok:
            print("\n🎉 登录功能正常工作!")
        else:
            print("\n❌ 登录功能存在问题")
    else:
        print("\n❌ 加密功能存在问题，跳过登录测试")


if __name__ == "__main__":
    asyncio.run(main())
