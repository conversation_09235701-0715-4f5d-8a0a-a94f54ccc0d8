#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ETF自动交易系统启动脚本

检查依赖、验证cookies并启动自动交易系统
"""

import os
import sys
import json
import time
import subprocess
import importlib.util
from datetime import datetime

def check_module(module_name):
    """检查模块是否已安装"""
    return importlib.util.find_spec(module_name) is not None

def check_dependencies():
    """检查所需依赖是否已安装"""
    required_modules = [
        "pytdx", "pandas", "numpy", "statsmodels", 
        "colorama", "requests", "argparse"
    ]
    
    missing_modules = []
    for module in required_modules:
        if not check_module(module):
            missing_modules.append(module)
    
    if missing_modules:
        print(f"缺少以下依赖模块: {', '.join(missing_modules)}")
        print("请使用以下命令安装依赖:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def check_cookies(cookies_file="cookies.json"):
    """检查cookies文件是否存在且有效"""
    if not os.path.exists(cookies_file):
        print(f"错误: cookies文件 '{cookies_file}' 不存在!")
        print("请先获取中信建投的cookies并保存到该文件")
        return False
    
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        
        if not cookies:
            print(f"错误: cookies文件 '{cookies_file}' 为空!")
            return False
        
        # 检查是否包含关键cookie (这里仅作示例，实际可能需要检查特定的cookie)
        required_cookies = ["JSESSIONID"]  # 根据实际情况修改
        missing_cookies = [c for c in required_cookies if c not in cookies]
        
        if missing_cookies:
            print(f"警告: cookies文件缺少以下关键cookie: {', '.join(missing_cookies)}")
            print("cookies可能已过期或不完整，请重新获取")
            return False
        
        print(f"cookies文件 '{cookies_file}' 检查通过")
        return True
    except json.JSONDecodeError:
        print(f"错误: cookies文件 '{cookies_file}' 格式不正确!")
        return False
    except Exception as e:
        print(f"检查cookies时出错: {str(e)}")
        return False

def check_files():
    """检查必要的文件是否存在"""
    required_files = ["auto_trade.py", "realtime_signal.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误: 缺少以下必要文件: {', '.join(missing_files)}")
        return False
    
    return True

def is_trading_time():
    """检查当前是否为交易时间"""
    now = datetime.now()
    weekday = now.weekday()
    
    # 如果是周末，不是交易时间
    if weekday >= 5:  # 5是周六，6是周日
        return False
    
    # 检查是否在交易时段 (9:30-11:30, 13:00-15:00)
    hour, minute = now.hour, now.minute
    
    # 上午交易时段
    if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute < 30):
        return True
    
    # 下午交易时段
    if (hour >= 13 and hour < 15):
        return True
    
    return False

def start_trading(args=None):
    """启动自动交易系统"""
    command = [sys.executable, "auto_trade.py"]
    
    # 添加命令行参数
    if args:
        for key, value in args.items():
            if value is not None:  # 只添加非空参数
                command.append(f"--{key}")
                command.append(str(value))
    
    print("启动自动交易系统...")
    print(f"执行命令: {' '.join(command)}")
    
    try:
        # 使用subprocess.run执行命令
        subprocess.run(command)
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
    except Exception as e:
        print(f"启动自动交易系统时出错: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("ETF自动交易系统启动工具")
    print("=" * 60)
    
    # 检查依赖
    print("\n检查依赖...")
    if not check_dependencies():
        return
    
    # 检查文件
    print("\n检查必要文件...")
    if not check_files():
        return
    
    # 检查cookies
    print("\n检查cookies...")
    if not check_cookies():
        choice = input("cookies检查失败，是否仍要继续? (y/n): ").strip().lower()
        if choice != 'y':
            print("程序退出")
            return
    
    # 检查交易时间
    if not is_trading_time():
        print("\n警告: 当前不是交易时间!")
        choice = input("是否仍要启动系统? (y/n): ").strip().lower()
        if choice != 'y':
            print("程序退出")
            return
    
    # 配置交易参数
    print("\n配置交易参数...")
    args = {}
    
    try:
        # 目标ETF
        target = input("请输入目标ETF代码 (默认: 518890): ").strip()
        if target:
            args['target'] = target
        
        # 交易ETF
        trade = input("请输入交易ETF代码 (默认: 518850): ").strip()
        if trade:
            args['trade'] = trade
        
        # 交易数量
        volume = input("请输入交易数量 (默认: 100): ").strip()
        if volume:
            args['volume'] = int(volume)
        
        # 检查间隔
        interval = input("请输入检查信号间隔(秒) (默认: 5): ").strip()
        if interval:
            args['interval'] = int(interval)
        
        # 高级参数
        advanced = input("是否配置高级参数? (y/n, 默认: n): ").strip().lower()
        if advanced == 'y':
            # 窗口大小
            window = input("请输入滚动窗口大小(分钟) (默认: 120): ").strip()
            if window:
                args['window'] = int(window)
            
            # 标准差倍数
            std = input("请输入标准差倍数 (默认: 1.2): ").strip()
            if std:
                args['std'] = float(std)
            
            # 最大持仓比例
            max_pos = input("请输入最大持仓比例 (默认: 1.0): ").strip()
            if max_pos:
                args['max_pos'] = float(max_pos)
            
            # cookies文件
            cookies = input("请输入cookies文件路径 (默认: cookies.json): ").strip()
            if cookies:
                args['cookies'] = cookies
    except ValueError as e:
        print(f"参数错误: {str(e)}")
        return
    
    # 确认启动
    print("\n交易参数配置完成，即将启动自动交易系统...")
    print("配置参数:")
    for key, value in args.items():
        print(f"  --{key}: {value}")
    
    choice = input("\n确认启动自动交易系统? (y/n): ").strip().lower()
    if choice != 'y':
        print("程序退出")
        return
    
    # 启动交易系统
    start_trading(args)

if __name__ == "__main__":
    main() 