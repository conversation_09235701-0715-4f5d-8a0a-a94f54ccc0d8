"""
实时数据管理器

负责管理和提供实时市场数据，支持多数据源和数据缓存
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Callable
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict

from .data_source import DataSource


class RealTimeDataManager:
    """
    实时数据管理器
    
    负责管理和提供实时市场数据，支持多数据源和数据缓存
    """
    
    def __init__(self, primary_data_source: DataSource, 
                backup_data_sources: List[DataSource] = None,
                logger: Optional[logging.Logger] = None):
        """
        初始化实时数据管理器
        
        Args:
            primary_data_source: 主数据源
            backup_data_sources: 备用数据源列表
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 设置数据源
        self.primary_data_source = primary_data_source
        self.backup_data_sources = backup_data_sources or []
        self.data_sources = [primary_data_source] + self.backup_data_sources
        
        # 价格缓存，格式：{code: {'price': price, 'timestamp': timestamp}}
        self.price_cache = {}
        # 行情缓存，格式：{code: {'quote': quote_dict, 'timestamp': timestamp}}
        self.quote_cache = {}
        # K线数据缓存，格式：{code_start_end_ktype: {'data': df, 'timestamp': timestamp}}
        self.kdata_cache = {}
        
        # 缓存过期时间（秒）
        self.price_cache_expiry = 3  # 价格缓存3秒过期
        self.quote_cache_expiry = 3  # 行情缓存3秒过期
        self.kdata_cache_expiry = 300  # K线数据缓存5分钟过期
        
        # 订阅的股票代码列表
        self.subscribed_codes = set()
        
        # 价格更新回调函数，格式：{code: [callback1, callback2, ...]}
        self.price_update_callbacks = defaultdict(list)
        
        # 自动更新线程
        self.auto_update_thread = None
        self.auto_update_interval = 3  # 自动更新间隔（秒）
        self.auto_update_running = False
        
        self.logger.info("实时数据管理器初始化完成")
    
    def subscribe(self, codes: List[str], callback: Optional[Callable[[str, float], None]] = None):
        """
        订阅股票实时数据
        
        Args:
            codes: 股票代码列表
            callback: 价格更新回调函数，接收参数(code, price)
        """
        for code in codes:
            formatted_code = self.primary_data_source.format_code(code)
            self.subscribed_codes.add(formatted_code)
            
            if callback:
                self.price_update_callbacks[formatted_code].append(callback)
                
        self.logger.info(f"订阅股票: {', '.join(codes)}")
    
    def unsubscribe(self, codes: List[str], callback: Optional[Callable] = None):
        """
        取消订阅股票实时数据
        
        Args:
            codes: 股票代码列表
            callback: 要移除的回调函数，如果为None则移除所有回调
        """
        for code in codes:
            formatted_code = self.primary_data_source.format_code(code)
            
            if callback:
                # 只移除特定回调
                if formatted_code in self.price_update_callbacks:
                    try:
                        self.price_update_callbacks[formatted_code].remove(callback)
                    except ValueError:
                        pass
            else:
                # 移除所有回调
                if formatted_code in self.price_update_callbacks:
                    self.price_update_callbacks[formatted_code] = []
            
            # 如果没有回调函数，则取消订阅
            if not self.price_update_callbacks[formatted_code]:
                self.subscribed_codes.discard(formatted_code)
                
        self.logger.info(f"取消订阅股票: {', '.join(codes)}")
    
    def get_price(self, code: str, use_cache: bool = True) -> Optional[float]:
        """
        获取指定股票的最新价格
        
        Args:
            code: 股票代码
            use_cache: 是否使用缓存
            
        Returns:
            Optional[float]: 最新价格，如果获取失败则返回None
        """
        formatted_code = self.primary_data_source.format_code(code)
        
        # 检查缓存
        if use_cache and formatted_code in self.price_cache:
            cache_time = self.price_cache[formatted_code]['timestamp']
            if (datetime.now() - cache_time).total_seconds() < self.price_cache_expiry:
                return self.price_cache[formatted_code]['price']
        
        # 依次尝试所有数据源
        for data_source in self.data_sources:
            try:
                if data_source.is_connected():
                    price = data_source.get_price(formatted_code)
                    if price is not None:
                        # 更新缓存
                        self.price_cache[formatted_code] = {
                            'price': price,
                            'timestamp': datetime.now()
                        }
                        return price
            except Exception as e:
                self.logger.warning(f"从数据源 {data_source.name} 获取 {formatted_code} 价格失败: {str(e)}")
                continue
        
        self.logger.error(f"无法从任何数据源获取股票 {formatted_code} 的价格")
        return None
    
    def get_quote(self, code: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取指定股票的行情数据
        
        Args:
            code: 股票代码
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, Any]: 行情数据字典，包含价格、成交量等信息
        """
        formatted_code = self.primary_data_source.format_code(code)
        
        # 检查缓存
        if use_cache and formatted_code in self.quote_cache:
            cache_time = self.quote_cache[formatted_code]['timestamp']
            if (datetime.now() - cache_time).total_seconds() < self.quote_cache_expiry:
                return self.quote_cache[formatted_code]['quote']
        
        # 依次尝试所有数据源
        for data_source in self.data_sources:
            try:
                if data_source.is_connected():
                    quote = data_source.get_quote(formatted_code)
                    if quote:
                        # 更新缓存
                        self.quote_cache[formatted_code] = {
                            'quote': quote,
                            'timestamp': datetime.now()
                        }
                        return quote
            except Exception as e:
                self.logger.warning(f"从数据源 {data_source.name} 获取 {formatted_code} 行情失败: {str(e)}")
                continue
        
        self.logger.error(f"无法从任何数据源获取股票 {formatted_code} 的行情")
        return {}
    
    def get_k_data(self, code: str, start_date: str, end_date: str, 
                  ktype: str = 'D', use_cache: bool = True) -> pd.DataFrame:
        """
        获取指定股票的K线数据
        
        Args:
            code: 股票代码
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            ktype: K线类型，D=日线，W=周线，M=月线，5=5分钟线，15=15分钟线，等
            use_cache: 是否使用缓存
            
        Returns:
            pd.DataFrame: K线数据，包含开盘价、收盘价、最高价、最低价、成交量等
        """
        formatted_code = self.primary_data_source.format_code(code)
        cache_key = f"{formatted_code}_{start_date}_{end_date}_{ktype}"
        
        # 检查缓存
        if use_cache and cache_key in self.kdata_cache:
            cache_time = self.kdata_cache[cache_key]['timestamp']
            if (datetime.now() - cache_time).total_seconds() < self.kdata_cache_expiry:
                return self.kdata_cache[cache_key]['data']
        
        # 依次尝试所有数据源
        for data_source in self.data_sources:
            try:
                if data_source.is_connected():
                    kdata = data_source.get_k_data(
                        formatted_code, start_date, end_date, ktype
                    )
                    if not kdata.empty:
                        # 更新缓存
                        self.kdata_cache[cache_key] = {
                            'data': kdata,
                            'timestamp': datetime.now()
                        }
                        return kdata
            except Exception as e:
                self.logger.warning(f"从数据源 {data_source.name} 获取 {formatted_code} K线数据失败: {str(e)}")
                continue
        
        self.logger.error(f"无法从任何数据源获取股票 {formatted_code} 的K线数据")
        return pd.DataFrame()
    
    def get_multiple_prices(self, codes: List[str], use_cache: bool = True) -> Dict[str, float]:
        """
        批量获取多个股票的最新价格
        
        Args:
            codes: 股票代码列表
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, float]: 股票代码到价格的映射
        """
        result = {}
        formatted_codes = [self.primary_data_source.format_code(code) for code in codes]
        
        # 筛选出需要实时获取的代码（缓存过期或不存在）
        codes_to_fetch = []
        for code in formatted_codes:
            if not use_cache or code not in self.price_cache or \
               (datetime.now() - self.price_cache[code]['timestamp']).total_seconds() >= self.price_cache_expiry:
                codes_to_fetch.append(code)
            else:
                # 使用缓存
                result[code] = self.price_cache[code]['price']
        
        if not codes_to_fetch:
            return result
            
        # 依次尝试所有数据源获取实时价格
        for data_source in self.data_sources:
            try:
                if data_source.is_connected() and codes_to_fetch:
                    prices = data_source.get_multiple_prices(codes_to_fetch)
                    
                    # 更新结果和缓存
                    for code, price in prices.items():
                        result[code] = price
                        self.price_cache[code] = {
                            'price': price,
                            'timestamp': datetime.now()
                        }
                    
                    # 移除已获取的代码
                    codes_to_fetch = [code for code in codes_to_fetch if code not in prices]
            except Exception as e:
                self.logger.warning(f"从数据源 {data_source.name} 批量获取价格失败: {str(e)}")
                continue
        
        if codes_to_fetch:
            self.logger.warning(f"无法获取部分股票的价格: {', '.join(codes_to_fetch)}")
            
        return result
    
    def start_auto_update(self, interval: int = None):
        """
        启动自动更新线程
        
        Args:
            interval: 更新间隔（秒）
        """
        if interval is not None:
            self.auto_update_interval = interval
            
        if self.auto_update_thread is not None and self.auto_update_thread.is_alive():
            self.logger.warning("自动更新线程已在运行")
            return
            
        self.auto_update_running = True
        self.auto_update_thread = threading.Thread(target=self._auto_update_task)
        self.auto_update_thread.daemon = True
        self.auto_update_thread.start()
        
        self.logger.info(f"启动自动更新线程，更新间隔: {self.auto_update_interval}秒")
    
    def stop_auto_update(self):
        """停止自动更新线程"""
        self.auto_update_running = False
        if self.auto_update_thread is not None:
            self.auto_update_thread.join(timeout=2.0)
            self.auto_update_thread = None
            
        self.logger.info("停止自动更新线程")
    
    def _auto_update_task(self):
        """自动更新任务"""
        while self.auto_update_running:
            try:
                if self.subscribed_codes:
                    # 批量获取价格
                    prices = self.get_multiple_prices(list(self.subscribed_codes), use_cache=False)
                    
                    # 触发回调
                    for code, price in prices.items():
                        callbacks = self.price_update_callbacks.get(code, [])
                        for callback in callbacks:
                            try:
                                callback(code, price)
                            except Exception as e:
                                self.logger.error(f"执行回调函数时出错: {str(e)}")
            except Exception as e:
                self.logger.error(f"自动更新任务出错: {str(e)}")
                
            time.sleep(self.auto_update_interval)
    
    def clear_cache(self, code: Optional[str] = None):
        """
        清除缓存
        
        Args:
            code: 股票代码，如果为None则清除所有缓存
        """
        if code is None:
            self.price_cache.clear()
            self.quote_cache.clear()
            self.kdata_cache.clear()
            self.logger.info("清除所有缓存")
        else:
            formatted_code = self.primary_data_source.format_code(code)
            if formatted_code in self.price_cache:
                del self.price_cache[formatted_code]
            if formatted_code in self.quote_cache:
                del self.quote_cache[formatted_code]
                
            # 清除K线缓存
            keys_to_delete = []
            for key in self.kdata_cache:
                if key.startswith(f"{formatted_code}_"):
                    keys_to_delete.append(key)
            for key in keys_to_delete:
                del self.kdata_cache[key]
                
            self.logger.info(f"清除股票 {formatted_code} 的缓存")
    
    def __del__(self):
        """析构函数，确保停止自动更新线程"""
        self.stop_auto_update() 