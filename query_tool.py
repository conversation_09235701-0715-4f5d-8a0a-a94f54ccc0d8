#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账户查询工具

提供命令行界面，用于查询账户资金、持仓和委托记录信息
"""

import os
import sys
import json
import logging
import argparse
import requests
from typing import Dict, Any

from citic_trader.query import QueryClient
from citic_trader.utils import setup_logger
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """
    从文件加载cookies
    
    Args:
        cookies_file: cookies文件路径，JSON格式
        
    Returns:
        Dict: cookies字典
    """
    try:
        if not os.path.exists(cookies_file):
            print(f"{Fore.RED}Cookies文件不存在: {cookies_file}")
            return {}
            
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
            print(f"{Fore.GREEN}成功从 {cookies_file} 加载cookies")
            return cookies
    except Exception as e:
        print(f"{Fore.RED}加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """
    设置会话，添加cookies
    
    Args:
        cookies: cookies字典
        
    Returns:
        requests.Session: 会话对象
    """
    session = requests.Session()
    
    # 添加cookies到会话
    for key, value in cookies.items():
        session.cookies.set(key, value)
    
    return session

def setup_user_info() -> Dict[str, Any]:
    """
    设置用户信息
    
    Returns:
        Dict: 用户信息字典
    """
    return {
        "account_id": "",  # 会自动从查询接口获取
        "stockholder_code": "",  # 会自动从查询接口获取
        "psw_session": "",  # 通常从cookies中获取
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def print_account_info(account_info: Dict[str, Any]) -> None:
    """
    打印账户信息
    
    Args:
        account_info: 账户信息字典
    """
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 资金信息 -----{Style.RESET_ALL}")
    funds = account_info["data"]["funds"]
    print(f"{Fore.WHITE}总资产: {Fore.YELLOW}{funds['total_assets']}元")
    print(f"{Fore.WHITE}可用资金: {Fore.YELLOW}{funds['available_cash']}元")
    print(f"{Fore.WHITE}可取资金: {Fore.YELLOW}{funds['withdrawable_cash']}元")
    print(f"{Fore.WHITE}冻结资金: {Fore.YELLOW}{funds['frozen_cash']}元")
    print(f"{Fore.WHITE}持仓市值: {Fore.YELLOW}{funds['market_value']}元")
    print(f"{Fore.WHITE}总盈亏: {Fore.YELLOW}{funds['total_profit']}元")
    print(f"{Fore.WHITE}今日盈亏: {Fore.YELLOW}{funds['today_profit']}元")
    
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 交易限制 -----{Style.RESET_ALL}")
    limits = account_info["data"]["limits"]
    print(f"{Fore.WHITE}最大可买入金额: {Fore.YELLOW}{limits['max_buy_money']}元")
    print(f"{Fore.WHITE}最大可卖出数量: {Fore.YELLOW}{limits['max_sell_qty']}份")
    
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 股东代码 -----{Style.RESET_ALL}")
    stockholder_code = account_info["data"]["stockholder_code"]
    print(f"{Fore.WHITE}股东代码: {Fore.YELLOW}{stockholder_code}")

def print_positions(positions: Dict[str, Any]) -> None:
    """
    打印持仓信息
    
    Args:
        positions: 持仓信息字典
    """
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 持仓信息 -----{Style.RESET_ALL}")
    
    if not positions["data"]:
        print(f"{Fore.YELLOW}无持仓")
        return
    
    for position in positions["data"]:
        profit_color = Fore.RED if float(position.get('earn_val', '0')) >= 0 else Fore.GREEN
        print(f"{Fore.WHITE}代码: {Fore.YELLOW}{position.get('code')}, "
              f"{Fore.WHITE}名称: {Fore.YELLOW}{position.get('name')}")
        print(f"  {Fore.WHITE}持仓数量: {Fore.YELLOW}{position.get('hold_num')}份")
        print(f"  {Fore.WHITE}可用数量: {Fore.YELLOW}{position.get('can_use')}份")
        print(f"  {Fore.WHITE}最新价格: {Fore.YELLOW}{position.get('new_price')}元")
        print(f"  {Fore.WHITE}持仓成本: {Fore.YELLOW}{position.get('hold_cost')}元")
        print(f"  {Fore.WHITE}持仓市值: {Fore.YELLOW}{position.get('hold_val')}元")
        print(f"  {Fore.WHITE}持仓盈亏: {profit_color}{position.get('earn_val')}元")
        print(f"  {Fore.WHITE}盈亏比例: {profit_color}{position.get('earn_per')}%")
        print(f"  {Fore.WHITE}今日盈亏: {profit_color}{position.get('earn_val_day')}元")
        print("  " + "-" * 30)

def print_orders(orders: Dict[str, Any]) -> None:
    """
    打印委托信息
    
    Args:
        orders: 委托信息字典
    """
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 委托信息 -----{Style.RESET_ALL}")
    
    if not orders["data"]:
        print(f"{Fore.YELLOW}无委托记录")
        return
    
    # 交易状态映射
    trade_state_map = {
        "0": "未报", "1": "待报", "2": "已报", "3": "已报待撤", 
        "4": "部成待撤", "5": "部撤", "6": "已撤", "7": "部成", "8": "已成", "9": "废单"
    }
    
    for order in orders["data"]:
        # 交易类型: 1-买入, 2-卖出
        trade_type = "买入" if order.get('trade_type') == "1" else "卖出" if order.get('trade_type') == "2" else "未知"
        trade_type_color = Fore.RED if trade_type == "买入" else Fore.GREEN if trade_type == "卖出" else Fore.YELLOW
        
        # 交易状态
        trade_state = trade_state_map.get(order.get('trade_state'), "未知")
        state_color = Fore.GREEN if trade_state == "已成" else Fore.RED if trade_state == "已撤" or trade_state == "废单" else Fore.YELLOW
        
        print(f"{Fore.WHITE}合同编号: {Fore.YELLOW}{order.get('contract_no')}")
        print(f"  {Fore.WHITE}代码: {Fore.YELLOW}{order.get('code')}, "
              f"{Fore.WHITE}名称: {Fore.YELLOW}{order.get('name')}")
        print(f"  {Fore.WHITE}交易类型: {trade_type_color}{trade_type}")
        print(f"  {Fore.WHITE}委托价格: {Fore.YELLOW}{order.get('order_price')}元")
        print(f"  {Fore.WHITE}委托数量: {Fore.YELLOW}{order.get('order_num')}份")
        print(f"  {Fore.WHITE}成交数量: {Fore.YELLOW}{order.get('match_num')}份")
        print(f"  {Fore.WHITE}成交价格: {Fore.YELLOW}{order.get('match_price')}元")
        print(f"  {Fore.WHITE}成交金额: {Fore.YELLOW}{order.get('trade_money')}元")
        print(f"  {Fore.WHITE}交易状态: {state_color}{trade_state}")
        print(f"  {Fore.WHITE}交易时间: {Fore.YELLOW}{order.get('trade_time')}")
        if order.get('cancel_time'):
            print(f"  {Fore.WHITE}撤单时间: {Fore.YELLOW}{order.get('cancel_time')}")
        print("  " + "-" * 30)

def print_records(records: Dict[str, Any]) -> None:
    """
    打印成交记录
    
    Args:
        records: 成交记录字典
    """
    print(f"\n{Fore.CYAN}{Style.BRIGHT}----- 成交记录 -----{Style.RESET_ALL}")
    
    if not records["data"]:
        print(f"{Fore.YELLOW}无成交记录")
        return
    
    for record in records["data"]:
        # 交易类型: 1-买入, 2-卖出
        trade_type = "买入" if record.get('trade_type') == "1" else "卖出" if record.get('trade_type') == "2" else "未知"
        trade_type_color = Fore.RED if trade_type == "买入" else Fore.GREEN if trade_type == "卖出" else Fore.YELLOW
        
        print(f"{Fore.WHITE}合同编号: {Fore.YELLOW}{record.get('contract_no')}")
        print(f"  {Fore.WHITE}代码: {Fore.YELLOW}{record.get('code')}, "
              f"{Fore.WHITE}名称: {Fore.YELLOW}{record.get('name')}")
        print(f"  {Fore.WHITE}交易类型: {trade_type_color}{trade_type}")
        print(f"  {Fore.WHITE}成交价格: {Fore.YELLOW}{record.get('match_price')}元")
        print(f"  {Fore.WHITE}成交数量: {Fore.YELLOW}{record.get('match_num')}份")
        print(f"  {Fore.WHITE}成交金额: {Fore.YELLOW}{record.get('match_money')}元")
        print(f"  {Fore.WHITE}成交时间: {Fore.YELLOW}{record.get('match_time')}")
        print(f"  {Fore.WHITE}交易手续费: {Fore.YELLOW}{record.get('trade_fee')}元")
        print("  " + "-" * 30)

def parse_arguments():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(description='账户查询工具')
    
    parser.add_argument('--action', choices=['account', 'positions', 'orders', 'records', 'all'], default='all',
                      help='查询动作: account(账户资金), positions(持仓), orders(委托), records(成交), all(全部)')
    parser.add_argument('--stock', type=str, default='',
                      help='股票代码，仅在查询持仓时有效')
    parser.add_argument('--date-range', type=str, default='1', choices=['1', '7', '30'],
                      help='日期范围: 1(当日), 7(7日内), 30(30日内)，仅在查询委托和成交时有效')
    parser.add_argument('--cookies', type=str, default='cookies.json',
                      help='cookies文件路径')
                      
    return parser.parse_args()

def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    logger.info("开始执行账户查询工具")
    
    # 加载cookies
    cookies = load_cookies(args.cookies)
    if not cookies:
        logger.error("未找到有效的cookies，请先保存cookies到文件")
        return
    
    # 设置会话
    session = setup_session(cookies)
    
    # 设置用户信息
    user_info = setup_user_info()
    
    # 创建查询客户端
    query_client = QueryClient(session, user_info, logger)
    logger.info("查询客户端创建成功")
    
    try:
        # 根据用户选择的操作进行查询
        if args.action in ['account', 'all']:
            # 查询账户资金信息
            print(f"{Fore.CYAN}{Style.BRIGHT}===== 查询账户资金信息 ====={Style.RESET_ALL}")
            account_info = query_client.query_account()
            if account_info["status"] == "success":
                print_account_info(account_info)
            else:
                print(f"{Fore.RED}查询账户资金信息失败: {account_info['message']}")
        
        if args.action in ['positions', 'all']:
            # 查询持仓信息
            print(f"{Fore.CYAN}{Style.BRIGHT}===== 查询持仓信息 ====={Style.RESET_ALL}")
            positions = query_client.query_positions(args.stock)
            if positions["status"] == "success":
                print_positions(positions)
            else:
                print(f"{Fore.RED}查询持仓信息失败: {positions['message']}")
        
        if args.action in ['orders', 'all']:
            # 查询委托信息
            print(f"{Fore.CYAN}{Style.BRIGHT}===== 查询委托信息 ====={Style.RESET_ALL}")
            orders = query_client.query_orders(args.date_range)
            if orders["status"] == "success":
                print_orders(orders)
            else:
                print(f"{Fore.RED}查询委托信息失败: {orders['message']}")
        
        if args.action in ['records', 'all']:
            # 查询成交信息
            print(f"{Fore.CYAN}{Style.BRIGHT}===== 查询成交信息 ====={Style.RESET_ALL}")
            records = query_client.query_records(args.date_range)
            if records["status"] == "success":
                print_records(records)
            else:
                print(f"{Fore.RED}查询成交信息失败: {records['message']}")
        
    except Exception as e:
        logger.error(f"查询过程中发生错误: {e}")
        print(f"{Fore.RED}查询过程中发生错误: {e}")
    
    logger.info("账户查询工具执行完毕")
    # 将session的cookies保存到cookies.json文件
    with open(args.cookies, 'w') as f:
        json.dump(session.cookies.get_dict(), f, indent=2)
    logger.info(f"Cookies已成功保存到{args.cookies}文件")
    print(f"{Fore.GREEN}Cookies已成功保存到{args.cookies}文件")

if __name__ == "__main__":
    main() 