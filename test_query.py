#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询模块测试脚本

用于测试中信建投交易账户的查询功能，包括账户资金、持仓和委托查询
"""

import os
import sys
import json
import logging
import getpass
from citic_trader.utils import setup_logger
from citic_trader.auth import AuthClient
from citic_trader.query import QueryClient

# 设置日志
logger = setup_logger(name="query_test", log_to_console=True, log_level=logging.INFO)

def login_account(username=None, password=None):
    """
    登录账户
    
    Args:
        username: 用户名（资金账号）
        password: 密码
        
    Returns:
        Tuple[requests.Session, Dict]: (会话对象, 用户信息字典)
    """
    # 如果未提供用户名和密码，从控制台请求输入
    if not username:
        username = input("请输入资金账号：")
    if not password:
        password = getpass.getpass("请输入密码：")
    
    # 创建认证客户端
    auth_client = AuthClient(logger)
    
    # 尝试登录
    logger.info(f"正在使用账号 {username} 尝试登录...")
    session, user_info = auth_client.login(username, password)
    
    if session and user_info:
        logger.info("登录成功！")
        return session, user_info, auth_client
    else:
        logger.error("登录失败！")
        return None, None, auth_client

def test_query_account(session, user_info):
    """
    测试查询账户资金和持仓信息
    
    Args:
        session: 会话对象
        user_info: 用户信息字典
    """
    logger.info("开始测试账户信息查询...")
    
    # 创建查询客户端
    query_client = QueryClient(session, user_info, logger)
    
    # 查询账户资金和持仓
    result = query_client.query_account()
    
    if result["status"] == "success":
        logger.info("账户信息查询成功！")
        
        # 打印账户资金信息
        funds_info = result["data"]["funds"]
        logger.info("账户资金信息:")
        logger.info(f"  总资产: {funds_info['total_assets']}")
        logger.info(f"  可用资金: {funds_info['available_cash']}")
        logger.info(f"  可取资金: {funds_info['withdrawable_cash']}")
        logger.info(f"  冻结资金: {funds_info['frozen_cash']}")
        logger.info(f"  持仓市值: {funds_info['market_value']}")
        logger.info(f"  总盈亏: {funds_info['total_profit']}")
        logger.info(f"  今日盈亏: {funds_info['today_profit']}")
        
        # 打印持仓信息
        positions = result["data"]["positions"]
        logger.info(f"持仓信息（共 {len(positions)} 条）:")
        for idx, pos in enumerate(positions):
            logger.info(f"  持仓 {idx+1}:")
            logger.info(f"    股票代码: {pos['code']}")
            logger.info(f"    股票名称: {pos['name']}")
            logger.info(f"    持仓数量: {pos['hold_num']}")
            logger.info(f"    可用数量: {pos['can_use']}")
            logger.info(f"    最新价格: {pos['new_price']}")
            logger.info(f"    持仓成本: {pos['hold_cost']}")
            logger.info(f"    持仓市值: {pos['hold_val']}")
            logger.info(f"    盈亏金额: {pos['earn_val']}")
            logger.info(f"    盈亏比例: {pos['earn_per']}%")
        
        # 打印委托信息
        orders = result["data"]["orders"]
        logger.info(f"委托信息（共 {len(orders)} 条）:")
        for idx, order in enumerate(orders):
            logger.info(f"  委托 {idx+1}:")
            logger.info(f"    合同编号: {order['contract_no']}")
            logger.info(f"    股票代码: {order['code']}")
            logger.info(f"    股票名称: {order['name']}")
            logger.info(f"    交易类型: {'买入' if order['trade_type'] == '1' else '卖出'}")
            logger.info(f"    委托价格: {order['order_price']}")
            logger.info(f"    委托数量: {order['order_num']}")
            logger.info(f"    成交数量: {order['match_num']}")
            logger.info(f"    成交价格: {order['match_price']}")
            logger.info(f"    交易状态: {order['trade_state']}")
    else:
        logger.error(f"账户信息查询失败: {result['message']}")

def test_query_positions(session, user_info, stock_code=None):
    """
    测试查询持仓信息
    
    Args:
        session: 会话对象
        user_info: 用户信息字典
        stock_code: 股票代码（可选）
    """
    logger.info("开始测试持仓信息查询...")
    
    # 创建查询客户端
    query_client = QueryClient(session, user_info, logger)
    
    # 查询持仓信息
    result = query_client.query_positions(stock_code)
    
    if result["status"] == "success":
        logger.info("持仓信息查询成功！")
        
        # 打印持仓信息
        positions = result["data"]["positions"]
        logger.info(f"持仓信息（共 {len(positions)} 条）:")
        for idx, pos in enumerate(positions):
            logger.info(f"  持仓 {idx+1}:")
            logger.info(f"    股票代码: {pos['code']}")
            logger.info(f"    股票名称: {pos['name']}")
            logger.info(f"    持仓数量: {pos['hold_num']}")
            logger.info(f"    可用数量: {pos['can_use']}")
            logger.info(f"    最新价格: {pos['new_price']}")
            logger.info(f"    持仓成本: {pos['hold_cost']}")
            logger.info(f"    持仓市值: {pos['hold_val']}")
            logger.info(f"    盈亏金额: {pos['earn_val']}")
            logger.info(f"    盈亏比例: {pos['earn_per']}%")
    else:
        logger.error(f"持仓信息查询失败: {result['message']}")

def test_query_orders(session, user_info):
    """
    测试查询委托信息
    
    Args:
        session: 会话对象
        user_info: 用户信息字典
    """
    logger.info("开始测试委托信息查询...")
    
    # 创建查询客户端
    query_client = QueryClient(session, user_info, logger)
    
    # 查询委托信息（查询今日委托）
    result = query_client.query_orders("1")
    
    if result["status"] == "success":
        logger.info("委托信息查询成功！")
        
        # 打印委托信息
        orders = result["data"]["orders"]
        logger.info(f"委托信息（共 {len(orders)} 条）:")
        for idx, order in enumerate(orders):
            logger.info(f"  委托 {idx+1}:")
            logger.info(f"    合同编号: {order['contract_no']}")
            logger.info(f"    股票代码: {order['code']}")
            logger.info(f"    股票名称: {order['name']}")
            logger.info(f"    交易类型: {'买入' if order['trade_type'] == '1' else '卖出'}")
            logger.info(f"    委托价格: {order['order_price']}")
            logger.info(f"    委托数量: {order['order_num']}")
            logger.info(f"    成交数量: {order['match_num']}")
            logger.info(f"    成交价格: {order['match_price']}")
            logger.info(f"    交易状态: {order['trade_state']}")
    else:
        logger.error(f"委托信息查询失败: {result['message']}")

def test_query_records(session, user_info):
    """
    测试查询成交记录
    
    Args:
        session: 会话对象
        user_info: 用户信息字典
    """
    logger.info("开始测试成交记录查询...")
    
    # 创建查询客户端
    query_client = QueryClient(session, user_info, logger)
    
    # 查询成交记录（查询今日成交）
    result = query_client.query_records("1")
    
    if result["status"] == "success":
        logger.info("成交记录查询成功！")
        
        # 打印成交记录
        records = result["data"]["records"]
        logger.info(f"成交记录（共 {len(records)} 条）:")
        for idx, record in enumerate(records):
            logger.info(f"  成交 {idx+1}:")
            logger.info(f"    成交编号: {record['matchno']}")
            logger.info(f"    合同编号: {record['contractno']}")
            logger.info(f"    股票代码: {record['code']}")
            logger.info(f"    股票名称: {record['name']}")
            logger.info(f"    交易类型: {'买入' if record['trade_type'] == '1' else '卖出'}")
            logger.info(f"    成交价格: {record['match_price']}")
            logger.info(f"    成交数量: {record['match_num']}")
            logger.info(f"    成交金额: {record['match_money']}")
            logger.info(f"    成交时间: {record['match_time']}")
    else:
        logger.error(f"成交记录查询失败: {result['message']}")

def run_all_tests():
    """
    运行所有查询测试
    """
    # 登录账户
    session, user_info, auth_client = login_account()
    
    if not session or not user_info:
        logger.error("登录失败，无法执行查询测试")
        return
    
    try:
        # 执行查询测试
        test_query_account(session, user_info)
        print("\n" + "=" * 50 + "\n")
        
        test_query_positions(session, user_info)
        print("\n" + "=" * 50 + "\n")
        
        test_query_orders(session, user_info)
        print("\n" + "=" * 50 + "\n")
        
        test_query_records(session, user_info)
    finally:
        # 确保登出账户
        logger.info("测试完成，正在登出账户...")
        auth_client.logout(session)

if __name__ == "__main__":
    # 根据命令行参数执行不同的测试
    if len(sys.argv) > 1:
        # 登录账户
        username = sys.argv[2] if len(sys.argv) > 2 else None
        password = sys.argv[3] if len(sys.argv) > 3 else None
        session, user_info, auth_client = login_account(username, password)
        
        if not session or not user_info:
            logger.error("登录失败，无法执行查询测试")
            sys.exit(1)
        
        try:
            if sys.argv[1] == "account":
                test_query_account(session, user_info)
            elif sys.argv[1] == "positions":
                stock_code = sys.argv[4] if len(sys.argv) > 4 else None
                test_query_positions(session, user_info, stock_code)
            elif sys.argv[1] == "orders":
                test_query_orders(session, user_info)
            elif sys.argv[1] == "records":
                test_query_records(session, user_info)
            else:
                print("用法: python test_query.py [account|positions|orders|records] [username] [password] [stock_code]")
        finally:
            # 确保登出账户
            logger.info("测试完成，正在登出账户...")
            auth_client.logout(session)
    else:
        # 默认运行所有测试
        run_all_tests() 