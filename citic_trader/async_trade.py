"""
Asynchronous trading module for CITIC Trader.
"""

import asyncio
import logging
import time
import random
from typing import Dict, List, Optional, Any, Tuple
import httpx
from datetime import datetime
from .encrypt import generate_order_sign_from_params
from .utils import format_stock_code
from .query import QueryClient
from .trade import TradeClient as SyncTradeClient
import json

class AsyncTradeClient:
    """
    Asynchronous trading client for CITIC Trader.
    """

    # 中信建投微信网厅实际API域名
    BASE_URL = "https://wzq.csc108.com/cgi-bin"
    ENTRUST_URL = f"{BASE_URL}/trade_entrust.fcgi"  # 委托接口（买入/卖出共用）
    CANCEL_URL = f"{BASE_URL}/tradecancel.cgi"  # 撤单接口
    ORDER_NO_URL = f"{BASE_URL}/trade_order_no.fcgi"  # 获取交易订单号
    SUBMIT_URL = f"{BASE_URL}/tradesubmit.fcgi"   # 实际交易提交接口
    PREPARE_URL = f"{BASE_URL}/tradeprepare.cgi"  # 交易准备接口
    TRADE_REFRESH_URL = f"{BASE_URL}/trade_refresh.fcgi"  # 交易刷新接口
    STOCK_INFO_URL = f"{BASE_URL}/stock_info.fcgi"  # 股票信息接口

    # 委托方向
    DIRECTION_BUY = "1"    # 买入
    DIRECTION_SELL = "2"   # 卖出

    def __init__(self, session: httpx.AsyncClient, user_info: Dict[str, Any], logger: logging.Logger):
        """
        Initialize the async trading client.
        """
        self.session = session
        self.user_info = user_info
        self.logger = logger
        self.app_info = user_info.get("app_info", {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "202504011653"
        })
        self.headers = {
            "Host": "wzq.csc108.com",
            "Connection": "keep-alive",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://wzq.csc108.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        self.stockholder_codes = {}
        self.last_trade_order_no = {}
        # We need an async way to initialize these.
        # self.query_client = AsyncQueryClient(self.session, self.user_info, self.logger)
        self.logger.info("异步交易客户端正在初始化...")

    async def initialize(self):
        """
        Asynchronously initialize the client.
        """
        await self._fetch_stockholder_codes_async()
        self.logger.info("交易客户端初始化完成")
        order_no_tasks = [self._get_trade_order_no_async("0"), self._get_trade_order_no_async("1")]
        results = await asyncio.gather(*order_no_tasks)
        self.last_trade_order_no = {"0": results[0], "1": results[1]}
        self.logger.info(f"trade_order_no:{self.last_trade_order_no}")
        # self.query_client = AsyncQueryClient(self.session, self.user_info, self.logger)
        # await self.query_client.initialize()
        self.logger.info("异步交易客户端初始化完成")

    def _format_error_response(self, error_msg: str, extra_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        格式化错误响应

        Args:
            error_msg: 错误消息
            extra_data: 额外数据

        Returns:
            Dict: 格式化的错误响应
        """
        response = {
            "status": "failed",
            "message": error_msg,
            "entrust_no": "",
            "data": {}
        }
        if extra_data:
            response.update(extra_data)
        return response

    def _get_stock_cls(self, stock_code: str) -> str:
        """
        根据股票代码获取股票类别

        Args:
            stock_code: 股票代码

        Returns:
            str: 股票类别
        """
        # 简单的股票类别判断逻辑
        if stock_code.startswith('6'):
            return "1"  # 沪市A股
        elif stock_code.startswith('0') or stock_code.startswith('3'):
            return "2"  # 深市A股
        elif stock_code.startswith('5'):
            return "3"  # 基金
        else:
            return "0"  # 默认

    async def _make_request_async(self, url: str, data: Dict[str, Any], log_prefix: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Sends an HTTP request asynchronously and handles the response.
        """
        try:
            headers = self.headers.copy()
            # 移除手动设置 Content-Length 的代码，让 httpx 自动处理
            # content_length = len("&".join(f"{k}={v}" for k, v in data.items()))
            # headers["Content-Length"] = str(content_length)
            
            timestamp = int(time.time() * 1000)
            response = await self.session.post(
                f"{url}?t={timestamp}", 
                data=data, 
                headers=headers
            )
            
            if response.status_code != 200:
                self.logger.error(f"{log_prefix}失败: 状态码 {response.status_code}")
                return False, {"retcode": "-1", "retmsg": f"HTTP错误: {response.status_code}"}
            
            try:
                response_content = response.json()
                return response_content.get("retcode") == "0", response_content
            except ValueError:
                self.logger.error(f"{log_prefix}响应解析失败: {response.text}")
                return False, {"retcode": "-1", "retmsg": "响应格式错误", "text": response.text}
        except Exception as e:
            self.logger.error(f"{log_prefix}过程中发生错误: {str(e)}")
            return False, {"retcode": "-1", "retmsg": str(e)}

    async def _fetch_stockholder_codes_async(self) -> None:
        """
        Fetches stockholder codes asynchronously.
        """
        try:
            userinfo_data = {
                "_appver": self.app_info["_appver"],
                "_osVer": self.app_info["_osVer"],
                "dealer": "1",
                "detail": "1",
                "come_from": "0"
            }
            
            # The original sync code had a bug here, using post with data in url
            url = f"https://wzq.csc108.com/cgi-bin/userinfo.fcgi"
            
            timestamp = int(time.time() * 1000)
            
            success, response_data = await self._make_request_async(f"{url}?t={timestamp}", userinfo_data, "获取股东代码")

            if success:
                for card in response_data.get("shareholdercards", []):
                    market = card.get("market", "")
                    code = card.get("code", "")
                    if market and code:
                        self.stockholder_codes[market] = code
                self.logger.info(f"股东代码获取成功: {self.stockholder_codes}")
            else:
                self.logger.error(f"获取股东代码失败: {response_data.get('retmsg', '未知错误')}")
        except Exception as e:
            self.logger.error(f"获取股东代码过程中发生错误: {str(e)}")

    async def _get_trade_order_no_async(self, market: str) -> str:
        """
        Gets a trade order number asynchronously.
        """
        order_no_data = {
            "market": market,
            "stock_code": "",
            "stockholder_code": self.stockholder_codes.get(market, ""),
            "retry_time": "0",
            "come_from": "0",
            "gm_flag": "0"
        }
            
        success, response_content = await self._make_request_async(
            self.ORDER_NO_URL, order_no_data, "获取交易订单号"
        )
        
        if success:
            trade_order_no = response_content.get("trade_order_no", "")
            self.logger.info(f"获取交易订单号成功: {trade_order_no}")
            return trade_order_no
        else:
            error_msg = response_content.get("retmsg", "未知错误")
            self.logger.error(f"获取交易订单号失败: {error_msg}")
            return ""

    async def _prepare_trade_async(self, market: str) -> bool:
        """
        Prepares for a trade asynchronously.
        """
        prepare_data = {
            "market": market,
            "come_from": "0",
            "gm_flag": "0"
        }
            
        success, response_content = await self._make_request_async(
            self.PREPARE_URL, prepare_data, "交易准备"
        )
        
        if success:
            self.logger.info("交易准备成功")
            if "timeseed" in response_content:
                self.timeseed = response_content["timeseed"]
            return True
        else:
            error_msg = response_content.get("retmsg", "未知错误")
            self.logger.error(f"交易准备失败: {error_msg}")
            return False
    
    async def get_stock_info_async(self, stock_code: str, need_quote: bool = True, need_five: bool = True) -> Dict[str, Any]:
        """
        Asynchronously gets stock information.
        """
        try:
            formatted_stock_code = format_stock_code(stock_code)
            market = "1" if formatted_stock_code.startswith("SH") else "0"
            clean_code = formatted_stock_code.replace("SH", "").replace("SZ", "")
            
            stock_info_data = {
                "market": market,
                "code": clean_code,
                "needquote": "1" if need_quote else "0",
                "needfive": "1" if need_five else "0",
                "gm_flag": "0"
            }
            
            self.logger.info(f"正在获取股票信息: {stock_code}")
            success, response_content = await self._make_request_async(
                self.STOCK_INFO_URL, stock_info_data, "获取股票信息"
            )
            
            if success:
                self.logger.info(f"获取股票信息成功: {stock_code}")
                stock_info = {
                    "code": clean_code,
                    "market": response_content.get('info', {}).get("market", ""),
                    "name": response_content.get('info', {}).get("name", ""),
                    "stock_cls": response_content.get('info', {}).get("class", "0"),
                    "price": response_content.get("price", "0"),
                    "open": response_content.get("open", "0"),
                    "high": response_content.get("high", "0"),
                    "low": response_content.get("low", "0"),
                    "pre_close": response_content.get("pre_close", "0"),
                    "volume": response_content.get("volume", "0"),
                    "amount": response_content.get("amount", "0"),
                    "date": response_content.get("date", ""),
                    "time": response_content.get("time", ""),
                }
                if need_five and "five" in response_content:
                    stock_info["five"] = response_content.get("five", {})

                return {
                    "status": "success",
                    "message": "获取股票信息成功",
                    "data": stock_info
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"获取股票信息失败: {error_msg}")
                return {
                    "status": "failed",
                    "message": error_msg,
                    "data": response_content
                }
        except Exception as e:
            error_msg = f"获取股票信息过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)


    async def buy_async(self, stock_code: str, price: float, volume: int = 0, stock_info: Optional[Dict[str, Any]] = None, order_type: str = "specified", commission_rate: float = 0.0001, fok: bool = False) -> Dict[str, Any]:
        """
        Asynchronously buys stock.
        """
        try:
            if not stock_code or not isinstance(stock_code, str):
                raise ValueError("股票代码不能为空且必须为字符串")
            if not price or price <= 0:
                raise ValueError("买入价格必须大于0")
            
            # --- 资金可用数不足 检查逻辑 (待完善) ---
            # 如果是"full"或"half"订单类型，需要检查资金可用数
            # if order_type in ["full", "half"]:
            #     # 这里需要调用查询可用资金的API
            #     # 例如: await self.query_client.get_available_funds_async()
            #     # 然后根据资金和股票信息计算可买数量
            #     self.logger.warning(f"目前买入订单类型 '{order_type}' 的资金检查功能尚未完全实现。")
            # --- END 资金可用数不足 检查逻辑 ---

            if stock_info is None:
                stock_info = await self.get_stock_info_async(stock_code)

            if stock_info.get("status") != "success":
                return self._format_error_response(f"获取股票信息失败: {stock_info.get('message', '未知错误')}")

            # Calculate volume for "full" or "half" order types if not specified
            if order_type == "full":
                # Assuming price from stock_info is current market price for calculation
                current_price = float(stock_info["data"]["price"])
                # This needs to query available funds from account to calculate
                self.logger.warning("买入类型 'full' 尚未实现自动计算最大可买数量，请手动指定 'volume'。")
                return self._format_error_response("买入类型 'full' 尚未实现自动计算最大可买数量。")
            elif order_type == "half":
                self.logger.warning("买入类型 'half' 尚未实现自动计算可买数量的一半，请手动指定 'volume'。")
                return self._format_error_response("买入类型 'half' 尚未实现自动计算可买数量的一半。")
            
            if volume <= 0:
                raise ValueError("买入数量必须大于0")

            return await self._execute_trade_async(
                stock_info=stock_info,
                price=price,
                volume=volume,
                action=self.DIRECTION_BUY,
                fok=fok
            )
        except ValueError as e:
            self.logger.error(f"买入参数错误: {str(e)}")
            return self._format_error_response(str(e))
        except Exception as e:
            error_msg = f"买入过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)

    async def sell_async(self, stock_code: str, price: float, volume: int, stock_info: Optional[Dict[str, Any]] = None, fok: bool = False) -> Dict[str, Any]:
        """
        Asynchronously sells stock.
        """
        try:
            if not stock_code or not isinstance(stock_code, str):
                raise ValueError("股票代码不能为空且必须为字符串")
            if not price or price <= 0:
                raise ValueError("卖出价格必须大于0")
            if volume <= 0:
                raise ValueError("卖出数量必须大于0")

            if stock_info is None:
                stock_info = await self.get_stock_info_async(stock_code)

            if stock_info.get("status") != "success":
                return self._format_error_response(f"获取股票信息失败: {stock_info.get('message', '未知错误')}")
            
            return await self._execute_trade_async(
                stock_info=stock_info,
                price=price,
                volume=volume,
                action=self.DIRECTION_SELL,
                fok=fok
            )
        except ValueError as e:
            self.logger.error(f"卖出参数错误: {str(e)}")
            return self._format_error_response(str(e))
        except Exception as e:
            error_msg = f"卖出过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)

    async def _execute_trade_async(self, stock_info: Dict[str, Any], price: float, volume: int, action: str, fok: bool) -> Dict[str, Any]:
        """
        Executes a trade asynchronously.
        """
        market = stock_info["data"]["market"]
        stock_code = stock_info["data"]["code"]
        price_str = f"{price:.3f}"
        volume_str = str(volume)

        # 并发执行所有异步依赖操作
        prepare_task = self._prepare_trade_async(market)
        trade_order_no = self.last_trade_order_no.get(market, "")
        generate_sign_task = asyncio.to_thread(generate_order_sign_from_params, stock_code, price_str, volume_str)
        if not trade_order_no:
             # Fallback to getting it now
             trade_order_no_task = self._get_trade_order_no_async(market)
             prepare_result, trade_order_no, order_sign = await asyncio.gather(
                prepare_task,
                trade_order_no_task,
                generate_sign_task)
        else:
            prepare_result, order_sign = await asyncio.gather(prepare_task, generate_sign_task)

        if not prepare_result:
            return self._format_error_response("交易准备失败")
        if not trade_order_no:
            return self._format_error_response("无法获取交易订单号")
        if not order_sign:
            return self._format_error_response("无法生成订单签名")

        trade_result = await self._submit_trade_async(
            stock_info=stock_info,
            price=price,
            volume=volume,
            action=action,
            trade_order_no=trade_order_no,
            order_sign=order_sign,
            fok=fok
        )
        self.last_trade_order_no[market] = await self._get_trade_order_no_async(market)
        if fok:
            self.logger.warning("FOK逻辑暂未在异步版本中完全实现")

        return trade_result

    async def _submit_trade_async(self, stock_info: Dict[str, Any], price: float,
                      volume: int, action: str, trade_order_no: str, order_sign: str, fok: bool) -> Dict[str, Any]:
        """
        Submits a trade request asynchronously.
        """
        try:
            market = stock_info["data"]["market"]
            stock_code = stock_info["data"]["code"]

            stockholder_code = self.stockholder_codes.get(market, "")
            if not stockholder_code:
                return self._format_error_response(f"未找到市场 {market} 的股东代码")

            price_str = f"{price:.3f}"
            volume_str = str(volume)

            entrant_key = f"{int(time.time() * 1000)}{random.randint(1000, 9999)}"

            stock_name = stock_info["data"].get("name", "交易股票")
            stock_cls = stock_info["data"].get("stock_cls", self._get_stock_cls(stock_code))

            trade_data = {
                **self.app_info,
                "scenes": "",
                "action": action,
                "type": "0",
                "market": market,
                "scode": stock_code,
                "name": stock_name,
                "price": price_str,
                "quantity": volume_str,
                "psw": "",
                "entrant_key": entrant_key,
                "stockholder_code": stockholder_code,
                "match_type": "0",
                "risk_ver": "1",
                "order_sign": order_sign,
                "trade_order_no": trade_order_no,
                "stock_cls": stock_cls,
                "come_from": "0",
                "gm_flag": "0",
            }
            action_text = "买入" if action == self.DIRECTION_BUY else "卖出"
            self.logger.info(f"正在{action_text}: {stock_code}, 价格: {price}, 数量: {volume}")

            success, response_content = await self._make_request_async(
                self.SUBMIT_URL, trade_data, f"{action_text}请求"
            )

            if success:
                entrust_no = response_content.get("entrust_no", "")
                contract_no = response_content.get("contract_no", "")
                self.logger.info(f"委托成功，合同编号: {contract_no}")
                return {
                    "status": "success",
                    "message": "委托成功",
                    "entrust_no": entrust_no,
                    "contract_no": contract_no,
                    "data": response_content
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"委托失败: {error_msg}")
                return self._format_error_response(error_msg, extra_data={"data": response_content})
        except Exception as e:
            error_msg = f"委托过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)

    async def save_cookies(self):
        """
        保存当前会话的cookies到文件，处理同名cookies冲突
        """
        cookies_to_save = {}
        # Iterate over the CookieJar to manually handle potential conflicts
        # This approach takes the last set cookie for a given name if multiple exist
        for cookie in self.session.cookies.jar: # Corrected from self.async_session.cookies.jar
            cookies_to_save[cookie.name] = cookie.value

        with open("cookies.json", "w") as f:
            json.dump(cookies_to_save, f, indent=2) 