"""
查询模块

处理中信建投交易账户的查询操作，包括账户资金、持仓和委托查询。
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
import requests

from .utils import format_stock_code

class QueryClient:
    """查询客户端
    
    处理中信建投微信网厅的账户资金、持仓和委托查询操作。
    """
    
    # 中信建投微信网厅实际API域名
    BASE_URL = "https://wzq.csc108.com/cgi-bin"
    HOME_SHOW_URL = f"{BASE_URL}/home_show.fcgi"  # 账户信息查询接口（新版）
    ORDERS_QUERY_URL = f"{BASE_URL}/orders_query.fcgi"  # 委托记录查询接口
    RECORDS_QUERY_URL = f"{BASE_URL}/records_query.fcgi"  # 成交记录查询接口
    TRADE_QUERY_URL = f"{BASE_URL}/tradequery.cgi"  # 交易状态查询接口
    
    # 错误响应模板
    ERROR_RESPONSE = {
        "status": "error",
        "message": "",
        "data": {}
    }
    
    def __init__(self, session: requests.Session, user_info: Dict[str, Any], logger: logging.Logger):
        """
        初始化查询客户端
        
        Args:
            session: 会话对象
            user_info: 用户信息
            logger: 日志记录器
        """
        self.session = session
        self.user_info = user_info
        self.logger = logger
        self.app_info = user_info.get("app_info", {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "202504011653"
        })
        # 添加标准请求头
        self.headers = {
            "Host": "wzq.csc108.com",
            "Connection": "keep-alive",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://wzq.csc108.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors", 
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        
        # 初始化股东代码字典
        self.stockholder_codes = {}  # 格式: {"0": "深市代码", "1": "沪市代码"}
        self.logger.info("查询客户端初始化完成")
    
    def _make_request(self, url: str, data: Dict[str, Any], log_prefix: str) -> Tuple[bool, Dict[str, Any]]:
        """
        发送HTTP请求并处理响应
        
        Args:
            url: 请求URL
            data: 请求数据
            log_prefix: 日志前缀
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 响应数据)
        """
        try:
            # 设置Content-Length
            headers = self.headers.copy()
            content_length = len("&".join(f"{k}={v}" for k, v in data.items()))
            headers["Content-Length"] = str(content_length)
            
            # 发送请求
            timestamp = int(time.time() * 1000)
            response = self.session.post(
                f"{url}?t={timestamp}", 
                data=data, 
                headers=headers
            )
            
            if response.status_code != 200:
                self.logger.error(f"{log_prefix}失败: 状态码 {response.status_code}")
                return False, {"retcode": "-1", "retmsg": f"HTTP错误: {response.status_code}"}
            
            # 解析响应数据
            try:
                response_content = response.json()
                return response_content.get("retcode") == "0", response_content
            except ValueError:
                self.logger.error(f"{log_prefix}响应解析失败: {response.text}")
                return False, {"retcode": "-1", "retmsg": "响应格式错误", "text": response.text}
        except Exception as e:
            self.logger.error(f"{log_prefix}过程中发生错误: {str(e)}")
            return False, {"retcode": "-1", "retmsg": str(e)}
    
    def _format_error_response(self, error_msg: str, extra_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_msg: 错误信息
            extra_data: 额外数据
            
        Returns:
            Dict: 格式化的错误响应
        """
        response = self.ERROR_RESPONSE.copy()
        response["message"] = error_msg
        if extra_data:
            response["data"] = extra_data
        return response
    
    def query_account(self, stock_code: str = "", qry_bulletin: str = "1") -> Dict[str, Any]:
        """
        查询账户资金、持仓和委托信息（新版 home_show.fcgi 接口）
        Args:
            stock_code: 股票代码（可选），如果提供，将只返回该股票的持仓信息
            qry_bulletin: 是否查询公告（"1"表示查询，"0"表示不查询）
        Returns:
            Dict: 账户信息字典
        """
        try:
            self.logger.info("正在查询账户信息...")

            # 构建请求数据
            home_show_data = {
                "scene": "0",
                "qry_bulletin": qry_bulletin,
                "come_from": "0",
                "gm_flag": "0",
                "is_replay_req": "1"
            }
            # 股票代码参数不再传递，接口不支持

            # 发送请求
            success, response_content = self._make_request(
                self.HOME_SHOW_URL, home_show_data, "查询账户信息"
            )

            if success:
                self.logger.info("查询账户信息成功")
                # 资金信息
                fundsinfo = response_content.get("fundsinfo", {})
                # 持仓信息
                positions = response_content.get("holdstock", [])
                # 委托信息（只有数量统计，没有明细）
                orderinfo = response_content.get("orderinfo", {})

                # 整理资金信息
                account_funds = {
                    "total_assets": fundsinfo.get("total_money", "0"),  # 总资产
                    "available_cash": fundsinfo.get("can_trade", "0"),  # 可用资金
                    "withdrawable_cash": fundsinfo.get("can_draw", "0"),  # 可取资金
                    "frozen_cash": fundsinfo.get("freeze_money", "0"),  # 冻结资金
                    "market_value": fundsinfo.get("hold_val", "0"),  # 持仓市值
                    "total_profit": fundsinfo.get("earn_val", "0"),  # 总盈亏
                    "today_profit": fundsinfo.get("earn_val_today", "0")  # 今日盈亏
                }

                # 整理持仓信息
                account_positions = []
                for pos in positions:
                    # 如果指定股票代码，只返回该股票
                    if stock_code:
                        if pos.get("code", "") != stock_code:
                            continue
                    position = {
                        "code": pos.get("code", ""),
                        "name": pos.get("name", ""),
                        "hold_num": pos.get("hold_num", "0"),
                        "can_use": pos.get("can_use", "0"),
                        "new_price": pos.get("new_price", "0"),
                        "hold_cost": pos.get("hold_cost", "0"),
                        "hold_val": pos.get("hold_val", "0"),
                        "earn_val": pos.get("earn_val", "0"),
                        "earn_per": pos.get("earn_per", "0"),
                        "earn_val_day": pos.get("earn_val_day", "0"),
                        "stockholder_code": pos.get("stockholder_code", "")
                    }
                    account_positions.append(position)

                # 整理委托信息（这里只能返回数量统计）
                account_orders = {
                    "order_num_all": orderinfo.get("order_num_all", "0"),
                    "order_num_undone": orderinfo.get("order_num_undone", "0")
                }

                # 组装最终返回
                account_info = {
                    "funds": account_funds,
                    "positions": account_positions,
                    "orders": account_orders,
                    "bulletin": response_content.get("bulletin", []),
                    "activityinfo": response_content.get("activityinfo", {}),
                    "aics": response_content.get("aics", {}),
                    "balanceinfo": response_content.get("balanceinfo", {})
                }

                return {
                    "status": "success",
                    "message": "查询账户信息成功",
                    "data": account_info
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"查询账户信息失败: {error_msg}")
                return self._format_error_response(error_msg, response_content)

        except Exception as e:
            error_msg = f"查询账户信息过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def query_positions(self, stock_code: str = "") -> Dict[str, Any]:
        """
        查询持仓信息
        
        Args:
            stock_code: 股票代码（可选），如果提供，将只返回该股票的持仓信息
            
        Returns:
            Dict: 持仓信息字典
        """
        result = self.query_account(stock_code)
        
        if result["status"] == "success":
            return {
                "status": "success",
                "message": "查询持仓信息成功",
                "data": result["data"]["positions"]
            }
        else:
            return result
    
    def query_orders(self, date_range: str = "1") -> Dict[str, Any]:
        """
        查询委托记录
        
        Args:
            date_range: 日期范围 ("1"-当日, "7"-7日内, "30"-30日内)
            
        Returns:
            Dict: 委托记录字典
        """
        try:
            self.logger.info(f"正在查询委托记录(日期范围: {date_range}天)...")
            
            # 构建请求数据
            orders_query_data = {
                "istrust": "0",  # 0表示普通委托
                "lnd": date_range,  # 日期范围
                "curpage": "1",  # 当前页码
                "come_from": "0",
                "gm_flag": "0"
            }
            
            # 发送请求
            success, response_content = self._make_request(
                self.ORDERS_QUERY_URL, orders_query_data, "查询委托记录"
            )
            
            if success:
                self.logger.info("查询委托记录成功")
                
                # 解析委托记录
                orders = []
                orders_list = response_content.get("list", [])
                
                for order in orders_list:
                    order_info = {
                        "contract_no": order.get("contract_no", ""),
                        "code": order.get("code", ""),
                        "name": order.get("name", ""),
                        "trade_type": order.get("trade_type", ""),  # 1-买入, 2-卖出
                        "order_price": order.get("order_price", "0"),
                        "order_num": order.get("order_num", "0"),
                        "match_num": order.get("match_num", "0"),
                        "match_price": order.get("match_price", "0"),
                        "trade_money": order.get("trade_money", "0"),
                        "trade_state": order.get("trade_state", ""),  # 交易状态
                        "trade_time": order.get("trade_time", ""),
                        "cancel_time": order.get("cancel_time", "")
                    }
                    orders.append(order_info)
                
                return {
                    "status": "success",
                    "message": "查询委托记录成功",
                    "data": orders
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"查询委托记录失败: {error_msg}")
                return self._format_error_response(error_msg, response_content)
                
        except Exception as e:
            error_msg = f"查询委托记录过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def query_records(self, date_range: str = "1") -> Dict[str, Any]:
        """
        查询成交记录
        
        Args:
            date_range: 日期范围 ("1"-当日, "7"-7日内, "30"-30日内)
            
        Returns:
            Dict: 成交记录字典
        """
        try:
            self.logger.info(f"正在查询成交记录(日期范围: {date_range}天)...")
            
            # 构建请求数据
            records_query_data = {
                "lnd": date_range,  # 日期范围
                "curpage": "1",  # 当前页码
                "come_from": "0",
                "gm_flag": "0"
            }
            
            # 发送请求
            success, response_content = self._make_request(
                self.RECORDS_QUERY_URL, records_query_data, "查询成交记录"
            )
            
            if success:
                self.logger.info("查询成交记录成功")
                
                # 解析成交记录
                records = []
                records_list = response_content.get("list", [])
                
                for record in records_list:
                    record_info = {
                        "contract_no": record.get("contract_no", ""),
                        "code": record.get("code", ""),
                        "name": record.get("name", ""),
                        "trade_type": record.get("trade_type", ""),  # 1-买入, 2-卖出
                        "match_price": record.get("match_price", "0"),
                        "match_num": record.get("match_num", "0"),
                        "match_money": record.get("match_money", "0"),
                        "match_time": record.get("match_time", ""),
                        "trade_fee": record.get("trade_fee", "0")  # 交易手续费
                    }
                    records.append(record_info)
                
                return {
                    "status": "success",
                    "message": "查询成交记录成功",
                    "data": records
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"查询成交记录失败: {error_msg}")
                return self._format_error_response(error_msg, response_content)
                
        except Exception as e:
            error_msg = f"查询成交记录过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def query_trade_status(self, contract_no: str, trade_time: str) -> Dict[str, Any]:
        """
        查询特定委托的成交状态
        
        Args:
            contract_no: 合同编号
            trade_time: 交易时间 (YYYY-MM-DD HH:MM:SS)
            
        Returns:
            Dict: 包含委托状态的字典
        """
        try:
            self.logger.info(f"正在查询委托状态: 合同编号 {contract_no}")
            
            # 构建请求数据
            query_data = {
                'id': '',
                'contract_no': contract_no,
                'trade_time': trade_time,
                'istiming': '0',
                'gm_flag': '0',
            }
            
            # 发送请求
            success, response_content = self._make_request(
                self.TRADE_QUERY_URL, query_data, "查询委托状态"
            )
            
            if success:
                self.logger.info(f"查询委托状态成功: 合同编号 {contract_no}")
                return {
                    "status": "success",
                    "message": "查询委托状态成功",
                    "data": response_content
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"查询委托状态失败: {error_msg}")
                return self._format_error_response(error_msg, response_content)

        except Exception as e:
            error_msg = f"查询委托状态过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def format_position_info(self, position: Dict[str, Any]) -> str:
        """
        格式化持仓信息为可读字符串
        
        Args:
            position: 持仓信息字典
        
        Returns:
            str: 格式化的持仓信息字符串
        """
        return (
            f"代码: {position.get('code')}, 名称: {position.get('name')}\n"
            f"  持仓数量: {position.get('hold_num')}份\n"
            f"  可用数量: {position.get('can_use')}份\n"
            f"  最新价格: {position.get('new_price')}元\n"
            f"  持仓成本: {position.get('hold_cost')}元\n"
            f"  持仓市值: {position.get('hold_val')}元\n"
            f"  持仓盈亏: {position.get('earn_val')}元\n"
            f"  盈亏比例: {position.get('earn_per')}%\n"
            f"  今日盈亏: {position.get('earn_val_day')}元\n"
            f"  股东代码: {position.get('stockholder_code')}"
        )
    
    def format_order_info(self, order: Dict[str, Any]) -> str:
        """
        格式化委托信息为可读字符串
        
        Args:
            order: 委托信息字典
        
        Returns:
            str: 格式化的委托信息字符串
        """
        # 交易类型: 1-买入, 2-卖出
        trade_type = "买入" if order.get('trade_type') == "1" else "卖出" if order.get('trade_type') == "2" else "未知"
        
        # 交易状态: 0-未报, 1-待报, 2-已报, 3-已报待撤, 4-部成待撤, 5-部撤, 6-已撤, 7-部成, 8-已成, 9-废单
        trade_state_map = {
            "0": "未报", "1": "待报", "2": "已报", "3": "已报待撤", 
            "4": "部成待撤", "5": "部撤", "6": "已撤", "7": "部成", "8": "已成", "9": "废单"
        }
        trade_state = trade_state_map.get(order.get('trade_state'), "未知")
        
        return (
            f"合同编号: {order.get('contract_no')}\n"
            f"  代码: {order.get('code')}, 名称: {order.get('name')}\n"
            f"  交易类型: {trade_type}\n"
            f"  委托价格: {order.get('order_price')}元\n"
            f"  委托数量: {order.get('order_num')}份\n"
            f"  成交数量: {order.get('match_num')}份\n"
            f"  成交价格: {order.get('match_price')}元\n"
            f"  成交金额: {order.get('trade_money')}元\n"
            f"  交易状态: {trade_state}\n"
            f"  交易时间: {order.get('trade_time')}\n"
            f"  撤单时间: {order.get('cancel_time')}"
        )
    
    