#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易数量计算和交易结果验证测试脚本

用于测试calculate_trade_volume和verify_trade_result功能
"""

import json
import logging
import requests
from typing import Dict, Any
from colorama import init, Fore, Style

from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger

# 初始化colorama
init(autoreset=True)

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """从文件加载cookies"""
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(Fore.RED + f"加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """设置会话，添加cookies"""
    session = requests.Session()
    for key, value in cookies.items():
        session.cookies.set(key, value)
    return session

def setup_user_info() -> Dict[str, Any]:
    """设置用户信息"""
    return {
        "account_id": "",  # 从cookies中获取或手动设置
        "stockholder_code": "",  # 从交易接口获取或手动设置
        "psw_session": "",  # 从cookies中获取或手动设置
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

class TradeVolumeCalculator:
    """交易数量计算器"""
    
    def __init__(self):
        # 设置日志
        self.logger = setup_logger(log_level=logging.INFO)
        
        # 加载cookies
        cookies_file = "cookies.json"
        cookies = load_cookies(cookies_file)
        if not cookies:
            self.logger.error("未找到有效的cookies，请先保存cookies到文件")
            return
        
        # 设置会话
        self.session = setup_session(cookies)
        
        # 设置用户信息
        user_info = setup_user_info()
        
        # 创建交易客户端
        self.trade_client = TradeClient(self.session, user_info, self.logger)
        self.logger.info("交易客户端创建成功")
        
        # 交易参数
        self.stock_code = "161130"  # 纳指LOF
        self.position = 0  # 当前持仓
        self.available_cash = 0  # 可用资金
        self.max_buy_qty = 0  # 最大可买入数量
        self.max_sell_qty = 0  # 最大可卖出数量
        self.stock_info = None  # 股票信息
    
    def get_stock_info(self) -> bool:
        """获取股票信息"""
        try:
            self.logger.info(f"获取股票信息: {self.stock_code}")
            result = self.trade_client.get_stock_info(self.stock_code)
            
            if result.get("status") == "success":
                self.stock_info = result
                price = result["data"].get("price", "0")
                self.logger.info(f"成功获取股票信息: {self.stock_code}, 价格: {price}")
                return True
            else:
                self.logger.error(f"获取股票信息失败: {result.get('message', '未知错误')}")
                return False
        except Exception as e:
            self.logger.error(f"获取股票信息过程中出错: {e}")
            return False
    
    def get_account_info(self) -> bool:
        """获取账户信息"""
        try:
            self.logger.info("获取账户信息...")
            result = self.trade_client.trade_show(self.stock_code)
            
            if result.get("status") == "success":
                # 更新可用资金和交易限制
                self.available_cash = float(result["data"]["funds"]["available_cash"])
                self.max_buy_qty = int(float(result["data"]["limits"]["max_buy_money"]) / float(self.stock_info["data"]["price"]) // 100 * 100)
                self.max_sell_qty = int(float(result["data"]["limits"]["max_sell_qty"]))
                
                # 更新当前持仓
                self.position = 0
                for position in result["data"]["positions"]:
                    if position.get("code") == self.stock_code:
                        self.position = int(position.get("hold_num", "0"))
                        break
                
                self.logger.info(f"账户信息获取成功")
                self.logger.info(f"可用资金: {self.available_cash}元")
                self.logger.info(f"当前持仓 {self.stock_code}: {self.position}份")
                self.logger.info(f"最大可买入: {self.max_buy_qty}份")
                self.logger.info(f"最大可卖出: {self.max_sell_qty}份")
                
                return True
            else:
                self.logger.error(f"获取账户信息失败: {result.get('message', '未知错误')}")
                return False
        except Exception as e:
            self.logger.error(f"获取账户信息过程中出错: {e}")
            return False
    
    def calculate_trade_volume(self, signal: int, current_price: float) -> int:
        """
        计算交易数量
        
        Args:
            signal: 交易信号 (1: 买入, -1: 卖出)
            current_price: 当前价格
        
        Returns:
            int: 交易数量
        """
        # 手续费率和最低手续费
        commission_rate = 0.0003  # 0.03%
        min_commission = 0.1  # 最低0.1元
        
        if signal == 1:  # 买入信号
            # 如果已经持有，不重复买入
            if self.position > 0:
                print(Fore.YELLOW + f"已持有 {self.position} 份，不重复买入")
                return 0
            
            # 计算最大可买入数量（考虑资金和交易限制）
            # 先计算可用资金减去手续费后能买多少份
            max_qty = int((self.available_cash / (1 + commission_rate)) / current_price) // 100 * 100
            max_qty = min(max_qty, self.max_buy_qty)
            
            # 如果最大可买入数量为0，则无法交易
            if max_qty <= 0:
                print(Fore.RED + "可用资金不足，无法买入")
                return 0
            
            # 计算手续费
            commission = max(current_price * max_qty * commission_rate, min_commission)
            
            # 检查总成本是否超过可用资金
            total_cost = current_price * max_qty + commission
            if total_cost > self.available_cash:
                # 调整购买数量
                max_qty = int((self.available_cash - min_commission) / (current_price * (1 + commission_rate))) // 100 * 100
                if max_qty <= 0:
                    print(Fore.RED + "考虑手续费后可用资金不足，无法买入")
                    return 0
                print(Fore.YELLOW + f"调整购买数量以符合资金限制: {max_qty} 份")
            
            print(Fore.GREEN + f"计算得出买入数量: {max_qty} 份")
            return max_qty
            
        elif signal == -1:  # 卖出信号
            # 如果没有持仓，不卖出
            if self.position <= 0:
                print(Fore.YELLOW + "没有持仓，无法卖出")
                return 0
            
            # 返回当前持仓数量（全部卖出）
            print(Fore.GREEN + f"计算得出卖出数量: {self.position} 份")
            return self.position
        
        return 0  # 不操作
    
    def test_trade_volume_calculation(self):
        """测试交易数量计算"""
        print(Fore.CYAN + Style.BRIGHT + "\n===== 测试交易数量计算 =====")
        
        # 获取股票信息
        if not self.get_stock_info():
            return
        
        # 获取账户信息
        if not self.get_account_info():
            return
        
        # 获取当前价格
        current_price = float(self.stock_info["data"].get("price", 0))
        if current_price <= 0:
            self.logger.error(f"获取股票价格失败")
            return
        
        print(Fore.CYAN + f"\n当前股票: {self.stock_code}, 价格: {current_price}元")
        print(Fore.CYAN + f"当前持仓: {self.position}份, 可用资金: {self.available_cash}元")
        
        # 测试买入信号
        print(Fore.CYAN + "\n----- 测试买入信号 -----")
        buy_volume = self.calculate_trade_volume(1, current_price)
        
        # 测试卖出信号
        print(Fore.CYAN + "\n----- 测试卖出信号 -----")
        sell_volume = self.calculate_trade_volume(-1, current_price)
        
        # 测试不同价格下的买入数量
        print(Fore.CYAN + "\n----- 测试不同价格下的买入数量 -----")
        test_prices = [current_price * 0.8, current_price, current_price * 1.2]
        for price in test_prices:
            print(Fore.CYAN + f"\n价格: {price}元")
            volume = self.calculate_trade_volume(1, price)
            
            # 计算交易金额和手续费
            if volume > 0:
                amount = price * volume
                commission = max(amount * 0.0003, 0.1)  # 0.03%手续费，最低0.1元
                total_cost = amount + commission
                print(f"交易金额: {amount:.2f}元")
                print(f"手续费: {commission:.2f}元")
                print(f"总成本: {total_cost:.2f}元")
        
        print(Fore.GREEN + Style.BRIGHT + "\n测试完成!")

def main():
    """主函数"""
    print(Fore.CYAN + Style.BRIGHT + "交易数量计算和交易结果验证测试脚本")
    
    # 创建交易数量计算器
    calculator = TradeVolumeCalculator()
    
    # 测试交易数量计算
    calculator.test_trade_volume_calculation()

if __name__ == "__main__":
    main() 