#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中信建投交易示例脚本

展示如何使用cookies直接调用trade.py进行买入、卖出和撤单操作，无需登录
"""

import json
import time
import logging
import requests
from typing import Dict, Any

from citic_trader.optimized_trade import OptimizedTradeClient
from citic_trader.utils import setup_logger

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """
    从文件加载cookies
    
    Args:
        cookies_file: cookies文件路径，JSON格式
        
    Returns:
        Dict: cookies字典
    """
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """
    设置会话，添加cookies
    
    Args:
        cookies: cookies字典
        
    Returns:
        requests.Session: 会话对象
    """
    session = requests.Session()
    
    # 添加cookies到会话
    for key, value in cookies.items():
        session.cookies.set(key, value)
    
    return session

def setup_user_info() -> Dict[str, Any]:
    """
    设置用户信息
    
    Returns:
        Dict: 用户信息字典
    """
    # 这里需要填写您的账户信息
    return {
        "account_id": "您的账户ID",  # 请替换为实际账户ID
        "stockholder_code": "**********",  # 请替换为实际股东代码
        "psw_session": "您的密码会话",  # 通常从cookies中获取
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def main():
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    logger.info("开始执行交易示例")
    
    # 加载cookies (请替换为您的cookies文件路径)
    cookies_file = "cookies.json"
    cookies = load_cookies(cookies_file)

    if not cookies:
        logger.error("未找到有效的cookies，请先保存cookies到文件")
        return
    
    # 设置会话
    session = setup_session(cookies)
    
    # 设置用户信息
    user_info = setup_user_info()
    
    
    # 创建交易客户端
    trade_client = OptimizedTradeClient(session, user_info, logger)
    logger.info("交易客户端创建成功")
    
    try:
        # 示例0: 获取股票信息
        print("\n===== 获取股票信息示例 =====")
        stock_code = "518850"
        print(f"正在获取股票信息: {stock_code}")
        stock_info_result = trade_client.get_stock_info(stock_code)
        
        # 如果获取股票信息成功，打印股票名称和当前价格
        if stock_info_result.get("status") == "success":
            stock_name = stock_info_result["data"].get("name", "")
            stock_price = stock_info_result["data"].get("price", "0")
            stock_market = stock_info_result["data"].get("market", "") # 获取市场代码
            print(f"股票名称: {stock_name}, 当前价格: {stock_price}")
        else:
            logger.error(f"无法获取股票 {stock_code} 的信息，请检查！")
            return

        # 示例1: 买入股票
        print("\n===== 买入股票示例 =====")
        print("正在买入: 161130, 价格: 3.151, 数量: 100")
        buy_result = trade_client.buy("161130", 3.151, 100, stock_info_result) # 传入股票信息
        print(f"买入结果: {buy_result}")
        
        # 等待一段时间，确保委托已经提交
        time.sleep(0)
        
        if buy_result.get("status") == "success":
            # 获取合同编号，用于后续撤单
            contract_no = buy_result.get("contract_no", "")
            entrust_no = buy_result.get("entrust_no", "")
            logger.info(f"买入委托成功，合同编号: {contract_no}, 委托编号: {entrust_no}")
            
            # 示例3: 撤销买入委托
            if contract_no:
                print("\n===== 撤单示例 =====")
                print(f"正在撤单: 合同编号 {contract_no}")
                cancel_result = trade_client.cancel(
                    contract_no, 
                    time.strftime("%Y-%m-%d %H:%M:%S"), 
                    "161130", 
                    "1",  # 1表示上海市场
                    "1",   # 1表示买入方向
                    stock_info_result # 传入股票信息
                )
                print(f"撤单结果: {cancel_result}")
                logger.info(f"撤单结果: {cancel_result}")
        # 示例2: 卖出股票
        print("\n===== 卖出股票示例 =====")
        print("正在卖出: 161130, 价格: 3.851, 数量: 100")
        sell_result = trade_client.sell("161130", 3.851, 100, stock_info_result) # 传入股票信息
        print(f"卖出结果: {sell_result}")
        logger.info(f"卖出结果: {sell_result}")

        if sell_result.get("status") == "success":
            # 获取合同编号，用于后续撤单
            contract_no = sell_result.get("contract_no", "")
            entrust_no = sell_result.get("entrust_no", "")
            logger.info(f"卖出委托成功，合同编号: {contract_no}, 委托编号: {entrust_no}")
            
            # 示例3: 撤销卖出委托
            if contract_no:
                print("\n===== 撤单示例 =====")
                print(f"正在撤单: 合同编号 {contract_no}")
                cancel_result = trade_client.cancel(
                    contract_no, 
                    time.strftime("%Y-%m-%d %H:%M:%S"), 
                    "161130", 
                    "1",  # 1表示上海市场
                    "2",   # 2表示卖出方向
                    stock_info_result # 传入股票信息
                )
                print(f"撤单结果: {cancel_result}")
                logger.info(f"撤单结果: {cancel_result}")
        
    except Exception as e:
        logger.error(f"交易过程中发生错误: {e}")
    
    logger.info("交易示例执行完毕")
    # 将session的cookies保存到cookies.json文件
    with open('cookies.json', 'w') as f:
        json.dump(session.cookies.get_dict(), f, indent=2)
    logger.info("Cookies已成功保存到cookies.json文件")

if __name__ == "__main__":
    main() 