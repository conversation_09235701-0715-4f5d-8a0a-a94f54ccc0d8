"""
风险管理模块

整合所有风险控制模块，提供统一的风险管理接口
"""

import logging
import json
import os
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime

from risk_control.funding_risk import FundingRiskController
from risk_control.trade_frequency_risk import TradeFrequencyController
from risk_control.price_risk import PriceRiskController
from risk_control.execution_risk import ExecutionRiskController

class RiskManager:
    """
    风险管理器
    
    整合所有风险控制模块，提供统一的风险管理接口
    """
    
    def __init__(self, config_path: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        初始化风险管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化各个风险控制器
        self.funding_risk_controller = FundingRiskController(
            config=self.config.get("funding_risk", {}),
            logger=self.logger
        )
        
        self.trade_frequency_controller = TradeFrequencyController(
            config=self.config.get("trade_frequency", {}),
            logger=self.logger
        )
        
        self.price_risk_controller = PriceRiskController(
            config=self.config.get("price_risk", {}),
            logger=self.logger
        )
        
        self.execution_risk_controller = ExecutionRiskController(
            config=self.config.get("execution_risk", {}),
            logger=self.logger
        )
        
        self.logger.info("风险管理器初始化完成")
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """
        加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 配置字典
        """
        default_config = {
            "funding_risk": {},
            "trade_frequency": {},
            "price_risk": {},
            "execution_risk": {},
            "enable_risk_control": True
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                self.logger.info(f"已从 {config_path} 加载风险控制配置")
                
                # 合并配置
                merged_config = default_config.copy()
                for section in merged_config:
                    if section in config:
                        merged_config[section].update(config[section])
                
                return merged_config
            except Exception as e:
                self.logger.error(f"加载风险控制配置失败: {e}")
        
        self.logger.info("使用默认风险控制配置")
        return default_config
    
    def save_config(self, config_path: str):
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            directory = os.path.dirname(config_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            # 收集当前配置
            current_config = {
                "funding_risk": self.funding_risk_controller.config,
                "trade_frequency": self.trade_frequency_controller.config,
                "price_risk": self.price_risk_controller.config,
                "execution_risk": self.execution_risk_controller.config,
                "enable_risk_control": self.config.get("enable_risk_control", True)
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(current_config, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"风险控制配置已保存到 {config_path}")
        except Exception as e:
            self.logger.error(f"保存风险控制配置失败: {e}")
    
    def update_account_info(self, total_assets: float, available_cash: float, positions: Dict[str, Dict[str, float]]):
        """
        更新账户信息
        
        Args:
            total_assets: 总资产 (元)
            available_cash: 可用资金 (元)
            positions: 持仓信息，格式: {code: {'amount': amount, 'cost': cost, 'market_value': value}}
        """
        # 更新资金风险控制器
        self.funding_risk_controller.update_account_info(total_assets, available_cash, positions)
        
        # 更新价格风险控制器
        self.price_risk_controller.update_positions(positions)
        
        self.logger.info("账户信息已更新")
    
    def update_price(self, code: str, price: float, timestamp: Optional[datetime] = None):
        """
        更新股票价格
        
        Args:
            code: 股票代码
            price: 当前价格
            timestamp: 时间戳，默认为当前时间
        """
        # 更新价格风险控制器
        self.price_risk_controller.update_price(code, price, timestamp)
    
    def set_custom_thresholds(self, code: str, stop_loss: Optional[float] = None, take_profit: Optional[float] = None):
        """
        为特定股票设置自定义止损止盈阈值
        
        Args:
            code: 股票代码
            stop_loss: 止损价格，如果为None则使用全局配置
            take_profit: 止盈价格，如果为None则使用全局配置
        """
        self.price_risk_controller.set_custom_thresholds(code, stop_loss, take_profit)
    
    def register_callback(self, event_type: str, callback: Callable):
        """
        注册回调函数
        
        Args:
            event_type: 事件类型，如 'emergency_stop', 'error', 'verification_failed'
            callback: 回调函数
        """
        self.execution_risk_controller.register_callback(event_type, callback)
    
    def emergency_stop_trading(self, reason: str):
        """
        紧急停止交易
        
        Args:
            reason: 停止原因
        """
        self.execution_risk_controller.emergency_stop_trading(reason)
    
    def reset_emergency_stop(self):
        """
        重置紧急停止状态
        """
        self.execution_risk_controller.reset_emergency_stop()
    
    def is_emergency_stop_active(self) -> bool:
        """
        检查紧急停止是否激活
        
        Returns:
            bool: 紧急停止是否激活
        """
        return self.execution_risk_controller.is_emergency_stop_active()
    
    def record_error(self, error_type: str, error_message: str, trade_info: Optional[Dict[str, Any]] = None):
        """
        记录错误
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            trade_info: 交易信息
        """
        self.execution_risk_controller.record_error(error_type, error_message, trade_info)
    
    def record_trade(self, trade_info: Dict[str, Any]):
        """
        记录交易
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
        """
        # 记录资金风险交易
        self.funding_risk_controller.record_trade(trade_info)
        
        # 记录交易频率
        self.trade_frequency_controller.record_trade(trade_info)
    
    def record_loss(self, loss: float):
        """
        记录亏损
        
        Args:
            loss: 亏损金额 (元)，正值表示亏损
        """
        self.funding_risk_controller.record_loss(loss)
    
    def verify_trade(self, order_info: Dict[str, Any], verification_result: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证交易
        
        Args:
            order_info: 订单信息
            verification_result: 验证结果
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 验证消息)
        """
        return self.execution_risk_controller.verify_trade(order_info, verification_result)
    
    def wait_and_retry(self, retry_function: Callable, error_type: str, max_retries: int = None) -> Any:
        """
        等待并重试函数
        
        Args:
            retry_function: 重试函数
            error_type: 错误类型
            max_retries: 最大重试次数，如果为None则使用配置中的值
            
        Returns:
            Any: 函数返回值
        """
        return self.execution_risk_controller.wait_and_retry(retry_function, error_type, max_retries)
    
    def check_risk(self, trade_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        检查所有风险
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
            
        Returns:
            Tuple[bool, Dict]: (是否通过风险检查, 风险详情)
        """
        if not self.config.get("enable_risk_control", True):
            return True, {"message": "风险控制已禁用"}
            
        # 检查是否紧急停止
        if self.is_emergency_stop_active():
            return False, {"message": "紧急停止已激活，禁止交易"}
        
        # 检查执行风险
        execution_passed, execution_details = self.execution_risk_controller.check_risk()
        if not execution_passed:
            return False, {"execution_risk": execution_details}
        
        # 检查资金风险
        funding_passed, funding_details = self.funding_risk_controller.check_risk(trade_info)
        if not funding_passed:
            return False, {"funding_risk": funding_details}
        
        # 检查交易频率风险
        frequency_passed, frequency_details = self.trade_frequency_controller.check_risk(trade_info)
        if not frequency_passed:
            return False, {"trade_frequency_risk": frequency_details}
        
        # 检查价格风险
        price_passed, price_details = self.price_risk_controller.check_risk(trade_info)
        if not price_passed:
            return False, {"price_risk": price_details}
        
        # 所有风险检查都通过
        return True, {
            "execution_risk": execution_details,
            "funding_risk": funding_details,
            "trade_frequency_risk": frequency_details,
            "price_risk": price_details
        }
    
    def start_monitoring(self, monitor_interval: int = 60):
        """
        启动风险监控线程
        
        Args:
            monitor_interval: 监控间隔 (秒)
        """
        return self.execution_risk_controller.start_monitoring(monitor_interval)
    
    def save_error_records(self, file_path: str):
        """
        保存错误记录到文件
        
        Args:
            file_path: 文件路径
        """
        self.execution_risk_controller.save_error_records(file_path)
    
    def save_trade_records(self, funding_record_path: str, frequency_record_path: str):
        """
        保存交易记录到文件
        
        Args:
            funding_record_path: 资金交易记录文件路径
            frequency_record_path: 频率交易记录文件路径
        """
        self.funding_risk_controller.save_trade_records(funding_record_path)
        self.trade_frequency_controller.save_trade_records(frequency_record_path)
    
    def get_risk_statistics(self) -> Dict[str, Any]:
        """
        获取风险统计信息
        
        Returns:
            Dict: 风险统计信息
        """
        return {
            "funding_risk": {
                "daily_loss": self.funding_risk_controller.get_daily_loss(),
                "position_info": self.funding_risk_controller.get_position_info()
            },
            "trade_frequency": {
                "daily_trades": self.trade_frequency_controller.get_daily_trades(),
                "group_trades": self.trade_frequency_controller.get_group_trades()
            },
            "price_risk": {
                "volatility_info": self.price_risk_controller.get_volatility_info()
            },
            "execution_risk": {
                "error_statistics": self.execution_risk_controller.get_error_statistics(),
                "emergency_stop": self.execution_risk_controller.is_emergency_stop_active()
            }
        }