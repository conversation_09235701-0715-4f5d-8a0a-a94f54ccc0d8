"""
中信建投登录模块

实现完整的登录流程，包括：
1. 获取交易准备信息（tradeprepare.cgi）
2. 密码加密
3. 登录验证（tradepasswd.cgi）
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, Optional, Tuple
from .encrypt import encrypt_password, decrypt_response


class CiticLogin:
    """中信建投登录管理器"""
    
    def __init__(self, base_url: str = "https://wzq.csc108.com"):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.cookies: Dict[str, str] = {}
        self.headers = {
            "Host": "wzq.csc108.com",
            "Connection": "keep-alive",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c25) XWEB/11581 Flue",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://wzq.csc108.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def set_cookies(self, cookies: Dict[str, str]):
        """设置cookies"""
        self.cookies.update(cookies)
    
    async def trade_prepare(self, scene: str = "trade") -> Dict[str, Any]:
        """
        获取交易准备信息
        
        Args:
            scene: 场景类型，默认为"trade"
            
        Returns:
            Dict: 包含key、timeseed等加密所需信息
        """
        url = f"{self.base_url}/cgi-bin/tradeprepare.cgi"
        
        # 添加时间戳参数
        timestamp = int(time.time() * 1000)
        params = {"t": str(timestamp)}
        
        # 请求数据
        data = {
            "scene": scene,
            "come_from": "0",
            "gm_flag": "0"
        }
        
        # 设置cookies字符串
        cookie_str = "; ".join([f"{k}={v}" for k, v in self.cookies.items()])
        headers = self.headers.copy()
        if cookie_str:
            headers["Cookie"] = cookie_str
        
        async with self.session.post(url, params=params, data=data, headers=headers) as response:
            if response.status != 200:
                raise Exception(f"tradeprepare请求失败: {response.status}")
            
            content = await response.text()
            result = decrypt_response(content)
            
            if result.get("retcode") != "0":
                raise Exception(f"tradeprepare失败: {result.get('retmsg', '未知错误')}")
            
            return result
    
    async def trade_passwd(self, password: str, prepare_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        密码验证登录
        
        Args:
            password: 用户密码
            prepare_config: tradeprepare返回的配置信息
            
        Returns:
            Dict: 登录结果，包含psw_session等信息
        """
        url = f"{self.base_url}/cgi-bin/tradepasswd.cgi"
        
        # 添加时间戳参数
        timestamp = int(time.time() * 1000)
        params = {"t": str(timestamp)}
        
        # 加密密码
        encrypted_password = encrypt_password(prepare_config, password)
        
        # 请求数据
        data = {
            "_appver": "7.0.20",
            "_osVer": "Windows1064", 
            "_buildh5ver": "202504011653",
            "action": "2",
            "psw": encrypted_password,
            "is_trade": "",
            "cosign_pk": "",
            "cosign": "",
            "come_from": "0",
            "gm_flag": "0"
        }
        
        # 设置cookies字符串
        cookie_str = "; ".join([f"{k}={v}" for k, v in self.cookies.items()])
        headers = self.headers.copy()
        if cookie_str:
            headers["Cookie"] = cookie_str
        
        async with self.session.post(url, params=params, data=data, headers=headers) as response:
            if response.status != 200:
                raise Exception(f"tradepasswd请求失败: {response.status}")
            
            content = await response.text()
            result = decrypt_response(content)
            
            if result.get("retcode") != "0":
                raise Exception(f"登录失败: {result.get('retmsg', '未知错误')}")
            
            return result
    
    async def login(self, password: str, cookies: Optional[Dict[str, str]] = None) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """
        完整登录流程
        
        Args:
            password: 用户密码
            cookies: 可选的cookies字典
            
        Returns:
            Tuple[Dict, Dict]: (登录结果, 更新后的cookies)
        """
        if cookies:
            self.set_cookies(cookies)
        
        try:
            # 1. 获取交易准备信息
            prepare_result = await self.trade_prepare()
            
            # 2. 执行密码验证登录
            login_result = await self.trade_passwd(password, prepare_result)
            
            return login_result, self.cookies
            
        except Exception as e:
            raise Exception(f"登录流程失败: {str(e)}")


async def login_with_password(password: str, cookies: Optional[Dict[str, str]] = None) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """
    便捷的登录函数
    
    Args:
        password: 用户密码
        cookies: 可选的cookies字典
        
    Returns:
        Tuple[Dict, Dict]: (登录结果, 更新后的cookies)
    """
    async with CiticLogin() as login_manager:
        return await login_manager.login(password, cookies)


# 同步版本的登录函数
def login_sync(password: str, cookies: Optional[Dict[str, str]] = None) -> Tuple[Dict[str, Any], Dict[str, str]]:
    """
    同步版本的登录函数
    
    Args:
        password: 用户密码
        cookies: 可选的cookies字典
        
    Returns:
        Tuple[Dict, Dict]: (登录结果, 更新后的cookies)
    """
    return asyncio.run(login_with_password(password, cookies))


if __name__ == "__main__":
    # 测试代码
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python login.py <密码> [cookies_json_file]")
        sys.exit(1)
    
    password = sys.argv[1]
    cookies = None
    
    if len(sys.argv) > 2:
        with open(sys.argv[2], 'r', encoding='utf-8') as f:
            cookies = json.load(f)
    
    try:
        result, updated_cookies = login_sync(password, cookies)
        print("登录成功!")
        print("登录结果:", json.dumps(result, ensure_ascii=False, indent=2))
        print("更新后的cookies:", json.dumps(updated_cookies, ensure_ascii=False, indent=2))
    except Exception as e:
        print(f"登录失败: {e}")
