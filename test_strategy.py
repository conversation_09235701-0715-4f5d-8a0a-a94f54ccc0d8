#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略模块测试脚本

用于测试交易策略的功能，特别是配对交易策略
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from citic_trader.utils import setup_logger, load_config
from strategies.base_strategy import BaseStrategy
from strategies.pair_trading import PairTradingStrategy
from market_data import (
    get_real_time_data_manager,
    connect_market_data_sources,
    disconnect_market_data_sources
)
from market_data.tdx_data_source import TdxDataSource
from market_data.real_time_data_manager import RealTimeDataManager
from datetime import datetime, timedelta

# 设置日志
logger = setup_logger(log_level=logging.INFO, log_to_file=True)

def test_pair_trading_strategy(base_code_override=None, target_code_override=None, window_override=None, std_dev_mult_override=None):
    """
    测试配对交易策略。
    """
    logger.info(f"开始测试配对交易策略")

    config = load_config("config.json")
    tdx_config = config.get("tdx_config", {})
    strategy_config_from_file = config.get("trading", {}) # 获取 config.json 中的 'trading' 部分

    # 将 config.json 中的键映射到策略参数的键
    strategy_params = {
        "base_code": base_code_override or strategy_config_from_file.get("base_etf", "518880"),
        "target_code": target_code_override or strategy_config_from_file.get("target_etf", "518890"),
        "trade_code": strategy_config_from_file.get("trade_etf", "518850"), # 假设 trade_code 从配置中获取
        "window": window_override or strategy_config_from_file.get("window", 120),
        "std_dev_mult": std_dev_mult_override or strategy_config_from_file.get("std_dev_mult", 1.2),
        "max_pos_size": strategy_config_from_file.get("max_pos_size", 1.0)
    }

    base_code = strategy_params["base_code"]
    target_code = strategy_params["target_code"]
    window = strategy_params["window"]

    # 1. 初始化数据源和实时数据管理器
    tdx_data_source = TdxDataSource(tdx_config=tdx_config, logger=logger, auto_connect=True)
    rtd_manager = RealTimeDataManager(primary_data_source=tdx_data_source, logger=logger)

    if not tdx_data_source.connected:
        logger.error("数据源连接失败，无法执行策略测试。")
        return

    try:
        # 2. 初始化策略
        strategy = PairTradingStrategy(params=strategy_params, logger=logger)
        logger.info(f"策略参数: {strategy_params}")

        # 3. 获取历史数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        # 获取足够的数据量以覆盖 'window' 参数
        # 假设 D 线 (日线) 数据
        historical_days_needed = window * 2 # 获取两倍于窗口大小的日期数据以确保数据量足够
        start_date = (datetime.now() - timedelta(days=historical_days_needed)).strftime('%Y-%m-%d')
        ktype = 'D' # 使用日线数据

        logger.info(f"获取 {base_code} 的历史数据 (从 {start_date} 到 {end_date}, {ktype}线)...")
        base_k_data = rtd_manager.get_k_data(base_code, start_date, end_date, ktype=ktype)

        logger.info(f"获取 {target_code} 的历史数据 (从 {start_date} 到 {end_date}, {ktype}线)....")
        target_k_data = rtd_manager.get_k_data(target_code, start_date, end_date, ktype=ktype)

        if base_k_data.empty or target_k_data.empty:
            logger.error("未能获取足够的历史数据，无法执行策略分析。")
            return

        # 确保两个数据集的时间索引匹配并排序
        common_index = base_k_data.index.intersection(target_k_data.index).sort_values()
        base_k_data = base_k_data.loc[common_index]
        target_k_data = target_k_data.loc[common_index]

        if len(common_index) < window:
             logger.error(f"历史数据点不足 {window} 个，无法计算策略指标。实际数据点: {len(common_index)}")
             return

        logger.info(f"成功获取 {base_code} 历史数据，共 {len(base_k_data)} 条。")
        logger.info(f"成功获取 {target_code} 历史数据，共 {len(target_k_data)} 条。")

        # 4. 喂入历史数据到策略
        logger.info("喂入历史数据到策略并计算指标...")
        # 逐点更新策略的历史数据
        for date_idx in common_index:
            market_data = {
                "base_price": base_k_data.loc[date_idx, 'close'],
                "target_price": target_k_data.loc[date_idx, 'close'],
                "trade_price": target_k_data.loc[date_idx, 'close'], # 对于配对交易，trade_price 可以是 target_price
                "timestamp": date_idx.strftime('%Y-%m-%d %H:%M:%S')
            }
            strategy.update_data(market_data) # 这将同时调用 _calculate_indicators

        # 5. 模拟实时行情并生成信号 (使用最新的数据点)
        logger.info("模拟实时行情并生成信号...")
        # 使用最新的数据点作为当前的实时行情进行信号生成
        latest_base_price = base_k_data['close'].iloc[-1]
        latest_target_price = target_k_data['close'].iloc[-1]

        # 构造最新的市场数据字典，用于传递给 get_signal 方法
        latest_market_data = {
            "base_price": latest_base_price,
            "target_price": latest_target_price,
            "trade_price": latest_target_price, # 交易价格，这里假设为目标价格
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 生成信号 (get_signal 方法返回单个信号)
        signal = strategy.get_signal(latest_market_data)

        if signal != 0:
            logger.info(f"生成了交易信号: {signal}")
            logger.info(f"  信号详情 (最新信息): {strategy.get_latest_info()}")
        else:
            logger.info("未生成交易信号。")

    except Exception as e:
        logger.error(f"测试配对交易策略时发生错误: {e}", exc_info=True)
    finally:
        if tdx_data_source and tdx_data_source.connected:
            tdx_data_source.disconnect()
            logger.info("数据源已断开连接。")

def plot_strategy_results(results, base_code, target_code):
    """
    绘制策略结果图表
    
    Args:
        results: 结果数据框
        base_code: 基准资产代码
        target_code: 目标资产代码
    """
    try:
        plt.figure(figsize=(16, 12))
        
        # 绘制价格
        plt.subplot(3, 1, 1)
        plt.plot(results.index, results['base_price'], label=f'{base_code} 价格')
        plt.plot(results.index, results['target_price'], label=f'{target_code} 价格')
        plt.title('价格走势')
        plt.legend()
        plt.grid(True)
        
        # 绘制z-score和信号
        plt.subplot(3, 1, 2)
        plt.plot(results.index, results['z_score'], label='Z-Score', color='blue')
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        plt.axhline(y=1, color='red', linestyle='--', alpha=0.3)
        plt.axhline(y=-1, color='green', linestyle='--', alpha=0.3)
        plt.axhline(y=2, color='red', linestyle='--', alpha=0.3)
        plt.axhline(y=-2, color='green', linestyle='--', alpha=0.3)
        
        # 在图上标记买入和卖出信号
        buy_signals = results[results['signal'] > 0].index
        sell_signals = results[results['signal'] < 0].index
        plt.scatter(buy_signals, results.loc[buy_signals, 'z_score'], color='green', marker='^', s=100, label='买入信号')
        plt.scatter(sell_signals, results.loc[sell_signals, 'z_score'], color='red', marker='v', s=100, label='卖出信号')
        
        plt.title('Z-Score 和交易信号')
        plt.legend()
        plt.grid(True)
        
        # 绘制累计收益
        plt.subplot(3, 1, 3)
        plt.plot(results.index, results['cumulative_returns'], label='策略收益', color='blue')
        plt.plot(results.index, results['base_cumulative_returns'], label='基准收益', color='orange')
        plt.title('累计收益')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('pair_trading_results.png')
        logger.info("结果图表已保存为 pair_trading_results.png")
        
    except Exception as e:
        logger.error(f"绘制结果图表时发生错误: {e}")

def print_statistics(results):
    """
    打印策略统计指标
    
    Args:
        results: 结果数据框
    """
    try:
        # 计算年化收益率和波动率
        returns = results['strategy_returns'].dropna()
        base_returns = results['base_returns'].dropna()
        
        if len(returns) == 0:
            logger.warning("没有足够的数据计算统计指标")
            return
        
        # 假设数据是5分钟K线，则一年有 (252*6.5*60/5) = 约19,656个数据点
        annual_factor = 19656 / len(returns) if len(returns) > 0 else 1
        
        # 计算统计指标
        stats = {
            "总收益率": results['cumulative_returns'].iloc[-1] if len(results) > 0 else 0,
            "年化收益率": (1 + results['cumulative_returns'].iloc[-1]) ** annual_factor - 1 if len(results) > 0 else 0,
            "年化波动率": returns.std() * np.sqrt(19656) if len(returns) > 0 else 0,
            "夏普比率": ((1 + results['cumulative_returns'].iloc[-1]) ** annual_factor - 1) / (returns.std() * np.sqrt(19656)) if len(returns) > 0 and returns.std() > 0 else 0,
            "最大回撤": compute_max_drawdown(results['cumulative_returns']),
            "胜率": len(returns[returns > 0]) / len(returns) if len(returns) > 0 else 0,
            "交易次数": (results['signal'] != 0).sum(),
            "买入次数": (results['signal'] > 0).sum(),
            "卖出次数": (results['signal'] < 0).sum()
        }
        
        # 打印统计指标
        logger.info("策略统计指标:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value:.4f}")
        
    except Exception as e:
        logger.error(f"计算统计指标时发生错误: {e}")

def compute_max_drawdown(returns):
    """
    计算最大回撤
    
    Args:
        returns: 收益率序列
    
    Returns:
        float: 最大回撤
    """
    # 创建累计收益序列
    cum_returns = returns.fillna(0)
    if len(cum_returns) == 0:
        return 0
    
    # 计算累计最大值
    cum_max = cum_returns.cummax()
    
    # 计算回撤
    drawdown = (cum_returns - cum_max) / (1 + cum_max)
    
    # 返回最大回撤
    return drawdown.min()

def test_custom_strategy():
    """
    测试自定义策略
    """
    # 这里可以添加更多的自定义策略测试
    pass

if __name__ == "__main__":
    # 可以根据需要调整测试参数
    test_pair_trading_strategy() # 使用默认配置值或命令行覆盖参数 