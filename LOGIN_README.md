# 中信建投登录模块使用指南

## 概述

本模块实现了中信建投微信网厅的完整登录流程，包括密码加密和身份验证。采用纯Python实现，避免了JavaScript环境的复杂性。

## 核心功能

### 1. 密码加密 (`citic_trader/encrypt.py`)
- 实现了与JavaScript端完全一致的RSA加密算法
- 支持时间种子处理和MD5哈希
- 自定义大数运算，确保加密结果准确

### 2. 登录管理 (`citic_trader/login.py`)
- 完整的登录流程实现
- 支持异步和同步两种调用方式
- 自动处理cookies和会话管理

## 安装依赖

```bash
pip install aiohttp
```

## 使用方法

### 基础用法

```python
from citic_trader.login import login_sync

# 同步登录
password = "your_password"
cookies = {}  # 从浏览器获取的cookies
result, updated_cookies = login_sync(password, cookies)
print(f"登录成功，会话: {result['psw_session']}")
```

### 异步用法

```python
import asyncio
from citic_trader.login import CiticLogin

async def login_example():
    async with CiticLogin() as login_manager:
        result, cookies = await login_manager.login(password, cookies)
        return result

result = asyncio.run(login_example())
```

### 分步骤登录

```python
async with CiticLogin() as login_manager:
    # 步骤1: 获取交易准备信息
    prepare_result = await login_manager.trade_prepare()
    
    # 步骤2: 密码验证
    login_result = await login_manager.trade_passwd(password, prepare_result)
```

## 获取Cookies

登录需要有效的cookies，可以通过以下方式获取：

1. **浏览器开发者工具**：
   - 打开中信建投微信网厅
   - 按F12打开开发者工具
   - 在Network标签页找到请求
   - 复制Cookie头部信息

2. **常用的Cookie字段**：
   ```json
   {
     "qlappid": "wx9cf8c670ebd68ce4",
     "qlskey": "your_qlskey_value",
     "qluin": "your_qluin_value",
     "wzq_dealer": "11100",
     "trade_in_cmschina": "1",
     "trade_in_partner": "1"
   }
   ```

## 测试

### 运行测试脚本

```bash
# 完整测试
python test_login.py your_password

# 仅测试加密功能
python test_login.py --encrypt-only
```

### 查看示例

```bash
python login_example.py
```

## API参考

### CiticLogin类

#### 方法

- `trade_prepare(scene="trade")`: 获取交易准备信息
- `trade_passwd(password, prepare_config)`: 密码验证
- `login(password, cookies=None)`: 完整登录流程
- `set_cookies(cookies)`: 设置cookies

#### 返回值

登录成功返回包含以下字段的字典：
- `retcode`: 返回码（"0"表示成功）
- `retmsg`: 返回消息
- `psw_session`: 会话信息（重要！）

### 便捷函数

- `login_sync(password, cookies=None)`: 同步登录
- `login_with_password(password, cookies=None)`: 异步登录

## 错误处理

常见错误及解决方案：

1. **"更新公钥失败"**: cookies过期，需要重新获取
2. **"密码输入错误"**: 检查密码是否正确
3. **"网络繁忙"**: 网络问题，稍后重试
4. **"响应解密失败"**: 服务器返回格式异常

## 安全注意事项

1. **密码安全**: 不要在代码中硬编码密码
2. **Cookies保护**: cookies包含敏感信息，妥善保管
3. **会话管理**: psw_session有时效性，需要定期更新
4. **网络安全**: 建议在安全网络环境下使用

## 集成到交易系统

```python
from citic_trader.login import login_sync
from citic_trader.trade import CiticTrader

# 登录获取会话
result, cookies = login_sync(password, cookies)
psw_session = result['psw_session']

# 初始化交易器
trader = CiticTrader(psw_session=psw_session, cookies=cookies)

# 执行交易
await trader.buy("000001", 10.0, 100)
```

## 技术细节

### 加密算法
- 使用RSA加密，公钥指数为65537
- 自定义填充方式，块大小128字节
- 时间种子参与加密过程
- MD5用于生成时间码

### 网络请求
- 使用aiohttp进行异步HTTP请求
- 自动处理gzip压缩
- 模拟微信浏览器User-Agent

### 数据格式
- 请求数据使用form-urlencoded格式
- 响应数据为Base64编码的JSON

## 故障排除

如果遇到问题，请检查：

1. **网络连接**: 确保能访问wzq.csc108.com
2. **Cookies有效性**: 使用最新的cookies
3. **密码正确性**: 确认交易密码无误
4. **依赖版本**: 确保aiohttp版本兼容

## 更新日志

- v1.0: 初始版本，实现基础登录功能
- v1.1: 优化加密算法，提高成功率
- v1.2: 添加异步支持和错误处理

## 贡献

欢迎提交Issue和Pull Request来改进这个模块。

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
