import random
import binascii
import hashlib

class TimeSeedHelper:
    def __init__(self, time_seed):
        self.time_seed = str(time_seed)  # 确保是字符串
        self.used = False
    
    def get(self):
        self.used = True
        return self.time_seed[:20]  # 取前20个字符
    
    def get_time_code(self):
        """完全模拟JS的getTimeCode方法：计算时间种子的MD5并取前14字符"""
        # 计算时间种子的MD5（32位小写十六进制）
        md5 = hashlib.md5()
        md5.update(self.time_seed.encode('utf-8'))
        md5_hash = md5.hexdigest()
        
        # 返回前14个字符（与JS行为一致）
        return md5_hash[:14]

def rsa_custom_encrypt(plaintext, pub_key_n, pub_key_e=65537):
    """
    完全模拟JS的RSA加密过程（支持多块分段）
    :param plaintext: 待加密字符串
    :param pub_key_n: RSA公钥模数(16进制字符串)
    :param pub_key_e: RSA公钥指数(默认65537)
    :return: 16进制大写加密结果（多块空格分隔）
    """
    # 1. 反转字符串 (模拟JS的_encrypt1)
    reversed_text = plaintext[::-1]
    
    # 2. 转换为字节数组
    byte_array = [ord(c) for c in reversed_text]
    orig_len = len(byte_array)
    
    # 3. 自定义填充 (128字节块大小)
    block_size = 128
    while len(byte_array) % block_size != 0:
        pos = len(byte_array)
        # JS填充规则:
        #   pos == 原始长度 或 pos == 块最后位置 -> 0
        #   pos == 块倒数第二位置 -> 2
        #   其他情况 -> 随机值
        if pos == orig_len or pos % block_size == block_size - 1:
            byte_array.append(0)
        elif pos % block_size == block_size - 2:
            byte_array.append(2)
        else:
            byte_array.append(random.randint(0, 255))
    
    # 4. 分块处理
    n_int = int(pub_key_n, 16)  # 16进制字符串转整数
    encrypted_blocks = []
    
    # 每128字节为一个块
    for i in range(0, len(byte_array), block_size):
        block = byte_array[i:i+block_size]
        
        # 5. 将字节数组转换为大整数 (模拟JS的k类)
        big_int = 0
        # 每2个字节组成一个16位整数 (小端序: 第一个字节在低位)
        for j in range(0, len(block), 2):
            byte1 = block[j]
            byte2 = block[j+1] if (j+1) < len(block) else 0
            value = byte1 | (byte2 << 8)
            big_int |= value << (j * 8)  # 移位操作构建大整数
        
        # 6. RSA加密: cipher = plain^e mod n
        cipher_int = pow(big_int, pub_key_e, n_int)
        
        # 7. 转换为固定长度256字符的16进制大写
        hex_cipher = format(cipher_int, '0256X')
        encrypted_blocks.append(hex_cipher)
    
    # 8. 用空格连接所有加密块（模拟JS返回格式）
    return " ".join(encrypted_blocks)

def encrypt_password(config, password):
    """
    :param config: 后端返回的配置字典
    :param password: 用户密码
    :return: 加密后的字符串
    """
    # 初始化时间种子
    ts_helper = TimeSeedHelper(config["timeseed"])
    
    # 构建待加密明文: 时间种子前缀 + 密码
    plaintext = ts_helper.get() + password
    
    # 执行RSA加密
    cipher = rsa_custom_encrypt(plaintext, config["key"])
    
    # 构建最终结果: 时间码 + 6个0 + 40个0 + 密文
    time_code = ts_helper.get_time_code()
    return f"{time_code}000000{'0'*40}{cipher}"

# 使用示例
if __name__ == "__main__":
    # 后端返回的配置
    config = {
        "retcode": "0",
        "retmsg": "OK",
        "encrypt_method": "0",
        "gm_check_sign_switch": "0",
        "key": "C0CEE4B6914866965BE0D3D3F155D85FA296CBBF13956EDBB32146C8C61E36B164BDD8F399CFEF37B60AB650BABBB1F62EFA679119B375E0E1A0272D7CD84DD6DE8D2C618753A7C2D79AEFBC2249F23A8797A4AC885CE795CBA8DB27F8BEBCC0DADCC155ED216BAE6A2923A98E45A7CD1BE1F330CB1B66054B2FC9A117522633",
        "key_front_and_broker": "****************************************************************************************************************************************************************************************************************************************************************",
        "needcheck": "1",
        "timeseed": "17448509762571406842"
    }
    
    # 测试密码
    password = "315717"
    
    # 执行加密
    encrypted = encrypt_password(config, password)
    print(f"加密结果: {encrypted}")

