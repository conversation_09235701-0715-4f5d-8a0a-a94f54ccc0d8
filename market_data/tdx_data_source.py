"""
通达信数据源实现

提供通过通达信接口获取市场数据的功能
"""

import os
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from datetime import datetime, timedelta
import requests
import re

from .data_source import DataSource

try:
    import pytdx.hq as tdx_hq
    import pytdx.params as tdx_params
    TDX_AVAILABLE = True
except ImportError:
    TDX_AVAILABLE = False
    

class TdxDataSource(DataSource):
    """
    通达信数据源
    
    通过pytdx库连接通达信服务器获取行情数据
    """
    
    # 默认通达信服务器列表 (备用)
    DEFAULT_TDX_SERVERS = [
        {"ip": "**************", "port": 7709, "name": "深圳双线主站1"},
        {"ip": "*************", "port": 7709, "name": "深圳双线主站2"},
        {"ip": "*************", "port": 7709, "name": "深圳双线主站3"},
        {"ip": "**************", "port": 7709, "name": "深圳双线主站4"},
        {"ip": "*************", "port": 7709, "name": "上海双线主站1"},
        {"ip": "*************", "port": 7709, "name": "上海双线主站2"},
    ]
    
    def __init__(self, tdx_config: Dict[str, Any], logger: Optional[logging.Logger] = None, auto_connect: bool = True):
        """
        初始化通达信数据源
        
        Args:
            tdx_config: 通达信配置字典，可以包含服务器列表URL和是否使用最优服务器等
            logger: 日志记录器
            auto_connect: 是否自动连接
        """
        if not TDX_AVAILABLE:
            raise ImportError("pytdx库未安装，请先安装: pip install pytdx")
            
        super().__init__(logger)
        
        self.tdx_config = tdx_config
        self.server_list_url = tdx_config.get("server_list_url")
        self.use_best_server = tdx_config.get("use_best_server", False)
        self.custom_servers = tdx_config.get("custom_servers", []) # 新增：从配置中获取自定义服务器列表
        
        # 连接超时时间（秒）
        self.timeout = tdx_config.get("timeout", 10)
        # 最大重试次数
        self.max_retry = tdx_config.get("max_retry", 3)
        # 当前使用的服务器
        self.current_server = None
        # 动态获取的服务器列表
        self.dynamic_servers = []

        if self.custom_servers: # 如果有自定义服务器，优先使用
            self.logger.info(f"检测到自定义服务器列表，共 {len(self.custom_servers)} 个。")
            if self.use_best_server:
                self.TDX_SERVERS = sorted(self.custom_servers, key=lambda x: self.test_server_latency(x['ip'], x['port']))
                self.logger.info("自定义服务器列表已按延迟排序。")
            else:
                self.TDX_SERVERS = self.custom_servers
        elif self.use_best_server and self.server_list_url: # 尝试从URL获取
            self.dynamic_servers = self.get_latest_server_list()
            if not self.dynamic_servers:
                self.logger.warning("未能从URL获取服务器列表，将使用默认服务器列表。")
                self.TDX_SERVERS = self.DEFAULT_TDX_SERVERS
            else:
                self.logger.info(f"已从 {self.server_list_url} 获取到 {len(self.dynamic_servers)} 个服务器。")
                # 对获取到的服务器进行延迟测试并排序
                self.TDX_SERVERS = sorted(self.dynamic_servers, key=lambda x: self.test_server_latency(x['ip'], x['port']))
                self.logger.info("服务器列表已按延迟排序。")
        else: # 否则使用默认服务器列表
            self.TDX_SERVERS = self.DEFAULT_TDX_SERVERS
            
        if auto_connect:
            self.connect()
    
    def _initialize(self):
        """初始化通达信API客户端"""
        self.api = tdx_hq.TdxHq_API(raise_exception=True)
    
    def get_latest_server_list(self) -> List[Dict[str, Any]]:
        """
        从指定URL获取通达信服务器列表。
        
        Args:
            url: 服务器列表的URL。
            
        Returns:
            List[Dict[str, Any]]: 服务器列表，每个元素包含'ip'、'port'和'name'。
        """
        server_list = []
        try:
            response = requests.get(self.server_list_url, timeout=self.timeout)
            response.raise_for_status() # 检查HTTP请求是否成功
            html_content = response.text
            
            # 使用正则表达式从HTML内容中提取服务器信息
            # 匹配模式：HostNameXX=名称 IPAddressXX=IP PortXX=端口
            pattern = re.compile(r"HostName\d+=([^\s]+)\s+IPAddress\d+=(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+Port\d+=(\d+)")
            
            for match in pattern.finditer(html_content):
                name = match.group(1)
                ip = match.group(2)
                port = int(match.group(3))
                server_list.append({"ip": ip, "port": port, "name": name})
            self.logger.info(f"成功从 {self.server_list_url} 获取到 {len(server_list)} 个服务器。")
        except requests.exceptions.RequestException as e:
            self.logger.warning(f"从URL获取服务器列表失败: {e}")
        except Exception as e:
            self.logger.error(f"解析服务器列表时发生错误: {e}")
        return server_list

    def test_server_latency(self, ip: str, port: int) -> float:
        """
        测试通达信服务器的连接延迟。
        
        Args:
            ip: 服务器IP地址。
            port: 服务器端口。
            
        Returns:
            float: 连接延迟（秒），如果连接失败则返回无穷大。
        """
        api = tdx_hq.TdxHq_API(raise_exception=False) # 不抛出异常，以便处理连接失败
        start_time = time.time()
        try:
            if api.connect(ip, port, time_out=self.timeout):
                api.disconnect()
                latency = time.time() - start_time
                self.logger.debug(f"测试服务器 {ip}:{port} 延迟: {latency:.4f} 秒")
                return latency
            else:
                self.logger.warning(f"无法连接到服务器 {ip}:{port} 进行延迟测试。")
                return float('inf')
        except Exception as e:
            self.logger.warning(f"测试服务器 {ip}:{port} 延迟时发生错误: {e}")
            return float('inf')
    
    def connect(self) -> bool:
        """
        连接到通达信服务器
        
        Returns:
            bool: 是否连接成功
        """
        if self.connected:
            self.logger.info("已经连接到通达信服务器")
            return True
            
        # 尝试连接服务器列表中的服务器
        for retry in range(self.max_retry):
            # 使用 self.TDX_SERVERS (可能已根据延迟排序或来自URL)
            for server in self.TDX_SERVERS:
                try:
                    self.logger.info(f"尝试连接通达信服务器: {server.get('name', '')} ({server['ip']}:{server['port']})")
                    connect_result = self.api.connect(server['ip'], server['port'], time_out=self.timeout)
                    
                    if connect_result:
                        self.connected = True
                        self.current_server = server
                        self.logger.info(f"成功连接到通达信服务器: {server.get('name', '')}")
                        return True
                except Exception as e:
                    self.logger.warning(f"连接通达信服务器 {server.get('name', '')} 失败: {str(e)}")
                    continue
                    
            self.logger.error(f"尝试连接所有通达信服务器失败，第 {retry+1}/{self.max_retry} 次重试")
            time.sleep(1)  # 等待1秒后重试
            
        self.logger.error("无法连接到任何通达信服务器")
        return False
    
    def disconnect(self) -> bool:
        """
        断开与通达信服务器的连接
        
        Returns:
            bool: 是否断开成功
        """
        if not self.connected:
            return True
            
        try:
            self.api.disconnect()
            self.connected = False
            self.logger.info("已断开与通达信服务器的连接")
            return True
        except Exception as e:
            self.logger.error(f"断开通达信服务器连接时出错: {str(e)}")
            return False
    
    def _ensure_connection(self):
        """确保已连接到通达信服务器"""
        if not self.connected:
            self.connect()
            if not self.connected:
                raise ConnectionError("无法连接到通达信服务器")
    
    def _get_market_code(self, code: str) -> Tuple[int, str]:
        """
        获取股票代码对应的市场代码
        
        Args:
            code: 股票代码
            
        Returns:
            Tuple[int, str]: 市场代码和格式化后的股票代码
        """
        code = self.format_code(code)
        
        # 判断市场类型
        if code.startswith(('sh', 'SH', '6', '5')):
            # 上海市场
            market_code = tdx_params.TDXParams.MARKET_SH
            if code.startswith(('sh', 'SH')):
                code = code[2:]
        elif code.startswith(('sz', 'SZ', '0', '1', '2', '3')):
            # 深圳市场
            market_code = tdx_params.TDXParams.MARKET_SZ
            if code.startswith(('sz', 'SZ')):
                code = code[2:]
        else:
            # 默认为深圳市场
            market_code = tdx_params.TDXParams.MARKET_SZ
            
        return market_code, code
    
    def format_code(self, code: str) -> str:
        """
        格式化股票代码
        
        Args:
            code: 原始股票代码
            
        Returns:
            str: 格式化后的股票代码
        """
        # 如果代码已经包含市场前缀，则直接返回
        if code.startswith(('sh', 'sz', 'SH', 'SZ')):
            return code.lower()
            
        # 根据代码前缀判断市场
        if code.startswith(('6', '5')):
            return f"sh{code}"
        elif code.startswith(('0', '1', '2', '3')):
            return f"sz{code}"
        else:
            # 默认为深圳市场
            return f"sz{code}"
    
    def get_price(self, code: str) -> Optional[float]:
        """
        获取指定股票的最新价格
        
        Args:
            code: 股票代码
            
        Returns:
            Optional[float]: 最新价格，如果获取失败则返回None
        """
        quote = self.get_quote(code)
        return quote.get('price')
    
    def get_quote(self, code: str) -> Dict[str, Any]:
        """
        获取指定股票的行情数据
        
        Args:
            code: 股票代码
            
        Returns:
            Dict[str, Any]: 行情数据字典，包含价格、成交量等信息
        """
        self._ensure_connection()
        
        market_code, pure_code = self._get_market_code(code)
        
        try:
            # 获取股票行情
            stock_quote = self.api.get_security_quotes([(market_code, pure_code)])
            
            if stock_quote and len(stock_quote) > 0:
                quote_data = stock_quote[0]
                
                # 构建统一的行情数据格式
                quote = {
                    'code': self.format_code(code),
                    'name': quote_data.get('name', ''),
                    'price': float(quote_data.get('price', 0)),
                    'open': float(quote_data.get('open', 0)),
                    'high': float(quote_data.get('high', 0)),
                    'low': float(quote_data.get('low', 0)),
                    'pre_close': float(quote_data.get('last_close', 0)),
                    'volume': float(quote_data.get('volume', 0)),
                    'amount': float(quote_data.get('amount', 0)),
                    'bid1': float(quote_data.get('bid1', 0)),
                    'bid1_volume': int(quote_data.get('bid1_vol', 0)),
                    'bid2': float(quote_data.get('bid2', 0)),
                    'bid2_volume': int(quote_data.get('bid2_vol', 0)),
                    'bid3': float(quote_data.get('bid3', 0)),
                    'bid3_volume': int(quote_data.get('bid3_vol', 0)),
                    'bid4': float(quote_data.get('bid4', 0)),
                    'bid4_volume': int(quote_data.get('bid4_vol', 0)),
                    'bid5': float(quote_data.get('bid5', 0)),
                    'bid5_volume': int(quote_data.get('bid5_vol', 0)),
                    'ask1': float(quote_data.get('ask1', 0)),
                    'ask1_volume': int(quote_data.get('ask1_vol', 0)),
                    'ask2': float(quote_data.get('ask2', 0)),
                    'ask2_volume': int(quote_data.get('ask2_vol', 0)),
                    'ask3': float(quote_data.get('ask3', 0)),
                    'ask3_volume': int(quote_data.get('ask3_vol', 0)),
                    'ask4': float(quote_data.get('ask4', 0)),
                    'ask4_volume': int(quote_data.get('ask4_vol', 0)),
                    'ask5': float(quote_data.get('ask5', 0)),
                    'ask5_volume': int(quote_data.get('ask5_vol', 0)),
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }
                
                return quote
            else:
                self.logger.warning(f"获取股票 {code} 行情数据失败")
                return {}
                
        except Exception as e:
            self.logger.error(f"获取股票 {code} 行情数据时出错: {str(e)}")
            # 如果连接断开，尝试重新连接
            if "连接断开" in str(e):
                self.connected = False
                self.connect()
            return {}
    
    def get_k_data(self, code: str, start_date: str, end_date: str, 
                  ktype: str = 'D') -> pd.DataFrame:
        """
        获取指定股票的K线数据
        
        Args:
            code: 股票代码
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            ktype: K线类型，D=日线，W=周线，M=月线，5=5分钟线，15=15分钟线，等
            
        Returns:
            pd.DataFrame: K线数据，包含开盘价、收盘价、最高价、最低价、成交量等
        """
        self._ensure_connection()
        
        market_code, pure_code = self._get_market_code(code)
        
        # 转换日期格式
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 确定K线类型
        if ktype == 'D':
            k_type = 9  # 日线
        elif ktype == 'W':
            k_type = 5  # 周线
        elif ktype == 'M':
            k_type = 6  # 月线
        elif ktype == '5':
            k_type = 0  # 5分钟线
        elif ktype == '15':
            k_type = 1  # 15分钟线
        elif ktype == '30':
            k_type = 2  # 30分钟线
        elif ktype == '60':
            k_type = 3  # 60分钟线
        else:
            k_type = 9  # 默认为日线
        
        try:
            # 计算需要获取的数据数量（日期范围）
            days_count = (end_date_obj - start_date_obj).days
            if days_count <= 0:
                days_count = 1
                
            # 通达信接口一次最多返回800条数据
            max_count = 800
            
            # 如果是分钟线，需要特殊处理
            if k_type < 5:
                # 分钟线需要按天获取
                all_data = []
                current_date = start_date_obj
                
                while current_date <= end_date_obj:
                    try:
                        # 获取当天的分钟线数据
                        minute_data = self.api.get_security_bars(
                            category=k_type,
                            market=market_code,
                            code=pure_code,
                            start=0,
                            count=240  # 一天最多240个5分钟
                        )
                        
                        if minute_data:
                            all_data.extend(minute_data)
                            
                        # 下一天
                        current_date += timedelta(days=1)
                    except Exception as e:
                        self.logger.error(f"获取 {code} {current_date.strftime('%Y-%m-%d')} 分钟线数据时出错: {str(e)}")
                        current_date += timedelta(days=1)
                        continue
            else:
                # 日线、周线、月线可以直接获取
                all_data = self.api.get_security_bars(
                    category=k_type,
                    market=market_code,
                    code=pure_code,
                    start=0,
                    count=min(days_count, max_count)
                )
            
            # 转换为DataFrame
            if all_data:
                df = pd.DataFrame([{
                    'date': item['datetime'],
                    'open': item['open'],
                    'high': item['high'],
                    'low': item['low'],
                    'close': item['close'],
                    'volume': item['vol'],
                    'amount': item['amount']
                } for item in all_data])
                
                # 设置日期为索引
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                
                # 过滤日期范围
                df = df[(df.index >= start_date) & (df.index <= end_date)]
                
                return df
            else:
                self.logger.warning(f"获取股票 {code} K线数据失败")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取股票 {code} K线数据时出错: {str(e)}")
            # 如果连接断开，尝试重新连接
            if "连接断开" in str(e):
                self.connected = False
                self.connect()
            return pd.DataFrame()
    
    def get_multiple_prices(self, codes: List[str]) -> Dict[str, float]:
        """
        批量获取多个股票的最新价格
        
        Args:
            codes: 股票代码列表
            
        Returns:
            Dict[str, float]: 股票代码到价格的映射
        """
        self._ensure_connection()
        
        result = {}
        
        # 将代码列表分批处理，每批最多100个
        batch_size = 100
        for i in range(0, len(codes), batch_size):
            batch_codes = codes[i:i+batch_size]
            
            # 准备请求参数
            stock_list = []
            for code in batch_codes:
                market_code, pure_code = self._get_market_code(code)
                stock_list.append((market_code, pure_code))
            
            try:
                # 批量获取行情
                quotes = self.api.get_security_quotes(stock_list)
                
                if quotes:
                    for j, quote in enumerate(quotes):
                        code = batch_codes[j]
                        price = float(quote.get('price', 0))
                        result[code] = price
                        
            except Exception as e:
                self.logger.error(f"批量获取股票价格时出错: {str(e)}")
                # 如果连接断开，尝试重新连接
                if "连接断开" in str(e):
                    self.connected = False
                    self.connect()
                    
        return result
    
    def __del__(self):
        """析构函数，确保断开连接"""
        if hasattr(self, 'api') and self.connected:
            try:
                self.api.disconnect()
            except:
                pass 