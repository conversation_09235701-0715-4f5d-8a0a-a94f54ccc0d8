#!/usr/bin/env python3
"""
中信建投登录使用示例（简化版）

展示如何使用登录模块进行身份验证
"""

import json
import time
from citic_trader.login import login_sync, CiticLogin


def example_basic_login():
    """基础登录示例"""
    print("=== 基础登录示例 ===")
    
    # 从文件加载cookies
    try:
        with open("cookies.json", "r", encoding="utf-8") as f:
            cookies = json.load(f)
        print(f"从cookies.json加载了 {len(cookies)} 个cookies")
    except FileNotFoundError:
        print("cookies.json文件不存在，使用空cookies")
        cookies = {}
    
    password = "your_password_here"  # 替换为实际密码
    
    try:
        result, updated_cookies = login_sync(password, cookies)
        
        print("登录成功!")
        print(f"会话信息: {result.get('psw_session', 'N/A')[:50]}...")
        
        # 保存更新后的cookies
        with open("cookies.json", "w", encoding="utf-8") as f:
            json.dump(updated_cookies, f, ensure_ascii=False, indent=2)
        
        return result
        
    except Exception as e:
        print(f"登录失败: {e}")
        return None


def example_step_by_step():
    """分步骤登录示例"""
    print("\n=== 分步骤登录示例 ===")
    
    # 加载cookies
    try:
        with open("cookies.json", "r", encoding="utf-8") as f:
            cookies = json.load(f)
    except FileNotFoundError:
        cookies = {}
    
    login_manager = CiticLogin()
    login_manager.set_cookies(cookies)
    
    try:
        # 步骤1: 获取交易准备信息
        print("步骤1: 获取交易准备信息...")
        prepare_result = login_manager.trade_prepare()
        print(f"✓ 获取成功，时间种子: {prepare_result['timeseed'][:20]}...")
        
        # 步骤2: 密码验证
        print("步骤2: 执行密码验证...")
        password = "your_password_here"  # 替换为实际密码
        login_result = login_manager.trade_passwd(password, prepare_result)
        print(f"✓ 验证成功，会话: {login_result.get('psw_session', 'N/A')[:50]}...")
        
        return login_result
        
    except Exception as e:
        print(f"✗ 步骤失败: {e}")
        return None


def example_with_error_handling():
    """带错误处理的登录示例"""
    print("\n=== 带错误处理的登录示例 ===")
    
    def safe_login(password: str, max_retries: int = 3):
        """安全登录，带重试机制"""
        for attempt in range(max_retries):
            try:
                print(f"尝试登录 (第{attempt + 1}次)...")
                
                # 加载cookies
                cookies = {}
                try:
                    with open("cookies.json", "r", encoding="utf-8") as f:
                        cookies = json.load(f)
                except FileNotFoundError:
                    print("警告: cookies.json不存在")
                
                # 执行登录
                result, updated_cookies = login_sync(password, cookies)
                
                print("✓ 登录成功!")
                
                # 保存cookies
                with open("cookies.json", "w", encoding="utf-8") as f:
                    json.dump(updated_cookies, f, ensure_ascii=False, indent=2)
                
                return result
                
            except Exception as e:
                print(f"✗ 第{attempt + 1}次登录失败: {e}")
                if attempt < max_retries - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                else:
                    print("所有重试都失败了")
                    raise e
    
    password = "your_password_here"  # 替换为实际密码
    
    try:
        result = safe_login(password)
        return result
    except Exception as e:
        print(f"最终登录失败: {e}")
        return None


def main():
    """主函数 - 运行所有示例"""
    print("中信建投登录模块使用示例")
    print("=" * 50)
    
    print("注意：运行这些示例需要:")
    print("1. 将 'your_password_here' 替换为实际的交易密码")
    print("2. 提供有效的cookies（保存在cookies.json文件中）")
    print("3. 确保网络连接正常")
    print()
    
    # 运行示例（注释掉避免实际执行）
    # example_basic_login()
    # example_step_by_step()
    # example_with_error_handling()
    
    print("示例代码已准备就绪，请根据需要修改密码和cookies后运行")


if __name__ == "__main__":
    main()
