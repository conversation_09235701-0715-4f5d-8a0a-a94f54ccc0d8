#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易模块测试脚本

用于测试中信建投交易账户的交易功能，包括买入、卖出和撤单
"""

import os
import sys
import json
import logging
import getpass
import time
from citic_trader.utils import setup_logger
from citic_trader.auth import AuthClient
from citic_trader.query import QueryClient
from citic_trader.trade import TradeClient

# 设置日志
logger = setup_logger(name="trade_test", log_to_console=True, log_level=logging.INFO)

def login_account(username=None, password=None):
    """
    登录账户
    
    Args:
        username: 用户名（资金账号）
        password: 密码
        
    Returns:
        Tuple[requests.Session, Dict]: (会话对象, 用户信息字典)
    """
    # 如果未提供用户名和密码，从控制台请求输入
    if not username:
        username = input("请输入资金账号：")
    if not password:
        password = getpass.getpass("请输入密码：")
    
    # 创建认证客户端
    auth_client = AuthClient(logger)
    
    # 尝试登录
    logger.info(f"正在使用账号 {username} 尝试登录...")
    session, user_info = auth_client.login(username, password)
    
    if session and user_info:
        logger.info("登录成功！")
        return session, user_info, auth_client
    else:
        logger.error("登录失败！")
        return None, None, auth_client

def test_get_stock_info(trade_client, stock_code="518880"):
    """
    测试获取股票信息
    
    Args:
        trade_client: 交易客户端
        stock_code: 股票代码
    """
    logger.info(f"开始测试获取股票信息，股票代码: {stock_code}")
    
    result = trade_client.get_stock_info(stock_code)
    
    if result["status"] == "success":
        logger.info("获取股票信息成功！")
        logger.info(f"股票代码: {result['data']['code']}")
        logger.info(f"股票名称: {result['data']['name']}")
        logger.info(f"市场类型: {result['data']['market']}")
        logger.info(f"股票类别: {result['data']['stock_cls']}")
        logger.info(f"当前价格: {result['data']['price']}")
        logger.info(f"昨收价格: {result['data']['pre_close']}")
        logger.info(f"最高价格: {result['data']['high']}")
        logger.info(f"最低价格: {result['data']['low']}")
        logger.info(f"成交量: {result['data']['volume']}")
        logger.info(f"成交额: {result['data']['amount']}")
        
        return result["data"]
    else:
        logger.error(f"获取股票信息失败: {result['message']}")
        return None

def test_buy(trade_client, stock_code, price, volume, is_real_trade=False):
    """
    测试买入功能
    
    Args:
        trade_client: 交易客户端
        stock_code: 股票代码
        price: 买入价格
        volume: 买入数量
        is_real_trade: 是否执行真实交易
    """
    logger.info(f"开始测试买入功能，股票代码: {stock_code}, 价格: {price}, 数量: {volume}")
    
    # 首先获取股票信息
    stock_info_result = trade_client.get_stock_info(stock_code)
    if stock_info_result["status"] != "success":
        logger.error(f"获取股票信息失败，无法执行买入测试: {stock_info_result['message']}")
        return None
    
    stock_info = stock_info_result["data"]
    
    # 输出买入信息
    logger.info(f"即将买入 {stock_info['name']}({stock_code}), 价格: {price}, 数量: {volume}")
    
    # 如果不是真实交易，则只模拟交易流程
    if not is_real_trade:
        logger.info("模拟交易模式，不执行真实买入操作")
        return {
            "status": "success",
            "message": "模拟买入成功",
            "entrust_no": "SIMULATED_BUY_" + str(int(time.time())),
            "data": {
                "code": stock_code,
                "name": stock_info["name"],
                "price": str(price),
                "volume": str(volume),
                "amount": str(round(price * volume, 2))
            }
        }
    
    # 执行真实买入
    buy_result = trade_client.buy(stock_code, price, volume, stock_info)
    
    if buy_result["status"] == "success":
        logger.info("买入委托提交成功！")
        logger.info(f"委托编号: {buy_result['entrust_no']}")
        return buy_result
    else:
        logger.error(f"买入委托提交失败: {buy_result['message']}")
        return buy_result

def test_sell(trade_client, stock_code, price, volume, is_real_trade=False):
    """
    测试卖出功能
    
    Args:
        trade_client: 交易客户端
        stock_code: 股票代码
        price: 卖出价格
        volume: 卖出数量
        is_real_trade: 是否执行真实交易
    """
    logger.info(f"开始测试卖出功能，股票代码: {stock_code}, 价格: {price}, 数量: {volume}")
    
    # 首先获取股票信息
    stock_info_result = trade_client.get_stock_info(stock_code)
    if stock_info_result["status"] != "success":
        logger.error(f"获取股票信息失败，无法执行卖出测试: {stock_info_result['message']}")
        return None
    
    stock_info = stock_info_result["data"]
    
    # 输出卖出信息
    logger.info(f"即将卖出 {stock_info['name']}({stock_code}), 价格: {price}, 数量: {volume}")
    
    # 如果不是真实交易，则只模拟交易流程
    if not is_real_trade:
        logger.info("模拟交易模式，不执行真实卖出操作")
        return {
            "status": "success",
            "message": "模拟卖出成功",
            "entrust_no": "SIMULATED_SELL_" + str(int(time.time())),
            "data": {
                "code": stock_code,
                "name": stock_info["name"],
                "price": str(price),
                "volume": str(volume),
                "amount": str(round(price * volume, 2))
            }
        }
    
    # 执行真实卖出
    sell_result = trade_client.sell(stock_code, price, volume, stock_info)
    
    if sell_result["status"] == "success":
        logger.info("卖出委托提交成功！")
        logger.info(f"委托编号: {sell_result['entrust_no']}")
        return sell_result
    else:
        logger.error(f"卖出委托提交失败: {sell_result['message']}")
        return sell_result

def test_cancel(trade_client, query_client, is_real_trade=False):
    """
    测试撤单功能
    
    Args:
        trade_client: 交易客户端
        query_client: 查询客户端
        is_real_trade: 是否执行真实交易
    """
    logger.info("开始测试撤单功能")
    
    # 首先查询当前委托
    query_result = query_client.query_orders("1")  # 查询今日委托
    
    if query_result["status"] != "success":
        logger.error(f"查询委托信息失败，无法执行撤单测试: {query_result['message']}")
        return None
    
    orders = query_result["data"]["orders"]
    
    # 查找可撤销的委托（未完全成交、未撤销的委托）
    cancel_candidates = []
    for order in orders:
        # 检查交易状态，通常状态码为"未成交"或"部分成交"的可以撤单
        if order["trade_state"] in ["未成交", "部分成交", "已报"]:
            cancel_candidates.append(order)
    
    if not cancel_candidates:
        logger.info("没有找到可撤销的委托")
        return None
    
    # 选择第一个可撤销的委托
    order_to_cancel = cancel_candidates[0]
    logger.info(f"找到可撤销的委托:")
    logger.info(f"  合同编号: {order_to_cancel['contract_no']}")
    logger.info(f"  股票代码: {order_to_cancel['code']}")
    logger.info(f"  股票名称: {order_to_cancel['name']}")
    logger.info(f"  交易类型: {'买入' if order_to_cancel['trade_type'] == '1' else '卖出'}")
    logger.info(f"  委托价格: {order_to_cancel['order_price']}")
    logger.info(f"  委托数量: {order_to_cancel['order_num']}")
    logger.info(f"  成交数量: {order_to_cancel['match_num']}")
    logger.info(f"  交易状态: {order_to_cancel['trade_state']}")
    
    # 如果不是真实交易，则只模拟撤单流程
    if not is_real_trade:
        logger.info("模拟交易模式，不执行真实撤单操作")
        return {
            "status": "success",
            "message": "模拟撤单成功",
            "contract_no": order_to_cancel['contract_no']
        }
    
    # 获取股票信息
    stock_info_result = trade_client.get_stock_info(order_to_cancel['code'])
    if stock_info_result["status"] != "success":
        logger.error(f"获取股票信息失败，无法执行撤单测试: {stock_info_result['message']}")
        return None
    
    stock_info = stock_info_result["data"]
    
    # 执行真实撤单
    cancel_result = trade_client.cancel(
        contract_no=order_to_cancel['contract_no'],
        trade_time=order_to_cancel.get('trade_time', ''),
        stock_code=order_to_cancel['code'],
        market=stock_info['market'],
        action=order_to_cancel['trade_type'],  # 1-买入, 2-卖出
        stock_info=stock_info
    )
    
    if cancel_result["status"] == "success":
        logger.info("撤单请求提交成功！")
        logger.info(f"合同编号: {order_to_cancel['contract_no']}")
        return cancel_result
    else:
        logger.error(f"撤单请求提交失败: {cancel_result['message']}")
        return cancel_result

def run_all_tests(is_real_trade=False):
    """
    运行所有交易测试
    
    Args:
        is_real_trade: 是否执行真实交易
    """
    # 登录账户
    session, user_info, auth_client = login_account()
    
    if not session or not user_info:
        logger.error("登录失败，无法执行交易测试")
        return
    
    try:
        # 创建交易客户端和查询客户端
        trade_client = TradeClient(session, user_info, logger)
        query_client = QueryClient(session, user_info, logger)
        
        # 测试获取股票信息
        logger.info("\n" + "=" * 50 + "\n")
        logger.info("测试获取股票信息")
        stock_info = test_get_stock_info(trade_client, "518880")  # 黄金ETF
        
        if not stock_info:
            logger.error("获取股票信息失败，无法继续执行交易测试")
            return
        
        # 计算模拟交易的价格和数量
        current_price = float(stock_info["price"])
        buy_price = round(current_price * 0.99, 2)  # 买入价略低于当前价
        sell_price = round(current_price * 1.01, 2)  # 卖出价略高于当前价
        volume = 100  # 交易数量，必须是100的整数倍
        
        # 测试买入
        logger.info("\n" + "=" * 50 + "\n")
        logger.info("测试买入功能")
        buy_result = test_buy(trade_client, "518880", buy_price, volume, is_real_trade)
        
        # 测试卖出
        logger.info("\n" + "=" * 50 + "\n")
        logger.info("测试卖出功能")
        sell_result = test_sell(trade_client, "518880", sell_price, volume, is_real_trade)
        
        # 测试撤单
        logger.info("\n" + "=" * 50 + "\n")
        logger.info("测试撤单功能")
        cancel_result = test_cancel(trade_client, query_client, is_real_trade)
        
    finally:
        # 确保登出账户
        logger.info("\n" + "=" * 50 + "\n")
        logger.info("测试完成，正在登出账户...")
        auth_client.logout(session)

if __name__ == "__main__":
    # 根据命令行参数执行不同的测试
    if len(sys.argv) > 1:
        # 登录账户
        username = sys.argv[2] if len(sys.argv) > 2 else None
        password = sys.argv[3] if len(sys.argv) > 3 else None
        session, user_info, auth_client = login_account(username, password)
        
        if not session or not user_info:
            logger.error("登录失败，无法执行交易测试")
            sys.exit(1)
        
        try:
            # 创建交易客户端和查询客户端
            trade_client = TradeClient(session, user_info, logger)
            query_client = QueryClient(session, user_info, logger)
            
            if sys.argv[1] == "info":
                # 测试获取股票信息
                stock_code = sys.argv[4] if len(sys.argv) > 4 else "518880"
                test_get_stock_info(trade_client, stock_code)
            elif sys.argv[1] == "buy":
                # 测试买入
                if len(sys.argv) < 7:
                    print("用法: python test_trade.py buy [username] [password] [stock_code] [price] [volume] [--real]")
                    sys.exit(1)
                
                stock_code = sys.argv[4]
                price = float(sys.argv[5])
                volume = int(sys.argv[6])
                is_real_trade = "--real" in sys.argv
                
                test_buy(trade_client, stock_code, price, volume, is_real_trade)
            elif sys.argv[1] == "sell":
                # 测试卖出
                if len(sys.argv) < 7:
                    print("用法: python test_trade.py sell [username] [password] [stock_code] [price] [volume] [--real]")
                    sys.exit(1)
                
                stock_code = sys.argv[4]
                price = float(sys.argv[5])
                volume = int(sys.argv[6])
                is_real_trade = "--real" in sys.argv
                
                test_sell(trade_client, stock_code, price, volume, is_real_trade)
            elif sys.argv[1] == "cancel":
                # 测试撤单
                is_real_trade = "--real" in sys.argv
                test_cancel(trade_client, query_client, is_real_trade)
            else:
                print("用法: python test_trade.py [info|buy|sell|cancel] [username] [password] [args...]")
        finally:
            # 确保登出账户
            logger.info("测试完成，正在登出账户...")
            auth_client.logout(session)
    else:
        # 默认运行所有测试（模拟模式）
        run_all_tests(is_real_trade=False) 