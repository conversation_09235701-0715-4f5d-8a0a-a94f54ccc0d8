#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易结果验证测试脚本

用于测试verify_trade_result功能，验证交易前后的账户变化
"""

import json
import time
import logging
import requests
from typing import Dict, Any, Tuple
from colorama import init, Fore, Style

from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger

# 初始化colorama
init(autoreset=True)

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """从文件加载cookies"""
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(Fore.RED + f"加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """设置会话，添加cookies"""
    session = requests.Session()
    for key, value in cookies.items():
        session.cookies.set(key, value)
    return session

def setup_user_info() -> Dict[str, Any]:
    """设置用户信息"""
    return {
        "account_id": "",  # 从cookies中获取或手动设置
        "stockholder_code": "",  # 从交易接口获取或手动设置
        "psw_session": "",  # 从cookies中获取或手动设置
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def print_position_info(positions: list) -> None:
    """打印持仓信息"""
    if not positions:
        print("无持仓信息")
        return
    
    print("\n持仓信息:")
    print("-" * 80)
    print(f"{'代码':<10} {'名称':<15} {'持仓数量':<10} {'可用数量':<10} {'成本价':<10} {'市值':<15}")
    print("-" * 80)
    
    for position in positions:
        code = position.get("code", "")
        name = position.get("name", "")
        hold_num = position.get("hold_num", "0")
        available_num = position.get("available_num", "0")
        cost_price = position.get("cost_price", "0")
        market_value = position.get("market_value", "0")
        
        print(f"{code:<10} {name:<15} {hold_num:<10} {available_num:<10} {cost_price:<10} {market_value:<15}")

def print_funds_info(funds: Dict[str, Any]) -> None:
    """打印资金信息"""
    print("\n资金信息:")
    print("-" * 50)
    for key, value in funds.items():
        print(f"{key}: {value}")

class TradeVerifier:
    """交易结果验证器"""
    
    def __init__(self):
        # 设置日志
        self.logger = setup_logger(log_level=logging.INFO)
        
        # 加载cookies
        cookies_file = "cookies.json"
        cookies = load_cookies(cookies_file)
        if not cookies:
            self.logger.error("未找到有效的cookies，请先保存cookies到文件")
            return
        
        # 设置会话
        self.session = setup_session(cookies)
        
        # 设置用户信息
        user_info = setup_user_info()
        
        # 创建交易客户端
        self.trade_client = TradeClient(self.session, user_info, self.logger)
        self.logger.info("交易客户端创建成功")
        
        # 交易参数
        self.stock_code = "161130"  # 纳指LOF
        self.account_info = None  # 账户信息
        self.position_before = 0  # 交易前持仓
        self.cash_before = 0  # 交易前资金
        self.position_after = 0  # 交易后持仓
        self.cash_after = 0  # 交易后资金
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            self.logger.info("获取账户信息...")
            result = self.trade_client.trade_show(self.stock_code)
            
            if result.get("status") == "success":
                self.account_info = result["data"]
                self.logger.info("账户信息获取成功")
                return result["data"]
            else:
                self.logger.error(f"获取账户信息失败: {result.get('message', '未知错误')}")
                return {}
        except Exception as e:
            self.logger.error(f"获取账户信息过程中出错: {e}")
            return {}
    
    def get_position_and_cash(self) -> Tuple[int, float]:
        """获取持仓和资金"""
        account_info = self.get_account_info()
        if not account_info:
            return 0, 0
        
        # 获取持仓
        position = 0
        for pos in account_info.get("positions", []):
            if pos.get("code") == self.stock_code:
                position = int(pos.get("hold_num", "0"))
                break
        
        # 获取可用资金
        cash = float(account_info.get("funds", {}).get("available_cash", "0"))
        
        return position, cash
    
    def simulate_buy(self, quantity: int, price: float) -> Dict[str, Any]:
        """模拟买入操作"""
        try:
            self.logger.info(f"模拟买入 {self.stock_code}, 数量: {quantity}, 价格: {price}")
            
            # 计算交易金额和手续费
            amount = quantity * price
            commission = max(amount * 0.0003, 0.1)  # 0.03%手续费，最低0.1元
            total_cost = amount + commission
            
            result = {
                "status": "success",
                "data": {
                    "order_id": "simulation_buy_" + str(int(time.time())),
                    "quantity": quantity,
                    "price": price,
                    "amount": amount,
                    "commission": commission,
                    "total_cost": total_cost
                }
            }
            
            return result
        except Exception as e:
            self.logger.error(f"模拟买入过程中出错: {e}")
            return {"status": "error", "message": str(e)}
    
    def simulate_sell(self, quantity: int, price: float) -> Dict[str, Any]:
        """模拟卖出操作"""
        try:
            self.logger.info(f"模拟卖出 {self.stock_code}, 数量: {quantity}, 价格: {price}")
            
            # 计算交易金额和手续费
            amount = quantity * price
            commission = max(amount * 0.0003, 0.1)  # 0.03%手续费，最低0.1元
            tax = amount * 0.001  # 0.1%印花税（股票）
            total_income = amount - commission - tax
            
            result = {
                "status": "success",
                "data": {
                    "order_id": "simulation_sell_" + str(int(time.time())),
                    "quantity": quantity,
                    "price": price,
                    "amount": amount,
                    "commission": commission,
                    "tax": tax,
                    "total_income": total_income
                }
            }
            
            return result
        except Exception as e:
            self.logger.error(f"模拟卖出过程中出错: {e}")
            return {"status": "error", "message": str(e)}
    
    def verify_trade_result(self, trade_type: str, quantity: int, price: float) -> bool:
        """
        验证交易结果
        
        Args:
            trade_type: 交易类型 ('buy' 或 'sell')
            quantity: 交易数量
            price: 交易价格
        
        Returns:
            bool: 验证结果
        """
        # 获取交易前的持仓和资金
        self.position_before, self.cash_before = self.get_position_and_cash()
        print(Fore.CYAN + f"\n交易前状态:")
        print(f"持仓: {self.position_before} 份")
        print(f"可用资金: {self.cash_before:.2f} 元")
        
        # 执行模拟交易
        if trade_type == 'buy':
            trade_result = self.simulate_buy(quantity, price)
            expected_position_change = quantity
            expected_cash_change = -trade_result["data"]["total_cost"] if trade_result["status"] == "success" else 0
        else:  # sell
            trade_result = self.simulate_sell(quantity, price)
            expected_position_change = -quantity
            expected_cash_change = trade_result["data"]["total_income"] if trade_result["status"] == "success" else 0
        
        # 打印交易结果
        if trade_result["status"] == "success":
            print(Fore.GREEN + f"\n模拟{trade_type}交易成功:")
            for key, value in trade_result["data"].items():
                if isinstance(value, (int, float)) and key not in ["order_id", "quantity"]:
                    print(f"{key}: {value:.2f}")
                else:
                    print(f"{key}: {value}")
        else:
            print(Fore.RED + f"\n模拟{trade_type}交易失败: {trade_result.get('message', '未知错误')}")
            return False
        
        # 模拟交易后状态变化
        expected_position_after = self.position_before + expected_position_change
        expected_cash_after = self.cash_before + expected_cash_change
        
        print(Fore.CYAN + f"\n预期交易后状态:")
        print(f"持仓: {expected_position_after} 份 (变化: {expected_position_change:+d})")
        print(f"可用资金: {expected_cash_after:.2f} 元 (变化: {expected_cash_change:+.2f})")
        
        # 在实际系统中，这里会等待交易完成
        print(Fore.YELLOW + "\n等待交易完成...")
        time.sleep(2)  # 模拟等待
        
        # 获取交易后的持仓和资金
        self.position_after, self.cash_after = self.get_position_and_cash()
        print(Fore.CYAN + f"\n实际交易后状态:")
        print(f"持仓: {self.position_after} 份 (变化: {self.position_after - self.position_before:+d})")
        print(f"可用资金: {self.cash_after:.2f} 元 (变化: {self.cash_after - self.cash_before:+.2f})")
        
        # 验证交易结果
        position_match = self.position_after == expected_position_after
        # 允许资金有小额误差（手续费计算可能有差异）
        cash_tolerance = 1.0  # 允许1元误差
        cash_match = abs(self.cash_after - expected_cash_after) <= cash_tolerance
        
        if position_match and cash_match:
            print(Fore.GREEN + Style.BRIGHT + "\n验证成功: 交易结果符合预期!")
            return True
        else:
            print(Fore.RED + Style.BRIGHT + "\n验证失败: 交易结果与预期不符!")
            if not position_match:
                print(Fore.RED + f"持仓不匹配: 预期 {expected_position_after}, 实际 {self.position_after}")
            if not cash_match:
                print(Fore.RED + f"资金不匹配: 预期 {expected_cash_after:.2f}, 实际 {self.cash_after:.2f}, 差额: {self.cash_after - expected_cash_after:.2f}")
            return False
    
    def test_verify_trade_result(self):
        """测试交易结果验证"""
        print(Fore.CYAN + Style.BRIGHT + "\n===== 测试交易结果验证 =====")
        
        # 获取账户信息
        account_info = self.get_account_info()
        if not account_info:
            print(Fore.RED + "获取账户信息失败，无法进行测试")
            return
        
        # 打印账户信息
        print_funds_info(account_info.get("funds", {}))
        print_position_info(account_info.get("positions", []))
        
        # 模拟价格
        price = 1.5  # 模拟价格
        
        # 测试买入验证
        print(Fore.CYAN + Style.BRIGHT + "\n----- 测试买入验证 -----")
        self.verify_trade_result('buy', 100, price)
        
        # 测试卖出验证
        print(Fore.CYAN + Style.BRIGHT + "\n----- 测试卖出验证 -----")
        self.verify_trade_result('sell', 100, price)
        
        print(Fore.GREEN + Style.BRIGHT + "\n测试完成!")

def main():
    """主函数"""
    print(Fore.CYAN + Style.BRIGHT + "交易结果验证测试脚本")
    
    # 创建交易结果验证器
    verifier = TradeVerifier()
    
    # 测试交易结果验证
    verifier.test_verify_trade_result()

if __name__ == "__main__":
    main() 