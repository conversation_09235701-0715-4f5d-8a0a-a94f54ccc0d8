"""
交易分析器

分析交易记录，计算交易统计数据和绩效指标
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.figure import Figure


class TradeAnalyzer:
    """
    交易分析器
    
    分析交易记录，计算交易统计数据和绩效指标
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化交易分析器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 交易记录DataFrame
        self.trades_df = None
        # 交易统计指标
        self.trade_stats = {}
        
        self.logger.info("交易分析器初始化完成")
    
    def load_trades(self, trades: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        加载交易记录
        
        Args:
            trades: 交易记录列表，每个交易为一个字典
            
        Returns:
            pd.DataFrame: 交易记录DataFrame
        """
        # 将交易记录转换为DataFrame
        self.trades_df = pd.DataFrame(trades)
        
        # 确保必要的列存在
        required_columns = ['code', 'direction', 'price', 'amount', 'timestamp']
        missing_columns = [col for col in required_columns if col not in self.trades_df.columns]
        
        if missing_columns:
            self.logger.warning(f"交易记录缺少必要的列: {', '.join(missing_columns)}")
            return pd.DataFrame()
        
        # 确保timestamp列是日期时间类型
        if 'timestamp' in self.trades_df.columns:
            if not pd.api.types.is_datetime64_any_dtype(self.trades_df['timestamp']):
                try:
                    self.trades_df['timestamp'] = pd.to_datetime(self.trades_df['timestamp'])
                except Exception as e:
                    self.logger.error(f"无法将timestamp列转换为日期时间类型: {str(e)}")
                    return pd.DataFrame()
        
        # 按时间排序
        self.trades_df = self.trades_df.sort_values('timestamp')
        
        # 计算交易金额
        if 'amount' in self.trades_df.columns and 'price' in self.trades_df.columns:
            self.trades_df['value'] = self.trades_df['price'] * self.trades_df['amount']
        
        self.logger.info(f"加载了 {len(self.trades_df)} 条交易记录")
        
        return self.trades_df
    
    def load_trades_from_file(self, file_path: str) -> pd.DataFrame:
        """
        从文件加载交易记录
        
        Args:
            file_path: 文件路径，支持CSV和Excel格式
            
        Returns:
            pd.DataFrame: 交易记录DataFrame
        """
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                self.logger.error(f"不支持的文件格式: {file_path}")
                return pd.DataFrame()
                
            return self.load_trades(df.to_dict('records'))
            
        except Exception as e:
            self.logger.error(f"从文件加载交易记录时出错: {str(e)}")
            return pd.DataFrame()
    
    def calculate_trade_stats(self) -> Dict[str, Any]:
        """
        计算交易统计指标
        
        Returns:
            Dict[str, Any]: 交易统计指标字典
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            self.logger.warning("没有交易记录可供分析")
            return {}
        
        # 交易总数
        total_trades = len(self.trades_df)
        
        # 买入和卖出交易数量
        buy_trades = len(self.trades_df[self.trades_df['direction'] == 'buy'])
        sell_trades = len(self.trades_df[self.trades_df['direction'] == 'sell'])
        
        # 交易金额统计
        total_value = self.trades_df['value'].sum()
        avg_trade_value = self.trades_df['value'].mean()
        max_trade_value = self.trades_df['value'].max()
        min_trade_value = self.trades_df['value'].min()
        
        # 交易时间统计
        first_trade_time = self.trades_df['timestamp'].min()
        last_trade_time = self.trades_df['timestamp'].max()
        trading_period_days = (last_trade_time - first_trade_time).days + 1
        
        # 交易频率
        trades_per_day = total_trades / max(trading_period_days, 1)
        
        # 按股票代码统计交易次数
        trades_by_code = self.trades_df['code'].value_counts().to_dict()
        
        # 按日期统计交易次数
        trades_by_date = self.trades_df.groupby(self.trades_df['timestamp'].dt.date).size().to_dict()
        
        # 最活跃的交易日
        most_active_date = max(trades_by_date.items(), key=lambda x: x[1])[0]
        most_active_date_trades = trades_by_date[most_active_date]
        
        # 最活跃的股票
        most_active_code = max(trades_by_code.items(), key=lambda x: x[1])[0]
        most_active_code_trades = trades_by_code[most_active_code]
        
        # 汇总统计指标
        stats = {
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'total_value': total_value,
            'avg_trade_value': avg_trade_value,
            'max_trade_value': max_trade_value,
            'min_trade_value': min_trade_value,
            'first_trade_time': first_trade_time,
            'last_trade_time': last_trade_time,
            'trading_period_days': trading_period_days,
            'trades_per_day': trades_per_day,
            'trades_by_code': trades_by_code,
            'trades_by_date': {str(k): v for k, v in trades_by_date.items()},  # 转换日期为字符串以便序列化
            'most_active_date': most_active_date,
            'most_active_date_trades': most_active_date_trades,
            'most_active_code': most_active_code,
            'most_active_code_trades': most_active_code_trades
        }
        
        self.trade_stats = stats
        self.logger.info("计算交易统计指标完成")
        
        return stats
    
    def calculate_pnl(self, positions: Optional[Dict[str, Dict[str, float]]] = None) -> pd.DataFrame:
        """
        计算每笔交易的盈亏
        
        Args:
            positions: 当前持仓信息，格式：{code: {'amount': amount, 'cost': cost}}，可选
            
        Returns:
            pd.DataFrame: 包含盈亏信息的交易记录DataFrame
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            self.logger.warning("没有交易记录可供分析")
            return pd.DataFrame()
        
        # 创建交易记录的副本
        trades = self.trades_df.copy()
        
        # 初始化持仓字典，格式：{code: {'amount': amount, 'cost': cost}}
        positions_dict = positions or {}
        
        # 初始化盈亏列
        trades['pnl'] = 0.0
        trades['cumulative_pnl'] = 0.0
        trades['position_amount'] = 0
        trades['position_cost'] = 0.0
        
        # 按时间顺序遍历交易记录，计算每笔交易的盈亏
        cumulative_pnl = 0.0
        
        for idx, trade in trades.iterrows():
            code = trade['code']
            direction = trade['direction']
            price = trade['price']
            amount = trade['amount']
            
            # 更新持仓
            if code not in positions_dict:
                positions_dict[code] = {'amount': 0, 'cost': 0.0}
            
            position = positions_dict[code]
            
            # 记录交易前的持仓
            trades.at[idx, 'position_amount'] = position['amount']
            trades.at[idx, 'position_cost'] = position['cost']
            
            # 计算盈亏并更新持仓
            if direction == 'buy':
                # 买入，更新持仓成本
                if position['amount'] == 0:
                    # 新建仓位
                    position['amount'] = amount
                    position['cost'] = price
                else:
                    # 加仓，计算加权平均成本
                    total_cost = position['amount'] * position['cost'] + amount * price
                    position['amount'] += amount
                    position['cost'] = total_cost / position['amount']
                
                # 买入没有即时盈亏
                trade_pnl = 0.0
                
            elif direction == 'sell':
                # 卖出，计算盈亏
                if position['amount'] > 0 and position['cost'] > 0:
                    # 计算盈亏
                    trade_pnl = (price - position['cost']) * min(amount, position['amount'])
                    
                    # 更新持仓
                    position['amount'] -= amount
                    if position['amount'] <= 0:
                        # 清仓
                        position['amount'] = 0
                        position['cost'] = 0.0
                else:
                    # 无持仓或持仓成本为0，无法计算盈亏
                    trade_pnl = 0.0
            else:
                # 其他交易类型
                trade_pnl = 0.0
            
            # 记录交易盈亏
            trades.at[idx, 'pnl'] = trade_pnl
            
            # 更新累计盈亏
            cumulative_pnl += trade_pnl
            trades.at[idx, 'cumulative_pnl'] = cumulative_pnl
        
        self.logger.info("计算交易盈亏完成")
        
        return trades
    
    def plot_pnl_curve(self, trades_with_pnl: pd.DataFrame) -> Figure:
        """
        绘制盈亏曲线
        
        Args:
            trades_with_pnl: 包含盈亏信息的交易记录DataFrame
            
        Returns:
            Figure: Matplotlib图形对象
        """
        if 'cumulative_pnl' not in trades_with_pnl.columns or len(trades_with_pnl) == 0:
            self.logger.warning("交易记录中缺少累计盈亏信息")
            return plt.figure()
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制累计盈亏曲线
        ax.plot(trades_with_pnl['timestamp'], trades_with_pnl['cumulative_pnl'], 
                marker='o', linestyle='-', color='blue', markersize=4)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 设置标题和标签
        ax.set_title('Cumulative P&L', fontsize=14)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('P&L', fontsize=12)
        
        # 格式化日期轴
        fig.autofmt_xdate()
        
        # 添加零线
        ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)
        
        # 添加最终盈亏标注
        final_pnl = trades_with_pnl['cumulative_pnl'].iloc[-1]
        ax.annotate(f'Final P&L: {final_pnl:.2f}', 
                   xy=(trades_with_pnl['timestamp'].iloc[-1], final_pnl),
                   xytext=(10, 0),
                   textcoords='offset points',
                   ha='left',
                   va='center',
                   bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5))
        
        plt.tight_layout()
        
        return fig
    
    def plot_trade_distribution(self) -> Figure:
        """
        绘制交易分布图
        
        Returns:
            Figure: Matplotlib图形对象
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            self.logger.warning("没有交易记录可供分析")
            return plt.figure()
        
        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        
        # 1. 按股票代码统计交易次数
        trades_by_code = self.trades_df['code'].value_counts().sort_values(ascending=False)
        top_n = min(10, len(trades_by_code))
        trades_by_code[:top_n].plot(kind='bar', ax=axes[0, 0], color='skyblue')
        axes[0, 0].set_title('Top Traded Stocks', fontsize=12)
        axes[0, 0].set_xlabel('Stock Code', fontsize=10)
        axes[0, 0].set_ylabel('Number of Trades', fontsize=10)
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 按日期统计交易次数
        trades_by_date = self.trades_df.groupby(self.trades_df['timestamp'].dt.date).size()
        trades_by_date.plot(kind='line', marker='o', ax=axes[0, 1], color='green')
        axes[0, 1].set_title('Trades by Date', fontsize=12)
        axes[0, 1].set_xlabel('Date', fontsize=10)
        axes[0, 1].set_ylabel('Number of Trades', fontsize=10)
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 买入和卖出交易比例
        direction_counts = self.trades_df['direction'].value_counts()
        direction_counts.plot(kind='pie', ax=axes[1, 0], autopct='%1.1f%%', colors=['lightcoral', 'lightblue'])
        axes[1, 0].set_title('Buy vs Sell Trades', fontsize=12)
        axes[1, 0].set_ylabel('')
        
        # 4. 交易金额分布
        if 'value' in self.trades_df.columns:
            self.trades_df['value'].hist(bins=20, ax=axes[1, 1], color='orange')
            axes[1, 1].set_title('Trade Value Distribution', fontsize=12)
            axes[1, 1].set_xlabel('Trade Value', fontsize=10)
            axes[1, 1].set_ylabel('Frequency', fontsize=10)
        
        plt.tight_layout()
        
        return fig
    
    def plot_trade_timing(self) -> Figure:
        """
        绘制交易时机分析图
        
        Returns:
            Figure: Matplotlib图形对象
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            self.logger.warning("没有交易记录可供分析")
            return plt.figure()
        
        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(14, 10))
        
        # 1. 按小时统计交易次数
        trades_by_hour = self.trades_df.groupby(self.trades_df['timestamp'].dt.hour).size()
        trades_by_hour.plot(kind='bar', ax=axes[0, 0], color='purple')
        axes[0, 0].set_title('Trades by Hour of Day', fontsize=12)
        axes[0, 0].set_xlabel('Hour', fontsize=10)
        axes[0, 0].set_ylabel('Number of Trades', fontsize=10)
        axes[0, 0].set_xticks(range(24))
        axes[0, 0].set_xticklabels(range(24))
        
        # 2. 按星期几统计交易次数
        trades_by_weekday = self.trades_df.groupby(self.trades_df['timestamp'].dt.dayofweek).size()
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        trades_by_weekday.index = [weekday_names[i] for i in trades_by_weekday.index]
        trades_by_weekday.plot(kind='bar', ax=axes[0, 1], color='teal')
        axes[0, 1].set_title('Trades by Day of Week', fontsize=12)
        axes[0, 1].set_xlabel('Day of Week', fontsize=10)
        axes[0, 1].set_ylabel('Number of Trades', fontsize=10)
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 按月份统计交易次数
        trades_by_month = self.trades_df.groupby(self.trades_df['timestamp'].dt.month).size()
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        trades_by_month.index = [month_names[i-1] for i in trades_by_month.index]
        trades_by_month.plot(kind='bar', ax=axes[1, 0], color='brown')
        axes[1, 0].set_title('Trades by Month', fontsize=12)
        axes[1, 0].set_xlabel('Month', fontsize=10)
        axes[1, 0].set_ylabel('Number of Trades', fontsize=10)
        
        # 4. 交易间隔分布
        if len(self.trades_df) > 1:
            # 计算相邻交易之间的时间间隔（分钟）
            sorted_trades = self.trades_df.sort_values('timestamp')
            time_diffs = sorted_trades['timestamp'].diff().dt.total_seconds() / 60
            time_diffs = time_diffs[time_diffs > 0]  # 排除第一个交易的NaN值
            
            if len(time_diffs) > 0:
                # 绘制交易间隔直方图
                time_diffs.hist(bins=20, ax=axes[1, 1], color='darkgreen')
                axes[1, 1].set_title('Trade Interval Distribution', fontsize=12)
                axes[1, 1].set_xlabel('Interval (minutes)', fontsize=10)
                axes[1, 1].set_ylabel('Frequency', fontsize=10)
                
                # 添加平均间隔标注
                mean_interval = time_diffs.mean()
                axes[1, 1].axvline(mean_interval, color='red', linestyle='--')
                axes[1, 1].text(mean_interval * 1.1, axes[1, 1].get_ylim()[1] * 0.9, 
                               f'Mean: {mean_interval:.1f} min', color='red')
        
        plt.tight_layout()
        
        return fig
    
    def generate_trade_report(self, positions: Optional[Dict[str, Dict[str, float]]] = None) -> Dict[str, Any]:
        """
        生成完整的交易报告
        
        Args:
            positions: 当前持仓信息，格式：{code: {'amount': amount, 'cost': cost}}，可选
            
        Returns:
            Dict[str, Any]: 交易报告字典，包含统计指标和图表
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            self.logger.warning("没有交易记录可供分析")
            return {}
        
        # 计算交易统计指标
        stats = self.calculate_trade_stats()
        
        # 计算交易盈亏
        trades_with_pnl = self.calculate_pnl(positions)
        
        # 生成图表
        pnl_fig = self.plot_pnl_curve(trades_with_pnl)
        distribution_fig = self.plot_trade_distribution()
        timing_fig = self.plot_trade_timing()
        
        # 生成报告
        report = {
            'stats': stats,
            'trades_with_pnl': trades_with_pnl,
            'pnl_curve': pnl_fig,
            'trade_distribution': distribution_fig,
            'trade_timing': timing_fig
        }
        
        self.logger.info("生成交易报告完成")
        
        return report
    
    def save_trade_report(self, report: Dict[str, Any], output_dir: str):
        """
        保存交易报告到文件
        
        Args:
            report: 交易报告字典
            output_dir: 输出目录
        """
        import os
        import json
        from matplotlib.backends.backend_pdf import PdfPages
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存统计指标到JSON文件
        stats_file = os.path.join(output_dir, 'trade_stats.json')
        with open(stats_file, 'w') as f:
            # 将日期转换为字符串
            stats = report['stats'].copy()
            for key, value in stats.items():
                if isinstance(value, (datetime, pd.Timestamp)):
                    stats[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            
            json.dump(stats, f, indent=4)
        
        # 保存交易记录（带盈亏）到CSV文件
        if 'trades_with_pnl' in report and not report['trades_with_pnl'].empty:
            trades_file = os.path.join(output_dir, 'trades_with_pnl.csv')
            report['trades_with_pnl'].to_csv(trades_file, index=False)
        
        # 保存图表到PDF文件
        figures_file = os.path.join(output_dir, 'trade_figures.pdf')
        with PdfPages(figures_file) as pdf:
            if 'pnl_curve' in report:
                pdf.savefig(report['pnl_curve'])
            if 'trade_distribution' in report:
                pdf.savefig(report['trade_distribution'])
            if 'trade_timing' in report:
                pdf.savefig(report['trade_timing'])
        
        # 单独保存每个图表为PNG文件
        if 'pnl_curve' in report:
            report['pnl_curve'].savefig(os.path.join(output_dir, 'pnl_curve.png'))
        if 'trade_distribution' in report:
            report['trade_distribution'].savefig(os.path.join(output_dir, 'trade_distribution.png'))
        if 'trade_timing' in report:
            report['trade_timing'].savefig(os.path.join(output_dir, 'trade_timing.png'))
        
        self.logger.info(f"交易报告已保存到目录: {output_dir}")
        
        # 关闭图表以释放内存
        for fig_key in ['pnl_curve', 'trade_distribution', 'trade_timing']:
            if fig_key in report:
                plt.close(report[fig_key])