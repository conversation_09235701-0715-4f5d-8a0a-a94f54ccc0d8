"""
交易策略基类

提供交易策略的基础架构，支持自定义交易策略
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
import pandas as pd
import numpy as np
import logging

class BaseStrategy(ABC):
    """
    交易策略基类
    
    所有自定义策略都应该继承自这个类并实现calculate_signal方法
    """
    
    def __init__(self, params: Dict[str, Any] = None, logger: logging.Logger = None):
        """
        初始化策略
        
        Args:
            params: 策略参数字典
            logger: 日志记录器
        """
        self.params = params or {}
        self.logger = logger or logging.getLogger(__name__)
        self.name = self.__class__.__name__
        # 初始化历史数据
        self.data_history = {}
        # 初始化信号历史
        self.signal_history = []
        # 当前持仓状态: 1(多头), -1(空头), 0(无持仓)
        self.current_position = 0
        
        # 初始化其他成员变量
        self._initialize()
        
        self.logger.info(f"策略 {self.name} 初始化完成")
    
    def _initialize(self):
        """
        初始化其他成员变量
        
        子类可以重写此方法以进行自定义初始化
        """
        pass
    
    @abstractmethod
    def calculate_signal(self, market_data: Dict[str, Any]) -> int:
        """
        计算交易信号
        
        Args:
            market_data: 市场数据字典，包含各种价格和指标
            
        Returns:
            int: 交易信号 1(买入), -1(卖出), 0(不操作)
        """
        raise NotImplementedError("子类必须实现calculate_signal方法")
    
    def update_data(self, market_data: Dict[str, Any]) -> None:
        """
        更新市场数据
        
        Args:
            market_data: 新的市场数据
        """
        # 保存原始数据
        for key, value in market_data.items():
            if key not in self.data_history:
                self.data_history[key] = []
            self.data_history[key].append(value)
        
        # 计算并保存指标
        self._calculate_indicators()
        
    def _calculate_indicators(self) -> None:
        """
        计算技术指标
        
        子类可以重写此方法以计算特定指标
        """
        pass
    
    def get_signal(self, market_data: Dict[str, Any]) -> int:
        """
        获取交易信号
        
        Args:
            market_data: 市场数据
            
        Returns:
            int: 交易信号 1(买入), -1(卖出), 0(不操作)
        """
        # 更新市场数据
        self.update_data(market_data)
        
        # 计算信号
        signal = self.calculate_signal(market_data)
        self.signal_history.append(signal)
        
        # 更新持仓状态
        if signal != 0:
            self.current_position = signal
        
        return signal
    
    def get_data_as_dataframe(self, columns: List[str] = None) -> pd.DataFrame:
        """
        将历史数据转换为DataFrame
        
        Args:
            columns: 需要包含的列名列表
            
        Returns:
            pd.DataFrame: 历史数据DataFrame
        """
        # 如果没有指定列名，使用所有列
        if columns is None:
            columns = list(self.data_history.keys())
            
        # 创建DataFrame
        df = pd.DataFrame()
        for col in columns:
            if col in self.data_history:
                df[col] = self.data_history[col]
        
        return df
    
    def add_indicator(self, name: str, values: List[Any]) -> None:
        """
        添加自定义指标到历史数据
        
        Args:
            name: 指标名称
            values: 指标值列表
        """
        self.data_history[name] = values
    
    def get_indicator_value(self, name: str, index: int = -1) -> Optional[Any]:
        """
        获取指定指标的值
        
        Args:
            name: 指标名称
            index: 索引，默认为-1（最新值）
            
        Returns:
            Any: 指标值，如果不存在则返回None
        """
        if name in self.data_history and len(self.data_history[name]) > 0:
            try:
                return self.data_history[name][index]
            except IndexError:
                return None
        return None
    
    def reset(self) -> None:
        """
        重置策略状态
        """
        self.data_history = {}
        self.signal_history = []
        self.current_position = 0
        self._initialize()
        self.logger.info(f"策略 {self.name} 已重置")
    
    def __str__(self) -> str:
        """
        返回策略的字符串表示
        
        Returns:
            str: 策略信息
        """
        return f"{self.name} - 当前持仓: {self.current_position}" 