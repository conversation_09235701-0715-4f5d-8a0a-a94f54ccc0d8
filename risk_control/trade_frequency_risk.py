"""
交易频率风险控制模块

提供交易频率风险控制功能，包括日交易次数限制、交易间隔控制和高频交易检测
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import os
from collections import deque, defaultdict

class TradeFrequencyController:
    """
    交易频率风险控制器
    
    控制交易频率相关的风险，防止过度交易和高频交易
    """
    
    def __init__(self, config: Dict[str, Any] = None, logger: Optional[logging.Logger] = None):
        """
        初始化交易频率风险控制器
        
        Args:
            config: 配置字典，包含风险控制参数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "max_daily_trades": 50,          # 每日最大交易次数
            "min_trade_interval": 30,        # 最小交易间隔 (秒)
            "high_freq_window": 300,         # 高频交易检测窗口 (秒)
            "high_freq_threshold": 10,       # 高频交易阈值 (次/窗口)
            "enable_daily_limit": True,      # 是否启用日交易次数限制
            "enable_interval_control": True, # 是否启用交易间隔控制
            "enable_high_freq_control": True # 是否启用高频交易检测
        }
        
        # 合并配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化风险控制状态
        self.daily_trades = 0                # 当日交易次数
        self.last_trade_time = {}            # 各股票最后交易时间 {code: datetime}
        self.high_freq_queue = {}            # 各股票交易时间队列 {code: deque}
        self.trade_records = []              # 交易记录
        self.last_reset_date = datetime.now().date()  # 最后重置日期
        
        # 股票分组交易计数
        self.stock_group_trades = defaultdict(int)  # 股票分组交易计数 {group: count}
        
        self.logger.info(f"交易频率风险控制器初始化完成, 配置: {json.dumps(self.config, ensure_ascii=False)}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        self.config.update(new_config)
        self.logger.info(f"交易频率风险控制器配置已更新: {json.dumps(new_config, ensure_ascii=False)}")
    
    def reset_daily_counters(self):
        """
        重置每日计数器
        """
        self.daily_trades = 0
        self.stock_group_trades = defaultdict(int)
        self.last_reset_date = datetime.now().date()
        self.logger.info("每日交易频率计数器已重置")
    
    def check_daily_reset(self):
        """
        检查是否需要重置每日计数器
        """
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.reset_daily_counters()
            self.trade_records = []
    
    def get_stock_group(self, code: str) -> str:
        """
        获取股票所属分组
        
        Args:
            code: 股票代码
            
        Returns:
            str: 股票分组名称
        """
        # 根据股票代码前缀判断分组
        if code.startswith('60'):
            return 'sh_main'     # 上海主板
        elif code.startswith('00'):
            return 'sz_main'     # 深圳主板
        elif code.startswith('30'):
            return 'gem'         # 创业板
        elif code.startswith('68'):
            return 'star'        # 科创板
        elif code.startswith('159') or code.startswith('510') or code.startswith('512') or code.startswith('513'):
            return 'etf'         # ETF
        else:
            return 'other'       # 其他
    
    def record_trade(self, trade_info: Dict[str, Any]):
        """
        记录交易
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
        """
        # 检查是否需要重置每日计数器
        self.check_daily_reset()
        
        # 提取交易信息
        code = trade_info.get('code', '')
        
        # 获取当前时间
        now = datetime.now()
        
        # 更新交易记录
        self.trade_records.append({
            "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
            "trade_info": trade_info
        })
        
        # 更新交易次数
        self.daily_trades += 1
        
        # 更新股票分组交易次数
        group = self.get_stock_group(code)
        self.stock_group_trades[group] += 1
        
        # 更新最后交易时间
        self.last_trade_time[code] = now
        
        # 更新高频交易队列
        if code not in self.high_freq_queue:
            self.high_freq_queue[code] = deque()
        
        self.high_freq_queue[code].append(now)
        
        # 清理过期的高频交易记录
        window_start = now - timedelta(seconds=self.config["high_freq_window"])
        while self.high_freq_queue[code] and self.high_freq_queue[code][0] < window_start:
            self.high_freq_queue[code].popleft()
        
        self.logger.info(f"已记录交易: {code}, 当日交易次数: {self.daily_trades}, 股票组 {group} 交易次数: {self.stock_group_trades[group]}")
    
    def check_risk(self, trade_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        检查交易频率风险
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
            
        Returns:
            Tuple[bool, Dict]: (是否通过风险检查, 风险详情)
        """
        # 检查是否需要重置每日计数器
        self.check_daily_reset()
        
        # 提取交易信息
        code = trade_info.get('code', '')
        
        # 获取当前时间
        now = datetime.now()
        
        # 初始化风险检查结果
        risk_details = {
            "max_daily_trades": {
                "passed": True,
                "limit": self.config["max_daily_trades"],
                "actual": self.daily_trades,
                "message": ""
            },
            "min_trade_interval": {
                "passed": True,
                "limit": self.config["min_trade_interval"],
                "actual": 0 if code not in self.last_trade_time else int((now - self.last_trade_time[code]).total_seconds()),
                "message": ""
            },
            "high_frequency": {
                "passed": True,
                "limit": self.config["high_freq_threshold"],
                "actual": 0 if code not in self.high_freq_queue else len(self.high_freq_queue[code]),
                "message": ""
            }
        }
        
        # 1. 检查日交易次数限制
        if self.config["enable_daily_limit"] and self.daily_trades >= self.config["max_daily_trades"]:
            risk_details["max_daily_trades"]["passed"] = False
            risk_details["max_daily_trades"]["message"] = f"当日交易次数 {self.daily_trades} 已达到上限 {self.config['max_daily_trades']}"
            self.logger.warning(risk_details["max_daily_trades"]["message"])
        
        # 2. 检查交易间隔控制
        if self.config["enable_interval_control"] and code in self.last_trade_time:
            seconds_since_last_trade = int((now - self.last_trade_time[code]).total_seconds())
            if seconds_since_last_trade < self.config["min_trade_interval"]:
                risk_details["min_trade_interval"]["passed"] = False
                risk_details["min_trade_interval"]["message"] = (
                    f"股票 {code} 距离上次交易仅 {seconds_since_last_trade} 秒, "
                    f"小于最小间隔 {self.config['min_trade_interval']} 秒"
                )
                self.logger.warning(risk_details["min_trade_interval"]["message"])
        
        # 3. 检查高频交易
        if self.config["enable_high_freq_control"] and code in self.high_freq_queue:
            # 清理过期的高频交易记录
            window_start = now - timedelta(seconds=self.config["high_freq_window"])
            while self.high_freq_queue[code] and self.high_freq_queue[code][0] < window_start:
                self.high_freq_queue[code].popleft()
            
            # 检查高频交易
            current_freq = len(self.high_freq_queue[code])
            if current_freq >= self.config["high_freq_threshold"]:
                risk_details["high_frequency"]["passed"] = False
                risk_details["high_frequency"]["actual"] = current_freq
                risk_details["high_frequency"]["message"] = (
                    f"股票 {code} 在过去 {self.config['high_freq_window']} 秒内交易 {current_freq} 次, "
                    f"超过阈值 {self.config['high_freq_threshold']} 次"
                )
                self.logger.warning(risk_details["high_frequency"]["message"])
        
        # 综合所有风险检查结果
        all_passed = all(detail["passed"] for detail in risk_details.values())
        
        if all_passed:
            self.logger.info(f"交易频率风险检查通过: {code}")
        else:
            self.logger.warning(f"交易频率风险检查未通过: {code}")
        
        return all_passed, risk_details
    
    def get_daily_trades(self) -> int:
        """
        获取当日交易次数
        
        Returns:
            int: 当日交易次数
        """
        # 检查是否需要重置每日计数器
        self.check_daily_reset()
        return self.daily_trades
    
    def get_group_trades(self, group: str = None) -> Dict[str, int]:
        """
        获取股票分组交易次数
        
        Args:
            group: 股票分组名称，如果为None则返回所有分组
            
        Returns:
            Dict[str, int]: 股票分组交易次数字典
        """
        # 检查是否需要重置每日计数器
        self.check_daily_reset()
        
        if group:
            return {group: self.stock_group_trades.get(group, 0)}
        return dict(self.stock_group_trades)
    
    def save_trade_records(self, file_path: str):
        """
        保存交易记录到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.trade_records, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"交易记录已保存到 {file_path}")
        except Exception as e:
            self.logger.error(f"保存交易记录失败: {e}")
    
    def get_high_frequency_stats(self, code: str = None) -> Dict[str, Any]:
        """
        获取高频交易统计信息
        
        Args:
            code: 股票代码，如果为None则返回所有股票
            
        Returns:
            Dict: 高频交易统计信息
        """
        now = datetime.now()
        window_start = now - timedelta(seconds=self.config["high_freq_window"])
        
        if code:
            # 清理过期的高频交易记录
            if code in self.high_freq_queue:
                while self.high_freq_queue[code] and self.high_freq_queue[code][0] < window_start:
                    self.high_freq_queue[code].popleft()
                return {
                    "code": code,
                    "window": self.config["high_freq_window"],
                    "trades": len(self.high_freq_queue.get(code, deque())),
                    "threshold": self.config["high_freq_threshold"]
                }
        else:
            # 返回所有股票的高频交易统计
            stats = {}
            for stock_code, queue in self.high_freq_queue.items():
                # 清理过期的高频交易记录
                while queue and queue[0] < window_start:
                    queue.popleft()
                stats[stock_code] = {
                    "window": self.config["high_freq_window"],
                    "trades": len(queue),
                    "threshold": self.config["high_freq_threshold"]
                }
            return stats 