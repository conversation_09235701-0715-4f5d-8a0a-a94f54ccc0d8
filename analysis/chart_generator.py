"""
图表生成器

生成各种交易相关图表，包括K线图、技术指标图等
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.gridspec import GridSpec
import mplfinance as mpf


class ChartGenerator:
    """
    图表生成器
    
    生成各种交易相关图表，包括K线图、技术指标图等
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化图表生成器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 图表样式
        self.style = {
            'figsize': (12, 8),
            'dpi': 100,
            'up_color': 'red',
            'down_color': 'green',
            'edge_color': 'black',
            'wick_color': 'black',
            'volume_up_color': 'red',
            'volume_down_color': 'green',
            'grid_color': '#b0b0b0',
            'grid_alpha': 0.3,
            'title_fontsize': 14,
            'axis_fontsize': 12,
            'label_fontsize': 10
        }
        
        self.logger.info("图表生成器初始化完成")
    
    def set_style(self, **kwargs):
        """
        设置图表样式
        
        Args:
            **kwargs: 样式参数
        """
        self.style.update(kwargs)
        self.logger.info("更新图表样式")
    
    def plot_candlestick(self, data: pd.DataFrame, title: str = 'Candlestick Chart') -> Figure:
        """
        绘制K线图
        
        Args:
            data: 价格数据，包含'open', 'high', 'low', 'close'列，索引为日期
            title: 图表标题
            
        Returns:
            Figure: Matplotlib图形对象
        """
        # 确保数据格式正确
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {', '.join(missing_columns)}")
            return plt.figure()
        
        # 创建图形
        fig = plt.figure(figsize=self.style['figsize'], dpi=self.style['dpi'])
        gs = GridSpec(2, 1, height_ratios=[3, 1])
        
        # 创建K线子图
        ax1 = fig.add_subplot(gs[0])
        
        # 绘制K线
        for i in range(len(data)):
            date = mdates.date2num(data.index[i])
            open_price = data['open'].iloc[i]
            close_price = data['close'].iloc[i]
            high_price = data['high'].iloc[i]
            low_price = data['low'].iloc[i]
            
            # 决定颜色
            if close_price >= open_price:
                color = self.style['up_color']
                body_height = close_price - open_price
                body_bottom = open_price
            else:
                color = self.style['down_color']
                body_height = open_price - close_price
                body_bottom = close_price
            
            # 绘制实体
            ax1.add_patch(plt.Rectangle((date - 0.4, body_bottom), 0.8, body_height, 
                                      fill=True, color=color, 
                                      edgecolor=self.style['edge_color'], linewidth=0.5))
            
            # 绘制上下影线
            ax1.plot([date, date], [body_bottom + body_height, high_price], 
                    color=self.style['wick_color'], linewidth=0.5)
            ax1.plot([date, date], [body_bottom, low_price], 
                    color=self.style['wick_color'], linewidth=0.5)
        
        # 设置x轴为日期格式
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # 添加网格线
        ax1.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
        
        # 设置标题和标签
        ax1.set_title(title, fontsize=self.style['title_fontsize'])
        ax1.set_ylabel('Price', fontsize=self.style['axis_fontsize'])
        
        # 创建成交量子图
        if 'volume' in data.columns:
            ax2 = fig.add_subplot(gs[1], sharex=ax1)
            
            # 绘制成交量柱状图
            for i in range(len(data)):
                date = mdates.date2num(data.index[i])
                open_price = data['open'].iloc[i]
                close_price = data['close'].iloc[i]
                volume = data['volume'].iloc[i]
                
                # 决定颜色
                if close_price >= open_price:
                    color = self.style['volume_up_color']
                else:
                    color = self.style['volume_down_color']
                
                # 绘制成交量柱
                ax2.add_patch(plt.Rectangle((date - 0.4, 0), 0.8, volume, 
                                          fill=True, color=color, 
                                          edgecolor=self.style['edge_color'], linewidth=0.5))
            
            # 添加网格线
            ax2.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
            
            # 设置标签
            ax2.set_ylabel('Volume', fontsize=self.style['axis_fontsize'])
            ax2.set_xlabel('Date', fontsize=self.style['axis_fontsize'])
        
        # 调整布局
        plt.tight_layout()
        fig.autofmt_xdate()
        
        return fig
    
    def plot_mplfinance(self, data: pd.DataFrame, 
                       title: str = 'Price Chart',
                       volume: bool = True,
                       mav: Optional[List[int]] = None,
                       additional_plots: Optional[List[Dict[str, Any]]] = None) -> Figure:
        """
        使用mplfinance库绘制更专业的K线图
        
        Args:
            data: 价格数据，包含'open', 'high', 'low', 'close'列，索引为日期
            title: 图表标题
            volume: 是否显示成交量
            mav: 移动平均线周期列表，如[5, 10, 20]
            additional_plots: 额外的图表，格式为[{'data': series, 'panel': 0, 'type': 'line', 'color': 'blue', 'width': 1}]
            
        Returns:
            Figure: Matplotlib图形对象
        """
        # 确保数据格式正确
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {', '.join(missing_columns)}")
            return plt.figure()
        
        # 转换列名为小写
        data_mpf = data.copy()
        data_mpf.columns = [col.lower() for col in data_mpf.columns]
        
        # 自定义样式
        mc = mpf.make_marketcolors(
            up=self.style['up_color'],
            down=self.style['down_color'],
            edge=self.style['edge_color'],
            wick=self.style['wick_color'],
            volume=self.style['volume_up_color']
        )
        
        s = mpf.make_mpf_style(
            marketcolors=mc,
            gridstyle='--',
            gridcolor=self.style['grid_color'],
            gridaxis='both',
            y_on_right=False,
            figsize=self.style['figsize']
        )
        
        # 准备额外的绘图参数
        kwargs = {
            'type': 'candle',
            'title': title,
            'volume': volume,
            'style': s,
            'returnfig': True,
            'figsize': self.style['figsize'],
            'panel_ratios': (3, 1) if volume else (1, 0),
            'tight_layout': True
        }
        
        # 添加移动平均线
        if mav:
            kwargs['mav'] = mav
        
        # 添加额外的图表
        if additional_plots:
            apds = []
            for plot in additional_plots:
                if 'data' in plot and 'type' in plot:
                    if plot['type'] == 'line':
                        apds.append(mpf.make_addplot(
                            plot['data'], 
                            panel=plot.get('panel', 0), 
                            color=plot.get('color', 'blue'),
                            width=plot.get('width', 1)
                        ))
                    elif plot['type'] == 'scatter':
                        apds.append(mpf.make_addplot(
                            plot['data'], 
                            panel=plot.get('panel', 0), 
                            color=plot.get('color', 'blue'),
                            marker=plot.get('marker', 'o'),
                            markersize=plot.get('markersize', 50)
                        ))
            
            if apds:
                kwargs['addplot'] = apds
        
        # 绘制图表
        fig, axes = mpf.plot(data_mpf, **kwargs)
        
        return fig
    
    def plot_technical_indicators(self, data: pd.DataFrame, 
                                title: str = 'Technical Indicators',
                                indicators: Dict[str, pd.Series] = None) -> Figure:
        """
        绘制技术指标图
        
        Args:
            data: 价格数据，包含'close'列，索引为日期
            title: 图表标题
            indicators: 指标字典，格式为{'指标名称': 指标Series}
            
        Returns:
            Figure: Matplotlib图形对象
        """
        if 'close' not in data.columns:
            self.logger.error("数据缺少'close'列")
            return plt.figure()
        
        if not indicators or not isinstance(indicators, dict):
            self.logger.warning("未提供有效的指标数据")
            indicators = {}
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.style['figsize'], dpi=self.style['dpi'])
        
        # 绘制价格线
        ax.plot(data.index, data['close'], label='Close Price', color='black', linewidth=1.5)
        
        # 绘制各种指标
        colors = plt.cm.tab10(np.linspace(0, 1, len(indicators)))
        
        for i, (name, indicator) in enumerate(indicators.items()):
            if isinstance(indicator, pd.Series):
                ax.plot(indicator.index, indicator, label=name, color=colors[i], linewidth=1)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
        
        # 设置x轴为日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # 设置标题和标签
        ax.set_title(title, fontsize=self.style['title_fontsize'])
        ax.set_xlabel('Date', fontsize=self.style['axis_fontsize'])
        ax.set_ylabel('Value', fontsize=self.style['axis_fontsize'])
        
        # 添加图例
        ax.legend()
        
        # 调整布局
        plt.tight_layout()
        fig.autofmt_xdate()
        
        return fig
    
    def plot_multi_indicators(self, data: pd.DataFrame, 
                            title: str = 'Technical Analysis',
                            indicators: Dict[str, Dict[str, Union[pd.Series, List[pd.Series]]]] = None) -> Figure:
        """
        绘制多面板技术指标图
        
        Args:
            data: 价格数据，包含'open', 'high', 'low', 'close'列，索引为日期
            title: 图表标题
            indicators: 指标字典，格式为{'面板名称': {'指标名称': 指标Series或Series列表}}
            
        Returns:
            Figure: Matplotlib图形对象
        """
        # 确保数据格式正确
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {', '.join(missing_columns)}")
            return plt.figure()
        
        if not indicators or not isinstance(indicators, dict):
            self.logger.warning("未提供有效的指标数据")
            indicators = {}
        
        # 计算需要的面板数量
        n_panels = len(indicators) + 1  # 价格面板 + 指标面板
        
        # 创建图形
        fig = plt.figure(figsize=self.style['figsize'], dpi=self.style['dpi'])
        gs = GridSpec(n_panels, 1, height_ratios=[3] + [1] * (n_panels - 1))
        
        # 创建价格面板
        ax_price = fig.add_subplot(gs[0])
        
        # 绘制K线
        for i in range(len(data)):
            date = mdates.date2num(data.index[i])
            open_price = data['open'].iloc[i]
            close_price = data['close'].iloc[i]
            high_price = data['high'].iloc[i]
            low_price = data['low'].iloc[i]
            
            # 决定颜色
            if close_price >= open_price:
                color = self.style['up_color']
                body_height = close_price - open_price
                body_bottom = open_price
            else:
                color = self.style['down_color']
                body_height = open_price - close_price
                body_bottom = close_price
            
            # 绘制实体
            ax_price.add_patch(plt.Rectangle((date - 0.4, body_bottom), 0.8, body_height, 
                                          fill=True, color=color, 
                                          edgecolor=self.style['edge_color'], linewidth=0.5))
            
            # 绘制上下影线
            ax_price.plot([date, date], [body_bottom + body_height, high_price], 
                        color=self.style['wick_color'], linewidth=0.5)
            ax_price.plot([date, date], [body_bottom, low_price], 
                        color=self.style['wick_color'], linewidth=0.5)
        
        # 设置价格面板的标题和标签
        ax_price.set_title(title, fontsize=self.style['title_fontsize'])
        ax_price.set_ylabel('Price', fontsize=self.style['axis_fontsize'])
        ax_price.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
        
        # 绘制各个指标面板
        axes = [ax_price]
        for i, (panel_name, panel_indicators) in enumerate(indicators.items(), 1):
            # 创建指标面板
            ax = fig.add_subplot(gs[i], sharex=ax_price)
            
            # 绘制该面板的所有指标
            for indicator_name, indicator_data in panel_indicators.items():
                if isinstance(indicator_data, pd.Series):
                    # 单个指标
                    ax.plot(indicator_data.index, indicator_data, label=indicator_name)
                elif isinstance(indicator_data, list):
                    # 多个指标（如多条均线）
                    colors = plt.cm.tab10(np.linspace(0, 1, len(indicator_data)))
                    for j, ind in enumerate(indicator_data):
                        if isinstance(ind, pd.Series):
                            ax.plot(ind.index, ind, label=f"{indicator_name}_{j+1}", color=colors[j])
            
            # 设置面板标题和标签
            ax.set_ylabel(panel_name, fontsize=self.style['axis_fontsize'])
            ax.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
            ax.legend(loc='upper left', fontsize=self.style['label_fontsize'])
            
            axes.append(ax)
        
        # 设置最后一个面板的x轴标签
        axes[-1].set_xlabel('Date', fontsize=self.style['axis_fontsize'])
        
        # 设置x轴为日期格式
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # 调整布局
        plt.tight_layout()
        fig.autofmt_xdate()
        
        return fig
    
    def plot_trade_signals(self, data: pd.DataFrame, 
                          buy_signals: pd.Series = None, 
                          sell_signals: pd.Series = None,
                          title: str = 'Trade Signals') -> Figure:
        """
        绘制带有交易信号的价格图
        
        Args:
            data: 价格数据，包含'close'列，索引为日期
            buy_signals: 买入信号Series，值为布尔型，True表示有信号
            sell_signals: 卖出信号Series，值为布尔型，True表示有信号
            title: 图表标题
            
        Returns:
            Figure: Matplotlib图形对象
        """
        if 'close' not in data.columns:
            self.logger.error("数据缺少'close'列")
            return plt.figure()
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.style['figsize'], dpi=self.style['dpi'])
        
        # 绘制价格线
        ax.plot(data.index, data['close'], label='Close Price', color='black', linewidth=1.5)
        
        # 绘制买入信号
        if buy_signals is not None and isinstance(buy_signals, pd.Series):
            buy_points = data.loc[buy_signals].index
            buy_prices = data.loc[buy_signals, 'close']
            ax.scatter(buy_points, buy_prices, marker='^', color='red', s=100, label='Buy Signal')
        
        # 绘制卖出信号
        if sell_signals is not None and isinstance(sell_signals, pd.Series):
            sell_points = data.loc[sell_signals].index
            sell_prices = data.loc[sell_signals, 'close']
            ax.scatter(sell_points, sell_prices, marker='v', color='green', s=100, label='Sell Signal')
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=self.style['grid_alpha'], color=self.style['grid_color'])
        
        # 设置x轴为日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.AutoDateLocator())
        
        # 设置标题和标签
        ax.set_title(title, fontsize=self.style['title_fontsize'])
        ax.set_xlabel('Date', fontsize=self.style['axis_fontsize'])
        ax.set_ylabel('Price', fontsize=self.style['axis_fontsize'])
        
        # 添加图例
        ax.legend()
        
        # 调整布局
        plt.tight_layout()
        fig.autofmt_xdate()
        
        return fig
    
    def save_chart(self, fig: Figure, filename: str, dpi: int = None):
        """
        保存图表到文件
        
        Args:
            fig: Matplotlib图形对象
            filename: 文件名
            dpi: 分辨率
        """
        if dpi is None:
            dpi = self.style['dpi']
            
        try:
            fig.savefig(filename, dpi=dpi, bbox_inches='tight')
            self.logger.info(f"图表已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存图表时出错: {str(e)}")
    
    def close_chart(self, fig: Figure):
        """
        关闭图表
        
        Args:
            fig: Matplotlib图形对象
        """
        plt.close(fig) 