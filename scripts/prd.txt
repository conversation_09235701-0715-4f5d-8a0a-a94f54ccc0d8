# 股票量化交易系统新功能需求

## 1. 交易功能增强

### 1.1 全仓交易单支持
**描述:** 在现有买入和卖出功能基础上，增加对全仓交易的支持。
**要求:**
- 允许用户指定"全仓买入"或"全仓卖出"，系统自动计算最大可买/卖数量。
- 买入时，基于可用资金计算最大可买数量。
- 卖出时，基于指定股票的持仓数量计算最大可卖数量。

### 1.2 市价单支持
**描述:** 引入市价单（Market Order）类型，允许用户以市场最优价格立即成交。
**要求:**
- 在买入和卖出接口中增加一个参数，用于选择订单类型（如：限价单/市价单）。
- 市价单无需指定价格，系统自动获取并使用当前市场最优价格。
- 确保市价单的快速执行，并处理可能的滑点。

### 1.3 即成即撤（IOC）功能
**描述:** 为交易订单增加即成即撤（Immediate-or-Cancel）功能。
**要求:**
- 在买入和卖出接口中增加一个参数，用于启用IOC功能。
- 对于IOC订单，未能立即成交的部分将自动撤销，不保留在委托列表中。

## 2. 实时与历史数据获取

### 2.1 实时数据获取模块
**描述:** 开发一个独立的模块，负责持续获取指定股票的实时行情数据。
**要求:**
- 能够连接行情数据源。
- 支持按需获取单只股票的实时价格、交易量等信息。
- 能够维护一个实时行情数据流。

### 2.2 历史数据获取模块
**描述:** 开发一个独立的模块，负责获取指定股票的历史行情数据。
**要求:**
- 能够连接历史数据源。
- 支持按日期范围获取股票的日K线、分钟K线等历史数据。
- 能够存储和管理获取到的历史数据。

## 3. 配对交易策略与执行

### 3.1 配对交易策略模块
**描述:** 开发核心的配对交易策略模块，利用实时和历史数据计算交易信号。
**要求:**
- 基于两只或多只相关性强的股票构建配对。
- 使用统计方法（如：协整关系、价差均值回归）分析配对关系。
- 实时监控配对价差，当价差偏离均值达到设定阈值时，生成买入或卖出信号。

### 3.2 交易信号触发与全仓执行
**描述:** 当配对交易策略生成交易信号时，自动调用交易模块执行全仓买入或卖出。
**要求:**
- 当出现买入信号时，对被低估的股票执行全仓买入。
- 当出现卖出信号时，对被高估的股票执行全仓卖出。
- 确保交易执行与策略信号的紧密联动，并考虑风控机制（如最大持仓比例）。 