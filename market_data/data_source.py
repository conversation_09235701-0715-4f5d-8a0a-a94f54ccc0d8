"""
数据源基类

定义数据源的接口规范，所有具体数据源实现都应继承自此类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import logging
import pandas as pd
from datetime import datetime

class DataSource(ABC):
    """
    数据源基类
    
    定义了获取行情数据的标准接口，所有具体数据源实现都应继承自此类
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化数据源
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.name = self.__class__.__name__
        self.connected = False
        
        # 初始化其他成员变量
        self._initialize()
        
        self.logger.info(f"数据源 {self.name} 初始化完成")
    
    def _initialize(self):
        """
        初始化其他成员变量
        
        子类可以重写此方法以进行自定义初始化
        """
        pass
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到数据源
        
        Returns:
            bool: 是否连接成功
        """
        raise NotImplementedError("子类必须实现connect方法")
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开与数据源的连接
        
        Returns:
            bool: 是否断开成功
        """
        raise NotImplementedError("子类必须实现disconnect方法")
    
    @abstractmethod
    def get_price(self, code: str) -> Optional[float]:
        """
        获取指定股票的最新价格
        
        Args:
            code: 股票代码
            
        Returns:
            Optional[float]: 最新价格，如果获取失败则返回None
        """
        raise NotImplementedError("子类必须实现get_price方法")
    
    @abstractmethod
    def get_quote(self, code: str) -> Dict[str, Any]:
        """
        获取指定股票的行情数据
        
        Args:
            code: 股票代码
            
        Returns:
            Dict[str, Any]: 行情数据字典，包含价格、成交量等信息
        """
        raise NotImplementedError("子类必须实现get_quote方法")
    
    @abstractmethod
    def get_k_data(self, code: str, start_date: str, end_date: str, 
                  ktype: str = 'D') -> pd.DataFrame:
        """
        获取指定股票的K线数据
        
        Args:
            code: 股票代码
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            ktype: K线类型，D=日线，W=周线，M=月线，5=5分钟线，15=15分钟线，等
            
        Returns:
            pd.DataFrame: K线数据，包含开盘价、收盘价、最高价、最低价、成交量等
        """
        raise NotImplementedError("子类必须实现get_k_data方法")
    
    @abstractmethod
    def get_multiple_prices(self, codes: List[str]) -> Dict[str, float]:
        """
        批量获取多个股票的最新价格
        
        Args:
            codes: 股票代码列表
            
        Returns:
            Dict[str, float]: 股票代码到价格的映射
        """
        raise NotImplementedError("子类必须实现get_multiple_prices方法")
    
    def is_connected(self) -> bool:
        """
        检查是否已连接到数据源
        
        Returns:
            bool: 是否已连接
        """
        return self.connected
    
    def format_code(self, code: str) -> str:
        """
        格式化股票代码
        
        Args:
            code: 原始股票代码
            
        Returns:
            str: 格式化后的股票代码
        """
        # 默认实现，子类可以根据需要重写
        return code
    
    def __str__(self) -> str:
        """
        返回数据源的字符串表示
        
        Returns:
            str: 数据源信息
        """
        status = "已连接" if self.connected else "未连接"
        return f"{self.name} - 状态: {status}" 