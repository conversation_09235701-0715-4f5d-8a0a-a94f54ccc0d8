"""
风险控制模块测试脚本

测试风险控制模块的各个功能
"""

import unittest
import logging
import os
import json
import time
from datetime import datetime, timedelta
from risk_control.risk_manager import RiskManager
from risk_control.funding_risk import FundingRiskController
from risk_control.trade_frequency_risk import TradeFrequencyController
from risk_control.price_risk import PriceRiskController
from risk_control.execution_risk import ExecutionRiskController

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

logger = logging.getLogger("test_risk_control")

class TestRiskControl(unittest.TestCase):
    """
    风险控制测试类
    """
    
    @classmethod
    def setUpClass(cls):
        """
        测试类初始化
        """
        # 创建测试数据目录
        os.makedirs("test_data", exist_ok=True)
        
        # 创建配置文件
        config = {
            "funding_risk": {
                "max_single_trade_amount": 50000,
                "max_daily_loss": 5000,
                "max_total_position": 200000,
                "max_position_ratio": 0.4
            },
            "trade_frequency": {
                "max_daily_trades": 30,
                "min_trade_interval": 20,
                "high_freq_window": 180,
                "high_freq_threshold": 5
            },
            "price_risk": {
                "stop_loss_ratio": 0.03,
                "take_profit_ratio": 0.08,
                "max_volatility": 0.02
            },
            "execution_risk": {
                "max_consecutive_errors": 2,
                "retry_interval": 3,
                "max_retry_count": 2
            }
        }
        
        with open("test_data/risk_config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        logger.info("测试类初始化完成")
    
    def setUp(self):
        """
        测试用例初始化
        """
        # 创建风险管理器
        self.risk_manager = RiskManager(config_path="test_data/risk_config.json", logger=logger)
        
        # 初始化账户信息
        self.risk_manager.update_account_info(
            total_assets=500000,
            available_cash=300000,
            positions={
                "600000": {"amount": 1000, "cost": 10.0, "market_value": 10000},
                "000001": {"amount": 2000, "cost": 15.0, "market_value": 30000}
            }
        )
        
        # 初始化股票价格
        self.risk_manager.update_price("600000", 10.0)
        self.risk_manager.update_price("000001", 15.0)
        
        logger.info("测试用例初始化完成")
    
    def test_funding_risk(self):
        """
        测试资金风险控制
        """
        logger.info("测试资金风险控制")
        
        # 测试单笔交易金额限制
        trade_info = {
            "code": "600000",
            "price": 10.0,
            "amount": 6000,
            "direction": "buy"
        }
        passed, details = self.risk_manager.funding_risk_controller.check_risk(trade_info)
        self.assertFalse(passed, "应该触发单笔交易金额限制")
        self.assertFalse(details["max_single_trade_amount"]["passed"])
        
        # 测试正常交易
        trade_info = {
            "code": "600000",
            "price": 10.0,
            "amount": 1000,
            "direction": "buy"
        }
        passed, details = self.risk_manager.funding_risk_controller.check_risk(trade_info)
        self.assertTrue(passed, "正常交易应该通过资金风险检查")
        
        # 测试总仓位限制
        # 先更新为接近限制的仓位
        self.risk_manager.update_account_info(
            total_assets=500000,
            available_cash=300000,
            positions={
                "600000": {"amount": 1000, "cost": 10.0, "market_value": 10000},
                "000001": {"amount": 2000, "cost": 15.0, "market_value": 180000}
            }
        )
        
        trade_info = {
            "code": "300001",
            "price": 20.0,
            "amount": 1000,
            "direction": "buy"
        }
        passed, details = self.risk_manager.funding_risk_controller.check_risk(trade_info)
        self.assertFalse(passed, "应该触发总仓位限制")
        self.assertFalse(details["max_position"]["passed"])
        
        logger.info("资金风险控制测试完成")
    
    def test_trade_frequency_interval_risk(self):
        """
        测试交易频率风险控制 - 交易间隔
        """
        logger.info("测试交易频率风险控制 - 交易间隔")

        trade_info = {
            "code": "600000",
            "price": 10.0,
            "amount": 100,
            "direction": "buy"
        }
        
        # 记录第一笔交易
        self.risk_manager.trade_frequency_controller.record_trade(trade_info)
        
        # 立即检查是否触发交易间隔限制
        passed, details = self.risk_manager.trade_frequency_controller.check_risk(trade_info)
        self.assertFalse(passed, "应该触发交易间隔限制")
        self.assertFalse(details["min_trade_interval"]["passed"])
        
        # 等待交易间隔后再次测试
        time.sleep(self.risk_manager.trade_frequency_controller.config["min_trade_interval"] + 1)
        passed, details = self.risk_manager.trade_frequency_controller.check_risk(trade_info)
        self.assertTrue(passed, "等待足够时间后应通过交易频率检查")
        
        logger.info("交易频率风险控制 - 交易间隔测试完成")

    def test_trade_frequency_high_freq_risk(self):
        """
        测试交易频率风险控制 - 高频交易
        """
        logger.info("测试交易频率风险控制 - 高频交易")

        # 记录多笔交易，触发高频交易阈值
        for i in range(self.risk_manager.trade_frequency_controller.config["high_freq_threshold"] + 1):
            trade_info = {
                "code": "000001",
                "price": 15.0 + i * 0.01,
                "amount": 100,
                "direction": "buy"
            }
            self.risk_manager.trade_frequency_controller.record_trade(trade_info)
            # 模拟短时间内的连续交易
            time.sleep(0.1) 
        
        trade_info_check = {
            "code": "000001",
            "price": 16.0,
            "amount": 100,
            "direction": "buy"
        }
        passed, details = self.risk_manager.trade_frequency_controller.check_risk(trade_info_check)
        self.assertFalse(passed, "应该触发高频交易检测")
        self.assertFalse(details["high_frequency"]["passed"])
        
        logger.info("交易频率风险控制 - 高频交易测试完成")
    
    def test_price_risk(self):
        """
        测试价格风险控制
        """
        logger.info("测试价格风险控制")
        
        # 添加价格历史
        for i in range(20):
            self.risk_manager.price_risk_controller.update_price(
                "600000", 
                10.0 + (0.1 * i), 
                datetime.now() - timedelta(minutes=20-i)
            )
        
        # 测试止损
        trade_info = {
            "code": "600000",
            "price": 9.5,  # 低于成本价10.0
            "amount": 500,
            "direction": "sell"
        }
        passed, details = self.risk_manager.price_risk_controller.check_risk(trade_info)
        self.assertFalse(passed, "应该触发止损")
        self.assertFalse(details["stop_loss"]["passed"])
        
        # 测试价格波动检测
        # 先添加高波动率的价格历史
        for i in range(20):
            # 剧烈波动的价格
            price = 10.0 + ((-1) ** i) * (0.3 * i)
            self.risk_manager.price_risk_controller.update_price(
                "000001", 
                price, 
                datetime.now() - timedelta(minutes=20-i)
            )
        
        trade_info = {
            "code": "000001",
            "price": 16.0,
            "amount": 100,
            "direction": "buy"
        }
        passed, details = self.risk_manager.price_risk_controller.check_risk(trade_info)
        self.assertFalse(passed, "应该触发价格波动检测")
        self.assertFalse(details["volatility"]["passed"])
        
        logger.info("价格风险控制测试完成")
    
    def test_execution_risk(self):
        """
        测试执行风险控制
        """
        logger.info("测试执行风险控制")
        
        # 测试错误记录和紧急停止
        self.risk_manager.execution_risk_controller.record_error(
            "network_error", 
            "网络连接超时", 
            {"code": "600000", "action": "buy"}
        )
        
        # 验证没有触发紧急停止
        self.assertFalse(self.risk_manager.is_emergency_stop_active())
        
        # 再记录一次错误，应该触发紧急停止（配置为连续2次错误）
        self.risk_manager.execution_risk_controller.record_error(
            "network_error", 
            "网络连接超时", 
            {"code": "600000", "action": "buy"}
        )
        
        # 验证触发紧急停止
        self.assertTrue(self.risk_manager.is_emergency_stop_active())
        
        # 重置紧急停止
        self.risk_manager.reset_emergency_stop()
        self.assertFalse(self.risk_manager.is_emergency_stop_active())
        
        logger.info("执行风险控制测试完成")
    
    def test_integrated_risk_check(self):
        """
        测试综合风险检查
        """
        logger.info("测试综合风险检查")
        
        # 正常交易
        trade_info = {
            "code": "600000",
            "price": 10.2,
            "amount": 100,
            "direction": "buy"
        }
        passed, details = self.risk_manager.check_risk(trade_info)
        self.assertTrue(passed, "正常交易应该通过综合风险检查")
        
        # 触发多种风险的交易
        trade_info = {
            "code": "600000",
            "price": 10.0,
            "amount": 10000,  # 超过单笔交易限额
            "direction": "buy"
        }
        passed, details = self.risk_manager.check_risk(trade_info)
        self.assertFalse(passed, "应该触发风险控制")
        
        logger.info("综合风险检查测试完成")
    
    def test_callback_functionality(self):
        """
        测试回调功能
        """
        logger.info("测试回调功能")
        
        callback_triggered = False
        callback_data = None
        
        def test_callback(data):
            nonlocal callback_triggered, callback_data
            callback_triggered = True
            callback_data = data
        
        # 注册回调
        self.risk_manager.register_callback("emergency_stop", test_callback)
        
        # 触发紧急停止
        self.risk_manager.emergency_stop_trading("测试回调")
        
        # 验证回调被触发
        self.assertTrue(callback_triggered)
        self.assertIsNotNone(callback_data)
        self.assertEqual(callback_data.get("reason"), "测试回调")
        
        logger.info("回调功能测试完成")
    
    @classmethod
    def tearDownClass(cls):
        """
        测试类清理
        """
        # 清理测试数据
        if os.path.exists("test_data/risk_config.json"):
            os.remove("test_data/risk_config.json")
        
        if os.path.exists("test_data"):
            os.rmdir("test_data")
        
        logger.info("测试类清理完成")

if __name__ == '__main__':
    unittest.main() 