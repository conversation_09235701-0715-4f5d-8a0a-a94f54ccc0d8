"""
交易模块

处理中信建投微信网厅的股票交易操作，包括买入、卖出和撤单。
"""

import json
import logging
import time
import random
from typing import Dict, List, Optional, Any, Tuple
import requests
import base64
from datetime import datetime
from .encrypt import generate_request_data, generate_order_sign_from_params, generate_entrant_key
from .utils import format_stock_code
from .query import QueryClient

class TradeClient:
    """交易客户端
    
    处理中信建投微信网厅的买入、卖出和撤单等交易操作。
    """
    
    # 中信建投微信网厅实际API域名
    BASE_URL = "https://wzq.csc108.com/cgi-bin"
    ENTRUST_URL = f"{BASE_URL}/trade_entrust.fcgi"  # 委托接口（买入/卖出共用）
    CANCEL_URL = f"{BASE_URL}/tradecancel.cgi"  # 撤单接口
    ORDER_NO_URL = f"{BASE_URL}/trade_order_no.fcgi"  # 获取交易订单号
    SUBMIT_URL = f"{BASE_URL}/tradesubmit.fcgi"   # 实际交易提交接口
    PREPARE_URL = f"{BASE_URL}/tradeprepare.cgi"  # 交易准备接口
    TRADE_REFRESH_URL = f"{BASE_URL}/trade_refresh.fcgi"  # 交易刷新接口
    STOCK_INFO_URL = f"{BASE_URL}/stock_info.fcgi"  # 股票信息接口
    
    # 委托方向
    DIRECTION_BUY = "1"    # 买入
    DIRECTION_SELL = "2"   # 卖出
    
    # 错误响应模板
    ERROR_RESPONSE = {
        "status": "error",
        "message": "",
        "entrust_no": "",
        "data": {}
    }
    
    def __init__(self, session: requests.Session, user_info: Dict[str, Any], logger: logging.Logger):
        """
        初始化交易客户端
        
        Args:
            session: 会话对象
            user_info: 用户信息
            logger: 日志记录器
        """
        self.session = session
        self.user_info = user_info
        self.logger = logger
        self.app_info = user_info.get("app_info", {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "202504011653"
        })
        # 添加标准请求头，参考HAR文件
        self.headers = {
            "Host": "wzq.csc108.com",
            "Connection": "keep-alive",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://wzq.csc108.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors", 
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        # init之后，调用trade_order_no接口，获取交易订单号
        

        # 初始化股东代码字典
        self.stockholder_codes = {}  # 格式: {"0": "深市代码", "1": "沪市代码"}
        self._fetch_stockholder_codes()  # 调用获取股东代码的方法
        self.logger.info("交易客户端初始化完成")
        self.last_trade_order_no = {"0": self._get_trade_order_no("0"), "1": self._get_trade_order_no("1")}
        self.logger.info(f"trade_order_no:{self.last_trade_order_no}")
        # 新增：初始化QueryClient
        self.query_client = QueryClient(self.session, self.user_info, self.logger)
    
    def _validate_trade_params(self, stock_code: str, price: float, volume: int, action_type: str) -> None:
        """
        验证交易参数
        
        Args:
            stock_code: 股票代码
            price: 交易价格
            volume: 交易数量
            action_type: 交易类型（"买入"或"卖出"）
            
        Raises:
            ValueError: 参数错误
        """
        if not stock_code or not isinstance(stock_code, str):
            raise ValueError(f"股票代码不能为空且必须为字符串")
        
        if not price or price <= 0:
            raise ValueError(f"{action_type}价格必须大于0")
        
        if not volume or volume <= 0 or volume % 100 != 0:
            raise ValueError(f"{action_type}数量必须为正整数且为100的整数倍")
    
    def _format_error_response(self, error_msg: str, extra_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_msg: 错误信息
            extra_data: 额外数据
            
        Returns:
            Dict: 格式化的错误响应
        """
        response = self.ERROR_RESPONSE.copy()
        response["message"] = error_msg
        if extra_data:
            response.update(extra_data)
        return response
    
    def _make_request(self, url: str, data: Dict[str, Any], log_prefix: str) -> Tuple[bool, Dict[str, Any]]:
        """
        发送HTTP请求并处理响应
        
        Args:
            url: 请求URL
            data: 请求数据
            log_prefix: 日志前缀
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 响应数据)
        """
        try:
            # 设置Content-Length
            headers = self.headers.copy()
            #content_length = len("&".join(f"{k}={v}" for k, v in data.items()))
            #headers["Content-Length"] = str(content_length)
            
            # 打印请求数据
            #self.logger.info(f"{log_prefix}请求数据: {data}")
            
            # 发送请求
            timestamp = int(time.time() * 1000)
            response = self.session.post(
                f"{url}?t={timestamp}", 
                data=data, 
                headers=headers
            )
            
            if response.status_code != 200:
                self.logger.error(f"{log_prefix}失败: 状态码 {response.status_code}")
                return False, {"retcode": "-1", "retmsg": f"HTTP错误: {response.status_code}"}
            
            # 解析响应数据
            try:
                response_content = response.json()
                return response_content.get("retcode") == "0", response_content
            except ValueError:
                self.logger.error(f"{log_prefix}响应解析失败: {response.text}")
                return False, {"retcode": "-1", "retmsg": "响应格式错误", "text": response.text}
        except Exception as e:
            self.logger.error(f"{log_prefix}过程中发生错误: {str(e)}")
            return False, {"retcode": "-1", "retmsg": str(e)}
    
    def get_stock_info(self, stock_code: str, need_quote: bool = True, need_five: bool = True) -> Dict[str, Any]:
        """
        获取股票信息
        
        根据提供的curl请求，获取股票的class、market、name等信息
        
        Args:
            stock_code: 股票代码（如：600000）
            need_quote: 是否需要行情数据
            need_five: 是否需要五档数据
            
        Returns:
            Dict: 包含股票信息的字典
        """
        try:
            # 格式化股票代码
            formatted_stock_code = format_stock_code(stock_code)
            market = "1" if formatted_stock_code.startswith("SH") else "0"  # 1表示上海，0表示深圳
            clean_code = formatted_stock_code.replace("SH", "").replace("SZ", "")
            
            # 构建请求数据
            stock_info_data = {
                "market": market,
                "code": clean_code,
                "needquote": "1" if need_quote else "0",
                "needfive": "1" if need_five else "0",
                "gm_flag": "0"
            }
            
            # 发送请求
            self.logger.info(f"正在获取股票信息: {stock_code}")
            success, response_content = self._make_request(
                self.STOCK_INFO_URL, stock_info_data, "获取股票信息"
            )
            
            if success:
                self.logger.info(f"获取股票信息成功: {stock_code}")
                # 提取有用信息
                stock_info = {
                    "code": clean_code,
                    "market": response_content.get('info').get("market", ""),
                    "name": response_content.get('info').get("name", ""),
                    "stock_cls": response_content.get('info').get("class", "0"),  # 股票类别
                    "price": response_content.get("price", "0"),    # 当前价格
                    "open": response_content.get("open", "0"),      # 开盘价
                    "high": response_content.get("high", "0"),      # 最高价
                    "low": response_content.get("low", "0"),        # 最低价
                    "pre_close": response_content.get("pre_close", "0"),  # 昨收价
                    "volume": response_content.get("volume", "0"),  # 成交量
                    "amount": response_content.get("amount", "0"),  # 成交额
                    "date": response_content.get("date", ""),       # 日期
                    "time": response_content.get("time", ""),       # 时间
                }
                
                # 如果需要五档数据，添加到结果中
                if need_five and "five" in response_content:
                    five_data = response_content.get("five", {})
                    stock_info["five"] = five_data
                
                return {
                    "status": "success",
                    "message": "获取股票信息成功",
                    "data": stock_info
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"获取股票信息失败: {error_msg}")
                return {
                    "status": "failed",
                    "message": error_msg,
                    "data": response_content
                }
        except Exception as e:
            error_msg = f"获取股票信息过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def buy(self, stock_code: str, price: float, volume: int = 0, stock_info: Dict[str, Any] = None, order_type: str = "specified", commission_rate: float = 0.0001, fok: bool = False) -> Dict[str, Any]:
        """
        买入股票
        
        完整交易流程：调用交易准备接口 -> 获取订单号 -> 提交买入请求
        
        Args:
            stock_code: 股票代码（如：600000）
            price: 买入价格
            volume: 买入数量（股），当order_type为"specified"时有效
            stock_info: 股票信息字典，包含market, code, name, stock_cls等
            order_type: 下单类型，可选"specified"（指定份额）, "full"（全仓）, "half"（半仓）
            commission_rate: 佣金率，默认为万分之一 (0.0001)
            fok: 是否为FOK（Fill Or Kill）指令单，默认为False。如果为True，未成交部分将被撤销。
            
        Returns:
            Dict: 包含委托结果的字典，如委托编号等
            
        Raises:
            ValueError: 参数错误
        """
        try:
            # 参数验证
            if not stock_code or not isinstance(stock_code, str):
                raise ValueError(f"股票代码不能为空且必须为字符串")
            if not price or price <= 0:
                raise ValueError(f"买入价格必须大于0")

            # 股票信息已从外部传入，直接使用
            if not stock_info or stock_info.get("status") != "success":
                return self._format_error_response(f"传入的股票信息无效或获取失败: {stock_info.get('message', '未知错误') if stock_info else '股票信息为空'}")

            actual_volume = volume
            if order_type in ["full", "half"]:
                # 获取可用资金
                account_info = self.trade_show()
                if account_info.get("status") != "success":
                    return self._format_error_response("获取账户信息失败，无法执行全仓/半仓买入")
                
                available_cash = float(account_info["data"]["limits"]["max_buy_money"])
                
                if order_type == "half":
                    available_cash /= 2
                
                # 计算可买入数量
                if price > 0:
                    calculated_volume = int(available_cash / price) # 计算出最大可买股数
                    # 确保是100的整数倍
                    actual_volume = (calculated_volume // 100) * 100
                else:
                    return self._format_error_response("股票价格为0，无法计算买入数量")

                if actual_volume <= 0:
                    return self._format_error_response("可用资金不足或计算出的买入数量为零，无法执行全仓/半仓买入")
            elif order_type == "specified":
                if not volume or volume <= 0 or volume % 100 != 0:
                    raise ValueError(f"买入数量必须为正整数且为100的整数倍")
            else:
                raise ValueError("无效的下单类型，可选值为: specified, full, half")

            # 执行完整交易流程
            return self._execute_trade(
                stock_info=stock_info, # 传递完整的股票信息
                price=price,
                volume=actual_volume, # 使用实际买入数量
                action=self.DIRECTION_BUY,
                fok=fok
            )
        except ValueError as e:
            self.logger.error(f"买入参数错误: {str(e)}")
            return self._format_error_response(str(e))
        except Exception as e:
            error_msg = f"买入过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def sell(self, stock_code: str, price: float, volume: int, stock_info: Dict[str, Any], fok: bool = False) -> Dict[str, Any]:
        """
        卖出股票
        
        完整交易流程：调用交易准备接口 -> 获取订单号 -> 提交卖出请求
        
        Args:
            stock_code: 股票代码（如：600000）
            price: 卖出价格
            volume: 卖出数量（股）
            stock_info: 股票信息字典，包含market, code, name, stock_cls等
            fok: 是否为FOK（Fill Or Kill）指令单，默认为False。如果为True，未成交部分将被撤销。
            
        Returns:
            Dict: 包含委托结果的字典，如委托编号等
            
        Raises:
            ValueError: 参数错误
        """
        try:
            # 参数验证
            self._validate_trade_params(stock_code, price, volume, "卖出")
            
            # 股票信息已从外部传入，直接使用
            if stock_info.get("status") != "success": # 检查传入的股票信息是否有效
                return self._format_error_response(f"传入的股票信息无效或获取失败: {stock_info.get('message', '未知错误')}")
            
            # 执行完整交易流程
            return self._execute_trade(
                stock_info=stock_info, # 传递完整的股票信息
                price=price,
                volume=volume,
                action=self.DIRECTION_SELL,
                fok=fok
            )
        except ValueError as e:
            self.logger.error(f"卖出参数错误: {str(e)}")
            return self._format_error_response(str(e))
        except Exception as e:
            error_msg = f"卖出过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def cancel(self, contract_no: str, trade_time: str, stock_code: str, market: str, action: str, stock_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        撤销委托
        
        Args:
            contract_no: 合同编号
            trade_time: 交易时间，格式：YYYY-MM-DD HH:MM:SS
            stock_code: 股票代码（不含市场前缀）
            market: 市场代码（1:上海，2:深圳，0:自动判断）
            action: 交易方向（1:买入，2:卖出）
            stock_info: 股票信息字典，包含market, code, name, stock_cls等
            
        Returns:
            Dict: 包含撤单结果的字典
            
        Raises:
            ValueError: 参数错误
        """
        # 参数验证
        if not contract_no or not isinstance(contract_no, str):
            return self._format_error_response("合同编号不能为空且必须为字符串", {"contract_no": contract_no})
        
        if not trade_time or not isinstance(trade_time, str):
            return self._format_error_response("交易时间不能为空且必须为字符串", {"contract_no": contract_no})
        
        if not stock_code or not isinstance(stock_code, str):
            return self._format_error_response("股票代码不能为空且必须为字符串", {"contract_no": contract_no})
        
        try:
            # 股票信息已从外部传入，直接使用
            if stock_info.get("status") != "success": # 检查传入的股票信息是否有效
                return self._format_error_response(f"传入的股票信息无效或获取失败: {stock_info.get('message', '未知错误')}")
            
            # 首先调用交易准备接口
            # 使用传入的stock_info中的market
            prepare_result = self._prepare_trade(stock_info["data"]["market"])
            if not prepare_result:
                return {
                    "status": "failed",
                    "message": "交易准备失败",
                    "contract_no": contract_no,
                    "data": {}
                }
            
            # 构建撤单请求数据
            cancel_data = {
                **self.app_info,
                "psw": "",
                "id": "",
                "contract_no": contract_no,
                "trade_time": trade_time,
                "scode": stock_info["data"]["code"], # 使用传入的股票信息
                "action": action,
                "market": stock_info["data"]["market"], # 使用传入的股票信息
                "come_from": "0",
                "gm_flag": "0"
            }
            
            # 发送撤单请求
            self.logger.info(f"正在撤单: 合同编号 {contract_no}")
            success, response_content = self._make_request(
                self.CANCEL_URL, cancel_data, "撤单"
            )
            
            if success:
                self.logger.info(f"撤单成功: 合同编号 {contract_no}")
                return {
                    "status": "success",
                    "message": "撤单成功",
                    "contract_no": contract_no,
                    "data": response_content
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"撤单失败: {error_msg}")
                return {
                    "status": "failed",
                    "message": error_msg,
                    "contract_no": contract_no,
                    "data": response_content
                }
        except Exception as e:
            error_msg = f"撤单过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "contract_no": contract_no,
                "data": {}
            }
    
    def _execute_trade(self, stock_info: Dict[str, Any], price: float, volume: int, action: str, fok: bool) -> Dict[str, Any]:
        """
        执行完整交易流程
        
        Args:
            stock_info: 股票信息字典，包含market, code, name, stock_cls等
            price: 交易价格
            volume: 交易数量
            action: 交易方向（1:买入，2:卖出）
            fok: 是否为FOK（Fill Or Kill）指令单，默认为False。
            
        Returns:
            Dict: 包含交易结果的字典
        """

        
        # 1. 获取交易订单号
        # 优先使用上次交易获取的订单号，如果没有则重新获取
        trade_order_no = self.last_trade_order_no.get(stock_info["data"]["market"], "")
        if not trade_order_no:
            trade_order_no = self._get_trade_order_no(stock_info["data"]["market"])
            if not trade_order_no:
                return {
                    "status": "failed",
                    "message": "无法获取交易订单号",
                    "entrust_no": "",
                    "data": {}
                }
        # 2. 调用交易准备接口
        prepare_result = self._prepare_trade(stock_info["data"]["market"])
        if not prepare_result:
            return {
                "status": "failed",
                "message": "交易准备失败",
                "entrust_no": "",
                "data": {}
            }                
        
        # 3. 提交交易请求
        trade_result = self._submit_trade(
            stock_info=stock_info,
            price=price,
            volume=volume,
            action=action,
            trade_order_no=trade_order_no,
            fok=fok # 将fok参数传递给_submit_trade
        )

        # 更新最近的交易订单号，以便下次交易使用

        self.last_trade_order_no[stock_info["data"]["market"]] = self._get_trade_order_no(stock_info["data"]["market"])
        if trade_result.get("status") == "success" and "data" in trade_result:
            # 4. 处理 FOK 指令单 - 新逻辑
            if fok and trade_result.get("contract_no"):
                contract_no = trade_result["contract_no"]
                self.logger.info(f"FOK指令单已提交，查询成交状态: 合同编号 {contract_no}")
                # 把时间戳转换为YYYY-MM-DD HH:MM:SS格式
                current_trade_time = datetime.fromtimestamp(int(trade_result['data']["trade_time"])).strftime("%Y-%m-%d %H:%M:%S")
                self.logger.info(f"current_trade_time: {current_trade_time}")
                # 查询成交状态
                status_result = self.query_client.query_trade_status(contract_no, current_trade_time)
                can_cancel = status_result.get("data", {}).get("info", {}).get("can_cancel")
                if can_cancel == "1":
                    self.logger.info(f"FOK订单可撤销，尝试撤单: 合同编号 {contract_no}")
                    cancel_result = self.cancel(
                        contract_no=contract_no,
                        trade_time=current_trade_time,
                        stock_code=stock_info["data"]["code"],
                        market=stock_info["data"]["market"],
                        action=action,
                        stock_info=stock_info
                    )
                    if cancel_result.get("status") == "success":
                        self.logger.info(f"FOK指令单撤销成功，表示未完全成交: 合同编号 {contract_no}")
                        trade_result["fok_status"] = "canceled_partial_fill"
                        trade_result["fok_message"] = "FOK订单未完全成交，剩余部分已撤销"
                    else:
                        self.logger.info(f"FOK指令单撤销失败，假设已完全成交: 合同编号 {contract_no}. 撤销失败原因: {cancel_result.get('message', '未知错误')}")
                        trade_result["fok_status"] = "completed_or_cancel_failed"
                        trade_result["fok_message"] = f"FOK订单已完全成交或无法撤销. 撤销失败原因: {cancel_result.get('message', '未知错误')}"
                else:
                    self.logger.info(f"FOK订单不可撤销，视为已完全成交: 合同编号 {contract_no}")
                    trade_result["fok_status"] = "completed"
                    trade_result["fok_message"] = "FOK订单已完全成交，无需撤单"
            elif fok and not trade_result.get("contract_no"):
                self.logger.warning("FOK指令单提交失败，未生成合同编号，无需检查和撤销。")
                trade_result["fok_status"] = "no_contract_no"
                trade_result["fok_message"] = "FOK指令单提交失败，未生成合同编号"

        return trade_result
    
    def _prepare_trade(self, market: str) -> bool:
        """
        交易前准备工作，调用tradeprepare.cgi接口
        
        Args:
            market: 市场代码（1-上海，2-深圳）
            
        Returns:
            bool: 准备是否成功
        """
        prepare_data = {
            "market": market,
            "come_from": "0",
            "gm_flag": "0"
        }
            
        success, response_content = self._make_request(
            self.PREPARE_URL, prepare_data, "交易准备"
        )
        
        if success:
            self.logger.info("交易准备成功")
            # 如果响应中包含timeseed，保存它
            if "timeseed" in response_content:
                self.timeseed = response_content["timeseed"]
            return True
        else:
            error_msg = response_content.get("retmsg", "未知错误")
            self.logger.error(f"交易准备失败: {error_msg}")
            return False
    
    def _get_trade_order_no(self, market: str) -> str:
        """
        获取交易订单号
        
        Args:
            market: 市场代码（1-上海，0-深圳）
            
        Returns:
            str: 交易订单号
        """
        
        order_no_data = {
            "market": market,
            "stock_code": "",
            "stockholder_code": self.stockholder_codes.get(market, ""),
            "retry_time": "0",
            "come_from": "0",
            "gm_flag": "0"
        }
            
        success, response_content = self._make_request(
            self.ORDER_NO_URL, order_no_data, "获取交易订单号"
        )
        
        if success:
            trade_order_no = response_content.get("trade_order_no", "")
            self.logger.info(f"获取交易订单号成功: {trade_order_no}")
            return trade_order_no
        else:
            error_msg = response_content.get("retmsg", "未知错误")
            self.logger.error(f"获取交易订单号失败: {error_msg}")
        return ""
    
    def _submit_trade(self, stock_info: Dict[str, Any], price: float, 
                      volume: int, action: str, trade_order_no: str, fok: bool) -> Dict[str, Any]:
        """
        提交交易请求
        
        Args:
            stock_info: 股票信息字典，包含market, code, name, stock_cls等
            price: 交易价格
            volume: 交易数量
            action: 交易方向（1:买入，2:卖出）
            trade_order_no: 交易订单号
            fok: 是否为FOK（Fill Or Kill）指令单，默认为False。如果为True，未成交部分将被撤销。
            
        Returns:
            Dict: 包含交易结果的字典
        """
        try:
            market = stock_info["data"]["market"]
            stock_code = stock_info["data"]["code"]
            
            # 动态选择股东代码
            stockholder_code = self.stockholder_codes.get(market, "")
            if not stockholder_code:
                return self._format_error_response(f"未找到市场 {market} 的股东代码")
            
            # 格式化价格和数量为字符串
            price_str = f"{price:.3f}"
            volume_str = str(volume)
            
            # 生成订单签名
            order_sign = generate_order_sign_from_params(stock_code, price_str, volume_str)
            self.logger.info(f"生成ordersign{order_sign}")
            # 生成entrant_key
            entrant_key = f"{int(time.time() * 1000)}{random.randint(1000, 9999)}"
            
            # 获取股票名称和类别
            # stock_info 已经传入，直接使用
            stock_name = stock_info["data"].get("name", "交易股票")
            stock_cls = stock_info["data"].get("stock_cls", self._get_stock_cls(stock_code))
            
            # 构建交易请求数据
            trade_data = {
                **self.app_info,
                "scenes": "",
                "action": action,
                "type": "0",
                "market": market,
                "scode": stock_code,
                "name": stock_name,
                "price": price_str,
                "quantity": volume_str,
                "psw": "",  # 密码已通过psw_session鉴权
                "entrant_key": entrant_key,
                "stockholder_code": stockholder_code,
                "match_type": "0",
                "risk_ver": "1",
                "order_sign": order_sign,
                "trade_order_no": trade_order_no,
                "stock_cls": stock_cls,
                "come_from": "0",
                "gm_flag": "0",
            }
            
            # 发送交易请求
            action_text = "买入" if action == self.DIRECTION_BUY else "卖出"
            self.logger.info(f"正在{action_text}: {stock_code}, 价格: {price}, 数量: {volume}")
            
            success, response_content = self._make_request(
                self.SUBMIT_URL, trade_data, f"{action_text}请求"
            )
            
            if success:
                # 提取委托编号
                entrust_no = response_content.get("entrust_no", "")
                contract_no = response_content.get("contract_no", "")
                self.logger.info(f"委托成功，合同编号: {contract_no}")
                return {
                    "status": "success",
                    "message": "委托成功",
                    "entrust_no": entrust_no,
                    "contract_no": contract_no,
                    "data": response_content
                }
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"委托失败: {error_msg}")
                return {
                    "status": "failed",
                    "message": error_msg,
                    "entrust_no": "",
                    "data": response_content
                }
        except Exception as e:
            error_msg = f"委托过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)
    
    def _get_stock_cls(self, stock_code: str) -> str:
        """
        根据股票代码获取股票类别
        
        Args:
            stock_code: 股票代码（不含市场前缀）
            
        Returns:
            str: 股票类别代码
        """
        # 股票类别代码：0-普通A股，5-ETF基金，3-债券等
        # 根据股票代码前缀判断
        if stock_code.startswith(("51", "58", "16")):
            return "5"  # ETF基金
        elif stock_code.startswith(("11", "12")):
            return "3"  # 债券
        else:
            return "0"  # 默认为普通A股
    
    def refresh_trade_status(self, scene: str = "1") -> Dict[str, Any]:
        """
        刷新交易状态
        
        Args:
            scene: 场景值，默认为1
            
        Returns:
            Dict: 包含交易状态的字典
        """
        refresh_data = {
            "scene": scene,
            "come_from": "0",
            "gm_flag": "0"
        }
            
        success, response_content = self._make_request(
            self.TRADE_REFRESH_URL, refresh_data, "刷新交易状态"
        )
        
        if success:
            return {
                "status": "success",
                "message": "刷新交易状态成功",
                "data": response_content
            }
        else:
            error_msg = response_content.get("retmsg", "未知错误")
            self.logger.error(f"刷新交易状态失败: {error_msg}")
            return {
                "status": "failed",
                "message": error_msg,
                "data": response_content
            }
    
    def trade_show(self, stock_code: str = "", qry_bulletin: str = "1") -> Dict[str, Any]:
        """
        获取账户持仓和资金信息
        
        Args:
            stock_code: 股票代码，如果为空则获取所有持仓
            qry_bulletin: 是否查询公告，默认为"1"
            
        Returns:
            Dict: 包含持仓、委托和资金信息的字典
        """
        try:
            # 构建请求数据
            trade_show_data = {
                "market": "0",  # 默认深市，会返回所有市场的数据
                "stock_code": stock_code,
                "stockholder_code": "",  # 留空，API会自动使用默认股东代码
                "qry_bulletin": qry_bulletin,
                "retry_time": "0",
                "come_from": "0",
                "gm_flag": "0"
            }
            
            # 发送请求
            self.logger.info(f"正在获取账户持仓和资金信息...")
            
            # 构建URL
            trade_show_url = f"{self.BASE_URL}/trade_show.fcgi"
            
            success, response_content = self._make_request(
                trade_show_url, trade_show_data, "获取账户信息"
            )
            
            if success:
                # 提取有用信息
                result = {
                    "status": "success",
                    "message": "获取账户信息成功",
                    "data": {
                        # 资金信息
                        "funds": {
                            "total_assets": response_content.get("fundsinfo", {}).get("total_money", "0"),  # 总资产
                            "available_cash": response_content.get("fundsinfo", {}).get("can_trade", "0"),  # 可用资金
                            "withdrawable_cash": response_content.get("fundsinfo", {}).get("can_draw", "0"),  # 可取资金
                            "frozen_cash": response_content.get("fundsinfo", {}).get("freeze_money", "0"),  # 冻结资金
                            "market_value": response_content.get("fundsinfo", {}).get("hold_val", "0"),  # 持仓市值
                            "total_profit": response_content.get("fundsinfo", {}).get("earn_val", "0"),  # 总盈亏
                            "today_profit": response_content.get("fundsinfo", {}).get("earn_val_today", "0"),  # 当日盈亏
                        },
                        # 持仓信息
                        "positions": response_content.get("holdstock", []),
                        # 委托信息
                        "orders": response_content.get("orderlist", []),
                        # 交易限制
                        "limits": {
                            "max_buy_money": response_content.get("max_buy_money", "0"),  # 最大可买入金额
                            "max_sell_qty": response_content.get("max_sell_qty", "0"),  # 最大可卖出数量
                        },
                        # 股东代码
                        "stockholder_code": response_content.get("stockholder_code", ""),
                        # 原始响应数据
                        "raw_response": response_content
                    }
                }
                
                # 打印持仓和资金摘要
                self.logger.info(f"账户信息获取成功")
                self.logger.info(f"可用资金: {result['data']['funds']['available_cash']}元")
                self.logger.info(f"持仓市值: {result['data']['funds']['market_value']}元")
                self.logger.info(f"持仓数量: {len(result['data']['positions'])}支股票/基金")
                
                return result
            else:
                error_msg = response_content.get("retmsg", "未知错误")
                self.logger.error(f"获取账户信息失败: {error_msg}")
                return {
                    "status": "failed",
                    "message": error_msg,
                    "data": response_content
                }
                
        except Exception as e:
            error_msg = f"获取账户信息过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return self._format_error_response(error_msg)

    def _fetch_stockholder_codes(self) -> None:
        """
        从userinfo接口获取股东代码，并按市场分类存储
        """
        try:
            # 构建请求数据
            userinfo_data = {
                "_appver": self.app_info["_appver"],
                "_osVer": self.app_info["_osVer"],
                "dealer": "1",
                "detail": "1",
                "come_from": "0"
            }
            
            # 发送请求
            timestamp = int(time.time() * 1000)
            response = self.session.post(
                f"https://wzq.csc108.com/cgi-bin/userinfo.fcgi?t={timestamp}",
                data=userinfo_data,
                headers=self.headers
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("retcode") == "0":
                    # 解析股东代码
                    for card in response_data.get("shareholdercards", []):
                        market = card.get("market", "")
                        code = card.get("code", "")
                        if market and code:
                            self.stockholder_codes[market] = code
                    self.logger.info(f"股东代码获取成功: {self.stockholder_codes}")
                else:
                    self.logger.error(f"获取股东代码失败: {response_data.get('retmsg', '未知错误')}")
            else:
                self.logger.error(f"HTTP错误: {response.status_code}")
        except Exception as e:
            self.logger.error(f"获取股东代码过程中发生错误: {str(e)}")