(self["webpackChunkoem_main"] = self["webpackChunkoem_main"] || []).push([
	[5355], {
		44667: function(t, e, n) {
			"use strict";

			function r(t) {
				return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, r(t)
			}
			var o, i, a, c, u;

			function s() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				s = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					o = "function" == typeof Symbol ? Symbol : {}, i = o.iterator || "@@iterator",
					a = o.asyncIterator || "@@asyncIterator",
					c = o.toStringTag || "@@toStringTag";

				function u(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					u({}, "")
				} catch (O) {
					u = function(t, e, n) {
						return t[e] = n
					}
				}
				function l(t, e, n, r) {
					var o = e && e.prototype instanceof d ? e : d,
						i = Object.create(o.prototype),
						a = new _(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return j()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = E(a, n);
									if (c) {
										if (c === p) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = f(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === p) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function f(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (O) {
						return {
							type: "throw",
							arg: O
						}
					}
				}
				t.wrap = l;
				var p = {};

				function d() {}
				function h() {}
				function m() {}
				var v = {};
				u(v, i, (function() {
					return this
				}));
				var y = Object.getPrototypeOf,
					g = y && y(y(N([])));
				g && g !== e && n.call(g, i) && (v = g);
				var w = m.prototype = d.prototype = Object.create(v);

				function b(t) {
					["next", "throw", "return"].forEach((function(e) {
						u(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function x(t, e) {
					function o(i, a, c, u) {
						var s = f(t[i], t, a);
						if ("throw" !== s.type) {
							var l = s.arg,
								p = l.value;
							return p && "object" == r(p) && n.call(p, "__await") ? e.resolve(p.__await).then((function(t) {
								o("next", t, c, u)
							}), (function(t) {
								o("throw", t, c, u)
							})) : e.resolve(p).then((function(t) {
								l.value = t, c(l)
							}), (function(t) {
								return o("throw", t, c, u)
							}))
						}
						u(s.arg)
					}
					var i;
					this._invoke = function(t, n) {
						function r() {
							return new e((function(e, r) {
								o(t, n, e, r)
							}))
						}
						return i = i ? i.then(r, r) : r()
					}
				}
				function E(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, E(t, e), "throw" === e.method)) return p;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return p
					}
					var r = f(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, p;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, p) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, p)
				}
				function S(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function k(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function _(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(S, this), this.reset(!0)
				}
				function N(t) {
					if (t) {
						var e = t[i];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return o.next = o
						}
					}
					return {
						next: j
					}
				}
				function j() {
					return {
						value: void 0,
						done: !0
					}
				}
				return h.prototype = m, u(w, "constructor", m), u(m, "constructor", h), h.displayName = u(m, c, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === h || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, m) : (t.__proto__ = m, u(t, c, "GeneratorFunction")), t.prototype = Object.create(w), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, b(x.prototype), u(x.prototype, a, (function() {
					return this
				})), t.AsyncIterator = x, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new x(l(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, b(w), u(w, c, "Generator"), u(w, i, (function() {
					return this
				})), u(w, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = N, _.prototype = {
					constructor: _,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(k), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, p) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), p
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), k(n), p
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									k(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: N(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), p
					}
				}, t
			}
			function l(t, e) {
				var n = "undefined" !== typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
				if (!n) {
					if (Array.isArray(t) || (n = h(t)) || e && t && "number" === typeof t.length) {
						n && (t = n);
						var r = 0,
							o = function() {};
						return {
							s: o,
							n: function() {
								return r >= t.length ? {
									done: !0
								} : {
									done: !1,
									value: t[r++]
								}
							},
							e: function(t) {
								throw t
							},
							f: o
						}
					}
					throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
				}
				var i, a = !0,
					c = !1;
				return {
					s: function() {
						n = n.call(t)
					},
					n: function() {
						var t = n.next();
						return a = t.done, t
					},
					e: function(t) {
						c = !0, i = t
					},
					f: function() {
						try {
							a || null == n.
							return ||n.
							return ()
						} finally {
							if (c) throw i
						}
					}
				}
			}
			function f(t, e, n) {
				return e in t ? Object.defineProperty(t, e, {
					value: n,
					enumerable: !0,
					configurable: !0,
					writable: !0
				}) : t[e] = n, t
			}
			function p(t, e) {
				return y(t) || v(t, e) || h(t, e) || d()
			}
			function d() {
				throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
			}
			function h(t, e) {
				if (t) {
					if ("string" === typeof t) return m(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? m(t, e) : void 0
				}
			}
			function m(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r
			}
			function v(t, e) {
				var n = null == t ? null : "undefined" !== typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
				if (null != n) {
					var r, o, i = [],
						a = !0,
						c = !1;
					try {
						for (n = n.call(t); !(a = (r = n.next()).done); a = !0) if (i.push(r.value), e && i.length === e) break
					} catch (u) {
						c = !0, o = u
					} finally {
						try {
							a || null == n["return"] || n["return"]()
						} finally {
							if (c) throw o
						}
					}
					return i
				}
			}
			function y(t) {
				if (Array.isArray(t)) return t
			}
			/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
			function g(t, e, n, r) {
				function o(t) {
					return t instanceof n ? t : new n((function(e) {
						e(t)
					}))
				}
				return new(n || (n = Promise))((function(n, i) {
					function a(t) {
						try {
							u(r.next(t))
						} catch (e) {
							i(e)
						}
					}
					function c(t) {
						try {
							u(r["throw"](t))
						} catch (e) {
							i(e)
						}
					}
					function u(t) {
						t.done ? n(t.value) : o(t.value).then(a, c)
					}
					u((r = r.apply(t, e || [])).next())
				}))
			}
			function w(t) {
				var e, n = "undefined" !== typeof window && (null === (e = null === window || void 0 === window ? void 0 : window.navigator) || void 0 === e ? void 0 : e.userAgent) || "";
				return t ? n.toLowerCase() : n
			}
			function b() {
				return /iPad|iPhone|iPod/.test(w())
			}
			function x() {
				return w("l").indexOf("android") > -1
			}
			function E() {
				return /qqnews/.test(w())
			}
			n.r(e), n.d(e, {
				ApiName: function() {
					return _
				},
				ErrCode: function() {
					return S
				},
				NATIVE_METHOD_NAME: function() {
					return P
				},
				NATIVE_MODULE_NAME: function() {
					return O
				},
				PUBLIC_TYPE_CONSTANTS: function() {
					return I
				},
				PolyfillKey: function() {
					return k
				},
				ToSchemeOrWebType: function() {
					return j
				},
				addChatHistory: function() {
					return Fe
				},
				adjustWebViewInfo: function() {
					return Er
				},
				broadcastEvent: function() {
					return nr
				},
				broadcastMsg: function() {
					return tr
				},
				browsingImage: function() {
					return Ye
				},
				changeCityChannel: function() {
					return Ur
				},
				changeLoginAccount: function() {
					return rn
				},
				checkAppStatus: function() {
					return sr
				},
				checkCalendarAuthority: function() {
					return Cr
				},
				chooseMedia: function() {
					return vr
				},
				chooseVideoCover: function() {
					return wr
				},
				closePage: function() {
					return le
				},
				closePopupView: function() {
					return ne
				},
				closeShareMenu: function() {
					return Nn
				},
				closeWebview: function() {
					return ee
				},
				createLauncherShortcut: function() {
					return Ze
				},
				createMaskShortcut: function() {
					return Xe
				},
				deleteFavoriteItems: function() {
					return De
				},
				deleteOmAccountLocalDraft: function() {
					return pn
				},
				disableCaptureImageShare: function() {
					return Ln
				},
				downloadApp: function() {
					return pr
				},
				downloadAppGdt: function() {
					return dr
				},
				downloadMedia: function() {
					return er
				},
				editVideo: function() {
					return yr
				},
				enableLogin: function() {
					return un
				},
				enableShare: function() {
					return Tn
				},
				encryptAiseeUrlParams: function() {
					return Be
				},
				fetchLocalData: function() {
					return rr
				},
				getABTestInfo: function() {
					return xe
				},
				getAppConfig: function() {
					return we
				},
				getAppInfo: function() {
					return ge
				},
				getAppLocalStorage: function() {
					return hn
				},
				getAppUsedSeconds: function() {
					return be
				},
				getChannel: function() {
					return zr
				},
				getCurrentLocationInfo: function() {
					return Or
				},
				getGestureQuit: function() {
					return Rn
				},
				getLocalStorage: function() {
					return vn
				},
				getNativeData: function() {
					return gn
				},
				getNativeMutualData: function() {
					return bn
				},
				getRemoteConfig: function() {
					return Ee
				},
				getSecurityRequestCheckCode: function() {
					return Le
				},
				getTmpValue: function() {
					return En
				},
				getUserInfo: function() {
					return nn
				},
				getUserPhotoAuthorization: function() {
					return Ar
				},
				getUserPhotoLocations: function() {
					return Tr
				},
				handleCurDownloadGame: function() {
					return hr
				},
				hideKeyboard: function() {
					return ze
				},
				installApp: function() {
					return fr
				},
				invoke: function() {
					return He
				},
				invokeBonBeanPanel: function() {
					return Ne
				},
				isAllPushClose: function() {
					return jr
				},
				isAppInstalled: function() {
					return ur
				},
				isFocus: function() {
					return Ce
				},
				isLogin: function() {
					return an
				},
				isNotificationEnabled: function() {
					return kr
				},
				isPageShowing: function() {
					return Re
				},
				isPublishForbidden: function() {
					return Mr
				},
				launchApp: function() {
					return lr
				},
				login: function() {
					return tn
				},
				logout: function() {
					return en
				},
				multiFollow: function() {
					return Ue
				},
				navigateOrPreviewChannel: function() {
					return pe
				},
				navigateToAudioHistoryPage: function() {
					return se
				},
				navigateToNewMyFocus: function() {
					return ue
				},
				navigateToTopicPage: function() {
					return he
				},
				navigateToUserCenter: function() {
					return fe
				},
				navigateToUserPage: function() {
					return ve
				},
				navigateWithComment: function() {
					return me
				},
				navigationWithItem: function() {
					return ae
				},
				notifyAppMsg: function() {
					return Kn
				},
				onReportSuccess: function() {
					return Pe
				},
				onWebCellError: function() {
					return xr
				},
				onWebCellReady: function() {
					return br
				},
				onWebCellUIChanged: function() {
					return Sr
				},
				openApp: function() {
					return ce
				},
				openAuthorizationPage: function() {
					return Ir
				},
				openLocalPush: function() {
					return Pr
				},
				openMiniProgram: function() {
					return te
				},
				openNativeUrl: function() {
					return ye
				},
				openNewsArticle: function() {
					return Kt
				},
				openPreviewImage: function() {
					return oe
				},
				openPushHistoryPage: function() {
					return re
				},
				openPushSwitchSettingPage: function() {
					return Lr
				},
				openUrl: function() {
					return Vt
				},
				openWebViewWithType: function() {
					return Xt
				},
				pageOnBack: function() {
					return We
				},
				preloadLottie: function() {
					return Me
				},
				previewVideo: function() {
					return gr
				},
				publishOmAccountLocalDraft: function() {
					return fn
				},
				publishWeibo: function() {
					return Ie
				},
				reLogin: function() {
					return cn
				},
				readOmAccountLocalDraft: function() {
					return ln
				},
				refreshWxAccessToken: function() {
					return on
				},
				regReceiver: function() {
					return Xn
				},
				registerLifecycle: function() {
					return Hr
				},
				removeTmpValue: function() {
					return Sn
				},
				reportCellExposure: function() {
					return Jn
				},
				reportContentShow: function() {
					return ar
				},
				reportDuration: function() {
					return Qn
				},
				reportToBeacon: function() {
					return $n
				},
				reportToBoss: function() {
					return Gn
				},
				reportToDT: function() {
					return Zn
				},
				reportUserSensitive: function() {
					return Yn
				},
				requestCalendarAuthority: function() {
					return _r
				},
				saveImageToLocal: function() {
					return je
				},
				sendPageRefreshEnd: function() {
					return ir
				},
				sendPageRefreshStart: function() {
					return or
				},
				sendRequest: function() {
					return Vn
				},
				sendRequestSpeed: function() {
					return cr
				},
				setActionBtnStyle: function() {
					return Un
				},
				setAppConfig: function() {
					return Se
				},
				setAppLocalStorage: function() {
					return dn
				},
				setBottomBarVisibility: function() {
					return Wn
				},
				setClosePanelShow: function() {
					return In
				},
				setGestureQuit: function() {
					return Mn
				},
				setLeftScrollEnable: function() {
					return qn
				},
				setLocalStorage: function() {
					return mn
				},
				setNativeData: function() {
					return yn
				},
				setNativeMutualData: function() {
					return wn
				},
				setOrientationEnable: function() {
					return Fn
				},
				setReportParams: function() {
					return Hn
				},
				setReturnBtnActionType: function() {
					return zn
				},
				setReturnBtnStyle: function() {
					return Dn
				},
				setShareInfo: function() {
					return jn
				},
				setStatusBarColor: function() {
					return Cn
				},
				setTitle: function() {
					return An
				},
				setTmpValue: function() {
					return xn
				},
				share: function() {
					return kn
				},
				showAlert: function() {
					return Je
				},
				showCaptureImageShare: function() {
					return Pn
				},
				showCitySelector: function() {
					return Dr
				},
				showH5Dialog: function() {
					return Ve
				},
				showImageShare: function() {
					return On
				},
				showMedalView: function() {
					return de
				},
				showOmAccountAlert: function() {
					return sn
				},
				showShareMenu: function() {
					return _n
				},
				showTipsDialog: function() {
					return Ke
				},
				showToast: function() {
					return $e
				},
				supportLauncherShortcut: function() {
					return Qe
				},
				switchSearchTab: function() {
					return ie
				},
				syncGetAppConfig: function() {
					return ke
				},
				syncRedpacketUpdateInfo: function() {
					return Oe
				},
				traversePage: function() {
					return qe
				},
				tryAddCalendarEvent: function() {
					return Nr
				},
				unRegisterLifecycle: function() {
					return $r
				},
				updateEditStatus: function() {
					return Te
				},
				updatePubContent: function() {
					return Ae
				},
				uploadImage: function() {
					return mr
				},
				uploadLogForAiSeeById: function() {
					return Ge
				},
				writeClipboard: function() {
					return _e
				},
				writeLog: function() {
					return Bn
				}
			});
			var S, k, _, N = function() {
				function t(t) {
					var e, n = this,
						r = t || {}, o = r.timeout,
						i = r.timeoutError,
						a = void 0 === i ? {
							code: -1,
							message: "timeout"
						} : i;
					this.promise = new Promise((function(t, r) {
						n.resolve = function(n) {
							e && clearTimeout(e), t(n)
						}, n.reject = r, o && (e = setTimeout((function() {
							r(a)
						}), o))
					}))
				}
				return t
			}();
			new N,
			function(t) {
				t["SUCCESS"] = "0", t[t["SUCCESS_NUMBER"] = 0] = "SUCCESS_NUMBER", t[t["FAILED_NUMBER"] = -1] = "FAILED_NUMBER", t[t["VAR_UNDEFINED"] = 1001] = "VAR_UNDEFINED", t[t["UNSUPPORTED_API"] = 1002] = "UNSUPPORTED_API", t[t["INVALID_PARAM"] = 1003] = "INVALID_PARAM", t[t["NOT_SUPPORT"] = 1004] = "NOT_SUPPORT", t[t["UNSUPPORTED_RUNTIME"] = 1005] = "UNSUPPORTED_RUNTIME", t[t["EXECUTION_FAILED"] = 1006] = "EXECUTION_FAILED", t[t["INJECTION_TIMINIG"] = 1007] = "INJECTION_TIMINIG", t["SHOW_CAPTURE_FAILED"] = "-3000"
			}(S || (S = {})),
			function(t) {
				t["comment"] = "comment", t["data"] = "data", t["params"] = "params", t["supportType"] = "supportType", t["shareType"] = "shareType", t["omgbizid"] = "omgbizid", t["omgBizid"] = "omgBizid", t["networkStatus"] = "networkStatus", t["isWeakLogin"] = "isWeakLogin", t["isLogin"] = "isLogin"
			}(k || (k = {})),
			function(t) {
				t["getAppInfo"] = "getAppInfo", t["getConfigInfo"] = "getConfigInfo", t["appConfig"] = "appConfig", t["getABTestInfo"] = "getABTestInfo", t["appUsedSeconds"] = "appUsedSeconds", t["getRemoteConfig"] = "getRemoteConfig", t["login"] = "login", t["logout"] = "logout", t["getUserInfo"] = "getUserInfo", t["changeMainAccount"] = "changeMainAccount", t["refreshWxAccessToken"] = "refreshWxAccessToken", t["share"] = "share", t["showActionMenu"] = "showActionMenu", t["dismissShareDialog"] = "dismissShareDialog", t["setShareArticleInfo"] = "setShareArticleInfo", t["showBigImageShareMenu"] = "showBigImageShareMenu", t["showCaptureBigImageShareMenu"] = "showCaptureBigImageShareMenu", t["disableSnapshotShare"] = "disableSnapshotShare", t["enableShare"] = "enableShare", t["setNativeData"] = "setNativeData", t["getNativeData"] = "getNativeData", t["setAppLocalStorage"] = "setAppletData", t["getAppLocalStorage"] = "getAppletData", t["setLocalStorage"] = "setLocalStorage", t["getLocalStorage"] = "getLocalStorage", t["setNativeMutualData"] = "setNativeMutualData", t["getNativeMutualData"] = "getNativeMutualData", t["setTitle"] = "setTitle", t["setClosePanelShow"] = "setClosePanelShow", t["setStatusBarColor"] = "setStatusBarColor", t["setActionBtn"] = "setActionBtn", t["setReturnBtn"] = "setReturnBtn", t["setReturnBtnActionType"] = "setReturnBtnActionType", t["setGestureQuit"] = "setGestureQuit", t["getGestureQuit"] = "getGestureQuit", t["setLeftScrollEnable"] = "setLeftScrollEnable", t["setBottomBarVisibility"] = "setBottomBarVisibility", t["setOrientationEnable"] = "setOrientationEnable", t["openUrl"] = "openUrl", t["openWebViewWithType"] = "openWebViewWithType", t["showNews"] = "showNews", t["navigationWithItem"] = "navigationWithItem", t["openMiniProgram"] = "openMiniProgram", t["closeWebview"] = "closeWebview", t["closePopupView"] = "closePopupView", t["gotoPushHistoryPage"] = "gotoPushHistoryPage", t["previewDetailImage"] = "previewDetailImage", t["switchSearchTab"] = "switchSearchTab", t["writeLog"] = "writeLog", t["reportToBoss"] = "reportToBoss", t["reportCellExposure"] = "reportCellExposure", t["reportDuration"] = "reportDuration", t["setReportParams"] = "setReportParams", t["reportToDT"] = "reportToDT", t["reportUserSensitive"] = "reportUserSensitiveInfo", t["sendRequest"] = "sendRequest", t["regReceiver"] = "regReceiver", t["notifyAppMsg"] = "notifyAppMsg", t["broadcastMsg"] = "broadcastMsg", t["downloadMedia"] = "downloadMedia", t["isAppInstalled"] = "isAppInstalled", t["checkAppStatus"] = "checkAppStatus", t["launchApp"] = "launchApp", t["installApp"] = "installApp", t["downloadApp"] = "downloadApp", t["downloadApp_gdt"] = "downloadApp_gdt", t["showToast"] = "showToast", t["showAlert"] = "showAlert", t["supportLauncherShortcut"] = "supportLauncherShortcut", t["createLauncherShortcut"] = "createLauncherShortcut", t["showImageBrowser"] = "showImageBrowser", t["showH5Dialog"] = "showH5Dialog", t["createMaskShortcut"] = "createMaskShortcut", t["isNotificationEnabled"] = "isNotificationEnabled", t["requestCalendarAuthority"] = "requestCalendarAuthority", t["tryAddCalendarEvent"] = "tryAddCalendarEvent", t["isAllPushClose"] = "isAllPushClose", t["getCurrentLocationInfo"] = "getCurrentLocationInfo", t["enableLocalPush"] = "enableLocalPush", t["gotoPushSwitchSettingPage"] = "gotoPushSwitchSettingPage", t["getUserPhotoLocations"] = "getUserPhotoLocations", t["userPhotoAuthorization"] = "userPhotoAuthorization", t["jumpToSysAuthorizationPage"] = "jumpToSysAuthorizationPage", t["gotoSysPushSettingPage"] = "gotoSysPushSettingPage", t["checkCalendarAuthority"] = "checkCalendarAuthority", t["uploadImage"] = "uploadImage", t["chooseMedia"] = "chooseMedia", t["editVideo"] = "editVideo", t["previewVideo"] = "previewVideo", t["chooseVideoCover"] = "chooseVideoCover", t["copy"] = "copy", t["invokeBonBeanPanel"] = "invokeBonBeanPanel", t["saveImageToLocal"] = "saveImageToLocal", t["syncRedpacketUpdateInfo"] = "syncRedpacketUpdateInfo", t["onReportSuccess"] = "onReportSuccess", t["getSecurityRequestCheckCode"] = "getSecurityRequestCheckCode", t["isFocus"] = "isFocus", t["updateEditStatus"] = "updateEditStatus", t["deleteFavoriteItems"] = "deleteFavoriteItems", t["onWebCellReady"] = "onWebCellReady", t["onWebCellError"] = "onWebCellError", t["adjustWebViewInfo"] = "adjustWebViewInfo", t["onWebCellUIChanged"] = "onWebCellUIChanged", t["updatePubContent"] = "updatePubContent", t["publishWeibo"] = "publishWeibo", t["multiFollow"] = "multiFollow", t["reportToBeacon"] = "reportToBeacon", t["reLogin"] = "reLogin", t["enableLogin"] = "enableLogin", t["showOmAccountAlert"] = "showOmAccountAlert", t["readOmAccountLocalDraft"] = "readOmAccountLocalDraft", t["publishOmAccountLocalDraft"] = "publishOmAccountArticleFromLocalDraft", t["deleteOmAccountLocalDraft"] = "deleteOmAccountLocalDraft", t["broadcastEvent"] = "broadcastEvent", t["setAppConfig"] = "setAppConfig", t["hideKeyboard"] = "hideKeyboard", t["preloadLottie"] = "preloadLottie", t["fetchLocalData"] = "fetchLocalData", t["setTmpObj"] = "setTmpObj", t["getTmpObj"] = "getTmpObj", t["isPageShowing"] = "isPageShowing", t["traversePage"] = "traversePage", t["pageRefreshStart"] = "pageRefreshStart", t["pageRefreshEnd"] = "pageRefreshEnd", t["hippyHasShowContent"] = "hippyHasShowContent", t["openApp"] = "openApp", t["handleCurDownloadGame"] = "handleCurDownloadGame", t["sendRequestSpeed"] = "sendRequestSpeed", t["navigateToNewMyFocus"] = "navigateToNewMyFocus", t["navigateToAudioHistoryPage"] = "navigateToAudioHistoryPage", t["closePage"] = "closePage", t["changeCityChannel"] = "changeCityChannel", t["showCitySelector"] = "showCitySelector", t["queryChannelInfo"] = "queryChannelInfo", t["navigateToUserCenter"] = "navigateToUserCenter", t["navigateOrPreviewChannel"] = "navigateOrPreviewChannel", t["showTipsDialog"] = "showTipsDialog", t["pageOnBack"] = "pageOnBack", t["showMedalView"] = "showMedalView", t["navigateToTopicPage"] = "navigateToTopicPage", t["navigateWithComment"] = "navigateWithComment", t["navigateToUserPage"] = "navigateToUserPage", t["openNativeUrl"] = "openNativeUrl", t["removeTmpObj"] = "removeTmpObj", t["isPublishForbidden"] = "isPublishForbidden", t["addChatHistory"] = "addChatHistory", t["encryptAiseeUrlParams"] = "encryptAiseeUrlParams", t["uploadLogForAiSeeById"] = "uploadLogForAiSeeById"
			}(_ || (_ = {}));
			var j, O = "QNBridge",
				P = "callMethod";
			(function(t) {
				t["WebType"] = "1", t["EntryIdType"] = "3", t["BonBonType"] = "9", t["TxShiPinType"] = "19", t["FullScreenType"] = "100"
			})(j || (j = {}));
			var L, T, A, I = {
				ContraryStatus: ["0", "1"],
				AppDownLoadStatus: ["0", "1", "2"],
				ContraryNumStatus: [0, 1],
				StyleNumStatus: [0, 1, 2],
				OnReportSuccessType: [0, 1, 2, 3],
				UpdatePubContentStatus: [1, 2],
				FontSizeLevel: [1, 2, 3, 4],
				NetWorkTypes: ["0", "1", "2"],
				NetWorkStatus: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
				AppStatus: [769, 774, 772],
				ShareDestination: ["wechatFriend", "qqFriend", "qzone", "weibo", "moments"],
				LoginType: ["all", "qqorweixin", "qq", "weixin", "phone", "huawei"],
				FileType: ["pic", "video", "all"],
				BooleanStatus: [!1, !0],
				RequestMethod: ["GET", "POST"],
				MediaType: ["image", "video", "audio"],
				ThemeType: ["default", "night"],
				WebviewType: ["1", "3", "9", "19", "100"],
				ContextRelatedType: ["none", "1", "2"],
				LocalDataActionType: ["audio", "favorite", "history"]
			}, C = function() {
				try {
					return !!Hippy && !! __ISHIPPY__
				} catch (t) {
					return !1
				}
			}, U = function() {
				var t;
				try {
					return C() && "ios" === (null === (t = null === Hippy || void 0 === Hippy ? void 0 : Hippy.device) || void 0 === t ? void 0 : t.platform.OS)
				} catch (e) {
					return !1
				}
			}, D = function() {
				var t;
				try {
					return C() && "android" === (null === (t = null === Hippy || void 0 === Hippy ? void 0 : Hippy.device) || void 0 === t ? void 0 : t.platform.OS)
				} catch (e) {
					return !1
				}
			}, z = function() {
				return Promise.reject({
					errCode: S.UNSUPPORTED_API,
					errStr: vt
				})
			}, M = function() {
				return Promise.reject({
					errCode: S.UNSUPPORTED_API,
					errStr: yt
				})
			}, R = function() {
				return Promise.reject({
					errCode: S.UNSUPPORTED_API,
					errStr: dt
				})
			}, q = function() {
				return b() || U() ? R : void 0
			}, W = function() {
				return Promise.reject({
					errCode: S.UNSUPPORTED_API,
					errStr: ht
				})
			}, F = function() {
				return x() || D() ? W : void 0
			}, B = function() {
				return Promise.reject({
					errCode: S.UNSUPPORTED_API,
					errStr: mt
				})
			}, G = function() {
				return {
					errCode: S.UNSUPPORTED_API,
					errStr: gt
				}
			}, H = function(t) {
				return C() ? At() < t : Tt() < t
			}, $ = function(t) {
				return H(t) ? B : void 0
			}, J = function(t, e) {
				return t && H(e) ? B : void 0
			}, Q = function(t, e) {
				var n = {}, r = function(t) {
					return t.replace(/([a-z])([A-Z])/g, "$1_$2").toLowerCase()
				};
				return Object.entries(t).forEach((function(t) {
					var o = p(t, 2),
						i = o[0],
						a = o[1];
					e.includes(i) ? n[r(i)] = a : n[i] = a
				})), n
			}, Z = function(t) {
				var e = t.data;
				if (!e) return e;
				var n = {};
				return Object.entries(e).forEach((function(t) {
					var e = p(t, 2),
						r = e[0],
						o = e[1];
					if (r in lt) {
						var i = function() {
							var t;
							n[null === (t = lt[r]) || void 0 === t ? void 0 : t.targetValue] = o
						}, a = function() {
							var t, e;
							K(null === (t = lt[r]) || void 0 === t ? void 0 : t.targetValue) ? n[r] = null !== (e = lt[r].targetValue[o]) && void 0 !== e ? e : o : n[r] = lt[r].targetValue
						};
						lt[r].isValue ? a() : i()
					} else n[r] = o
				})), n
			}, Y = function(t) {
				return null === t || void 0 === t
			}, V = function(t) {
				if (Y(t)) return !1;
				if ("boolean" === typeof t) return t;
				if ("number" === typeof t) return 0 !== t;
				var e = "".concat(t);
				return !!e && !["0", "false"].includes(e)
			}, X = function(t) {
				if ("string" !== typeof t) return t;
				try {
					return JSON.parse(t)
				} catch (L) {
					return t
				}
			}, K = function(t) {
				return "[object Object]" === String(t)
			}, tt = function(t) {
				var e = t.param,
					n = t.key,
					r = t.condition;
				return !C() && !r || !e || !n
			}, et = function(t) {
				var e = t.param,
					n = t.key,
					r = t.condition;
				if (tt({
					param: e,
					key: n,
					condition: r
				})) return e;
				var o = e;
				return (null === e || void 0 === e ? void 0 : e[n]) && (o[n] = JSON.stringify(e[n])), o
			}, nt = function(t, e) {
				return C() ? e : t
			}, rt = function() {
				return "undefined" !== typeof window
			}, ot = "Net-None",
				at = "wifi",
				ct = "gsm",
				ut = (o = {}, f(o, ot, "0"), f(o, at, "1"), f(o, ct, "2"), o),
				st = {
					0: !1,
					1: !0
				}, lt = (i = {}, f(i, k.networkStatus, {
					targetValue: ut,
					isValue: !0
				}), f(i, k.omgbizid, {
					targetValue: k.omgBizid,
					isValue: !1
				}), f(i, k.isWeakLogin, {
					targetValue: st,
					isValue: !0
				}), f(i, k.isLogin, {
					targetValue: st,
					isValue: !0
				}), i),
				ft = "当前非腾讯新闻客户端环境",
				pt = "JSAPI调用异常",
				dt = "iOS暂未支持",
				ht = "Android暂未支持",
				mt = "当前版本暂不支持",
				vt = "Hippy暂未支持",
				yt = "Web暂未支持",
				gt = "JSI暂未支持",
				wt = "https://mat1.gtimg.com/www/js/newsapp/jsapi/news.js?_tsid=1",
				bt = "https://mat1.gtimg.com/qqcdn/news-h5/iframe-communicate-sdk.js",
				xt = 800,
				Et = {
					disableCaptureImageShare: 6160,
					createMaskShortcut: 6290,
					setClosePanelShow: 6290,
					getUserPhotoLocations: 6350,
					getUserPhotoAuthorization: 6350,
					openAuthorizationPage: 6350,
					reportDT: 6550,
					queryChannelInfo: 6607,
					multiFollow: 6670,
					navigateToNewMyFocus: 6697,
					omAccountVersion: 6797,
					isPublishForbidden: 6857,
					addChatHistory: 6867,
					encryptAiseeUrlParams: 6877,
					uploadLogForAiSeeById: 6887
				}, St = {
					errCode: S.SUCCESS_NUMBER,
					errStr: ""
				}, kt = function(t, e) {
					var n = document.createElement("script");
					n.type = "text/javascript", n.async = !0, n.onload = function() {
						e()
					}, n.src = t;
					var r = document.getElementsByTagName("script")[0];
					r.parentNode && r.parentNode.insertBefore(n, r)
				}, _t = function(t) {
					var e, n = null === document || void 0 === document ? void 0 : document.getElementsByTagName("script");
					if (!n || 0 === (null === n || void 0 === n ? void 0 : n.length)) return !1;
					var r, o = l(n);
					try {
						for (o.s(); !(r = o.n()).done;) {
							var i = r.value;
							if (-1 !== (null === (e = null === i || void 0 === i ? void 0 : i.src) || void 0 === e ? void 0 : e.indexOf(t))) return !0
						}
					} catch (a) {
						o.e(a)
					} finally {
						o.f()
					}
					return !1
				}, Nt = function(t) {
					(null === window || void 0 === window ? void 0 : window.TencentNews) ? t.resolve() : kt(wt, (function() {
						t.resolve()
					}))
				}, jt = function(t) {
					(null === window || void 0 === window ? void 0 : window.TencentNews) ? t.resolve() : (null === document || void 0 === document || document.addEventListener("TencentNewsJSInjectionComplete", (function() {
						t.resolve()
					})), setTimeout((function() {
						t.resolve()
					}), xt))
				}, Ot = function(t) {
					x() && E() ? Nt(t) : b() && E() ? jt(t) : setTimeout((function() {
						t.resolve()
					}), xt)
				}, Pt = function(t) {
					Ot(t)
				}, Lt = function(t) {
					var e = t.eventName,
						n = t.param,
						r = function() {
							var t, r, o = n;
							o && (o.url = null === window || void 0 === window ? void 0 : window.location.href), null === (r = null === (t = null === window || void 0 === window ? void 0 : window.iframeCommunicateSDK) || void 0 === t ? void 0 : t.iframeInvoke) || void 0 === r || r.call(t, {
								eventName: e,
								paramTemp: o
							})
						};
					_t(bt) ? r() : kt(bt, (function() {
						r()
					}))
				}, Tt = function() {
					if (!rt()) return 0;
					var t = window.navigator.userAgent.toLowerCase(),
						e = t.match(/qqnews\/([\d.]+)/),
						n = null === e || void 0 === e ? void 0 : e[1],
						r = Number(n ? n.replace(/\./g, "") : 0);
					return r
				}, At = function() {
					var t;
					try {
						var e = null === (t = null === __HIPPYNATIVEGLOBAL__ || void 0 === __HIPPYNATIVEGLOBAL__ ? void 0 : __HIPPYNATIVEGLOBAL__.Platform) || void 0 === t ? void 0 : t.VersionName;
						if (!e) return -1;
						var n = parseInt(e.replace(/[^0-9]/gi, ""), 10);
						return isNaN(n) ? -1 : n
					} catch (r) {
						return -1
					}
				}, It = {
					hippy: z
				}, Ct = {
					web: M
				}, Ut = (a = {}, f(a, _.appUsedSeconds, It), f(a, _.supportLauncherShortcut, It), f(a, _.createLauncherShortcut, It), f(a, _.showH5Dialog, It), f(a, _.downloadApp, It), f(a, _.uploadImage, It), f(a, _.chooseMedia, It), f(a, _.editVideo, It), f(a, _.chooseVideoCover, It), f(a, _.showNews, It), f(a, _.openMiniProgram, It), f(a, _.closeWebview, It), f(a, _.closePopupView, It), f(a, _.gotoPushHistoryPage, It), f(a, _.switchSearchTab, It), f(a, _.regReceiver, It), f(a, _.notifyAppMsg, It), f(a, _.setClosePanelShow, It), f(a, _.setReturnBtn, It), f(a, _.setReturnBtnActionType, It), f(a, _.requestCalendarAuthority, It), f(a, _.tryAddCalendarEvent, It), f(a, _.getCurrentLocationInfo, It), f(a, _.getUserPhotoLocations, It), f(a, _.userPhotoAuthorization, It), f(a, _.checkCalendarAuthority, It), f(a, _.setReportParams, It), f(a, _.changeMainAccount, It), f(a, _.showBigImageShareMenu, It), f(a, _.showCaptureBigImageShareMenu, It), f(a, _.disableSnapshotShare, It), f(a, _.setAppLocalStorage, It), f(a, _.getAppLocalStorage, It), f(a, _.setLocalStorage, It), f(a, _.getLocalStorage, It), f(a, _.copy, It), f(a, _.invokeBonBeanPanel, It), f(a, _.saveImageToLocal, It), f(a, _.syncRedpacketUpdateInfo, It), f(a, _.onReportSuccess, It), f(a, _.getSecurityRequestCheckCode, It), f(a, _.onWebCellReady, It), f(a, _.onWebCellError, It), f(a, _.adjustWebViewInfo, It), f(a, _.onWebCellUIChanged, It), f(a, _.reportUserSensitive, It), a),
				Dt = (c = {}, f(c, _.updateEditStatus, Ct), f(c, _.updatePubContent, Ct), f(c, _.deleteFavoriteItems, Ct), f(c, _.getABTestInfo, Ct), f(c, _.getRemoteConfig, Ct), f(c, _.enableShare, Ct), f(c, _.setNativeData, Ct), f(c, _.getNativeData, Ct), f(c, _.setNativeMutualData, Ct), f(c, _.getNativeMutualData, Ct), f(c, _.navigationWithItem, Ct), f(c, _.reportCellExposure, Ct), f(c, _.reportDuration, Ct), f(c, _.reLogin, Ct), f(c, _.enableLogin, Ct), f(c, _.broadcastEvent, Ct), f(c, _.setAppConfig, Ct), f(c, _.preloadLottie, Ct), f(c, _.fetchLocalData, Ct), f(c, _.setTmpObj, Ct), f(c, _.getTmpObj, Ct), f(c, _.isPageShowing, Ct), f(c, _.traversePage, Ct), f(c, _.pageRefreshStart, Ct), f(c, _.pageRefreshEnd, Ct), f(c, _.hippyHasShowContent, Ct), f(c, _.sendRequestSpeed, Ct), f(c, _.navigateToAudioHistoryPage, Ct), f(c, _.closePage, Ct), f(c, _.changeCityChannel, Ct), f(c, _.showCitySelector, Ct), f(c, _.navigateToUserCenter, Ct), f(c, _.navigateOrPreviewChannel, Ct), f(c, _.showTipsDialog, Ct), f(c, _.pageOnBack, Ct), f(c, _.showMedalView, Ct), f(c, _.navigateToTopicPage, Ct), f(c, _.navigateWithComment, Ct), f(c, _.navigateToUserPage, Ct), f(c, _.removeTmpObj, Ct), c),
				zt = Object.assign(Object.assign(Object.assign({}, Ut), Dt), (u = {}, f(u, _.setAppLocalStorage, {
					web: q()
				}), f(u, _.checkAppStatus, {
					web: q(),
					hippy: z
				}), f(u, _.launchApp, {
					web: q(),
					hippy: z
				}), f(u, _.installApp, {
					web: q(),
					hippy: z
				}), f(u, _.downloadApp_gdt, {
					web: q(),
					hippy: z
				}), f(u, _.downloadApp_gdt, {
					web: null !== (L = q()) && void 0 !== L ? L : $(Et.createMaskShortcut)
				}), f(u, _.setClosePanelShow, {
					web: null !== (T = q()) && void 0 !== T ? T : $(Et.setClosePanelShow)
				}), f(u, _.getUserPhotoLocations, {
					web: $(Et.getUserPhotoLocations)
				}), f(u, _.userPhotoAuthorization, {
					web: $(Et.getUserPhotoAuthorization)
				}), f(u, _.jumpToSysAuthorizationPage, {
					web: $(Et.openAuthorizationPage)
				}), f(u, _.disableSnapshotShare, {
					web: null !== (A = F()) && void 0 !== A ? A : $(Et.disableCaptureImageShare)
				}), f(u, _.multiFollow, {
					web: $(Et.multiFollow),
					hippy: z
				}), f(u, _.hideKeyboard, {
					web: M,
					hippy: F()
				}), f(u, _.openApp, {
					web: q(),
					hippy: q()
				}), f(u, _.navigateToNewMyFocus, {
					web: M,
					hippy: $(Et.navigateToNewMyFocus)
				}), f(u, _.queryChannelInfo, {
					web: M,
					hippy: $(Et.queryChannelInfo)
				}), f(u, _.reportToDT, {
					web: J(E() && x(), Et.reportDT),
					hippy: z
				}), f(u, _.showOmAccountAlert, {
					web: $(Et.omAccountVersion),
					hippy: z
				}), f(u, _.readOmAccountLocalDraft, {
					web: $(Et.omAccountVersion),
					hippy: z
				}), f(u, _.publishOmAccountLocalDraft, {
					web: $(Et.omAccountVersion),
					hippy: z
				}), f(u, _.deleteOmAccountLocalDraft, {
					web: $(Et.omAccountVersion),
					hippy: z
				}), f(u, _.isPublishForbidden, {
					web: $(Et.isPublishForbidden),
					hippy: z
				}), f(u, _.addChatHistory, {
					web: $(Et.addChatHistory),
					hippy: z
				}), f(u, _.encryptAiseeUrlParams, {
					web: $(Et.encryptAiseeUrlParams),
					hippy: z
				}), f(u, _.uploadLogForAiSeeById, {
					web: $(Et.uploadLogForAiSeeById),
					hippy: z
				}), u)),
				Mt = f({}, _.adjustWebViewInfo, {
					invoke: Lt
				}),
				Rt = function(t) {
					var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
					return new Promise((function(n, r) {
						var o;
						try {
							null === (o = null === Hippy || void 0 === Hippy ? void 0 : Hippy.bridge) || void 0 === o || o.callNativeWithPromise(O, P, Object.assign(Object.assign({}, e), {
								methodName: t
							})).then((function(t) {
								n(X(t))
							}), (function() {
								r({
									errCode: S.INJECTION_TIMINIG,
									errStr: ""
								})
							}))
						} catch (i) {
							r({
								errCode: S.INJECTION_TIMINIG,
								errStr: i.message
							})
						}
					}))
				}, qt = {
					invoke: function(t) {
						var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, n = arguments.length > 2 ? arguments[2] : void 0;
						return g(this, void 0, void 0, s().mark((function r() {
							var o, i;
							return s().wrap((function(r) {
								while (1) switch (r.prev = r.next) {
									case 0:
										return E() || n({
											errCode: S.INJECTION_TIMINIG,
											errStr: ft
										}), r.prev = 1, r.next = 4, Jt();
									case 4:
										o = TencentNews, i = o.invoke, i(t, e), r.next = 11;
										break;
									case 8:
										r.prev = 8, r.t0 = r["catch"](1), n({
											errCode: S.INJECTION_TIMINIG,
											errStr: pt
										});
									case 11:
									case "end":
										return r.stop()
								}
							}), r, null, [
								[1, 8]
							])
						})))
					}
				}, Wt = function(t) {
					var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
					return new Promise((function(n, r) {
						qt.invoke(t, Object.assign(Object.assign({}, e), {
							onCallback: function(t) {
								var e = t.errCode;
								String(e) !== S.SUCCESS ? r(t) : n(t)
							}
						}), r)
					}))
				}, Ft = function(t) {
					var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null;
					e ? window.TencentNews.invoke(t, e) : window.TencentNews.invoke(t)
				}, Bt = function(t) {
					var e, n = t.eventName,
						r = t.param,
						o = void 0 === r ? null : r;
					if (null === (e = Mt[n]) || void 0 === e ? void 0 : e.invoke) return Mt[n].invoke({
						eventName: n,
						param: o
					})
				}, Gt = function(t) {
					var e, n, r = t.eventName,
						o = t.param,
						i = void 0 === o ? {} : o;
					if (null === (e = zt[r]) || void 0 === e ? void 0 : e.web) return zt[r].web();
					if (!i) return Promise.reject({
						errCode: S.INVALID_PARAM,
						errStr: ""
					});
					if (x() && (null === (n = null === window || void 0 === window ? void 0 : window.DtJsReporter) || void 0 === n ? void 0 : n.reportEvent)) {
						var a = i;
						a.eventId = i.eventID, window.DtJsReporter.reportEvent(a);
						var c = {
							errCode: S.SUCCESS,
							errStr: ""
						};
						return Promise.resolve(c)
					}
					return Wt(r, i)
				}, Ht = new N;
			Pt(Ht);
			var $t, Jt = function() {
				return Ht.promise
			}, Qt = function(t) {
				var e, n, r = t.eventName,
					o = t.param,
					i = void 0 === o ? {} : o;
				return C() ? (null === (e = zt[r]) || void 0 === e ? void 0 : e.hippy) ? zt[r].hippy() : Rt(r, i) : (null === (n = zt[r]) || void 0 === n ? void 0 : n.web) ? zt[r].web() : Wt(r, i)
			}, Zt = function(t) {
				var e = t.eventName;
				if (C()) try {
					return getTurboModule(e)
				} catch (n) {
					return G()
				}
				return G()
			}, Yt = function(t) {
				var e = t.eventName,
					n = t.param,
					r = void 0 === n ? null : n;
				return g(void 0, void 0, void 0, s().mark((function t() {
					var n;
					return s().wrap((function(t) {
						while (1) switch (t.prev = t.next) {
							case 0:
								if (!E()) {
									t.next = 6;
									break
								}
								return t.next = 3, Jt();
							case 3:
								rt() && (null === (n = window.TencentNews) || void 0 === n ? void 0 : n[e]) && Ft(e, r), t.next = 7;
								break;
							case 6:
								C() || Bt({
									eventName: e,
									param: r
								});
							case 7:
							case "end":
								return t.stop()
						}
					}), t)
				})))
			}, Vt = function(t) {
				return Qt({
					eventName: _.openUrl,
					param: t
				})
			}, Xt = function(t) {
				var e = t.type,
					n = void 0 === e ? j.WebType : e,
					r = t.url;
				return Qt({
					eventName: _.openWebViewWithType,
					param: {
						url: r,
						type: n
					}
				})
			}, Kt = function(t) {
				return Qt({
					eventName: _.showNews,
					param: t
				})
			}, te = function(t) {
				return Qt({
					eventName: _.openMiniProgram,
					param: t
				})
			}, ee = function() {
				return Qt({
					eventName: _.closeWebview
				})
			}, ne = function() {
				return Qt({
					eventName: _.closePopupView
				})
			}, re = function() {
				return Qt({
					eventName: _.gotoPushHistoryPage
				})
			}, oe = function(t) {
				return Qt({
					eventName: _.previewDetailImage,
					param: t
				})
			}, ie = function(t) {
				return Qt({
					eventName: _.switchSearchTab,
					param: t
				})
			}, ae = function(t) {
				return Qt({
					eventName: _.navigationWithItem,
					param: t
				})
			}, ce = function(t) {
				return Qt({
					eventName: _.openApp,
					param: t
				})
			}, ue = function(t) {
				return Qt({
					eventName: _.navigateToNewMyFocus,
					param: t
				})
			}, se = function() {
				return Qt({
					eventName: _.navigateToAudioHistoryPage
				})
			}, le = function(t) {
				return Qt({
					eventName: _.closePage,
					param: t
				})
			}, fe = function(t) {
				return Qt({
					eventName: _.navigateToUserCenter,
					param: t
				})
			}, pe = function(t) {
				return Qt({
					eventName: _.navigateOrPreviewChannel,
					param: t
				})
			}, de = function(t) {
				return Qt({
					eventName: _.showMedalView,
					param: t
				})
			}, he = function(t) {
				return Qt({
					eventName: _.navigateToTopicPage,
					param: t
				})
			}, me = function(t) {
				return Qt({
					eventName: _.navigateWithComment,
					param: et({
						param: t,
						key: k.comment
					})
				})
			}, ve = function(t) {
				return Qt({
					eventName: _.navigateToUserPage,
					param: t
				})
			}, ye = function(t) {
				return Qt({
					eventName: _.openNativeUrl,
					param: t
				})
			}, ge = function() {
				return new Promise((function(t, e) {
					Qt({
						eventName: _.getAppInfo
					}).then((function(e) {
						var n = Z({
							data: e
						});
						t(n)
					}), (function(t) {
						e(t)
					}))
				}))
			}, we = function() {
				return new Promise((function(t, e) {
					Qt({
						eventName: _.getConfigInfo
					}).then((function(e) {
						var n = Z({
							data: e
						});
						t(n)
					}), (function(t) {
						e(t)
					}))
				}))
			}, be = function() {
				return Qt({
					eventName: _.appUsedSeconds
				})
			}, xe = function() {
				return Qt({
					eventName: _.getABTestInfo
				})
			}, Ee = function(t) {
				return Qt({
					eventName: _.getRemoteConfig,
					param: t
				})
			}, Se = function(t) {
				return Qt({
					eventName: _.setAppConfig,
					param: t
				})
			}, ke = function() {
				var t = Zt({
					eventName: _.appConfig
				}),
					e = Z({
						data: t
					});
				return e
			}, _e = function(t) {
				return Qt({
					eventName: _.copy,
					param: t
				})
			}, Ne = function(t) {
				return Qt({
					eventName: _.invokeBonBeanPanel,
					param: t
				})
			}, je = function(t) {
				return Qt({
					eventName: _.saveImageToLocal,
					param: t
				})
			}, Oe = function(t) {
				return Qt({
					eventName: _.syncRedpacketUpdateInfo,
					param: t
				})
			}, Pe = function(t) {
				return Qt({
					eventName: _.onReportSuccess,
					param: t
				})
			}, Le = function(t) {
				return Qt({
					eventName: _.getSecurityRequestCheckCode,
					param: t
				})
			}, Te = function(t) {
				return Qt({
					eventName: _.updateEditStatus,
					param: t
				})
			}, Ae = function(t) {
				return Qt({
					eventName: _.updatePubContent,
					param: t
				})
			}, Ie = function(t) {
				return Qt({
					eventName: _.publishWeibo,
					param: t
				})
			}, Ce = function(t) {
				return Qt({
					eventName: _.isFocus,
					param: t
				})
			}, Ue = function(t) {
				return Qt({
					eventName: _.multiFollow,
					param: t
				})
			}, De = function(t) {
				return Qt({
					eventName: _.deleteFavoriteItems,
					param: t
				})
			}, ze = function() {
				return Qt({
					eventName: _.hideKeyboard
				})
			}, Me = function(t) {
				return Qt({
					eventName: _.preloadLottie,
					param: t
				})
			}, Re = function(t) {
				return Qt({
					eventName: _.isPageShowing,
					param: t
				})
			}, qe = function(t) {
				return Qt({
					eventName: _.traversePage,
					param: t
				})
			}, We = function(t) {
				return Qt({
					eventName: _.pageOnBack,
					param: t
				})
			}, Fe = function(t) {
				return Qt({
					eventName: _.addChatHistory,
					param: t
				})
			}, Be = function(t) {
				return Qt({
					eventName: _.encryptAiseeUrlParams,
					param: t
				})
			}, Ge = function(t) {
				return Qt({
					eventName: _.uploadLogForAiSeeById,
					param: t
				})
			}, He = function(t) {
				var e;
				return Qt({
					eventName: t.eventName,
					param: null !== (e = t.eventObject) && void 0 !== e ? e : {}
				})
			}, $e = function(t) {
				return Qt({
					eventName: _.showToast,
					param: t
				})
			}, Je = function(t) {
				return Qt({
					eventName: _.showAlert,
					param: t
				})
			}, Qe = function() {
				return Qt({
					eventName: _.supportLauncherShortcut
				})
			}, Ze = function(t) {
				return Qt({
					eventName: _.createLauncherShortcut,
					param: t
				})
			}, Ye = function(t) {
				return Qt({
					eventName: _.showImageBrowser,
					param: t
				})
			}, Ve = function(t) {
				return Qt({
					eventName: _.showH5Dialog,
					param: t
				})
			}, Xe = function(t) {
				return Qt({
					eventName: _.createMaskShortcut,
					param: t
				})
			}, Ke = function(t) {
				return Qt({
					eventName: _.showTipsDialog,
					param: {
						content: {
							url: t.content.url,
							w: t.content.width,
							h: t.content.height
						}
					}
				})
			}, tn = function(t) {
				return new Promise((function(e, n) {
					Qt({
						eventName: _.login,
						param: et({
							param: t,
							key: k.supportType
						})
					}).then((function(t) {
						var n = Z({
							data: t
						});
						e(n)
					}), (function(t) {
						n(t)
					}))
				}))
			}, en = function(t) {
				return Qt({
					eventName: _.logout,
					param: t
				})
			}, nn = function() {
				return new Promise((function(t, e) {
					Qt({
						eventName: _.getUserInfo,
						param: {
							ignorePhone: !1
						}
					}).then((function(e) {
						var n = Z({
							data: e
						});
						t(n)
					}), (function(t) {
						e(t)
					}))
				}))
			}, rn = function(t) {
				return Qt({
					eventName: _.changeMainAccount,
					param: t
				})
			}, on = function() {
				return Qt({
					eventName: _.refreshWxAccessToken
				})
			}, an = function() {
				return new Promise((function(t, e) {
					Qt({
						eventName: _.getUserInfo,
						param: {
							ignorePhone: !1
						}
					}).then((function(e) {
						var n = !! (null === e || void 0 === e ? void 0 : e[null === e || void 0 === e ? void 0 : e.account]),
							r = null === e || void 0 === e ? void 0 : e.isWeakLogin,
							o = Object.assign({
								hasLogin: n && V(!r)
							}, St);
						t(o)
					}), (function(t) {
						e(t)
					}))
				}))
			}, cn = function(t) {
				return Qt({
					eventName: _.reLogin,
					param: t
				})
			}, un = function(t) {
				return Qt({
					eventName: _.enableLogin,
					param: t
				})
			}, sn = function(t) {
				return Qt({
					eventName: _.showOmAccountAlert,
					param: t
				})
			}, ln = function() {
				return Qt({
					eventName: _.readOmAccountLocalDraft
				})
			}, fn = function() {
				return Qt({
					eventName: _.publishOmAccountLocalDraft
				})
			}, pn = function() {
				return Qt({
					eventName: _.deleteOmAccountLocalDraft
				})
			}, dn = function(t) {
				return Qt({
					eventName: _.setAppLocalStorage,
					param: t
				})
			}, hn = function(t) {
				return Qt({
					eventName: _.getAppLocalStorage,
					param: t
				})
			}, mn = function(t) {
				return Qt({
					eventName: _.setLocalStorage,
					param: t
				})
			}, vn = function(t) {
				return Qt({
					eventName: _.getLocalStorage,
					param: t
				})
			}, yn = function(t) {
				return Qt({
					eventName: _.setNativeData,
					param: t
				})
			}, gn = function(t) {
				return Qt({
					eventName: _.getNativeData,
					param: t
				})
			}, wn = function(t) {
				return Qt({
					eventName: _.setNativeMutualData,
					param: t
				})
			}, bn = function(t) {
				return Qt({
					eventName: _.getNativeData,
					param: t
				})
			}, xn = function(t) {
				return Qt({
					eventName: _.setTmpObj,
					param: t
				})
			}, En = function(t) {
				return Qt({
					eventName: _.getTmpObj,
					param: t
				})
			}, Sn = function(t) {
				return Qt({
					eventName: _.removeTmpObj,
					param: t
				})
			}, kn = function(t) {
				return Qt({
					eventName: _.share,
					param: t
				})
			}, _n = function() {
				return Qt({
					eventName: _.showActionMenu
				})
			}, Nn = function() {
				return Qt({
					eventName: _.dismissShareDialog
				})
			}, jn = function(t) {
				return Qt({
					eventName: _.setShareArticleInfo,
					param: t
				})
			}, On = function(t) {
				return Qt({
					eventName: _.showBigImageShareMenu,
					param: Q(t, [k.shareType])
				})
			}, Pn = function(t) {
				return Qt({
					eventName: _.showCaptureBigImageShareMenu,
					param: t
				})
			}, Ln = function() {
				return Qt({
					eventName: _.disableSnapshotShare
				})
			}, Tn = function(t) {
				return Qt({
					eventName: _.enableShare,
					param: t
				})
			}, An = function(t) {
				return Qt({
					eventName: _.setTitle,
					param: t
				})
			}, In = function(t) {
				return Qt({
					eventName: _.setClosePanelShow,
					param: t
				})
			}, Cn = function(t) {
				return Qt({
					eventName: _.setStatusBarColor,
					param: t
				})
			}, Un = function(t) {
				return Qt({
					eventName: _.setActionBtn,
					param: t
				})
			}, Dn = function(t) {
				return Qt({
					eventName: _.setReturnBtn,
					param: t
				})
			}, zn = function(t) {
				return Qt({
					eventName: _.setReturnBtnActionType,
					param: t
				})
			}, Mn = function(t) {
				return Qt({
					eventName: _.setGestureQuit,
					param: t
				})
			}, Rn = function(t) {
				return Qt({
					eventName: _.getGestureQuit,
					param: t
				})
			}, qn = function(t) {
				return Qt({
					eventName: _.setLeftScrollEnable,
					param: t
				})
			}, Wn = function(t) {
				return Qt({
					eventName: _.setBottomBarVisibility,
					param: t
				})
			}, Fn = function(t) {
				return Qt({
					eventName: _.setOrientationEnable,
					param: t
				})
			}, Bn = function(t) {
				return Qt({
					eventName: _.writeLog,
					param: t
				})
			}, Gn = function(t) {
				return Qt({
					eventName: _.reportToBoss,
					param: et({
						param: t,
						key: k.params
					})
				})
			}, Hn = function(t) {
				return Qt({
					eventName: _.setReportParams,
					param: t
				})
			}, $n = function(t) {
				return Qt({
					eventName: _.reportToBeacon,
					param: et({
						param: t,
						key: k.params,
						condition: b()
					})
				})
			}, Jn = function(t) {
				return Qt({
					eventName: _.reportCellExposure,
					param: t
				})
			}, Qn = function(t) {
				return Qt({
					eventName: _.reportDuration,
					param: t
				})
			}, Zn = function(t) {
				return Gt({
					eventName: _.reportToDT,
					param: t
				})
			}, Yn = function(t) {
				return Qt({
					eventName: _.reportUserSensitive,
					param: t
				})
			}, Vn = function(t) {
				return Qt({
					eventName: _.sendRequest,
					param: et({
						param: t,
						key: k.data
					})
				})
			}, Xn = function() {
				return Qt({
					eventName: _.regReceiver
				})
			}, Kn = function(t) {
				return Qt({
					eventName: _.notifyAppMsg,
					param: t
				})
			}, tr = function(t) {
				return Qt({
					eventName: _.broadcastMsg,
					param: t
				})
			}, er = function(t) {
				return Qt({
					eventName: _.downloadMedia,
					param: t
				})
			}, nr = function(t) {
				return Qt({
					eventName: _.broadcastEvent,
					param: t
				})
			}, rr = function(t) {
				return Qt({
					eventName: _.fetchLocalData,
					param: t
				})
			}, or = function(t) {
				return Qt({
					eventName: _.pageRefreshStart,
					param: t
				})
			}, ir = function(t) {
				return Qt({
					eventName: _.pageRefreshEnd,
					param: t
				})
			}, ar = function(t) {
				return Qt({
					eventName: _.hippyHasShowContent,
					param: t
				})
			}, cr = function(t) {
				return Qt({
					eventName: _.sendRequestSpeed,
					param: et({
						param: t,
						key: k.data
					})
				})
			}, ur = function(t) {
				return Qt({
					eventName: _.isAppInstalled,
					param: t
				})
			}, sr = function(t) {
				return Qt({
					eventName: _.checkAppStatus,
					param: t
				})
			}, lr = function(t) {
				return Qt({
					eventName: _.launchApp,
					param: t
				})
			}, fr = function(t) {
				return Qt({
					eventName: _.installApp,
					param: t
				})
			}, pr = function(t) {
				return Qt({
					eventName: _.downloadApp,
					param: t
				})
			}, dr = function(t) {
				return Qt({
					eventName: _.downloadApp_gdt,
					param: t
				})
			}, hr = function(t) {
				return Qt({
					eventName: _.handleCurDownloadGame,
					param: t
				})
			}, mr = function(t) {
				return Qt({
					eventName: _.uploadImage,
					param: t
				})
			}, vr = function(t) {
				return Qt({
					eventName: _.chooseMedia,
					param: t
				})
			}, yr = function(t) {
				return Qt({
					eventName: _.editVideo,
					param: t
				})
			}, gr = function(t) {
				return Qt({
					eventName: _.previewVideo,
					param: t
				})
			}, wr = function(t) {
				return Qt({
					eventName: _.chooseVideoCover,
					param: t
				})
			}, br = function() {
				return Yt({
					eventName: _.onWebCellReady
				})
			}, xr = function(t) {
				return Yt({
					eventName: _.onWebCellError,
					param: t
				})
			}, Er = function(t) {
				return Yt({
					eventName: _.adjustWebViewInfo,
					param: t
				})
			}, Sr = function() {
				return Yt({
					eventName: _.onWebCellUIChanged
				})
			}, kr = function() {
				return Qt({
					eventName: _.isNotificationEnabled
				})
			}, _r = function() {
				return Qt({
					eventName: _.requestCalendarAuthority
				})
			}, Nr = function(t) {
				return Qt({
					eventName: _.tryAddCalendarEvent,
					param: t
				})
			}, jr = function() {
				return Qt({
					eventName: _.isAllPushClose
				})
			}, Or = function() {
				return Qt({
					eventName: _.getCurrentLocationInfo
				})
			}, Pr = function() {
				return Qt({
					eventName: _.enableLocalPush
				})
			}, Lr = function() {
				return Qt({
					eventName: _.gotoPushSwitchSettingPage
				})
			}, Tr = function(t) {
				return Qt({
					eventName: _.getUserPhotoLocations,
					param: t
				})
			}, Ar = function() {
				return Qt({
					eventName: _.userPhotoAuthorization
				})
			}, Ir = function() {
				return Qt({
					eventName: nt(_.jumpToSysAuthorizationPage, _.gotoSysPushSettingPage)
				})
			}, Cr = function() {
				return Qt({
					eventName: _.checkCalendarAuthority
				})
			}, Ur = function(t) {
				return Qt({
					eventName: _.changeCityChannel,
					param: t
				})
			}, Dr = function(t) {
				return Qt({
					eventName: _.showCitySelector,
					param: t
				})
			}, zr = function(t) {
				return Qt({
					eventName: _.queryChannelInfo,
					param: t
				})
			}, Mr = function() {
				return new Promise((function(t, e) {
					Qt({
						eventName: _.isPublishForbidden
					}).then((function(e) {
						var n = 1 !== (null === e || void 0 === e ? void 0 : e.publishdomestic),
							r = Object.assign({
								isForbidden: n
							}, St);
						t(r)
					}), (function(t) {
						e(t)
					}))
				}))
			};
			(function(t) {
				t["themeChanged"] = "themeChanged", t["loginStatueChanged"] = "loginStatueChanged", t["onRefresh"] = "onRefresh", t["OnShow"] = "OnShow", t["OnHide"] = "OnHide"
			})($t || ($t = {}));
			var Rr = {}, qr = {}, Wr = function(t, e) {
				(null === Rr || void 0 === Rr ? void 0 : Rr[t]) || (Rr[t] = []), Rr[t].push({
					fn: e
				})
			}, Fr = function(t, e, n) {
				var r;
				null === (r = null === n || void 0 === n ? void 0 : n.$on) || void 0 === r || r.call(n, t, e), (null === n || void 0 === n ? void 0 : n.addListener) && (qr[t] = null === n || void 0 === n ? void 0 : n.addListener(it, e))
			}, Br = function(t, e) {
				var n;
				null === (n = Rr[t]) || void 0 === n || n.forEach((function(t) {
					t.fn(e)
				}))
			}, Gr = function() {
				rt() && (window.webPageManager = {
					pageOnShow: function(t) {
						Br($t.OnShow, t)
					},
					pageOnHide: function(t) {
						Br($t.OnHide, t)
					},
					themeChanged: function(t) {
						Br($t.themeChanged, t)
					}
				}, window.webCellManager = {
					themeChanged: function(t) {
						Br($t.themeChanged, t)
					},
					loginStatueChanged: function(t) {
						Br($t.loginStatueChanged, t)
					},
					channelDidRefreshData: function(t) {
						Br($t.onRefresh, t)
					},
					channelDidAppear: function(t) {
						Br($t.OnShow, t)
					},
					channelDidDisappear: function(t) {
						Br($t.OnHide, t)
					},
					nativeDidFinishLoad: function() {},
					onDetach: function() {},
					onAttach: function() {}
				})
			}, Hr = function(t, e) {
				var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null;
				E() ? (Wr(t, e), Gr()) : C() && n && Fr(t, e, n)
			}, $r = function(t, e) {
				var n, r, o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null;
				C() && (null === (n = null === o || void 0 === o ? void 0 : o.$off) || void 0 === n || n.call(o, t, e), null === (r = qr[t]) || void 0 === r || r.remove())
			}
		},
		49381: function(t, e, n) {
			t.exports = n(5699)
		},
		8647: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(75291),
				i = n(16427),
				a = n(13630),
				c = n(77705),
				u = n(67235),
				s = n(94333),
				l = n(17214),
				f = n(84805),
				p = n(92973);
			t.exports = function(t) {
				return new Promise((function(e, n) {
					var d, h = t.data,
						m = t.headers,
						v = t.responseType;

					function y() {
						t.cancelToken && t.cancelToken.unsubscribe(d), t.signal && t.signal.removeEventListener("abort", d)
					}
					r.isFormData(h) && delete m["Content-Type"];
					var g = new XMLHttpRequest;
					if (t.auth) {
						var w = t.auth.username || "",
							b = t.auth.password ? unescape(encodeURIComponent(t.auth.password)) : "";
						m.Authorization = "Basic " + btoa(w + ":" + b)
					}
					var x = c(t.baseURL, t.url);

					function E() {
						if (g) {
							var r = "getAllResponseHeaders" in g ? u(g.getAllResponseHeaders()) : null,
								i = v && "text" !== v && "json" !== v ? g.response : g.responseText,
								a = {
									data: i,
									status: g.status,
									statusText: g.statusText,
									headers: r,
									config: t,
									request: g
								};
							o((function(t) {
								e(t), y()
							}), (function(t) {
								n(t), y()
							}), a), g = null
						}
					}
					if (g.open(t.method.toUpperCase(), a(x, t.params, t.paramsSerializer), !0), g.timeout = t.timeout, "onloadend" in g ? g.onloadend = E : g.onreadystatechange = function() {
						g && 4 === g.readyState && (0 !== g.status || g.responseURL && 0 === g.responseURL.indexOf("file:")) && setTimeout(E)
					}, g.onabort = function() {
						g && (n(l("Request aborted", t, "ECONNABORTED", g)), g = null)
					}, g.onerror = function() {
						n(l("Network Error", t, null, g)), g = null
					}, g.ontimeout = function() {
						var e = t.timeout ? "timeout of " + t.timeout + "ms exceeded" : "timeout exceeded",
							r = t.transitional || f.transitional;
						t.timeoutErrorMessage && (e = t.timeoutErrorMessage), n(l(e, t, r.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED", g)), g = null
					}, r.isStandardBrowserEnv()) {
						var S = (t.withCredentials || s(x)) && t.xsrfCookieName ? i.read(t.xsrfCookieName) : void 0;
						S && (m[t.xsrfHeaderName] = S)
					}
					"setRequestHeader" in g && r.forEach(m, (function(t, e) {
						"undefined" === typeof h && "content-type" === e.toLowerCase() ? delete m[e] : g.setRequestHeader(e, t)
					})), r.isUndefined(t.withCredentials) || (g.withCredentials = !! t.withCredentials), v && "json" !== v && (g.responseType = t.responseType), "function" === typeof t.onDownloadProgress && g.addEventListener("progress", t.onDownloadProgress), "function" === typeof t.onUploadProgress && g.upload && g.upload.addEventListener("progress", t.onUploadProgress), (t.cancelToken || t.signal) && (d = function(t) {
						g && (n(!t || t && t.type ? new p("canceled") : t), g.abort(), g = null)
					}, t.cancelToken && t.cancelToken.subscribe(d), t.signal && (t.signal.aborted ? d() : t.signal.addEventListener("abort", d))), h || (h = null), g.send(h)
				}))
			}
		},
		5699: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(28765),
				i = n(3551),
				a = n(16216),
				c = n(84805);

			function u(t) {
				var e = new i(t),
					n = o(i.prototype.request, e);
				return r.extend(n, i.prototype, e), r.extend(n, e), n.create = function(e) {
					return u(a(t, e))
				}, n
			}
			var s = u(c);
			s.Axios = i, s.Cancel = n(92973), s.CancelToken = n(47327), s.isCancel = n(76974), s.VERSION = n(76847).version, s.all = function(t) {
				return Promise.all(t)
			}, s.spread = n(30509), s.isAxiosError = n(76139), t.exports = s, t.exports["default"] = s
		},
		92973: function(t) {
			"use strict";

			function e(t) {
				this.message = t
			}
			e.prototype.toString = function() {
				return "Cancel" + (this.message ? ": " + this.message : "")
			}, e.prototype.__CANCEL__ = !0, t.exports = e
		},
		47327: function(t, e, n) {
			"use strict";
			var r = n(92973);

			function o(t) {
				if ("function" !== typeof t) throw new TypeError("executor must be a function.");
				var e;
				this.promise = new Promise((function(t) {
					e = t
				}));
				var n = this;
				this.promise.then((function(t) {
					if (n._listeners) {
						var e, r = n._listeners.length;
						for (e = 0; e < r; e++) n._listeners[e](t);
						n._listeners = null
					}
				})), this.promise.then = function(t) {
					var e, r = new Promise((function(t) {
						n.subscribe(t), e = t
					})).then(t);
					return r.cancel = function() {
						n.unsubscribe(e)
					}, r
				}, t((function(t) {
					n.reason || (n.reason = new r(t), e(n.reason))
				}))
			}
			o.prototype.throwIfRequested = function() {
				if (this.reason) throw this.reason
			}, o.prototype.subscribe = function(t) {
				this.reason ? t(this.reason) : this._listeners ? this._listeners.push(t) : this._listeners = [t]
			}, o.prototype.unsubscribe = function(t) {
				if (this._listeners) {
					var e = this._listeners.indexOf(t); - 1 !== e && this._listeners.splice(e, 1)
				}
			}, o.source = function() {
				var t, e = new o((function(e) {
					t = e
				}));
				return {
					token: e,
					cancel: t
				}
			}, t.exports = o
		},
		76974: function(t) {
			"use strict";
			t.exports = function(t) {
				return !(!t || !t.__CANCEL__)
			}
		},
		3551: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(13630),
				i = n(15450),
				a = n(29170),
				c = n(16216),
				u = n(81149),
				s = u.validators;

			function l(t) {
				this.defaults = t, this.interceptors = {
					request: new i,
					response: new i
				}
			}
			l.prototype.request = function(t) {
				"string" === typeof t ? (t = arguments[1] || {}, t.url = arguments[0]) : t = t || {}, t = c(this.defaults, t), t.method ? t.method = t.method.toLowerCase() : this.defaults.method ? t.method = this.defaults.method.toLowerCase() : t.method = "get";
				var e = t.transitional;
				void 0 !== e && u.assertOptions(e, {
					silentJSONParsing: s.transitional(s.boolean),
					forcedJSONParsing: s.transitional(s.boolean),
					clarifyTimeoutError: s.transitional(s.boolean)
				}, !1);
				var n = [],
					r = !0;
				this.interceptors.request.forEach((function(e) {
					"function" === typeof e.runWhen && !1 === e.runWhen(t) || (r = r && e.synchronous, n.unshift(e.fulfilled, e.rejected))
				}));
				var o, i = [];
				if (this.interceptors.response.forEach((function(t) {
					i.push(t.fulfilled, t.rejected)
				})), !r) {
					var l = [a, void 0];
					Array.prototype.unshift.apply(l, n), l = l.concat(i), o = Promise.resolve(t);
					while (l.length) o = o.then(l.shift(), l.shift());
					return o
				}
				var f = t;
				while (n.length) {
					var p = n.shift(),
						d = n.shift();
					try {
						f = p(f)
					} catch (h) {
						d(h);
						break
					}
				}
				try {
					o = a(f)
				} catch (h) {
					return Promise.reject(h)
				}
				while (i.length) o = o.then(i.shift(), i.shift());
				return o
			}, l.prototype.getUri = function(t) {
				return t = c(this.defaults, t), o(t.url, t.params, t.paramsSerializer).replace(/^\?/, "")
			}, r.forEach(["delete", "get", "head", "options"], (function(t) {
				l.prototype[t] = function(e, n) {
					return this.request(c(n || {}, {
						method: t,
						url: e,
						data: (n || {}).data
					}))
				}
			})), r.forEach(["post", "put", "patch"], (function(t) {
				l.prototype[t] = function(e, n, r) {
					return this.request(c(r || {}, {
						method: t,
						url: e,
						data: n
					}))
				}
			})), t.exports = l
		},
		15450: function(t, e, n) {
			"use strict";
			var r = n(28011);

			function o() {
				this.handlers = []
			}
			o.prototype.use = function(t, e, n) {
				return this.handlers.push({
					fulfilled: t,
					rejected: e,
					synchronous: !! n && n.synchronous,
					runWhen: n ? n.runWhen : null
				}), this.handlers.length - 1
			}, o.prototype.eject = function(t) {
				this.handlers[t] && (this.handlers[t] = null)
			}, o.prototype.forEach = function(t) {
				r.forEach(this.handlers, (function(e) {
					null !== e && t(e)
				}))
			}, t.exports = o
		},
		77705: function(t, e, n) {
			"use strict";
			var r = n(98297),
				o = n(18894);
			t.exports = function(t, e) {
				return t && !r(e) ? o(t, e) : e
			}
		},
		17214: function(t, e, n) {
			"use strict";
			var r = n(5150);
			t.exports = function(t, e, n, o, i) {
				var a = new Error(t);
				return r(a, e, n, o, i)
			}
		},
		29170: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(90802),
				i = n(76974),
				a = n(84805),
				c = n(92973);

			function u(t) {
				if (t.cancelToken && t.cancelToken.throwIfRequested(), t.signal && t.signal.aborted) throw new c("canceled")
			}
			t.exports = function(t) {
				u(t), t.headers = t.headers || {}, t.data = o.call(t, t.data, t.headers, t.transformRequest), t.headers = r.merge(t.headers.common || {}, t.headers[t.method] || {}, t.headers), r.forEach(["delete", "get", "head", "post", "put", "patch", "common"], (function(e) {
					delete t.headers[e]
				}));
				var e = t.adapter || a.adapter;
				return e(t).then((function(e) {
					return u(t), e.data = o.call(t, e.data, e.headers, t.transformResponse), e
				}), (function(e) {
					return i(e) || (u(t), e && e.response && (e.response.data = o.call(t, e.response.data, e.response.headers, t.transformResponse))), Promise.reject(e)
				}))
			}
		},
		5150: function(t) {
			"use strict";
			t.exports = function(t, e, n, r, o) {
				return t.config = e, n && (t.code = n), t.request = r, t.response = o, t.isAxiosError = !0, t.toJSON = function() {
					return {
						message: this.message,
						name: this.name,
						description: this.description,
						number: this.number,
						fileName: this.fileName,
						lineNumber: this.lineNumber,
						columnNumber: this.columnNumber,
						stack: this.stack,
						config: this.config,
						code: this.code,
						status: this.response && this.response.status ? this.response.status : null
					}
				}, t
			}
		},
		16216: function(t, e, n) {
			"use strict";
			var r = n(28011);
			t.exports = function(t, e) {
				e = e || {};
				var n = {};

				function o(t, e) {
					return r.isPlainObject(t) && r.isPlainObject(e) ? r.merge(t, e) : r.isPlainObject(e) ? r.merge({}, e) : r.isArray(e) ? e.slice() : e
				}
				function i(n) {
					return r.isUndefined(e[n]) ? r.isUndefined(t[n]) ? void 0 : o(void 0, t[n]) : o(t[n], e[n])
				}
				function a(t) {
					if (!r.isUndefined(e[t])) return o(void 0, e[t])
				}
				function c(n) {
					return r.isUndefined(e[n]) ? r.isUndefined(t[n]) ? void 0 : o(void 0, t[n]) : o(void 0, e[n])
				}
				function u(n) {
					return n in e ? o(t[n], e[n]) : n in t ? o(void 0, t[n]) : void 0
				}
				var s = {
					url: a,
					method: a,
					data: a,
					baseURL: c,
					transformRequest: c,
					transformResponse: c,
					paramsSerializer: c,
					timeout: c,
					timeoutMessage: c,
					withCredentials: c,
					adapter: c,
					responseType: c,
					xsrfCookieName: c,
					xsrfHeaderName: c,
					onUploadProgress: c,
					onDownloadProgress: c,
					decompress: c,
					maxContentLength: c,
					maxBodyLength: c,
					transport: c,
					httpAgent: c,
					httpsAgent: c,
					cancelToken: c,
					socketPath: c,
					responseEncoding: c,
					validateStatus: u
				};
				return r.forEach(Object.keys(t).concat(Object.keys(e)), (function(t) {
					var e = s[t] || i,
						o = e(t);
					r.isUndefined(o) && e !== u || (n[t] = o)
				})), n
			}
		},
		75291: function(t, e, n) {
			"use strict";
			var r = n(17214);
			t.exports = function(t, e, n) {
				var o = n.config.validateStatus;
				n.status && o && !o(n.status) ? e(r("Request failed with status code " + n.status, n.config, null, n.request, n)) : t(n)
			}
		},
		90802: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(84805);
			t.exports = function(t, e, n) {
				var i = this || o;
				return r.forEach(n, (function(n) {
					t = n.call(i, t, e)
				})), t
			}
		},
		84805: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = n(91164),
				i = n(5150),
				a = {
					"Content-Type": "application/x-www-form-urlencoded"
				};

			function c(t, e) {
				!r.isUndefined(t) && r.isUndefined(t["Content-Type"]) && (t["Content-Type"] = e)
			}
			function u() {
				var t;
				return ("undefined" !== typeof XMLHttpRequest || "undefined" !== typeof process && "[object process]" === Object.prototype.toString.call(process)) && (t = n(8647)), t
			}
			function s(t, e, n) {
				if (r.isString(t)) try {
					return (e || JSON.parse)(t), r.trim(t)
				} catch (o) {
					if ("SyntaxError" !== o.name) throw o
				}
				return (n || JSON.stringify)(t)
			}
			var l = {
				transitional: {
					silentJSONParsing: !0,
					forcedJSONParsing: !0,
					clarifyTimeoutError: !1
				},
				adapter: u(),
				transformRequest: [function(t, e) {
					return o(e, "Accept"), o(e, "Content-Type"), r.isFormData(t) || r.isArrayBuffer(t) || r.isBuffer(t) || r.isStream(t) || r.isFile(t) || r.isBlob(t) ? t : r.isArrayBufferView(t) ? t.buffer : r.isURLSearchParams(t) ? (c(e, "application/x-www-form-urlencoded;charset=utf-8"), t.toString()) : r.isObject(t) || e && "application/json" === e["Content-Type"] ? (c(e, "application/json"), s(t)) : t
				}],
				transformResponse: [function(t) {
					var e = this.transitional || l.transitional,
						n = e && e.silentJSONParsing,
						o = e && e.forcedJSONParsing,
						a = !n && "json" === this.responseType;
					if (a || o && r.isString(t) && t.length) try {
						return JSON.parse(t)
					} catch (c) {
						if (a) {
							if ("SyntaxError" === c.name) throw i(c, this, "E_JSON_PARSE");
							throw c
						}
					}
					return t
				}],
				timeout: 0,
				xsrfCookieName: "XSRF-TOKEN",
				xsrfHeaderName: "X-XSRF-TOKEN",
				maxContentLength: -1,
				maxBodyLength: -1,
				validateStatus: function(t) {
					return t >= 200 && t < 300
				},
				headers: {
					common: {
						Accept: "application/json, text/plain, */*"
					}
				}
			};
			r.forEach(["delete", "get", "head"], (function(t) {
				l.headers[t] = {}
			})), r.forEach(["post", "put", "patch"], (function(t) {
				l.headers[t] = r.merge(a)
			})), t.exports = l
		},
		76847: function(t) {
			t.exports = {
				version: "0.24.0"
			}
		},
		28765: function(t) {
			"use strict";
			t.exports = function(t, e) {
				return function() {
					for (var n = new Array(arguments.length), r = 0; r < n.length; r++) n[r] = arguments[r];
					return t.apply(e, n)
				}
			}
		},
		13630: function(t, e, n) {
			"use strict";
			var r = n(28011);

			function o(t) {
				return encodeURIComponent(t).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
			}
			t.exports = function(t, e, n) {
				if (!e) return t;
				var i;
				if (n) i = n(e);
				else if (r.isURLSearchParams(e)) i = e.toString();
				else {
					var a = [];
					r.forEach(e, (function(t, e) {
						null !== t && "undefined" !== typeof t && (r.isArray(t) ? e += "[]" : t = [t], r.forEach(t, (function(t) {
							r.isDate(t) ? t = t.toISOString() : r.isObject(t) && (t = JSON.stringify(t)), a.push(o(e) + "=" + o(t))
						})))
					})), i = a.join("&")
				}
				if (i) {
					var c = t.indexOf("#"); - 1 !== c && (t = t.slice(0, c)), t += (-1 === t.indexOf("?") ? "?" : "&") + i
				}
				return t
			}
		},
		18894: function(t) {
			"use strict";
			t.exports = function(t, e) {
				return e ? t.replace(/\/+$/, "") + "/" + e.replace(/^\/+/, "") : t
			}
		},
		16427: function(t, e, n) {
			"use strict";
			var r = n(28011);
			t.exports = r.isStandardBrowserEnv() ? function() {
				return {
					write: function(t, e, n, o, i, a) {
						var c = [];
						c.push(t + "=" + encodeURIComponent(e)), r.isNumber(n) && c.push("expires=" + new Date(n).toGMTString()), r.isString(o) && c.push("path=" + o), r.isString(i) && c.push("domain=" + i), !0 === a && c.push("secure"), document.cookie = c.join("; ")
					},
					read: function(t) {
						var e = document.cookie.match(new RegExp("(^|;\\s*)(" + t + ")=([^;]*)"));
						return e ? decodeURIComponent(e[3]) : null
					},
					remove: function(t) {
						this.write(t, "", Date.now() - 864e5)
					}
				}
			}() : function() {
				return {
					write: function() {},
					read: function() {
						return null
					},
					remove: function() {}
				}
			}()
		},
		98297: function(t) {
			"use strict";
			t.exports = function(t) {
				return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)
			}
		},
		76139: function(t) {
			"use strict";

			function e(t) {
				return e = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, e(t)
			}
			t.exports = function(t) {
				return "object" === e(t) && !0 === t.isAxiosError
			}
		},
		94333: function(t, e, n) {
			"use strict";
			var r = n(28011);
			t.exports = r.isStandardBrowserEnv() ? function() {
				var t, e = /(msie|trident)/i.test(navigator.userAgent),
					n = document.createElement("a");

				function o(t) {
					var r = t;
					return e && (n.setAttribute("href", r), r = n.href), n.setAttribute("href", r), {
						href: n.href,
						protocol: n.protocol ? n.protocol.replace(/:$/, "") : "",
						host: n.host,
						search: n.search ? n.search.replace(/^\?/, "") : "",
						hash: n.hash ? n.hash.replace(/^#/, "") : "",
						hostname: n.hostname,
						port: n.port,
						pathname: "/" === n.pathname.charAt(0) ? n.pathname : "/" + n.pathname
					}
				}
				return t = o(window.location.href),
				function(e) {
					var n = r.isString(e) ? o(e) : e;
					return n.protocol === t.protocol && n.host === t.host
				}
			}() : function() {
				return function() {
					return !0
				}
			}()
		},
		91164: function(t, e, n) {
			"use strict";
			var r = n(28011);
			t.exports = function(t, e) {
				r.forEach(t, (function(n, r) {
					r !== e && r.toUpperCase() === e.toUpperCase() && (t[e] = n, delete t[r])
				}))
			}
		},
		67235: function(t, e, n) {
			"use strict";
			var r = n(28011),
				o = ["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"];
			t.exports = function(t) {
				var e, n, i, a = {};
				return t ? (r.forEach(t.split("\n"), (function(t) {
					if (i = t.indexOf(":"), e = r.trim(t.substr(0, i)).toLowerCase(), n = r.trim(t.substr(i + 1)), e) {
						if (a[e] && o.indexOf(e) >= 0) return;
						a[e] = "set-cookie" === e ? (a[e] ? a[e] : []).concat([n]) : a[e] ? a[e] + ", " + n : n
					}
				})), a) : a
			}
		},
		30509: function(t) {
			"use strict";
			t.exports = function(t) {
				return function(e) {
					return t.apply(null, e)
				}
			}
		},
		81149: function(t, e, n) {
			"use strict";

			function r(t) {
				return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, r(t)
			}
			var o = n(76847).version,
				i = {};
			["object", "boolean", "number", "function", "string", "symbol"].forEach((function(t, e) {
				i[t] = function(n) {
					return r(n) === t || "a" + (e < 1 ? "n " : " ") + t
				}
			}));
			var a = {};

			function c(t, e, n) {
				if ("object" !== r(t)) throw new TypeError("options must be an object");
				var o = Object.keys(t),
					i = o.length;
				while (i-- > 0) {
					var a = o[i],
						c = e[a];
					if (c) {
						var u = t[a],
							s = void 0 === u || c(u, a, t);
						if (!0 !== s) throw new TypeError("option " + a + " must be " + s)
					} else if (!0 !== n) throw Error("Unknown option " + a)
				}
			}
			i.transitional = function(t, e, n) {
				function r(t, e) {
					return "[Axios v" + o + "] Transitional option '" + t + "'" + e + (n ? ". " + n : "")
				}
				return function(n, o, i) {
					if (!1 === t) throw new Error(r(o, " has been removed" + (e ? " in " + e : "")));
					return e && !a[o] && (a[o] = !0, console.warn(r(o, " has been deprecated since v" + e + " and will be removed in the near future"))), !t || t(n, o, i)
				}
			}, t.exports = {
				assertOptions: c,
				validators: i
			}
		},
		28011: function(t, e, n) {
			"use strict";

			function r(t) {
				return r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, r(t)
			}
			var o = n(28765),
				i = Object.prototype.toString;

			function a(t) {
				return "[object Array]" === i.call(t)
			}
			function c(t) {
				return "undefined" === typeof t
			}
			function u(t) {
				return null !== t && !c(t) && null !== t.constructor && !c(t.constructor) && "function" === typeof t.constructor.isBuffer && t.constructor.isBuffer(t)
			}
			function s(t) {
				return "[object ArrayBuffer]" === i.call(t)
			}
			function l(t) {
				return "undefined" !== typeof FormData && t instanceof FormData
			}
			function f(t) {
				var e;
				return e = "undefined" !== typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(t) : t && t.buffer && t.buffer instanceof ArrayBuffer, e
			}
			function p(t) {
				return "string" === typeof t
			}
			function d(t) {
				return "number" === typeof t
			}
			function h(t) {
				return null !== t && "object" === r(t)
			}
			function m(t) {
				if ("[object Object]" !== i.call(t)) return !1;
				var e = Object.getPrototypeOf(t);
				return null === e || e === Object.prototype
			}
			function v(t) {
				return "[object Date]" === i.call(t)
			}
			function y(t) {
				return "[object File]" === i.call(t)
			}
			function g(t) {
				return "[object Blob]" === i.call(t)
			}
			function w(t) {
				return "[object Function]" === i.call(t)
			}
			function b(t) {
				return h(t) && w(t.pipe)
			}
			function x(t) {
				return "undefined" !== typeof URLSearchParams && t instanceof URLSearchParams
			}
			function E(t) {
				return t.trim ? t.trim() : t.replace(/^\s+|\s+$/g, "")
			}
			function S() {
				return ("undefined" === typeof navigator || "ReactNative" !== navigator.product && "NativeScript" !== navigator.product && "NS" !== navigator.product) && ("undefined" !== typeof window && "undefined" !== typeof document)
			}
			function k(t, e) {
				if (null !== t && "undefined" !== typeof t) if ("object" !== r(t) && (t = [t]), a(t)) for (var n = 0, o = t.length; n < o; n++) e.call(null, t[n], n, t);
				else for (var i in t) Object.prototype.hasOwnProperty.call(t, i) && e.call(null, t[i], i, t)
			}
			function _() {
				var t = {};

				function e(e, n) {
					m(t[n]) && m(e) ? t[n] = _(t[n], e) : m(e) ? t[n] = _({}, e) : a(e) ? t[n] = e.slice() : t[n] = e
				}
				for (var n = 0, r = arguments.length; n < r; n++) k(arguments[n], e);
				return t
			}
			function N(t, e, n) {
				return k(e, (function(e, r) {
					t[r] = n && "function" === typeof e ? o(e, n) : e
				})), t
			}
			function j(t) {
				return 65279 === t.charCodeAt(0) && (t = t.slice(1)), t
			}
			t.exports = {
				isArray: a,
				isArrayBuffer: s,
				isBuffer: u,
				isFormData: l,
				isArrayBufferView: f,
				isString: p,
				isNumber: d,
				isObject: h,
				isPlainObject: m,
				isUndefined: c,
				isDate: v,
				isFile: y,
				isBlob: g,
				isFunction: w,
				isStream: b,
				isURLSearchParams: x,
				isStandardBrowserEnv: S,
				forEach: k,
				merge: _,
				extend: N,
				trim: E,
				stripBOM: j
			}
		},
		65666: function(t, e, n) {
			"use strict";
			n.d(e, {
				$X: function() {
					return c
				},
				Ac: function() {
					return m
				},
				CF: function() {
					return s
				},
				Ff: function() {
					return i
				},
				Ut: function() {
					return a
				},
				g8: function() {
					return u
				},
				hq: function() {
					return p
				},
				mJ: function() {
					return l
				},
				oF: function() {
					return d
				},
				rW: function() {
					return h
				},
				vF: function() {
					return f
				}
			});
			var r = n(51346),
				o = !1;
			try {
				o = !1
			} catch (v) {}
			var i = function(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : r.C0.WZQ;
				if (!o && window.$broker && r.DS[window.$broker.id]) return r.DS[window.$broker.id][e] + t;
				try {
					if (o) {
						var n = e === r.C0.WZQ ? "https://wzq.tenpay.com".concat(r.DS[r.sN.TENPAY][e]) : r.DS[r.sN.TENPAY][e];
						return n + t
					}
				} catch (i) {}
				return r.DS[r.sN.TENPAY][e] + t
			}, a = !o && window && window.$broker && window.$broker.id,
				c = i("userinfo.fcgi"),
				u = i("userstock.fcgi"),
				s = (i("stockinfo.fcgi"), i("stock_strategy.fcgi")),
				l = i("activity/ad.fcgi", r.C0.TENPAY),
				f = i("ad.fcgi"),
				p = (i("ifzqfinance", r.C0.PROXY_QQ), i("ifzqgtimg", r.C0.PROXY_QQ), i("mp_bind.cgi")),
				d = i("", r.C0.SQT),
				h = (i("ifzqgtimg/appstock/app/rank/indexRankDetail2", r.C0.PROXY_QQ), i("stockpicking_plat.fcgi"), i("wzq_oem_activity.cgi")),
				m = i("usersetting.fcgi", r.C0.TENPAY)
		},
		93381: function(t, e, n) {
			"use strict";
			n.d(e, {
				Km: function() {
					return c
				},
				WB: function() {
					return u
				},
				XY: function() {
					return a
				},
				Zi: function() {
					return s
				},
				hk: function() {
					return r
				},
				rB: function() {
					return i
				},
				yK: function() {
					return o
				}
			});
			var r = {
				DEFAULT: "wzq",
				MP: "zxg_xcx",
				DAFENG: "df",
				GUOSEN: "oem_guosen",
				ZHONGXINJIANTOU: "oem_jiantou",
				ZHONGJINCAIFU: "oem_zhongjin",
				ZHONGXIN: "oem_zhongxin",
				GUANGFA: "oem_guangfa"
			}, o = {
				DEFAULT: "https://wzq.tenpay.com",
				DAFENG: "https://m.dafenghk.com",
				GUOSEN: "https://wzq.guosen.com.cn",
				ZHONGXINJIANTOU: "https://wzq.csc108.com",
				ZHONGJINCAIFU: "https://wzq.ciccwm.com",
				ZHONGXIN: "https://wzq.citics.com",
				GUANGFA: "https://wzq.gf.com.cn"
			}, i = {
				DEFAULT: {
					yijiandaxin: ""
				},
				DAFENG: {
					yijiandaxin: ""
				},
				GUOSEN: {
					yijiandaxin: "1658290384926"
				},
				ZHONGXINJIANTOU: {
					yijiandaxin: "i96n5r"
				},
				ZHONGJINCAIFU: {
					yijiandaxin: "bYMps3oUGz"
				},
				ZHONGXIN: {
					yijiandaxin: ""
				},
				GUANGFA: {
					yijiandaxin: ""
				}
			}, a = {
				DEFAULT: "",
				GUOSEN: "https://wzq.guosen.com.cn/domain_upstream/aics/wzq/aics-guosen/index",
				ZHONGXINJIANTOU: "https://wzq.csc108.com/domain_upstream/aics/wzq/aics/index"
			}, c = {
				DEFAULT: "2022-07-14",
				GUOSEN: "2022-07-14",
				ZHONGXINJIANTOU: "2022-07-14",
				ZHONGJINCAIFU: "2022-10-07",
				ZHONGXIN: "2022-10-28",
				GUANGFA: "2023-01-31"
			}, u = {
				DEFAULT: "",
				GUOSEN: "已为您升级国信自选股的整体体验和数据，如有任何使用问题可通过智能客服联系我们",
				ZHONGXINJIANTOU: "已为您升级中信建投自选股的整体体验和数据，如有任何使用问题可通过智能客服联系我们",
				ZHONGJINCAIFU: "已为您升级中金自选股的整体体验和数据，如有任何使用问题可通过在线客服联系我们。",
				ZHONGXIN: "已为您升级中信证券自选股的整体体验和数据",
				GUANGFA: "已为您升级广发自选股的整体体验和数据"
			}, s = {
				GUOSEN: 1,
				ZHONGXINJIANTOU: 1,
				ZHONGJINCAIFU: 0,
				ZHONGXIN: 0,
				GUANGFA: 0
			}
		},
		51346: function(t, e, n) {
			"use strict";
			n.d(e, {
				C0: function() {
					return o
				},
				DS: function() {
					return r
				},
				sN: function() {
					return i
				}
			});
			var r = {
				TENPAY: {
					WZQ: "/cgi-bin/",
					SNP: "https://snp.tenpay.com/cgi/cgi-bin/",
					PROXY_QQ: "https://proxy.finance.qq.com/",
					BISHENG: "https://bisheng.tenpay.com/fcgi-bin/",
					SQT: "https://sqt.gtimg.cn/",
					TENPAY: "https://wzq.tenpay.com/cgi-bin/"
				},
				DAFENG: {
					WZQ: "/cgi-bin/",
					SNP: "/snptenpaycom/cgi-bin/",
					PROXY_QQ: "/proxyfinanceqqcom/",
					BISHENG: "/bishengtenpaycom/fcgi-bin/",
					SQT: "/sqtgtimgcn/",
					TENPAY: "/wzqtenpaycom/cgi-bin/"
				},
				GUOSEN: {
					WZQ: "/cgi-bin/",
					SNP: "/domain_upstream/snptenpaycom/cgi-bin/",
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom/",
					BISHENG: "/domain_upstream/bishengtenpaycom/fcgi-bin/",
					SQT: "/domain_upstream/sqtgtimgcn/",
					TENPAY: "/domain_upstream/wzqtenpaycom/cgi-bin/"
				},
				ZHONGXINJIANTOU: {
					WZQ: "/cgi-bin/",
					SNP: "https://zxgcloud.csc108.com/domain_upstream/snptenpaycom/cgi-bin/",
					PROXY_QQ: "https://zxgcloud.csc108.com/domain_upstream/proxyfinanceqqcom/",
					BISHENG: "https://zxgcloud.csc108.com/domain_upstream/bishengtenpaycom/fcgi-bin/",
					SQT: "https://zxgcloud.csc108.com/domain_upstream/sqtgtimgcn/",
					TENPAY: "https://zxgcloud.csc108.com/domain_upstream/wzqtenpaycom/cgi-bin/"
				},
				ZHONGJINCAIFU: {
					WZQ: "/cgi-bin/",
					SNP: "/domain_upstream/snptenpaycom/cgi-bin/",
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom/",
					BISHENG: "/domain_upstream/bishengtenpaycom/fcgi-bin/",
					SQT: "/domain_upstream/sqtgtimgcn/",
					TENPAY: "/domain_upstream/wzqtenpaycom/cgi-bin/"
				},
				ZHONGXIN: {
					WZQ: "/cgi-bin/",
					SNP: "/domain_upstream/snptenpaycom/cgi-bin/",
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom/",
					BISHENG: "/domain_upstream/bishengtenpaycom/fcgi-bin/",
					SQT: "/domain_upstream/sqtgtimgcn/",
					TENPAY: "/domain_upstream/wzqtenpaycom/cgi-bin/"
				},
				GUANGFA: {
					WZQ: "/cgi-bin/",
					SNP: "/domain_upstream/snptenpaycom/cgi-bin/",
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom/",
					BISHENG: "/domain_upstream/bishengtenpaycom/fcgi-bin/",
					SQT: "/domain_upstream/sqtgtimgcn/",
					TENPAY: "/domain_upstream/wzqtenpaycom/cgi-bin/"
				}
			}, o = {
				WZQ: "WZQ",
				SNP: "SNP",
				PROXY_QQ: "PROXY_QQ",
				OPENAPI_QQ: "OPENAPI_QQ",
				BISHENG: "BISHENG",
				SQT: "SQT",
				TENPAY: "TENPAY"
			}, i = {
				TENPAY: "TENPAY",
				DAFENG: "DAFENG",
				GUOSEN: "GUOSEN",
				ZHONGXINJIANTOU: "ZHONGXINJIANTOU",
				ZHONGJINCAIFU: "ZHONGJINCAIFU",
				ZHONGXIN: "ZHONGXIN",
				GUANGFA: "GUANGFA"
			}
		},
		1728: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return _
				}
			});
			var r = n(93666),
				o = n(49381),
				i = n.n(o),
				a = n(61201),
				c = n.n(a),
				u = n(16212),
				s = {
					GUOSEN: {
						title: "贴心服务，尽在国信",
						desc: "热门资讯、炒股工具、精彩活动，都在这里",
						imgUrl: "https://st.gtimg.com/image/mp-broker/oem/logo/guoxin/logo.png"
					},
					ZHONGXINJIANTOU: {
						title: "贴心服务，尽在中信建投",
						desc: "热门资讯、炒股工具、精彩活动，都在这里",
						imgUrl: "https://st.gtimg.com/image/mp-broker/oem/logo/zhongxinjiantou/share.png"
					},
					ZHONGJINCAIFU: {
						title: "贴心服务，尽在中金财富",
						desc: "热门资讯、炒股工具、精彩活动，都在这里",
						imgUrl: "https://st.gtimg.com/image/mp-broker/oem/logo/zhongjincaifu/share.png"
					},
					ZHONGXIN: {
						title: "贴心服务，尽在中信",
						desc: "热门资讯、炒股工具、精彩活动，都在这里",
						imgUrl: "https://st.gtimg.com/image/mp-broker/oem/logo/zhongxin/share.png"
					},
					GUANGFA: {
						title: "贴心服务，尽在广发",
						desc: "热门资讯、炒股工具、精彩活动，都在这里",
						imgUrl: "https://st.gtimg.com/image/mp-broker/oem/logo/guangfa/share.png"
					}
				}, l = n(93381),
				f = n(65666),
				p = n(3422);

			function d(t) {
				return d = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, d(t)
			}
			function h() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				h = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (O) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new _(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return j()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = E(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (O) {
						return {
							type: "throw",
							arg: O
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function m() {}
				var v = {};
				c(v, o, (function() {
					return this
				}));
				var y = Object.getPrototypeOf,
					g = y && y(y(N([])));
				g && g !== e && n.call(g, o) && (v = g);
				var w = m.prototype = f.prototype = Object.create(v);

				function b(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function x(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == d(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function E(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, E(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function S(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function k(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function _(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(S, this), this.reset(!0)
				}
				function N(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: j
					}
				}
				function j() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = m, c(w, "constructor", m), c(m, "constructor", p), p.displayName = c(m, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, m) : (t.__proto__ = m, c(t, a, "GeneratorFunction")), t.prototype = Object.create(w), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, b(x.prototype), c(x.prototype, i, (function() {
					return this
				})), t.AsyncIterator = x, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new x(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, b(w), c(w, a, "Generator"), c(w, o, (function() {
					return this
				})), c(w, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = N, _.prototype = {
					constructor: _,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(k), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), k(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									k(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: N(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			function m(t, e) {
				var n = Object.keys(t);
				if (Object.getOwnPropertySymbols) {
					var r = Object.getOwnPropertySymbols(t);
					e && (r = r.filter((function(e) {
						return Object.getOwnPropertyDescriptor(t, e).enumerable
					}))), n.push.apply(n, r)
				}
				return n
			}
			function v(t) {
				for (var e = 1; e < arguments.length; e++) {
					var n = null != arguments[e] ? arguments[e] : {};
					e % 2 ? m(Object(n), !0).forEach((function(e) {
						y(t, e, n[e])
					})) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : m(Object(n)).forEach((function(e) {
						Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
					}))
				}
				return t
			}
			function y(t, e, n) {
				return e in t ? Object.defineProperty(t, e, {
					value: n,
					enumerable: !0,
					configurable: !0,
					writable: !0
				}) : t[e] = n, t
			}
			function g(t, e, n, r, o, i, a) {
				try {
					var c = t[i](a),
						u = c.value
				} catch (s) {
					return void n(s)
				}
				c.done ? e(u) : Promise.resolve(u).then(r, o)
			}
			function w(t) {
				return function() {
					var e = this,
						n = arguments;
					return new Promise((function(r, o) {
						var i = t.apply(e, n);

						function a(t) {
							g(i, r, o, a, c, "next", t)
						}
						function c(t) {
							g(i, r, o, a, c, "throw", t)
						}
						a(void 0)
					}))
				}
			}
			function b(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function x(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function E(t, e, n) {
				return e && x(t.prototype, e), n && x(t, n), Object.defineProperty(t, "prototype", {
					writable: !1
				}), t
			}
			var S = new r["default"],
				k = !1;
			try {
				k = !1
			} catch (N) {}
			var _ = function() {
				function t(e) {
					b(this, t), this.context = e, Object.defineProperty(this, "ENV", {
						value: "oem",
						writable: !1
					})
				}
				return E(t, [{
					key: "request",
					value: function() {
						var t = w(h().mark((function t(e) {
							var n, r, o, a, u, s = arguments;
							return h().wrap((function(t) {
								while (1) switch (t.prev = t.next) {
									case 0:
										if (n = s.length > 1 && void 0 !== s[1] ? s[1] : "GET", r = s.length > 2 && void 0 !== s[2] ? s[2] : {}, o = s.length > 3 && void 0 !== s[3] ? s[3] : {}, r && (0, p.Z)(r) && !e.includes("app=") && (e.includes("/domain_upstream/proxyfinanceqqcom/") || e.includes("/domain_upstream/sqtgtimgcn/")) && (delete r.app, r = v({
											app: l.hk[f.Ut] || l.hk.DEFAULT
										}, r)), a = v({
											url: e,
											method: n,
											data: c().stringify(r)
										}, o), !k) {
											t.next = 11;
											break
										}
										return t.next = 8, getApp().globalData.$request(e, r, v({
											method: n
										}, o));
									case 8:
										u = t.sent, t.next = 20;
										break;
									case 11:
										if (!window.$request) {
											t.next = 17;
											break
										}
										return t.next = 14, window.$request(e, r, v({
											method: n
										}, o));
									case 14:
										u = t.sent, t.next = 20;
										break;
									case 17:
										return t.next = 19, i().request(a);
									case 19:
										u = t.sent;
									case 20:
										return t.abrupt("return", u.data || u);
									case 21:
									case "end":
										return t.stop()
								}
							}), t)
						})));

						function e(e) {
							return t.apply(this, arguments)
						}
						return e
					}()
				}, {
					key: "routeTo",
					value: function(t) {
						window.$wujie && t && ("/detail" === t.path || "/information/detail" === t.path) && (t.path = "/wj_main".concat(t.path)), window.$wujie && t && -1 !== t.path.indexOf("/wj_hq") && (t.path = t.path.replace("/wj_hq", "")), this.context.$router.push(t)
					}
				}, {
					key: "report",
					value: function(t) {
						var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
						window.$stat && window.$stat.click(t, void 0, void 0, e)
					}
				}, {
					key: "sdk",
					value: function() {
						return window.$sdk ? window.$sdk : {}
					}
				}, {
					key: "wjBus",
					value: function() {
						var t, e, r;
						if (k) return {};
						if (window.__POWERED_BY_WUJIE__) return null !== (e = null === (r = window.$wujie) || void 0 === r ? void 0 : r.bus) && void 0 !== e ? e : {};
						var o = n(83432);
						return null !== (t = null === o || void 0 === o ? void 0 : o.
						default.bus) && void 0 !== t ? t : {}
					}
				}, {
					key: "wjBusOn",
					value: function() {
						var t = w(h().mark((function t(e, n) {
							var r, o;
							return h().wrap((function(t) {
								while (1) switch (t.prev = t.next) {
									case 0:
										return t.next = 2, this.wjBus();
									case 2:
										o = t.sent, null === o || void 0 === o || null === (r = o.$on) || void 0 === r || r.call(o, e, n);
									case 4:
									case "end":
										return t.stop()
								}
							}), t, this)
						})));

						function e(e, n) {
							return t.apply(this, arguments)
						}
						return e
					}()
				}, {
					key: "wjBusOff",
					value: function() {
						var t = w(h().mark((function t(e, n) {
							var r, o;
							return h().wrap((function(t) {
								while (1) switch (t.prev = t.next) {
									case 0:
										return t.next = 2, this.wjBus();
									case 2:
										o = t.sent, null === o || void 0 === o || null === (r = o.$off) || void 0 === r || r.call(o, e, n);
									case 4:
									case "end":
										return t.stop()
								}
							}), t, this)
						})));

						function e(e, n) {
							return t.apply(this, arguments)
						}
						return e
					}()
				}, {
					key: "wjBusEmit",
					value: function() {
						var t = w(h().mark((function t(e) {
							var n, r, o, i, a, c = arguments;
							return h().wrap((function(t) {
								while (1) switch (t.prev = t.next) {
									case 0:
										return t.next = 2, this.wjBus();
									case 2:
										for (r = t.sent, o = c.length, i = new Array(o > 1 ? o - 1 : 0), a = 1; a < o; a++) i[a - 1] = c[a];
										null === r || void 0 === r || null === (n = r.$emit) || void 0 === n || n.call.apply(n, [r, e].concat(i));
									case 5:
									case "end":
										return t.stop()
								}
							}), t, this)
						})));

						function e(e) {
							return t.apply(this, arguments)
						}
						return e
					}()
				}, {
					key: "busOn",
					value: function(t, e) {
						S.$on(t, e)
					}
				}, {
					key: "busOff",
					value: function(t, e) {
						S.$off(t, e)
					}
				}, {
					key: "busEmit",
					value: function(t) {
						for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++) n[r - 1] = arguments[r];
						S.$emit.apply(S, [t].concat(n))
					}
				}, {
					key: "setTitle",
					value: function(t) {
						document.title = t
					}
				}, {
					key: "getStorage",
					value: function(t) {
						var e = localStorage.getItem(t);
						try {
							return JSON.parse(e)
						} catch (n) {
							return e
						}
					}
				}, {
					key: "setStorage",
					value: function(t, e) {
						var n = "object" === d(e) ? JSON.stringify(e) : e;
						localStorage.setItem(t, n)
					}
				}, {
					key: "getSession",
					value: function(t) {
						var e = sessionStorage.getItem(t);
						try {
							return JSON.parse(e)
						} catch (n) {
							return e
						}
					}
				}, {
					key: "setSession",
					value: function(t, e) {
						var n = "object" === d(e) ? JSON.stringify(e) : e;
						sessionStorage.setItem(t, n)
					}
				}, {
					key: "getCookie",
					value: function(t) {
						return u.get(t)
					}
				}, {
					key: "setCookie",
					value: function(t, e) {
						var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
						u.set(t, e, n)
					}
				}, {
					key: "toast",
					value: function(t) {
						var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "success",
							r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1500;
						if (!k) {
							var o = n(49873),
								i = o.Toast;
							try {
								i[e]({
									message: t,
									duration: r
								})
							} catch (a) {
								i.success({
									message: t,
									duration: r
								})
							}
						}
					}
				}, {
					key: "openExtraWebview",
					value: function(t) {
						window.open(t)
					}
				}, {
					key: "useShare",
					value: function() {
						var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
						if (window.$broker && window.$broker.id) {
							var e = (null === t || void 0 === t ? void 0 : t.link) || location.href || "",
								n = "";
							if (n = e.indexOf("?") > -1 ? "".concat(e, "&from=share") : "".concat(e, "?from=share"), window.__POWERED_BY_WUJIE__) {
								var r = "".concat(window.parent.location.origin).concat(window.parent.location.pathname);
								n = "".concat(r, "#wj_hq").concat(this.context.$route.fullPath)
							}
							var o = v(v({}, s[window.$broker.id]), {}, {
								link: n
							});
							if (this.sdk() && this.sdk().setShareInfo) {
								var i, a, c = v(v({}, o), t);
								this.sdk().setShareInfo(c, c);
								var u = {
									message: "menuItem:share:appMessage",
									timeline: "menuItem:share:timeline",
									wework: "menuItem:wework"
								};
								null === (i = (a = this.sdk()).showMenuItems) || void 0 === i || i.call(a, {
									menuList: (null === t || void 0 === t ? void 0 : t.shareMenu) || [u.message, u.timeline, u.wework]
								})
							}
						}
					}
				}]), t
			}()
		},
		85011: function(t, e, n) {
			"use strict";
			n.d(e, {
				$o: function() {
					return p
				},
				A1: function() {
					return o
				},
				qQ: function() {
					return f
				}
			});
			var r = n(60908),
				o = window && window.navigator && /qqnews/.test(navigator.userAgent),
				i = function() {}, a = o ? n(44667) : {
					setLocalStorage: i,
					getLocalStorage: i
				}, c = a.setLocalStorage,
				u = a.getLocalStorage,
				s = function(t, e) {
					console.log("qqnews-jsapi-".concat(t), e)
				}, l = function(t) {
					return new Promise((function(e) {
						u({
							key: t
						}).then((function(n) {
							if (n && 0 === +n.errCode) {
								var r = n.value;
								try {
									var o = "" === r ? r : JSON.parse(r);
									e(o)
								} catch (i) {
									e(r)
								}
							} else e(null), s("getLocalStorage-key:".concat(t), n)
						})).
						catch ((function(n) {
							e(null), s("getLocalStorage-key:".concat(t), n)
						}))
					}))
				}, f = function(t, e) {
					return new Promise((function(n) {
						c({
							key: t,
							value: JSON.stringify(e)
						}).then((function(e) {
							e && 0 === +e.errCode ? n(e) : (n(), s("setLocalStorage-key:".concat(t), e))
						})).
						catch ((function(e) {
							n(), s("setLocalStorage-key:".concat(t), e)
						}))
					}))
				}, p = function(t) {
					return l(t).then((function(e) {
						if (!e) {
							var n = r.Xb.getItem(t);
							if (n) return f(t, n), r.Xb.setItem(t, ""), n
						}
						return e
					}))
				}
		},
		75520: function(t, e, n) {
			"use strict";
			n.d(e, {
				$v: function() {
					return h
				},
				A: function() {
					return P
				},
				Hl: function() {
					return u
				},
				Km: function() {
					return G
				},
				L0: function() {
					return s
				},
				M7: function() {
					return T
				},
				Mc: function() {
					return w
				},
				Mt: function() {
					return y
				},
				N6: function() {
					return W
				},
				OV: function() {
					return I
				},
				Rw: function() {
					return d
				},
				S0: function() {
					return A
				},
				Ww: function() {
					return B
				},
				Xj: function() {
					return f
				},
				ZJ: function() {
					return q
				},
				_R: function() {
					return R
				},
				a9: function() {
					return U
				},
				aF: function() {
					return E
				},
				aU: function() {
					return b
				},
				ap: function() {
					return l
				},
				dh: function() {
					return M
				},
				kO: function() {
					return g
				},
				mn: function() {
					return D
				},
				nv: function() {
					return _
				},
				pm: function() {
					return C
				},
				qF: function() {
					return a
				},
				qX: function() {
					return c
				},
				qh: function() {
					return p
				},
				r2: function() {
					return k
				},
				rp: function() {
					return L
				},
				sS: function() {
					return S
				},
				vt: function() {
					return v
				},
				zL: function() {
					return F
				},
				zn: function() {
					return m
				}
			});
			var r = n(40566),
				o = n(80324),
				i = n(10617),
				a = "hs",
				c = "hk",
				u = "us",
				s = "0",
				l = "1",
				f = "2",
				p = "3",
				d = "5",
				h = "P",
				m = "U",
				v = "I",
				y = "S",
				g = "D",
				w = "Z",
				b = "C",
				x = [
					["1", "000001"],
					["0", "399001"],
					["0", "399006"],
					["0", "399005"],
					["1", "000300"],
					["0", "399905"],
					["2", "HSI"],
					["2", "HSCEI"],
					["2", "HSCCI"],
					["2", "HSTECH"],
					["3", "DJI"],
					["3", "INX"],
					["3", "IXIC"],
					["3", "NDX"]
				],
				E = function(t) {
					return {
						0: "sz",
						1: "sh",
						2: "hk",
						3: "us",
						p: "pt",
						bj: "bj",
						nq: "nq",
						zhai: "zhai",
						fu: "fu"
					}[t]
				}, S = function(t, e) {
					return F(t) ? "pt".concat(e) : N(t) ? t + e : ["sz", "sh", "hk", "us"][t] + e || ""
				}, k = function(t) {
					var e = t.slice(0, 2),
						n = ["sz", "sh", "hk", "us"].indexOf(e);
					return {
						market: -1 === n ? e : "".concat(n),
						scode: t.slice(2)
					}
				}, _ = function(t) {
					return (t || "").replace(/(\.N|\.OQ|\.AM|\.PS|\.OTC)/g, "")
				}, N = function(t) {
					return "bj" === t || "nq" === t
				}, j = function(t) {
					return 1 === +t
				}, O = function(t) {
					return 0 === +t
				}, P = function(t) {
					return j(t) || O(t)
				}, L = function(t) {
					return 2 === +t
				}, T = function(t) {
					return 3 === +t
				}, A = function(t) {
					return t >= "0930" && t <= "1130" || t >= "1300" && t <= "1500"
				}, I = function(t) {
					return t >= "0930" && t <= "1200" || t >= "1300" && t <= "1600"
				}, C = function(t) {
					return t >= "2130" && t <= "2359" || t >= "0000" && t <= "0400"
				}, U = function(t) {
					return t >= "0900" && t <= "1210" || t >= "1300" && t <= "1610"
				}, D = function(t) {
					return t >= "2130" && t <= "2359" || t >= "0000" && t <= "0400"
				}, z = function(t, e) {
					var n = (0, r.Z)([e, t], ""),
						a = [
							["2", "CES300"],
							["2", "CES100"]
						];
					return (0, o.Z)((0, i.Z)(x, (function(t) {
						return (0, r.Z)(t, "")
					})), n) >= 0 || (0, o.Z)((0, i.Z)(a, (function(t) {
						return (0, r.Z)(t, "")
					})), n) >= 0
				}, M = function(t, e) {
					return "undefined" === typeof e ? /^ZS/.test(t) || "INDEX" === t : O(e) ? /^39/.test(t) || /^98/.test(t) : j(e) ? /^000/.test(t) : z(t, e)
				}, R = function(t) {
					var e = ["FJ", "FJ-CX", "LOF", "ETF", "QDII-LOF/ETF"],
						n = -1 !== e.findIndex((function(e) {
							return t === e
						}));
					return 5 === +t || n
				}, q = function(t, e) {
					return "undefined" === typeof e ? "GP-A-KCB" === t : j(e) && 0 === t.indexOf("68")
				}, W = function(t, e) {
					return "undefined" === typeof e ? "GP-A-CYB" === t : O(e) && 0 === t.indexOf("30")
				}, F = function(t) {
					return "p" === t || "pt" === t
				}, B = function(t) {
					return "ph" === t
				}, G = function(t) {
					return "pu" === t
				}
		},
		60908: function(t, e, n) {
			"use strict";

			function r(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function o(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function i(t, e, n) {
				return e && o(t.prototype, e), n && o(t, n), Object.defineProperty(t, "prototype", {
					writable: !1
				}), t
			}
			n.d(e, {
				Xb: function() {
					return s
				},
				y7: function() {
					return f
				}
			});
			var a = function() {
				function t(e) {
					r(this, t), this.adapter = e
				}
				return i(t, [{
					key: "setItem",
					value: function(t, e) {
						return this.adapter.setItem(t, JSON.stringify(e))
					}
				}, {
					key: "getItem",
					value: function(t) {
						var e = this.adapter.getItem(t);
						try {
							return JSON.parse(e)
						} catch (n) {
							return e
						}
					}
				}, {
					key: "hasOwnProperty",
					value: function(t) {
						return this.adapter.hasOwnProperty(t)
					}
				}, {
					key: "hasItem",
					value: function(t) {
						return this.hasOwnProperty(t)
					}
				}, {
					key: "removeItem",
					value: function(t) {
						this.adapter.removeItem(t)
					}
				}, {
					key: "clear",
					value: function() {
						this.adapter.clear()
					}
				}, {
					key: "key",
					value: function(t) {
						return JSON.parse(this.adapter.key(t))
					}
				}, {
					key: "length",
					get: function() {
						return this.adapter.length
					}
				}]), t
			}(),
				c = {
					setItem: function(t, e) {
						wx.setStorageSync(t, e)
					},
					getItem: function(t) {
						wx.getStorageSync(t)
					},
					hasOwnProperty: function(t) {
						var e = wx.getStorageInfoSync(),
							n = e.keys,
							r = void 0 === n ? [] : n;
						return r.includes(t)
					},
					hasItem: function(t) {
						var e = wx.getStorageInfoSync(),
							n = e.keys,
							r = void 0 === n ? [] : n;
						return r.includes(t)
					},
					removeItem: function(t) {
						wx.removeStorageSync(t)
					},
					clear: function() {
						wx.clearStorage()
					},
					key: function(t) {
						var e = wx.getStorageInfoSync(),
							n = e.keys,
							r = void 0 === n ? [] : n;
						return r[t]
					},
					length: function() {
						var t = wx.getStorageInfoSync(),
							e = t.keys,
							n = void 0 === e ? [] : e;
						return n.length
					}
				}, u = !1;
			try {
				u = !1
			} catch (p) {}
			var s = new a(u ? c : window.localStorage),
				l = !1;
			try {
				l = !1
			} catch (p) {}
			var f = new a(l ? c : window.sessionStorage);
			n(16212)
		},
		83432: function(t, e, n) {
			"use strict";
			n.r(e), n.d(e, {
				default: function() {
					return tr
				}
			});
			var r = n(93666),
				o = "data-wujie-id",
				i = "data-wujie-Flag",
				a = "data-wujie-attach-css-flag",
				c = "wujie_iframe",
				u = "_wujie_all_event",
				s = "position: fixed; z-index: 2147483647; visibility: hidden; inset: 0px; backface-visibility: hidden;",
				l = "url参数为空",
				f = "子应用调用reload无法生效",
				p = "此报错可以忽略，iframe主动中断主应用代码在子应用运行",
				d = "事件订阅数量为空",
				h = "window上不存在fetch属性，需要自行polyfill",
				m = "监听事件回调函数不存在",
				v = "当前浏览器不支持无界，子应用将采用iframe方式渲染",
				y = "脚本请求出现错误",
				g = "样式请求出现错误",
				w = "无界组件短时间重复渲染了两次，可能存在性能问题请检查代码";

			function b(t) {
				return b = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, b(t)
			}
			function x(t, e) {
				return N(t) || _(t, e) || S(t, e) || E()
			}
			function E() {
				throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
			}
			function S(t, e) {
				if (t) {
					if ("string" === typeof t) return k(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? k(t, e) : void 0
				}
			}
			function k(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r
			}
			function _(t, e) {
				var n = null == t ? null : "undefined" !== typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
				if (null != n) {
					var r, o, i = [],
						a = !0,
						c = !1;
					try {
						for (n = n.call(t); !(a = (r = n.next()).done); a = !0) if (i.push(r.value), e && i.length === e) break
					} catch (u) {
						c = !0, o = u
					} finally {
						try {
							a || null == n["return"] || n["return"]()
						} finally {
							if (c) throw o
						}
					}
					return i
				}
			}
			function N(t) {
				if (Array.isArray(t)) return t
			}
			function j(t) {
				return "function" === typeof t
			}
			function O(t) {
				return "LINK" === (null === t || void 0 === t ? void 0 : t.toUpperCase()) || "STYLE" === (null === t || void 0 === t ? void 0 : t.toUpperCase()) || "SCRIPT" === (null === t || void 0 === t ? void 0 : t.toUpperCase()) || "IFRAME" === (null === t || void 0 === t ? void 0 : t.toUpperCase())
			}
			var P = window.Proxy && window.CustomElementRegistry,
				L = "function" === typeof document.all && "undefined" === typeof document.all,
				T = new WeakMap,
				A = function(t) {
					if (T.has(t)) return !0;
					var e = L ? "function" === typeof t && "undefined" !== typeof t : "function" === typeof t;
					return e && T.set(t, e), e
				}, I = new WeakMap;

			function C(t) {
				if (I.has(t)) return I.get(t);
				var e = 0 === t.name.indexOf("bound ") && !t.hasOwnProperty("prototype");
				return I.set(t, e), e
			}
			var U = new WeakMap;

			function D(t) {
				var e = t.prototype && t.prototype.constructor === t && Object.getOwnPropertyNames(t.prototype).length > 1;
				if (e) return !0;
				if (U.has(t)) return U.get(t);
				var n = e;
				if (!n) {
					var r = t.toString(),
						o = /^function\b\s[A-Z].*/,
						i = /^class\b/;
					n = o.test(r) || i.test(r)
				}
				return U.set(t, n), n
			}
			function z(t, e) {
				var n = t[e];
				if (A(n) && !C(n) && !D(n)) {
					var r = Function.prototype.bind.call(n, t);
					for (var o in n) r[o] = n[o];
					return n.hasOwnProperty("prototype") && !r.hasOwnProperty("prototype") && Object.defineProperty(r, "prototype", {
						value: n.prototype,
						enumerable: !1,
						writable: !0
					}), r
				}
				return n
			}
			function M(t) {
				return window.document.querySelector("iframe[".concat(o, '="').concat(t, '"]'))
			}
			function R(t) {
				if (!t) throw Q(l), new Error;
				var e = q(t),
					n = e.protocol + "//" + e.host,
					r = e.pathname + e.search + e.hash;
				return {
					urlElement: e,
					appHostPath: n,
					appRoutePath: r
				}
			}
			function q(t) {
				var e = window.document.createElement("a");
				return e.href = t, e
			}
			function W(t) {
				var e = t.search.replace("?", "").split("&"),
					n = {};
				return e.forEach((function(t) {
					var e = t.split("="),
						r = x(e, 2),
						o = r[0],
						i = r[1];
					o && i && (n[o] = i)
				})), n
			}
			function F(t) {
				var e = W(q(window.location.href));
				return Object.keys(e).includes(t)
			}
			function B(t, e, n) {
				var r = t.Element.prototype.setAttribute;
				e.prototype.setAttribute = function(t, e) {
					var o = e;
					t === n && (o = new URL(e, this.baseURI || "").href), r.call(this, t, o)
				};
				var o = Object.getOwnPropertyDescriptor(e.prototype, n),
					i = o.enumerable,
					a = o.configurable,
					c = o.get,
					u = o.set;
				Object.defineProperty(e.prototype, n, {
					enumerable: i,
					configurable: a,
					get: function() {
						return c.call(this)
					},
					set: function(t) {
						u.call(this, new URL(t, this.baseURI).href)
					}
				})
			}
			function G(t, e) {
				var n, r = q(window.location.href),
					o = W(r);
				r = null;
				var i = window.decodeURIComponent(o[t] || ""),
					a = null === (n = i.match(/^{([^}]*)}/)) || void 0 === n ? void 0 : n[1];
				return e && a ? i.replace("{".concat(a, "}"), e[a]) : i
			}
			var H = window.requestIdleCallback || function(t) {
					return setTimeout(t, 1)
				};

			function $(t) {
				return "string" === typeof t ? document.querySelector(t) : t
			}
			function J(t, e) {
				null === console || void 0 === console || console.warn("[wujie warn]: ".concat(t), e)
			}
			function Q(t, e) {
				null === console || void 0 === console || console.error("[wujie error]: ".concat(t), e)
			}
			function Z(t) {
				var e = t.indexOf(">") + 1,
					n = t.lastIndexOf("<");
				return t.substring(e, n)
			}
			function Y(t) {
				if ("object" === b(t)) return "/";
				try {
					var e = new URL(t, location.href),
						n = e.origin,
						r = e.pathname,
						o = r.split("/");
					return o.pop(), "".concat(n).concat(o.join("/"), "/")
				} catch (i) {
					return console.warn(i), ""
				}
			}
			function V(t) {
				return function(e) {
					for (var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), o = 1; o < n; o++) r[o - 1] = arguments[o];
					return t.reduce((function(t, e) {
						return j(e) ? e.apply(void 0, [t].concat(r)) : t
					}), e || "")
				}
			}
			function X(t) {
				Promise.resolve().then(t)
			}
			function K(t, e) {
				for (var n = arguments.length, r = new Array(n > 2 ? n - 2 : 0), o = 2; o < n; o++) r[o - 2] = arguments[o];
				try {
					t.map((function(t) {
						return t[e]
					})).filter((function(t) {
						return j(t)
					})).forEach((function(t) {
						return t.apply(void 0, r)
					}))
				} catch (i) {
					Q(i)
				}
			}
			function tt(t, e) {
				return it(t) || ot(t, e) || nt(t, e) || et()
			}
			function et() {
				throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
			}
			function nt(t, e) {
				if (t) {
					if ("string" === typeof t) return rt(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? rt(t, e) : void 0
				}
			}
			function rt(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r
			}
			function ot(t, e) {
				var n = null == t ? null : "undefined" !== typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
				if (null != n) {
					var r, o, i = [],
						a = !0,
						c = !1;
					try {
						for (n = n.call(t); !(a = (r = n.next()).done); a = !0) if (i.push(r.value), e && i.length === e) break
					} catch (u) {
						c = !0, o = u
					} finally {
						try {
							a || null == n["return"] || n["return"]()
						} finally {
							if (c) throw o
						}
					}
					return i
				}
			}
			function it(t) {
				if (Array.isArray(t)) return t
			}
			var at = /(<script[\s\S]*?>)[\s\S]*?<\/script>/gi,
				ct = /<(script)\s+((?!type=('|")text\/ng\x2Dtemplate\3)[\s\S])*?>[\s\S]*?<\/\1>/i,
				ut = /.*\ssrc=('|")?([^>'"\s]+)/,
				st = /.*\stype=('|")?([^>'"\s]+)/,
				lt = /.*\sentry\s*.*/,
				ft = /.*\sasync\s*.*/,
				pt = /.*\sdefer\s*.*/,
				dt = /.*\snomodule\s*.*/,
				ht = /.*\stype=('|")?module('|")?\s*.*/,
				mt = /<(link)\s+[\s\S]*?>/gi,
				vt = /\srel=('|")?(preload|prefetch|modulepreload)\1/,
				yt = /.*\shref=('|")?([^>'"\s]+)/,
				gt = /.*\sas=('|")?font\1.*/,
				wt = /<style[^>]*>[\s\S]*?<\/style>/gi,
				bt = /\s+rel=('|")?stylesheet\1.*/,
				xt = /.*\shref=('|")?([^>'"\s]+)/,
				Et = /<!--([\s\S]*?)-->/g,
				St = /<link(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,
				kt = /<style(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,
				_t = /<script(\s+|\s+[\s\S]+\s+)ignore(\s*|\s+[\s\S]*|=[\s\S]*)>/i,
				Nt = /.*\scrossorigin=?('|")?(use-credentials|anonymous)?('|")?/i;

			function jt(t) {
				return t.startsWith("//") || t.startsWith("http://") || t.startsWith("https://")
			}
			function Ot(t, e) {
				return new URL(t, e).toString()
			}
			function Pt(t) {
				var e = ["text/javascript", "module", "application/javascript", "text/ecmascript", "application/ecmascript"];
				return !t || -1 !== e.indexOf(t)
			}
			function Lt() {
				var t = window.document.createElement("script");
				return "noModule" in t
			}
			var Tt = function(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
				return "\x3c!-- ".concat(e ? "prefetch/preload/modulepreload" : "", " link ").concat(t, " replaced by wujie --\x3e")
			}, At = function(t) {
				return "\x3c!-- inline-style-".concat(t, " replaced by wujie --\x3e")
			}, It = function(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "";
				return "\x3c!-- ".concat(e, " script ").concat(t, " replaced by wujie --\x3e")
			}, Ct = "\x3c!-- inline scripts replaced by wujie --\x3e",
				Ut = function(t) {
					return "\x3c!-- ignore asset ".concat(t || "file", " replaced by wujie --\x3e")
				}, Dt = function(t, e) {
					return "\x3c!-- ".concat(e ? "nomodule" : "module", " script ").concat(t, " ignored by wujie --\x3e")
				};

			function zt(t, e, n) {
				var r = [],
					o = [],
					i = null,
					a = Lt(),
					c = t.replace(Et, "").replace(mt, (function(t) {
						var n = !! t.match(bt);
						if (n) {
							var r = t.match(xt),
								i = t.match(St);
							if (r) {
								var a = r && r[2],
									c = a;
								return a && !jt(a) && (c = Ot(a, e)), i ? Ut(c) : (o.push({
									src: c
								}), Tt(c))
							}
						}
						var u = t.match(vt) && t.match(yt) && !t.match(gt);
						if (u) {
							var s = t.match(yt),
								l = tt(s, 3),
								f = l[2];
							return Tt(f, !0)
						}
						return t
					})).replace(wt, (function(t) {
						if (kt.test(t)) return Ut("style file");
						var e = Z(t);
						return o.push({
							src: "",
							content: e
						}), At(o.length - 1)
					})).replace(at, (function(t, n) {
						var o = n.match(_t),
							c = !! n.match(ht),
							u = n.match(Nt),
							s = (null === u || void 0 === u ? void 0 : u[2]) || "",
							l = a && !! n.match(dt) || !a && c,
							f = n.match(st),
							p = f && f[2];
						if (!Pt(p)) return t;
						if (ct.test(t) && n.match(ut)) {
							var d = n.match(lt),
								h = n.match(ut),
								m = h && h[2];
							if (i && d) throw new SyntaxError("You should not set multiply entry script!");
							if (m && !jt(m) && (m = Ot(m, e)), i = i || d && m, o) return Ut(m || "js file");
							if (l) return Dt(m || "js file", a);
							if (m) {
								var v = !! n.match(ft),
									y = !! n.match(pt);
								return r.push(v || y ? {
									async: v,
									defer: y,
									src: m,
									module: c,
									crossorigin: !! u,
									crossoriginType: s
								} : {
									src: m,
									module: c,
									crossorigin: !! u,
									crossoriginType: s
								}), It(m, (v ? "async" : y && "defer") || "")
							}
							return t
						}
						if (o) return Ut("js file");
						if (l) return Dt("js file", a);
						var g = Z(t),
							w = g.split(/[\r\n]+/).every((function(t) {
								return !t.trim() || t.trim().startsWith("//")
							}));
						return !w && g && r.push({
							src: null,
							content: g,
							module: c,
							crossorigin: !! u,
							crossoriginType: s
						}), Ct
					})),
					u = {
						template: c,
						scripts: r,
						styles: o,
						entry: i || r[r.length - 1]
					};
				return "function" === typeof n && (u = n(u)), u
			}
			function Mt(t) {
				return Mt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, Mt(t)
			}
			function Rt() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				Rt = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (N) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new S(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return _()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = b(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (N) {
						return {
							type: "throw",
							arg: N
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function d() {}
				var h = {};
				c(h, o, (function() {
					return this
				}));
				var m = Object.getPrototypeOf,
					v = m && m(m(k([])));
				v && v !== e && n.call(v, o) && (h = v);
				var y = d.prototype = f.prototype = Object.create(h);

				function g(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function w(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == Mt(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function b(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, b(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function x(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function E(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function S(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(x, this), this.reset(!0)
				}
				function k(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: _
					}
				}
				function _() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = d, c(y, "constructor", d), c(d, "constructor", p), p.displayName = c(d, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, d) : (t.__proto__ = d, c(t, a, "GeneratorFunction")), t.prototype = Object.create(y), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, g(w.prototype), c(w.prototype, i, (function() {
					return this
				})), t.AsyncIterator = w, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new w(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, g(y), c(y, a, "Generator"), c(y, o, (function() {
					return this
				})), c(y, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = k, S.prototype = {
					constructor: S,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									E(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: k(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			var qt = function(t, e, n, r) {
				function o(t) {
					return t instanceof n ? t : new n((function(e) {
						e(t)
					}))
				}
				return new(n || (n = Promise))((function(n, i) {
					function a(t) {
						try {
							u(r.next(t))
						} catch (e) {
							i(e)
						}
					}
					function c(t) {
						try {
							u(r["throw"](t))
						} catch (e) {
							i(e)
						}
					}
					function u(t) {
						t.done ? n(t.value) : o(t.value).then(a, c)
					}
					u((r = r.apply(t, e || [])).next())
				}))
			}, Wt = {}, Ft = {}, Bt = {};
			if (!window.fetch) throw Q(h), new Error;
			var Gt = window.fetch.bind(window);

			function Ht(t) {
				return t
			}
			function $t(t, e, n) {
				return qt(this, void 0, void 0, Rt().mark((function r() {
					var o, i, a;
					return Rt().wrap((function(r) {
						while (1) switch (r.prev = r.next) {
							case 0:
								return o = V(t.plugins.map((function(t) {
									return t.cssLoader
								}))), i = n().map((function(t) {
									var e = t.src,
										n = t.contentPromise;
									return {
										src: e,
										contentPromise: n.then((function(t) {
											return o(t, e)
										}))
									}
								})), r.next = 4, Jt(e, i);
							case 4:
								return a = r.sent, r.abrupt("return", t.replace ? t.replace(a) : a);
							case 6:
							case "end":
								return r.stop()
						}
					}), r)
				})))
			}
			function Jt(t, e) {
				return qt(this, void 0, void 0, Rt().mark((function n() {
					var r;
					return Rt().wrap((function(n) {
						while (1) switch (n.prev = n.next) {
							case 0:
								return r = t, n.abrupt("return", Promise.all(e.map((function(t, e) {
									return t.contentPromise.then((function(n) {
										t.src ? r = r.replace(Tt(t.src), "<style>/* ".concat(t.src, " */").concat(n, "</style>")) : n && (r = r.replace(At(e), "<style>/* inline-style-".concat(e, " */").concat(n, "</style>")))
									}))
								}))).then((function() {
									return r
								})));
							case 2:
							case "end":
								return n.stop()
						}
					}), n)
				})))
			}
			var Qt = function(t) {
				return t.startsWith("<")
			}, Zt = function(t, e, n, r, o) {
				return e[t] || (e[t] = n(t).then((function(n) {
					if (n.status >= 400) {
						if (e[t] = null, r) return Q(g, {
							src: t,
							response: n
						}), null === o || void 0 === o || o(t, new Error(g)), "";
						throw Q(y, {
							src: t,
							response: n
						}), null === o || void 0 === o || o(t, new Error(y)), new Error(y)
					}
					return n.text()
				})).
				catch ((function(n) {
					if (e[t] = null, r) return Q(g, t), null === o || void 0 === o || o(t, n), "";
					throw Q(y, t), null === o || void 0 === o || o(t, n), n
				})))
			};

			function Yt(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : Gt,
					n = arguments.length > 2 ? arguments[2] : void 0;
				return t.map((function(t) {
					var r = t.src,
						o = t.content;
					return o ? {
						src: "",
						contentPromise: Promise.resolve(o)
					} : Qt(r) ? {
						src: "",
						contentPromise: Promise.resolve(Z(r))
					} : {
						src: r,
						contentPromise: Zt(r, Wt, e, !0, n)
					}
				}))
			}
			function Vt(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : Gt,
					n = arguments.length > 2 ? arguments[2] : void 0;
				return t.map((function(t) {
					var r = t.src,
						o = t.async,
						i = t.defer,
						a = t.module,
						c = null;
					return c = (o || i) && r && !a ? new Promise((function(t, o) {
						return H((function() {
							return Zt(r, Ft, e, !1, n).then(t, o)
						}))
					})) : a && r ? Promise.resolve("") : r ? Zt(r, Ft, e, !1, n) : Promise.resolve(t.content), Object.assign(Object.assign({}, t), {
						contentPromise: c
					})
				}))
			}
			function Xt(t, e) {
				var n, r = null !== (n = e.fetch) && void 0 !== n ? n : Gt,
					o = e.plugins,
					i = e.loadError,
					a = o ? V(o.map((function(t) {
						return t.htmlLoader
					}))) : Ht,
					c = o ? o.map((function(t) {
						return t.jsExcludes
					})).reduce((function(t, e) {
						return t.concat(e)
					}), []).filter((function(t) {
						return t
					})) : [],
					u = o ? o.map((function(t) {
						return t.cssExcludes
					})).reduce((function(t, e) {
						return t.concat(e)
					}), []).filter((function(t) {
						return t
					})) : [],
					s = Y,
					l = function(t, e) {
						return r(t).then((function(t) {
							return t.text()
						}), (function(e) {
							return null === i || void 0 === i || i(t, e), Promise.reject(e)
						})).then((function(n) {
							var o = s(t),
								a = zt(e(n), o),
								l = a.template,
								f = a.scripts,
								p = a.styles;
							return {
								template: l,
								assetPublicPath: o,
								getExternalScripts: function() {
									return Vt(f.filter((function(t) {
										return !t.src || !c.length || !c.includes(t.src)
									})), r, i)
								},
								getExternalStyleSheets: function() {
									return Yt(p.filter((function(t) {
										return !t.src || !u.length || !u.includes(t.src)
									})), r, i)
								}
							}
						}))
					};
				return (null === e || void 0 === e ? void 0 : e.plugins.some((function(t) {
					return t.htmlLoader
				}))) ? l(t, a) : Bt[t] || (Bt[t] = l(t, a))
			}
			var Kt = window.__POWERED_BY_WUJIE__ ? window.__WUJIE.inject.idToSandboxMap : new Map;

			function te(t) {
				return Kt.get(t) || null
			}
			function ee(t, e) {
				Kt.set(t, e)
			}
			function ne(t) {
				Kt.delete(t)
			}
			var re = {
				modifyLocalProperties: ["createElement", "createTextNode", "documentURI", "URL", "getElementsByTagName"],
				modifyProperties: ["createElement", "createTextNode", "documentURI", "URL", "getElementsByTagName", "getElementsByClassName", "getElementsByName", "getElementById", "querySelector", "querySelectorAll", "documentElement", "scrollingElement", "forms", "images", "links"],
				shadowProperties: ["activeElement", "childElementCount", "children", "firstElementChild", "firstChild", "fullscreenElement", "lastElementChild", "pictureInPictureElement", "pointerLockElement", "styleSheets"],
				shadowMethods: ["append", "contains", "getSelection", "elementFromPoint", "elementsFromPoint", "getAnimations", "replaceChildren"],
				documentProperties: ["characterSet", "compatMode", "contentType", "designMode", "dir", "doctype", "embeds", "fullscreenEnabled", "hidden", "implementation", "lastModified", "pictureInPictureEnabled", "plugins", "readyState", "referrer", "visibilityState", "fonts"],
				documentMethods: ["execCommand", "createRange", "exitFullscreen", "exitPictureInPicture", "getElementsByTagNameNS", "hasFocus", "prepend"],
				documentEvents: ["onreadystatechange", "onpointerlockchange", "onpointerlockerror", "onbeforecopy", "onbeforecut", "onbeforepaste", "onfreeze", "onresume", "onsearch", "onfullscreenchange", "onfullscreenerror", "onsecuritypolicyviolation", "onvisibilitychange"],
				ownerProperties: ["head", "body"]
			}, oe = ["DOMContentLoaded", "fullscreenchange", "fullscreenerror", "readystatechange", "scroll", "selectionchange", "visibilitychange", "wheel"],
				ie = ["gotpointercapture", "lostpointercapture"],
				ae = ["hashchange", "popstate"],
				ce = {
					IMG: "src",
					A: "href",
					SOURCE: "src"
				}, ue = ["getComputedStyle", "visualViewport", "matchMedia", "DOMParser"],
				se = [/animationFrame$/i, /resizeObserver$|mutationObserver$|intersectionObserver$/i, /height$|width$|left$/i, /^screen/i, /X$|Y$/],
				le = HTMLElement.prototype.appendChild,
				fe = HTMLElement.prototype.removeChild,
				pe = HTMLHeadElement.prototype.insertBefore,
				de = HTMLBodyElement.prototype.insertBefore,
				he = Window.prototype.addEventListener,
				me = Window.prototype.removeEventListener,
				ve = Node.prototype.appendChild,
				ye = window.__POWERED_BY_WUJIE__ ? window.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__ : Document.prototype.querySelector;

			function ge(t, e) {
				return Object.defineProperties(t, {
					srcElement: {
						get: e
					},
					target: {
						get: e
					}
				}), t
			}
			function we(t, e) {
				var n = new CustomEvent(e),
					r = ge(n, (function() {
						return t
					}));
				j(t["on".concat(e)]) ? t["on".concat(e)](r) : t.dispatchEvent(r)
			}
			function be(t, e) {
				if (t.innerHTML && !e.degrade) {
					var n = Ve([t.sheet]);
					n && e.shadowRoot.head.appendChild(n)
				}
			}
			function xe(t, e, n) {
				var r = Object.getOwnPropertyDescriptor(Element.prototype, "innerHTML"),
					o = Object.getOwnPropertyDescriptor(HTMLElement.prototype, "innerText"),
					i = Object.getOwnPropertyDescriptor(Node.prototype, "textContent");
				Object.defineProperties(t, {
					innerHTML: {
						get: function() {
							return r.get.call(t)
						},
						set: function(o) {
							var i = this;
							r.set.call(t, e(o)), X((function() {
								return be(i, n)
							}))
						}
					},
					innerText: {
						get: function() {
							return o.get.call(t)
						},
						set: function(r) {
							var i = this;
							o.set.call(t, e(r)), X((function() {
								return be(i, n)
							}))
						}
					},
					textContent: {
						get: function() {
							return i.get.call(t)
						},
						set: function(r) {
							var o = this;
							i.set.call(t, e(r)), X((function() {
								return be(o, n)
							}))
						}
					},
					appendChild: {
						value: function(r) {
							var o = this;
							return X((function() {
								return be(o, n)
							})), r.nodeType === Node.TEXT_NODE ? ve.call(t, t.ownerDocument.createTextNode(e(r.textContent))) : ve(r)
						}
					}
				})
			}
			function Ee(t) {
				return function(e, n) {
					var r, a, c = this,
						u = e,
						s = t.rawDOMAppendOrInsertBefore,
						l = t.wujieId,
						f = te(l);
					if (!O(u.tagName) || !l) return s.call(this, u, n);
					var p = f.styleSheetElements,
						d = f.replace,
						h = f.fetch,
						m = f.plugins,
						v = f.iframe,
						y = f.lifecycles,
						g = v.contentDocument;
					if (u.tagName) switch (null === (r = u.tagName) || void 0 === r ? void 0 : r.toUpperCase()) {
						case "LINK":
							var b = u,
								x = b.href;
							x && !m.map((function(t) {
								return t.cssExcludes
							})).reduce((function(t, e) {
								return t.concat(e)
							}), []).filter((function(t) {
								return t
							})).includes(x) && Yt([{
								src: x
							}], h, y.loadError).forEach((function(t) {
								var e = t.src,
									r = t.contentPromise;
								return r.then((function(t) {
									var r = g.createElement("style");
									r.innerHTML = V(m.map((function(t) {
										return t.cssLoader
									})))(d ? d(t) : t, e), p.push(r), be(r, f), s.call(c, r, n), we(u, "load"), u = null
								}), (function() {
									we(u, "error"), u = null
								}))
							}));
							var E = g.createComment("dynamic link ".concat(x, " replaced by wujie"));
							return s.call(this, E, n);
						case "STYLE":
							var S = e;
							p.push(S);
							var k = S.innerHTML,
								_ = function(t) {
									return V(m.map((function(t) {
										return t.cssLoader
									})))(d ? d(t) : t)
								};
							return k && (S.innerHTML = _(k)), xe(S, _, f), be(S, f), s.call(this, u, n);
						case "SCRIPT":
							var N = u,
								j = N.src,
								P = N.text;
							if (j && !m.map((function(t) {
								return t.jsExcludes
							})).reduce((function(t, e) {
								return t.concat(e)
							}), []).filter((function(t) {
								return t
							})).includes(j)) {
								var L = function(t) {
									if (null === f.iframe) return J(w);
									En(t, f.iframe.contentWindow), we(u, "load"), u = null
								};
								Vt([{
									src: j
								}], h, y.loadError).forEach((function(t) {
									return t.contentPromise.then((function(e) {
										var n;
										if (null === f.execQueue) return J(w);
										var r = null === (n = f.execQueue) || void 0 === n ? void 0 : n.length;
										f.execQueue.push((function() {
											return f.fiber ? H((function() {
												L(Object.assign(Object.assign({}, t), {
													content: e
												}))
											})) : L(Object.assign(Object.assign({}, t), {
												content: e
											}))
										})), r || f.execQueue.shift()()
									}), (function() {
										we(u, "error"), u = null
									}))
								}))
							} else {
								var T = null === (a = f.execQueue) || void 0 === a ? void 0 : a.length;
								f.execQueue.push((function() {
									return H((function() {
										En({
											src: null,
											content: P
										}, f.iframe.contentWindow)
									}))
								})), T || f.execQueue.shift()()
							}
							var A = g.createComment("dynamic script ".concat(j, " replaced by wujie"));
							return s.call(this, A, n);
						case "IFRAME":
							if ("" === u.getAttribute(i)) return ve.call(ye.call(this.ownerDocument, "html"), u);
							var I = s.call(this, u, n);
							try {
								if (!u.getAttribute(o)) {
									var C = u.contentWindow.document.createElement("script");
									C.type = "text/javascript", C.innerHTML = "Array.prototype.slice.call(window.parent.frames).some(iframe => {if(iframe.name === '".concat(l, "'){window.parent = iframe;return true};return false})"), u.contentDocument.head.insertBefore(C, u.contentDocument.head.firstChild)
								}
							} catch (U) {
								Q(U)
							}
							return I;
						default:
					}
				}
			}
			function Se(t, e) {
				t.head.appendChild = Ee({
					rawDOMAppendOrInsertBefore: ve,
					wujieId: e
				}), t.head.insertBefore = Ee({
					rawDOMAppendOrInsertBefore: pe,
					wujieId: e
				}), t.body.appendChild = Ee({
					rawDOMAppendOrInsertBefore: ve,
					wujieId: e
				}), t.body.insertBefore = Ee({
					rawDOMAppendOrInsertBefore: de,
					wujieId: e
				})
			}
			function ke() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				ke = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (N) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new S(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return _()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = b(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (N) {
						return {
							type: "throw",
							arg: N
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function d() {}
				var h = {};
				c(h, o, (function() {
					return this
				}));
				var m = Object.getPrototypeOf,
					v = m && m(m(k([])));
				v && v !== e && n.call(v, o) && (h = v);
				var y = d.prototype = f.prototype = Object.create(h);

				function g(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function w(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == _e(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function b(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, b(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function x(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function E(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function S(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(x, this), this.reset(!0)
				}
				function k(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: _
					}
				}
				function _() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = d, c(y, "constructor", d), c(d, "constructor", p), p.displayName = c(d, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, d) : (t.__proto__ = d, c(t, a, "GeneratorFunction")), t.prototype = Object.create(y), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, g(w.prototype), c(w.prototype, i, (function() {
					return this
				})), t.AsyncIterator = w, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new w(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, g(y), c(y, a, "Generator"), c(y, o, (function() {
					return this
				})), c(y, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = k, S.prototype = {
					constructor: S,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									E(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: k(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			function _e(t) {
				return _e = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, _e(t)
			}
			function Ne(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function je(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function Oe(t, e, n) {
				return e && je(t.prototype, e), n && je(t, n), Object.defineProperty(t, "prototype", {
					writable: !1
				}), t
			}
			function Pe(t, e) {
				if ("function" !== typeof e && null !== e) throw new TypeError("Super expression must either be null or a function");
				t.prototype = Object.create(e && e.prototype, {
					constructor: {
						value: t,
						writable: !0,
						configurable: !0
					}
				}), Object.defineProperty(t, "prototype", {
					writable: !1
				}), e && ze(t, e)
			}
			function Le(t) {
				var e = Ue();
				return function() {
					var n, r = Me(t);
					if (e) {
						var o = Me(this).constructor;
						n = Reflect.construct(r, arguments, o)
					} else n = r.apply(this, arguments);
					return Te(this, n)
				}
			}
			function Te(t, e) {
				if (e && ("object" === _e(e) || "function" === typeof e)) return e;
				if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined");
				return Ae(t)
			}
			function Ae(t) {
				if (void 0 === t) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
				return t
			}
			function Ie(t) {
				var e = "function" === typeof Map ? new Map : void 0;
				return Ie = function(t) {
					if (null === t || !De(t)) return t;
					if ("function" !== typeof t) throw new TypeError("Super expression must either be null or a function");
					if ("undefined" !== typeof e) {
						if (e.has(t)) return e.get(t);
						e.set(t, n)
					}
					function n() {
						return Ce(t, arguments, Me(this).constructor)
					}
					return n.prototype = Object.create(t.prototype, {
						constructor: {
							value: n,
							enumerable: !1,
							writable: !0,
							configurable: !0
						}
					}), ze(n, t)
				}, Ie(t)
			}
			function Ce(t, e, n) {
				return Ce = Ue() ? Reflect.construct.bind() : function(t, e, n) {
					var r = [null];
					r.push.apply(r, e);
					var o = Function.bind.apply(t, r),
						i = new o;
					return n && ze(i, n.prototype), i
				}, Ce.apply(null, arguments)
			}
			function Ue() {
				if ("undefined" === typeof Reflect || !Reflect.construct) return !1;
				if (Reflect.construct.sham) return !1;
				if ("function" === typeof Proxy) return !0;
				try {
					return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))), !0
				} catch (t) {
					return !1
				}
			}
			function De(t) {
				return -1 !== Function.toString.call(t).indexOf("[native code]")
			}
			function ze(t, e) {
				return ze = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t, e) {
					return t.__proto__ = e, t
				}, ze(t, e)
			}
			function Me(t) {
				return Me = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t) {
					return t.__proto__ || Object.getPrototypeOf(t)
				}, Me(t)
			}
			var Re = function(t, e, n, r) {
				function o(t) {
					return t instanceof n ? t : new n((function(e) {
						e(t)
					}))
				}
				return new(n || (n = Promise))((function(n, i) {
					function a(t) {
						try {
							u(r.next(t))
						} catch (e) {
							i(e)
						}
					}
					function c(t) {
						try {
							u(r["throw"](t))
						} catch (e) {
							i(e)
						}
					}
					function u(t) {
						t.done ? n(t.value) : o(t.value).then(a, c)
					}
					u((r = r.apply(t, e || [])).next())
				}))
			}, qe = {
				":root": ":host"
			}, We = function(t) {
				Pe(n, t);
				var e = Le(n);

				function n() {
					return Ne(this, n), e.apply(this, arguments)
				}
				return Oe(n, [{
					key: "connectedCallback",
					value: function() {
						if (!this.shadowRoot) {
							var t = this.attachShadow({
								mode: "open"
							}),
								e = te(this.getAttribute(o));
							bn(t, e.iframe.contentWindow), e.shadowRoot = t
						}
					}
				}, {
					key: "disconnectedCallback",
					value: function() {
						var t = te(this.getAttribute(o));
						t.unmount()
					}
				}]), n
			}(Ie(HTMLElement));

			function Fe() {
				customElements.get("wujie-app") || customElements.define("wujie-app", We)
			}
			function Be(t) {
				var e = window.document.createElement("wujie-app");
				return e.setAttribute(o, t), e.classList.add(c), e
			}
			function Ge(t, e) {
				var n = $(e);
				return n && !n.contains(t) && (Ye(n), t && le.call(n, t)), n
			}
			function He(t, e) {
				return Re(this, void 0, void 0, ke().mark((function n() {
					var r, o, i, a, c, u;
					return ke().wrap((function(n) {
						while (1) switch (n.prev = n.next) {
							case 0:
								return r = t.iframe.contentDocument, o = t.plugins, i = t.replace, a = function(t, e) {
									return V(o.map((function(t) {
										return t.cssLoader
									})))(i ? i(t) : t, e)
								}, c = t.plugins.map((function(t) {
									return t.cssBeforeLoaders
								})).reduce((function(t, e) {
									return t.concat(e)
								}), []).filter((function(t) {
									return "object" === _e(t)
								})).reverse(), u = t.plugins.map((function(t) {
									return t.cssAfterLoaders
								})).reduce((function(t, e) {
									return t.concat(e)
								}), []).filter((function(t) {
									return "object" === _e(t)
								})), n.next = 7, Promise.all([Promise.all(Yt(c, t.fetch, t.lifecycles.loadError).map((function(t) {
									var e = t.src,
										n = t.contentPromise;
									return n.then((function(t) {
										return {
											src: e,
											content: t
										}
									}))
								}))).then((function(t) {
									t.forEach((function(t) {
										var n, o = t.src,
											i = t.content;
										if (i) {
											var c = r.createElement("style");
											c.setAttribute("type", "text/css"), c.appendChild(r.createTextNode(i ? a(i, o) : i));
											var u = e.querySelector("head");
											null === u || void 0 === u || u.insertBefore(c, null === (n = e.querySelector("head")) || void 0 === n ? void 0 : n.firstChild)
										}
									}))
								})), Promise.all(Yt(u, t.fetch, t.lifecycles.loadError).map((function(t) {
									var e = t.src,
										n = t.contentPromise;
									return n.then((function(t) {
										return {
											src: e,
											content: t
										}
									}))
								}))).then((function(t) {
									t.forEach((function(t) {
										var n = t.src,
											o = t.content;
										if (o) {
											var i = r.createElement("style");
											i.setAttribute("type", "text/css"), i.appendChild(r.createTextNode(o ? a(o, n) : o)), e.appendChild(i)
										}
									}))
								}))]).then((function() {
									return e
								}), (function() {
									return e
								}));
							case 7:
								return n.abrupt("return", n.sent);
							case 8:
							case "end":
								return n.stop()
						}
					}), n)
				})))
			}
			function $e(t, e) {
				var n = t.document,
					r = n.createElement("html");
				r.innerHTML = e;
				var o = n.createTreeWalker(r, NodeFilter.SHOW_ELEMENT),
					i = o.currentNode;
				while (i) {
					bn(i, t);
					var a = ce[i.tagName],
						c = i[a];
					a && i.setAttribute(a, new URL(c, i.baseURI || "").href), i = o.nextNode()
				}
				if (!r.querySelector("head")) {
					var u = n.createElement("head");
					r.appendChild(u)
				}
				if (!r.querySelector("body")) {
					var s = n.createElement("body");
					r.appendChild(s)
				}
				return r
			}
			function Je(t, e, n) {
				return Re(this, void 0, void 0, ke().mark((function r() {
					var o, i, a;
					return ke().wrap((function(r) {
						while (1) switch (r.prev = r.next) {
							case 0:
								return o = $e(e, n), r.next = 3, He(e.__WUJIE, o);
							case 3:
								i = r.sent, t.appendChild(i), a = document.createElement("div"), a.setAttribute("style", s), i.insertBefore(a, i.firstChild), t.head = t.querySelector("head"), t.body = t.querySelector("body"), Object.defineProperty(t.firstChild, "parentNode", {
									enumerable: !0,
									configurable: !0,
									get: function() {
										return e.document
									}
								}), Se(t, e.__WUJIE.id);
							case 12:
							case "end":
								return r.stop()
						}
					}), r)
				})))
			}
			function Qe(t) {
				var e = document.createElement("iframe");
				return e.setAttribute("style", "width: 100%; height:100%"), e.setAttribute(o, t), e
			}
			function Ze(t, e, n) {
				return Re(this, void 0, void 0, ke().mark((function r() {
					var o, i;
					return ke().wrap((function(r) {
						while (1) switch (r.prev = r.next) {
							case 0:
								return Ye(t), o = $e(e, n), r.next = 4, He(e.__WUJIE, o);
							case 4:
								i = r.sent, t.appendChild(i), Object.defineProperty(t.firstElementChild, "parentNode", {
									enumerable: !0,
									configurable: !0,
									get: function() {
										return e.document
									}
								}), Se(t, e.__WUJIE.id);
							case 8:
							case "end":
								return r.stop()
						}
					}), r)
				})))
			}
			function Ye(t) {
				while (t.firstChild) fe.call(t, t.firstChild)
			}
			function Ve(t) {
				for (var e, n, r = [], o = /:root/g, i = 0; i < t.length; i++) for (var a = null !== (n = null === (e = t[i]) || void 0 === e ? void 0 : e.cssRules) && void 0 !== n ? n : [], c = 0; c < a.length; c++) {
					var u = a[c].cssText;
					o.test(u) && r.push(u.replace(o, (function(t) {
						return qe[t]
					})))
				}
				if (r.length) {
					var s = window.document.createElement("style");
					return s.innerHTML = r.join(""), s
				}
			}
			function Xe(t) {
				var e = t.__WUJIE,
					n = e.sync,
					r = e.id,
					o = e.prefix;
				if (n) {
					var i = t.location.pathname + t.location.search + t.location.hash,
						a = "";
					o && Object.keys(o).forEach((function(t) {
						var e = o[t];
						i.startsWith(e) && (!a || e.length > o[a].length) && (a = t)
					}));
					var c = q(window.location.href),
						u = W(c);
					u[r] = window.encodeURIComponent(a ? i.replace(o[a], "{".concat(a, "}")) : i);
					var s = "?" + Object.keys(u).map((function(t) {
						return t + "=" + u[t]
					})).join("&");
					c.search = s, c.href !== window.location.href && window.history.replaceState(null, "", c.href), c = null
				}
			}
			function Ke(t) {
				var e = t.location,
					n = e.pathname,
					r = e.search,
					o = e.hash,
					i = t.__WUJIE,
					a = i.id,
					c = i.url,
					u = i.sync,
					s = i.execFlag,
					l = i.prefix,
					f = i.inject,
					p = u && !s ? G(a, l) : c,
					d = (/^http/.test(p) ? null : p) || c,
					h = R(d),
					m = h.appRoutePath,
					v = n + r + o;
				v !== m && t.history.replaceState(null, "", f.mainHostPath + m)
			}
			function tn() {
				var t = q(window.location.href),
					e = W(t);
				Object.keys(e).forEach((function(t) {
					var n, r = te(t);
					if (r) {
						var o = r.degrade ? !window.document.contains(M(r.id)) : !window.document.contains(null === (n = null === r || void 0 === r ? void 0 : r.shadowRoot) || void 0 === n ? void 0 : n.host);
						r.execFlag && r.sync && !r.hrefFlag && o && delete e[t]
					}
				}));
				var n = "?" + Object.keys(e).map((function(t) {
					return t + "=" + e[t]
				})).join("&");
				t.search = n, t.href !== window.location.href && window.history.replaceState(null, "", t.href), t = null
			}
			function en(t, e) {
				var n = q(window.location.href),
					r = W(n);
				r[t] = window.encodeURIComponent(e);
				var o = "?" + Object.keys(r).map((function(t) {
					return t + "=" + r[t]
				})).join("&");
				n.search = o, window.history.pushState(null, "", n.href), n = null
			}
			function nn() {
				window.addEventListener("popstate", (function() {
					var t = q(window.location.href),
						e = W(t);
					t = null, Object.keys(e).map((function(t) {
						return te(t)
					})).filter((function(t) {
						return t
					})).forEach((function(t) {
						var n = e[t.id],
							r = ye.call(t.iframe.contentDocument, "body");
						if (/http/.test(n)) t.degrade ? (Ge(t.document.firstElementChild, r), Sn(window.decodeURIComponent(n), M(t.id).parentElement)) : Sn(window.decodeURIComponent(n), t.shadowRoot.host.parentElement), t.hrefFlag = !0;
						else if (t.hrefFlag) {
							if (t.degrade) {
								var o = Qe(t.id);
								Ge(o, t.el), Ye(o.contentDocument), dn(o.contentWindow, t.iframe.contentWindow), o.contentWindow.onunload = function() {
									t.unmount()
								}, o.contentDocument.appendChild(r.firstElementChild), t.document = o.contentDocument
							} else Ge(t.shadowRoot.host, t.el);
							t.hrefFlag = !1
						}
					}))
				}))
			}
			function rn(t, e, n) {
				return e in t ? Object.defineProperty(t, e, {
					value: n,
					enumerable: !0,
					configurable: !0,
					writable: !0
				}) : t[e] = n, t
			}
			function on(t) {
				t.addEventListener = function(e, n, r) {
					if (K(t.__WUJIE.plugins, "windowAddEventListenerHook", t, e, n, r), ae.includes(e)) return he.call(t, e, n, r);
					he.call(window.window, e, n, r)
				}, t.removeEventListener = function(e, n, r) {
					if (K(t.__WUJIE.plugins, "windowRemoveEventListenerHook", t, e, n, r), ae.includes(e)) return me.call(t, e, n, r);
					me.call(window.window, e, n, r)
				}
			}
			function an(t, e) {
				t.__WUJIE_PUBLIC_PATH__ = e + "/", t.$wujie = t.__WUJIE.provide, t.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__ = t.Document.prototype.querySelector, t.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__ = t.Document.prototype.querySelectorAll
			}
			function cn(t, e, n) {
				var r = t.history,
					o = r.pushState,
					i = r.replaceState;
				r.pushState = function(i, a, c) {
					var u = n + t.location.pathname + t.location.search + t.location.hash,
						s = new URL(null === c || void 0 === c ? void 0 : c.replace(e, ""), u).href,
						l = void 0 === c;
					o.call(r, i, a, l ? void 0 : s), l || (un(t, e, n), Xe(t))
				}, r.replaceState = function(o, a, c) {
					var u = n + t.location.pathname + t.location.search + t.location.hash,
						s = new URL(null === c || void 0 === c ? void 0 : c.replace(e, ""), u).href,
						l = void 0 === c;
					i.call(r, o, a, l ? void 0 : s), l || (un(t, e, n), Xe(t))
				}
			}
			function un(t, e, n) {
				var r, o = new URL(null === (r = t.location.href) || void 0 === r ? void 0 : r.replace(n, ""), e),
					i = ye.call(t.document, "base");
				i && i.setAttribute("href", e + o.pathname)
			}
			function sn(t) {
				function e(e) {
					var n = t[e];
					try {
						return "function" !== typeof n || D(n) ? t[e] = window[e] : t[e] = window[e].bind(window), !0
					} catch (r) {
						return J(r.message), !1
					}
				}
				Object.getOwnPropertyNames(t).forEach((function(n) {
					"getSelection" !== n ? ue.includes(n) ? e(n) : se.some((function(r) {
						return !(!r.test(n) || !(n in t.parent)) && e(n)
					})) : Object.defineProperty(t, n, {
						get: function() {
							return t.document[n]
						}
					})
				}));
				var n = Object.getOwnPropertyNames(window).filter((function(t) {
					return /^on/.test(t)
				}));
				n.forEach((function(e) {
					var n = Object.getOwnPropertyDescriptor(t, e) || {
						enumerable: !0,
						writable: !0
					};
					try {
						Object.defineProperty(t, e, {
							enumerable: n.enumerable,
							configurable: !0,
							get: function() {
								return window[e]
							},
							set: function(r) {
								(n.writable || n.set) && (window[e] = null === r || void 0 === r ? void 0 : r.bind(t))
							}
						})
					} catch (e) {
						J(e.message)
					}
				})), K(t.__WUJIE.plugins, "windowPropertyOverride", t)
			}
			function ln(t) {
				var e = t.EventTarget.prototype.addEventListener,
					n = t.EventTarget.prototype.removeEventListener,
					r = t.__WUJIE;
				t.EventTarget.prototype.addEventListener = function(t, n, o) {
					var i = r.elementEventCacheMap.get(this);
					return i ? i.find((function(t) {
						return t.handler === n
					})) || i.push({
						type: t,
						handler: n,
						options: o
					}) : r.elementEventCacheMap.set(this, [{
						type: t,
						handler: n,
						options: o
					}]), e.call(this, t, n, o)
				}, t.EventTarget.prototype.removeEventListener = function(t, e, o) {
					var i = r.elementEventCacheMap.get(this);
					if (i) {
						var a = null === i || void 0 === i ? void 0 : i.findIndex((function(t) {
							return t.handler === e
						}));
						i.splice(a, 1)
					}
					return (null === i || void 0 === i ? void 0 : i.length) || r.elementEventCacheMap.delete(this), n.call(this, t, e, o)
				}
			}
			function fn(t, e) {
				var n = e.__WUJIE,
					r = new WeakMap,
					o = document.createTreeWalker(t),
					i = o.currentNode;
				while (i) {
					var a = n.elementEventCacheMap.get(i);
					(null === a || void 0 === a ? void 0 : a.length) && (r.set(i, a), a.forEach((function(t) {
						i.addEventListener(t.type, t.handler, t.options)
					}))), i = o.nextNode()
				}
				n.elementEventCacheMap = r
			}
			function pn(t, e, n) {
				var r = n.__WUJIE,
					o = new WeakMap,
					i = r.elementEventCacheMap.get(t);
				(null === i || void 0 === i ? void 0 : i.length) && (o.set(e, i), i.forEach((function(t) {
					e.addEventListener(t.type, t.handler, t.options)
				}))), r.elementEventCacheMap = o
			}
			function dn(t, e) {
				Object.defineProperty(t.Event.prototype, "timeStamp", {
					get: function() {
						return e.document.createEvent("Event").timeStamp
					}
				})
			}
			function hn(t) {
				var e = t.__WUJIE,
					n = new WeakMap,
					r = new WeakMap;
				t.Document.prototype.addEventListener = function(o, i, a) {
					var c = n.get(i),
						u = r.get(i);
					return c || (c = "function" === typeof i ? i.bind(this) : i, n.set(i, c)), u ? u.includes(o) || u.push(o) : r.set(i, [o]), K(t.__WUJIE.plugins, "documentAddEventListenerHook", t, o, c, a), e.degrade ? e.document.firstElementChild.addEventListener(o, c, a) : oe.includes(o) ? window.document.addEventListener(o, c, a) : ie.includes(o) ? (window.document.addEventListener(o, c, a), void e.shadowRoot.addEventListener(o, c, a)) : void e.shadowRoot.addEventListener(o, c, a)
				}, t.Document.prototype.removeEventListener = function(o, i, a) {
					var c = n.get(i),
						u = r.get(i);
					if (c) {
						if ((null === u || void 0 === u ? void 0 : u.includes(o)) && (u.splice(u.indexOf(o), 1), u.length || (n.delete(i), r.delete(i))), K(t.__WUJIE.plugins, "documentRemoveEventListenerHook", t, o, c, a), e.degrade) return e.document.firstElementChild.removeEventListener(o, c, a);
						if (oe.includes(o)) return window.document.removeEventListener(o, c, a);
						if (ie.includes(o)) return window.document.removeEventListener(o, c, a), void e.shadowRoot.removeEventListener(o, c, a);
						e.shadowRoot.removeEventListener(o, c, a)
					} else J(m)
				};
				var o = Object.keys(t.HTMLElement.prototype).filter((function(t) {
					return /^on/.test(t)
				})),
					i = Object.keys(t.Document.prototype).filter((function(t) {
						return /^on/.test(t)
					}));
				o.filter((function(t) {
					return i.includes(t)
				})).forEach((function(n) {
					var r = Object.getOwnPropertyDescriptor(t.Document.prototype, n) || {
						enumerable: !0,
						writable: !0
					};
					try {
						Object.defineProperty(t.Document.prototype, n, {
							enumerable: r.enumerable,
							configurable: !0,
							get: function() {
								return (e.degrade ? e.document : e.shadowRoot).firstElementChild[n]
							},
							set: r.writable ? function(r) {
								e.degrade ? e.document.firstElementChild[n] = r.bind(t.document) : e.shadowRoot.firstElementChild[n] = r.bind(t.document)
							} : void 0
						})
					} catch (n) {
						J(n.message)
					}
				}));
				var a = re.ownerProperties,
					c = re.modifyProperties,
					u = re.shadowProperties,
					s = re.shadowMethods,
					l = re.documentProperties,
					f = re.documentMethods,
					p = re.documentEvents;
				c.concat(u, s, l, f).forEach((function(n) {
					var r = Object.getOwnPropertyDescriptor(t.Document.prototype, n) || {
						enumerable: !0,
						writable: !0
					};
					try {
						Object.defineProperty(t.Document.prototype, n, {
							enumerable: r.enumerable,
							configurable: !0,
							get: function() {
								return e.proxyDocument[n]
							},
							set: void 0
						})
					} catch (o) {
						J(o.message)
					}
				})), p.forEach((function(n) {
					var r = Object.getOwnPropertyDescriptor(t.Document.prototype, n) || {
						enumerable: !0,
						writable: !0
					};
					try {
						Object.defineProperty(t.Document.prototype, n, {
							enumerable: r.enumerable,
							configurable: !0,
							get: function() {
								return (e.degrade ? e : window).document[n]
							},
							set: r.writable ? function(r) {
								(e.degrade ? e : window).document[n] = r.bind(t.document)
							} : void 0
						})
					} catch (o) {
						J(o.message)
					}
				})), a.forEach((function(n) {
					Object.defineProperty(t.document, n, {
						enumerable: !0,
						configurable: !0,
						get: function() {
							return e.proxyDocument[n]
						},
						set: void 0
					})
				})), K(t.__WUJIE.plugins, "documentPropertyOverride", t)
			}
			function mn(t) {
				var e = t.Node.prototype.getRootNode;
				t.Node.prototype.getRootNode = function(n) {
					var r = e.call(this, n);
					return r === t.__WUJIE.shadowRoot ? t.document : r
				}
			}
			function vn(t) {
				B(t, t.HTMLImageElement, "src"), B(t, t.HTMLAnchorElement, "href"), B(t, t.HTMLSourceElement, "src"), B(t, t.HTMLLinkElement, "href"), B(t, t.HTMLScriptElement, "src")
			}
			function yn(t, e) {
				var n = t.document,
					r = n.createElement("base"),
					o = q(t.location.href),
					i = q(e);
				r.setAttribute("href", i.protocol + "//" + i.host + o.pathname), n.head.appendChild(r)
			}
			function gn(t) {
				var e = t.document;
				Ye(e);
				var n = e.createElement("html");
				n.innerHTML = "<head></head><body></body>", e.appendChild(n), yn(t, t.__WUJIE.url), sn(t), hn(t), mn(t), vn(t)
			}
			function wn(t, e) {
				t.__WUJIE.iframeReady = new Promise((function(n) {
					function r() {
						setTimeout((function() {
							"about:blank" === t.location.href ? r() : (t.stop(), gn(t), F(t.__WUJIE.id) || t.history.replaceState(null, "", e), n())
						}), 0)
					}
					r()
				}))
			}
			function bn(t, e) {
				var n = e.__WUJIE.proxyLocation;
				Object.defineProperty(t, "baseURI", {
					configurable: !0,
					get: function() {
						return n.protocol + "//" + n.host + n.pathname
					},
					set: void 0
				}), Object.defineProperty(t, "ownerDocument", {
					configurable: !0,
					get: function() {
						return e.document
					}
				})
			}
			function xn(t) {
				t.addEventListener("hashchange", (function() {
					return Xe(t)
				})), t.addEventListener("popstate", (function() {
					Xe(t)
				}))
			}
			function En(t, e) {
				var n = t.src,
					r = t.module,
					o = t.content,
					i = t.crossorigin,
					a = t.crossoriginType,
					c = t.callback,
					u = e.document.createElement("script"),
					s = e.document.createElement("script"),
					l = e.__WUJIE,
					f = l.replace,
					p = l.plugins,
					d = V(p.map((function(t) {
						return t.jsLoader
					})))(f ? f(o) : o, n);
				o ? e.__WUJIE.degrade || r || (d = "(function(window, self, global, location) {\n      ".concat(d, "\n}).bind(window.__WUJIE.proxy)(\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxyLocation,\n);")) : (n && u.setAttribute("src", n), i && u.setAttribute("crossorigin", a)), r && u.setAttribute("type", "module"), u.textContent = d || "", s.textContent = "if(window.__WUJIE.execQueue && window.__WUJIE.execQueue.length){ window.__WUJIE.execQueue.shift()()}";
				var h = ye.call(e.document, "head");
				if (/^<!DOCTYPE html/i.test(d)) return Q(y, t), h.appendChild(s);
				h.appendChild(u), null === c || void 0 === c || c(e), h.appendChild(s)
			}
			function Sn(t, e) {
				var n = window.document.createElement("iframe");
				n.setAttribute("src", t), n.setAttribute("style", "height:100%;width:100%"), Ge(n, e)
			}
			function kn(t, e, n, r, o) {
				var a = window.document.createElement("iframe"),
					c = n + o,
					u = Object.assign(Object.assign({}, e), rn({
						src: n,
						style: "display: none",
						name: t.id
					}, i, ""));
				Object.keys(u).forEach((function(t) {
					return a.setAttribute(t, u[t])
				})), window.document.body.appendChild(a);
				var s = a.contentWindow;
				return s.__WUJIE = t, an(s, r), wn(s, c), cn(s, r, n), on(s), s.__WUJIE.degrade && ln(s), xn(s), a
			}
			function _n(t, e, n) {
				var r = t.contentWindow.__WUJIE,
					o = r.shadowRoot,
					i = r.id,
					a = r.degrade,
					c = r.document,
					u = e;
				if (!/^http/.test(u)) {
					var s = q(u);
					u = n + s.pathname + s.search + s.hash, s = null
				}
				if (t.contentWindow.__WUJIE.hrefFlag = !0, a) {
					var l = ye.call(t.contentDocument, "body");
					Ge(c.firstElementChild, l), Sn(window.decodeURIComponent(u), M(i).parentElement)
				} else Sn(u, o.host.parentElement);
				return en(i, u), !0
			}
			function Nn(t, e, n, r) {
				var o = new Proxy(t.contentWindow, {
					get: function(t, e) {
						return "location" === e ? t.__WUJIE.proxyLocation : "self" === e ? t.__WUJIE.proxy : "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__" === e || "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__" === e ? t[e] : z(t, e)
					},
					set: function(t, e, n) {
						return t[e] = n, !0
					},
					has: function(t, e) {
						return e in t
					}
				}),
					i = new Proxy({}, {
						get: function(e, n) {
							var r, o = window.document,
								i = t.contentWindow.__WUJIE.shadowRoot;
							if ("createElement" === n || "createTextNode" === n) return new Proxy(o[n], {
								apply: function(e, n, r) {
									var o = e.apply(t.contentDocument, r);
									return bn(o, t.contentWindow), o
								}
							});
							if ("documentURI" === n || "URL" === n) return t.contentWindow.__WUJIE.proxyLocation.href;
							if ("getElementsByTagName" === n || "getElementsByClassName" === n || "getElementsByName" === n) return new Proxy(i.querySelectorAll, {
								apply: function(e, r, o) {
									var a = o[0];
									return "getElementsByTagName" === n && "script" === a ? t.contentDocument.scripts : ("getElementsByClassName" === n && (a = "." + a), "getElementsByName" === n && (a = '[name="'.concat(a, '"]')), e.call(i, a))
								}
							});
							if ("getElementById" === n) return new Proxy(i.querySelector, {
								apply: function(t, e, n) {
									return t.call(i, '[id="'.concat(n[0], '"]'))
								}
							});
							if ("querySelector" === n || "querySelectorAll" === n) return i[n].bind(i);
							if ("documentElement" === n || "scrollingElement" === n) return i.firstElementChild;
							if ("forms" === n) return i.querySelectorAll("form");
							if ("images" === n) return i.querySelectorAll("img");
							if ("links" === n) return i.querySelectorAll("a");
							var a = re.ownerProperties,
								c = re.shadowProperties,
								u = re.shadowMethods,
								s = re.documentProperties,
								l = re.documentMethods;
							return a.concat(c).includes(n.toString()) ? "activeElement" === n && null === i.activeElement ? i.body : i[n] : u.includes(n.toString()) ? null !== (r = z(i, n)) && void 0 !== r ? r : z(o, n) : s.includes(n.toString()) ? o[n] : l.includes(n.toString()) ? z(o, n) : void 0
						}
					}),
					a = new Proxy({}, {
						get: function(o, i) {
							var a = t.contentWindow.location;
							return "host" === i || "hostname" === i || "protocol" === i || "port" === i ? e[i] : "href" === i ? a[i].replace(n, r) : "reload" === i ? (J(f), function() {
								return null
							}) : z(a, i)
						},
						set: function(e, n, o) {
							return "href" === n ? _n(t, o, r) : (t.contentWindow.location[n] = o, !0)
						},
						ownKeys: function() {
							return Object.keys(t.contentWindow.location).filter((function(t) {
								return "reload" !== t
							}))
						},
						getOwnPropertyDescriptor: function(t, e) {
							return {
								enumerable: !0,
								configurable: !0,
								value: this[e]
							}
						}
					});
				return {
					proxyWindow: o,
					proxyDocument: i,
					proxyLocation: a
				}
			}
			function jn(t, e, n, r) {
				var o = {}, i = t.contentWindow.__WUJIE;
				Object.defineProperties(o, {
					createElement: {
						get: function() {
							return function() {
								for (var e = arguments.length, n = new Array(e), r = 0; r < e; r++) n[r] = arguments[r];
								var o = window.document.createElement.apply(t.contentDocument, n);
								return bn(o, t.contentWindow), o
							}
						}
					},
					createTextNode: {
						get: function() {
							return function() {
								for (var e = arguments.length, n = new Array(e), r = 0; r < e; r++) n[r] = arguments[r];
								var o = window.document.createTextNode.apply(t.contentDocument, n);
								return bn(o, t.contentWindow), o
							}
						}
					},
					documentURI: {
						get: function() {
							return i.proxyLocation.href
						}
					},
					URL: {
						get: function() {
							return i.proxyLocation.href
						}
					},
					getElementsByTagName: {
						get: function() {
							return function() {
								var e = arguments.length <= 0 ? void 0 : arguments[0];
								return "script" === e ? t.contentDocument.scripts : i.document.getElementsByTagName(e)
							}
						}
					}
				});
				var a = re.modifyLocalProperties,
					c = re.modifyProperties,
					u = re.ownerProperties,
					s = re.shadowProperties,
					l = re.shadowMethods,
					p = re.documentProperties,
					d = re.documentMethods;
				c.filter((function(t) {
					return !a.includes(t)
				})).concat(u, s, l, p, d).forEach((function(t) {
					Object.defineProperty(o, t, {
						get: function() {
							return A(i.document[t]) ? i.document[t].bind(i.document) : i.document[t]
						}
					})
				}));
				var h = {}, m = t.contentWindow.location,
					v = Object.keys(m),
					y = ["host", "hostname", "port", "protocol", "port"];
				return y.forEach((function(t) {
					h[t] = e[t]
				})), Object.defineProperties(h, {
					href: {
						get: function() {
							return m.href.replace(n, r)
						},
						set: function(e) {
							_n(t, e, r)
						}
					},
					reload: {
						get: function() {
							return J(f),
							function() {
								return null
							}
						}
					}
				}), v.filter((function(t) {
					return !y.concat(["href", "reload"]).includes(t)
				})).forEach((function(t) {
					Object.defineProperty(h, t, {
						get: function() {
							return A(m[t]) ? m[t].bind(m) : m[t]
						}
					})
				})), {
					proxyDocument: o,
					proxyLocation: h
				}
			}
			function On(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function Pn(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function Ln(t, e, n) {
				return e && Pn(t.prototype, e), n && Pn(t, n), Object.defineProperty(t, "prototype", {
					writable: !1
				}), t
			}
			var Tn = window.__POWERED_BY_WUJIE__ ? window.__WUJIE.inject.appEventObjMap : new Map,
				An = function() {
					function t(e) {
						On(this, t), this.id = e, this.$clear(), Tn.get(this.id) || Tn.set(this.id, {}), this.eventObj = Tn.get(this.id)
					}
					return Ln(t, [{
						key: "$on",
						value: function(t, e) {
							var n = this.eventObj[t];
							return n ? (n.includes(e) || n.push(e), this) : (this.eventObj[t] = [e], this)
						}
					}, {
						key: "$onAll",
						value: function(t) {
							return this.$on(u, t)
						}
					}, {
						key: "$once",
						value: function(t, e) {
							var n = function() {
								this.$off(t, n), e.apply(void 0, arguments)
							}.bind(this);
							this.$on(t, n)
						}
					}, {
						key: "$off",
						value: function(t, e) {
							var n, r = this.eventObj[t];
							if (!t || !r || !r.length) return J("".concat(t, " ").concat(d)), this;
							var o = r.length;
							while (o--) if (n = r[o], n === e) {
								r.splice(o, 1);
								break
							}
							return this
						}
					}, {
						key: "$offAll",
						value: function(t) {
							return this.$off(u, t)
						}
					}, {
						key: "$emit",
						value: function(t) {
							var e = [],
								n = [];
							if (Tn.forEach((function(r) {
								r[t] && (e = e.concat(r[t])), r[u] && (n = n.concat(r[u]))
							})), !t || 0 === e.length && 0 === n.length) J("".concat(t, " ").concat(d));
							else try {
								for (var r = arguments.length, o = new Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++) o[i - 1] = arguments[i];
								for (var a = 0, c = e.length; a < c; a++) {
									var s;
									(s = e)[a].apply(s, o)
								}
								for (var l = 0, f = n.length; l < f; l++) {
									var p;
									(p = n)[l].apply(p, [t].concat(o))
								}
							} catch (h) {
								Q(h)
							}
							return this
						}
					}, {
						key: "$clear",
						value: function() {
							var t, e = null !== (t = Tn.get(this.id)) && void 0 !== t ? t : {}, n = Object.keys(e);
							return n.forEach((function(t) {
								return delete e[t]
							})), this
						}
					}]), t
				}();

			function In(t) {
				return In = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, In(t)
			}
			function Cn() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				Cn = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (N) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new S(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return _()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = b(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (N) {
						return {
							type: "throw",
							arg: N
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function d() {}
				var h = {};
				c(h, o, (function() {
					return this
				}));
				var m = Object.getPrototypeOf,
					v = m && m(m(k([])));
				v && v !== e && n.call(v, o) && (h = v);
				var y = d.prototype = f.prototype = Object.create(h);

				function g(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function w(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == In(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function b(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, b(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function x(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function E(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function S(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(x, this), this.reset(!0)
				}
				function k(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: _
					}
				}
				function _() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = d, c(y, "constructor", d), c(d, "constructor", p), p.displayName = c(d, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, d) : (t.__proto__ = d, c(t, a, "GeneratorFunction")), t.prototype = Object.create(y), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, g(w.prototype), c(w.prototype, i, (function() {
					return this
				})), t.AsyncIterator = w, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new w(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, g(y), c(y, a, "Generator"), c(y, o, (function() {
					return this
				})), c(y, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = k, S.prototype = {
					constructor: S,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									E(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: k(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			function Un(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function Dn(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function zn(t, e, n) {
				return e && Dn(t.prototype, e), n && Dn(t, n), Object.defineProperty(t, "prototype", {
					writable: !1
				}), t
			}
			var Mn = function(t, e, n, r) {
				function o(t) {
					return t instanceof n ? t : new n((function(e) {
						e(t)
					}))
				}
				return new(n || (n = Promise))((function(n, i) {
					function a(t) {
						try {
							u(r.next(t))
						} catch (e) {
							i(e)
						}
					}
					function c(t) {
						try {
							u(r["throw"](t))
						} catch (e) {
							i(e)
						}
					}
					function u(t) {
						t.done ? n(t.value) : o(t.value).then(a, c)
					}
					u((r = r.apply(t, e || [])).next())
				}))
			}, Rn = function() {
				function t(e) {
					Un(this, t), this.elementEventCacheMap = new WeakMap, window.__POWERED_BY_WUJIE__ ? this.inject = window.__WUJIE.inject : this.inject = {
						idToSandboxMap: Kt,
						appEventObjMap: Tn,
						mainHostPath: window.location.protocol + "//" + window.location.host
					};
					var n = e.name,
						r = e.url,
						o = e.attrs,
						i = e.fiber,
						a = e.degrade,
						c = e.lifecycles,
						u = e.plugins;
					this.id = n, this.fiber = i, this.degrade = a || !P, this.bus = new An(this.id), this.url = r, this.provide = {
						bus: this.bus
					}, this.styleSheetElements = [], this.execQueue = [], this.lifecycles = c, this.plugins = Array.isArray(u) ? u : [];
					var s = R(r),
						l = s.urlElement,
						f = s.appHostPath,
						p = s.appRoutePath,
						d = this.inject.mainHostPath,
						h = kn(this, o, d, f, p);
					if (this.iframe = h, this.degrade) {
						var m = jn(h, l, d, f),
							v = m.proxyDocument,
							y = m.proxyLocation;
						this.proxyDocument = v, this.proxyLocation = y
					} else {
						var g = Nn(h, l, d, f),
							w = g.proxyWindow,
							b = g.proxyDocument,
							x = g.proxyLocation;
						this.proxy = w, this.proxyDocument = b, this.proxyLocation = x
					}
					this.provide.location = this.proxyLocation, ee(this.id, this)
				}
				return zn(t, [{
					key: "active",
					value: function(t) {
						return Mn(this, void 0, void 0, Cn().mark((function e() {
							var n, r, o, i, a, c, u, s, l, f, p, d, h, m, v = this;
							return Cn().wrap((function(e) {
								while (1) switch (e.prev = e.next) {
									case 0:
										return n = t.sync, r = t.url, o = t.el, i = t.template, a = t.props, c = t.alive, u = t.prefix, s = t.fetch, l = t.replace, this.url = r, this.sync = n, this.alive = c, this.hrefFlag = !1, this.prefix = null !== u && void 0 !== u ? u : this.prefix, this.replace = null !== l && void 0 !== l ? l : this.replace, this.provide.props = null !== a && void 0 !== a ? a : this.provide.props, e.next = 10, this.iframeReady;
									case 10:
										if (f = this.iframe.contentWindow, p = s ? function(t, e) {
											return s("string" === typeof t ? new URL(t, v.proxyLocation.href).href : t, e)
										} : this.fetch, p && (f.fetch = p, this.fetch = p), this.execFlag && this.alive || Ke(f), Xe(f), this.template = null !== i && void 0 !== i ? i : this.template, !this.degrade) {
											e.next = 38;
											break
										}
										if (d = Qe(this.id), h = ye.call(f.document, "body"), this.el = Ge(d, null !== o && void 0 !== o ? o : h), Ye(d.contentDocument), o && Ye(h), dn(d.contentWindow, f), d.contentWindow.onunload = function() {
											v.unmount()
										}, !this.document) {
											e.next = 34;
											break
										}
										if (!this.alive) {
											e.next = 29;
											break
										}
										d.contentDocument.appendChild(this.document.firstElementChild), fn(d.contentDocument.firstElementChild, f), e.next = 32;
										break;
									case 29:
										return e.next = 31, Ze(d.contentDocument, this.iframe.contentWindow, this.template);
									case 31:
										pn(this.document.firstElementChild, d.contentDocument.firstElementChild, f);
									case 32:
										e.next = 36;
										break;
									case 34:
										return e.next = 36, Ze(d.contentDocument, this.iframe.contentWindow, this.template);
									case 36:
										return this.document = d.contentDocument, e.abrupt("return");
									case 38:
										if (!this.shadowRoot) {
											e.next = 44;
											break
										}
										if (this.el = Ge(this.shadowRoot.host, o), !this.alive) {
											e.next = 42;
											break
										}
										return e.abrupt("return");
									case 42:
										e.next = 46;
										break;
									case 44:
										m = ye.call(f.document, "body"), this.el = Ge(Be(this.id), null !== o && void 0 !== o ? o : m);
									case 46:
										return e.next = 48, Je(this.shadowRoot, f, this.template);
									case 48:
										this.provide.shadowRoot = this.shadowRoot;
									case 49:
									case "end":
										return e.stop()
								}
							}), e, this)
						})))
					}
				}, {
					key: "start",
					value: function(t) {
						return Mn(this, void 0, void 0, Cn().mark((function e() {
							var n, r, o, i, a, c, u, s = this;
							return Cn().wrap((function(e) {
								while (1) switch (e.prev = e.next) {
									case 0:
										return this.execFlag = !0, e.next = 3, t();
									case 3:
										if (n = e.sent, this.iframe) {
											e.next = 6;
											break
										}
										return e.abrupt("return");
									case 6:
										return r = this.iframe.contentWindow, r.__POWERED_BY_WUJIE__ = !0, o = this.plugins.map((function(t) {
											return t.jsBeforeLoaders
										})).reduce((function(t, e) {
											return t.concat(e)
										}), []).filter((function(t) {
											return "object" === In(t)
										})), i = this.plugins.map((function(t) {
											return t.jsAfterLoaders
										})).reduce((function(t, e) {
											return t.concat(e)
										}), []).filter((function(t) {
											return "object" === In(t)
										})), a = [], c = [], u = [], n.forEach((function(t) {
											t.defer ? u.push(t) : t.async ? c.push(t) : a.push(t)
										})), o.forEach((function(t) {
											s.execQueue.push((function() {
												return s.fiber ? H((function() {
													return En(t, r)
												})) : En(t, r)
											}))
										})), a.concat(u).forEach((function(t) {
											s.execQueue.push((function() {
												return t.contentPromise.then((function(e) {
													return s.fiber ? H((function() {
														return En(Object.assign(Object.assign({}, t), {
															content: e
														}), r)
													})) : En(Object.assign(Object.assign({}, t), {
														content: e
													}), r)
												}))
											}))
										})), c.forEach((function(t) {
											t.contentPromise.then((function(e) {
												s.fiber ? H((function() {
													return En(Object.assign(Object.assign({}, t), {
														content: e
													}), r)
												})) : En(Object.assign(Object.assign({}, t), {
													content: e
												}), r)
											}))
										})), this.execQueue.push(this.fiber ? function() {
											return H((function() {
												return s.mount()
											}))
										} : function() {
											return s.mount()
										}), i.forEach((function(t) {
											s.execQueue.push((function() {
												return s.fiber ? H((function() {
													return En(t, r)
												})) : En(t, r)
											}))
										})), this.execQueue.shift()(), e.abrupt("return", new Promise((function(t) {
											s.execQueue.push((function() {
												var e;
												t(), null === (e = s.execQueue.shift()) || void 0 === e || e()
											}))
										})));
									case 21:
									case "end":
										return e.stop()
								}
							}), e, this)
						})))
					}
				}, {
					key: "mount",
					value: function() {
						var t, e, n, r, o, i, a;
						this.mountFlag || (this.attachHostCssRules(), j(this.iframe.contentWindow.__WUJIE_MOUNT) && (null === (e = null === (t = this.lifecycles) || void 0 === t ? void 0 : t.beforeMount) || void 0 === e || e.call(t, this.iframe.contentWindow), this.iframe.contentWindow.__WUJIE_MOUNT(), null === (r = null === (n = this.lifecycles) || void 0 === n ? void 0 : n.afterMount) || void 0 === r || r.call(n, this.iframe.contentWindow), this.mountFlag = !0), this.alive && (null === (i = null === (o = this.lifecycles) || void 0 === o ? void 0 : o.activated) || void 0 === i || i.call(o, this.iframe.contentWindow)), null === (a = this.execQueue.shift()) || void 0 === a || a())
					}
				}, {
					key: "unmount",
					value: function() {
						var t, e, n, r, o, i;
						this.degrade ? setTimeout((function() {
							return tn()
						})) : tn(), this.alive && (null === (e = null === (t = this.lifecycles) || void 0 === t ? void 0 : t.deactivated) || void 0 === e || e.call(t, this.iframe.contentWindow)), this.mountFlag && (!j(this.iframe.contentWindow.__WUJIE_UNMOUNT) || this.alive || this.hrefFlag || (null === (r = null === (n = this.lifecycles) || void 0 === n ? void 0 : n.beforeUnmount) || void 0 === r || r.call(n, this.iframe.contentWindow), this.iframe.contentWindow.__WUJIE_UNMOUNT(), null === (i = null === (o = this.lifecycles) || void 0 === o ? void 0 : o.afterUnmount) || void 0 === i || i.call(o, this.iframe.contentWindow), this.mountFlag = !1, this.bus.$clear(), this.degrade || Ye(this.shadowRoot)))
					}
				}, {
					key: "destroy",
					value: function() {
						var t = this;
						this.bus.$clear(), this.shadowRoot = null, this.iframe = null, this.proxy = null, this.proxyDocument = null, this.proxyLocation = null, this.execQueue = null, this.provide = null, this.styleSheetElements = null, this.bus = null, this.el = null, this.replace = null, this.fetch = null, this.execFlag = null, this.mountFlag = null, this.hrefFlag = null, this.document = null, this.elementEventCacheMap = null, this.lifecycles = null, this.plugins = null, this.provide = null, this.inject = null, this.execQueue = null, this.prefix = null, (window.__POWERED_BY_WUJIE__ ? window.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__.call(window.document, "iframe") : window.document.querySelectorAll("iframe")).forEach((function(e) {
							e.name === t.id && e.parentNode.removeChild(e)
						})), ne(this.id)
					}
				}, {
					key: "rebuildStyleSheets",
					value: function() {
						var t = this;
						this.styleSheetElements && this.styleSheetElements.length && this.styleSheetElements.forEach((function(e) {
							le.call(t.degrade ? t.document.head : t.shadowRoot.head, e)
						})), this.attachHostCssRules()
					}
				}, {
					key: "attachHostCssRules",
					value: function() {
						if (!this.degrade && !this.shadowRoot.host.hasAttribute(a)) {
							var t = Ve(Array.from(this.iframe.contentDocument.querySelectorAll("style")).map((function(t) {
								return t.sheet
							})));
							t && (this.shadowRoot.head.appendChild(t), this.styleSheetElements.push(t), this.shadowRoot.host.setAttribute(a, ""))
						}
					}
				}]), t
			}();

			function qn(t) {
				return qn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, qn(t)
			}
			function Wn() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				Wn = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (N) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new S(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return _()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = b(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (N) {
						return {
							type: "throw",
							arg: N
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function d() {}
				var h = {};
				c(h, o, (function() {
					return this
				}));
				var m = Object.getPrototypeOf,
					v = m && m(m(k([])));
				v && v !== e && n.call(v, o) && (h = v);
				var y = d.prototype = f.prototype = Object.create(h);

				function g(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function w(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == qn(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function b(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, b(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function x(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function E(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function S(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(x, this), this.reset(!0)
				}
				function k(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: _
					}
				}
				function _() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = d, c(y, "constructor", d), c(d, "constructor", p), p.displayName = c(d, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, d) : (t.__proto__ = d, c(t, a, "GeneratorFunction")), t.prototype = Object.create(y), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, g(w.prototype), c(w.prototype, i, (function() {
					return this
				})), t.AsyncIterator = w, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new w(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, g(y), c(y, a, "Generator"), c(y, o, (function() {
					return this
				})), c(y, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = k, S.prototype = {
					constructor: S,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									E(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: k(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			var Fn = function(t, e, n, r) {
				function o(t) {
					return t instanceof n ? t : new n((function(e) {
						e(t)
					}))
				}
				return new(n || (n = Promise))((function(n, i) {
					function a(t) {
						try {
							u(r.next(t))
						} catch (e) {
							i(e)
						}
					}
					function c(t) {
						try {
							u(r["throw"](t))
						} catch (e) {
							i(e)
						}
					}
					function u(t) {
						t.done ? n(t.value) : o(t.value).then(a, c)
					}
					u((r = r.apply(t, e || [])).next())
				}))
			}, Bn = new An(Date.now().toString());
			if (window.__WUJIE && !window.__POWERED_BY_WUJIE__) throw J(p), new Error(p);

			function Gn(t) {
				var e, n, r, o, i, a, c, u, s, l, f = t.name,
					p = t.url,
					d = t.replace,
					h = t.el,
					m = t.sync,
					v = t.prefix,
					y = t.alive,
					g = t.props,
					w = t.attrs,
					b = void 0 === w ? {} : w,
					x = t.fiber,
					E = void 0 === x || x,
					S = t.degrade,
					k = t.plugins,
					_ = t.fetch,
					N = t.beforeLoad,
					O = t.beforeMount,
					P = t.afterMount,
					L = t.beforeUnmount,
					T = t.afterUnmount,
					A = t.activated,
					I = t.deactivated,
					C = t.loadError;
				return Fn(this, void 0, void 0, Wn().mark((function t() {
					var w, x, U, D, z, M, R, q, W, F, B;
					return Wn().wrap((function(t) {
						while (1) switch (t.prev = t.next) {
							case 0:
								if (w = te(f), x = {
									beforeLoad: N,
									beforeMount: O,
									afterMount: P,
									beforeUnmount: L,
									afterUnmount: T,
									activated: A,
									deactivated: I,
									loadError: C
								}, !w) {
									t.next = 37;
									break
								}
								if (w.plugins = Array.isArray(k) ? k : [], w.lifecycles = x, U = w.iframe.contentWindow, !w.preload) {
									t.next = 9;
									break
								}
								return t.next = 9, Promise.resolve(w.preload);
							case 9:
								if (!y) {
									t.next = 24;
									break
								}
								return t.next = 12, w.active({
									url: p,
									sync: m,
									prefix: v,
									el: h,
									props: g,
									alive: y,
									fetch: _,
									replace: d
								});
							case 12:
								if (w.execFlag) {
									t.next = 20;
									break
								}
								return null === (n = null === (e = w.lifecycles) || void 0 === e ? void 0 : e.beforeLoad) || void 0 === n || n.call(e, w.iframe.contentWindow), t.next = 16, Xt(p, {
									fetch: _ || window.fetch,
									plugins: w.plugins,
									loadError: w.lifecycles.loadError
								});
							case 16:
								return D = t.sent, z = D.getExternalScripts, t.next = 20, w.start(z);
							case 20:
								return null === (o = null === (r = w.lifecycles) || void 0 === r ? void 0 : r.activated) || void 0 === o || o.call(r, w.iframe.contentWindow), t.abrupt("return", w.destroy);
							case 24:
								if (!j(U.__WUJIE_MOUNT)) {
									t.next = 36;
									break
								}
								return w.unmount(), t.next = 28, w.active({
									url: p,
									sync: m,
									prefix: v,
									el: h,
									props: g,
									alive: y,
									fetch: _,
									replace: d
								});
							case 28:
								return null === (a = null === (i = w.lifecycles) || void 0 === i ? void 0 : i.beforeMount) || void 0 === a || a.call(i, w.iframe.contentWindow), U.__WUJIE_MOUNT(), null === (u = null === (c = w.lifecycles) || void 0 === c ? void 0 : c.afterMount) || void 0 === u || u.call(c, w.iframe.contentWindow), w.mountFlag = !0, w.rebuildStyleSheets(), t.abrupt("return", w.destroy);
							case 36:
								w.destroy();
							case 37:
								return M = new Rn({
									name: f,
									url: p,
									attrs: b,
									fiber: E,
									degrade: S,
									plugins: k,
									lifecycles: x
								}), null === (l = null === (s = M.lifecycles) || void 0 === s ? void 0 : s.beforeLoad) || void 0 === l || l.call(s, M.iframe.contentWindow), t.next = 41, Xt(p, {
									fetch: _ || window.fetch,
									plugins: M.plugins,
									loadError: M.lifecycles.loadError
								});
							case 41:
								return R = t.sent, q = R.template, W = R.getExternalScripts, F = R.getExternalStyleSheets, t.next = 47, $t(M, q, F);
							case 47:
								return B = t.sent, t.next = 50, M.active({
									url: p,
									sync: m,
									prefix: v,
									template: B,
									el: h,
									props: g,
									alive: y,
									fetch: _,
									replace: d
								});
							case 50:
								return t.next = 52, M.start(W);
							case 52:
								return t.abrupt("return", M.destroy);
							case 53:
							case "end":
								return t.stop()
						}
					}), t)
				})))
			}
			function Hn(t) {
				var e = this,
					n = t.name,
					r = t.url,
					o = t.replace,
					i = t.props,
					a = t.attrs,
					c = void 0 === a ? {} : a,
					u = t.fiber,
					s = void 0 === u || u,
					l = t.alive,
					f = t.degrade,
					p = t.fetch,
					d = t.exec,
					h = t.plugins,
					m = t.beforeLoad,
					v = t.beforeMount,
					y = t.afterMount,
					g = t.beforeUnmount,
					w = t.afterUnmount,
					b = t.activated,
					x = t.deactivated,
					E = t.loadError;
				H((function() {
					if (!te(n) && !F(n)) {
						var t = {
							beforeLoad: m,
							beforeMount: v,
							afterMount: y,
							beforeUnmount: g,
							afterUnmount: w,
							activated: b,
							deactivated: x,
							loadError: E
						}, a = new Rn({
							name: n,
							url: r,
							attrs: c,
							fiber: s,
							degrade: f,
							plugins: h,
							lifecycles: t
						});
						if (a.preload) return a.preload;
						var u = function() {
							return Fn(e, void 0, void 0, Wn().mark((function t() {
								var e, n, c, u, s, f, h;
								return Wn().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return null === (n = null === (e = a.lifecycles) || void 0 === e ? void 0 : e.beforeLoad) || void 0 === n || n.call(e, a.iframe.contentWindow), t.next = 3, Xt(r, {
												fetch: p || window.fetch,
												plugins: a.plugins,
												loadError: a.lifecycles.loadError
											});
										case 3:
											return c = t.sent, u = c.template, s = c.getExternalScripts, f = c.getExternalStyleSheets, t.next = 9, $t(a, u, f);
										case 9:
											return h = t.sent, t.next = 12, a.active({
												url: r,
												props: i,
												alive: l,
												template: h,
												fetch: p,
												replace: o
											});
										case 12:
											if (!d) {
												t.next = 15;
												break
											}
											return t.next = 15, a.start(s);
										case 15:
										case "end":
											return t.stop()
									}
								}), t)
							})))
						};
						a.preload = u()
					}
				}))
			}
			function $n(t) {
				var e = te(t);
				e && e.destroy()
			}
			function Jn(t) {
				return Jn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, Jn(t)
			}
			function Qn() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				Qn = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, o = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					a = r.toStringTag || "@@toStringTag";

				function c(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					c({}, "")
				} catch (N) {
					c = function(t, e, n) {
						return t[e] = n
					}
				}
				function u(t, e, n, r) {
					var o = e && e.prototype instanceof f ? e : f,
						i = Object.create(o.prototype),
						a = new S(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return _()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = b(a, n);
									if (c) {
										if (c === l) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = s(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === l) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function s(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (N) {
						return {
							type: "throw",
							arg: N
						}
					}
				}
				t.wrap = u;
				var l = {};

				function f() {}
				function p() {}
				function d() {}
				var h = {};
				c(h, o, (function() {
					return this
				}));
				var m = Object.getPrototypeOf,
					v = m && m(m(k([])));
				v && v !== e && n.call(v, o) && (h = v);
				var y = d.prototype = f.prototype = Object.create(h);

				function g(t) {
					["next", "throw", "return"].forEach((function(e) {
						c(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function w(t, e) {
					function r(o, i, a, c) {
						var u = s(t[o], t, i);
						if ("throw" !== u.type) {
							var l = u.arg,
								f = l.value;
							return f && "object" == Jn(f) && n.call(f, "__await") ? e.resolve(f.__await).then((function(t) {
								r("next", t, a, c)
							}), (function(t) {
								r("throw", t, a, c)
							})) : e.resolve(f).then((function(t) {
								l.value = t, a(l)
							}), (function(t) {
								return r("throw", t, a, c)
							}))
						}
						c(u.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function b(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, b(t, e), "throw" === e.method)) return l;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return l
					}
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, l;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, l) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, l)
				}
				function x(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function E(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function S(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(x, this), this.reset(!0)
				}
				function k(t) {
					if (t) {
						var e = t[o];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								i = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return i.next = i
						}
					}
					return {
						next: _
					}
				}
				function _() {
					return {
						value: void 0,
						done: !0
					}
				}
				return p.prototype = d, c(y, "constructor", d), c(d, "constructor", p), p.displayName = c(d, a, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === p || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, d) : (t.__proto__ = d, c(t, a, "GeneratorFunction")), t.prototype = Object.create(y), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, g(w.prototype), c(w.prototype, i, (function() {
					return this
				})), t.AsyncIterator = w, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new w(u(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, g(y), c(y, a, "Generator"), c(y, o, (function() {
					return this
				})), c(y, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = k, S.prototype = {
					constructor: S,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(E), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, l) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), l
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), E(n), l
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									E(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: k(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), l
					}
				}, t
			}
			function Zn(t, e, n, r, o, i, a) {
				try {
					var c = t[i](a),
						u = c.value
				} catch (Q) {
					return void n(Q)
				}
				c.done ? e(u) : Promise.resolve(u).then(r, o)
			}
			function Yn(t) {
				return function() {
					var e = this,
						n = arguments;
					return new Promise((function(r, o) {
						var i = t.apply(e, n);

						function a(t) {
							Zn(i, r, o, a, c, "next", t)
						}
						function c(t) {
							Zn(i, r, o, a, c, "throw", t)
						}
						a(void 0)
					}))
				}
			}
			nn(), Fe(), P || J(v);
			var Vn = !! r.createApp,
				Xn = {
					name: "WujieVue",
					props: {
						width: {
							type: String,
							default: ""
						},
						height: {
							type: String,
							default: ""
						},
						name: {
							type: String,
							default: ""
						},
						url: {
							type: String,
							default: ""
						},
						sync: {
							type: Boolean,
							default: !1
						},
						prefix: {
							type: Object,
							default: void 0
						},
						alive: {
							type: Boolean,
							default: !1
						},
						props: {
							type: Object,
							default: void 0
						},
						replace: {
							type: Function,
							default: void 0
						},
						fetch: {
							type: Function,
							default: void 0
						},
						fiber: {
							type: Boolean,
							default: !0
						},
						degrade: {
							type: Boolean,
							default: !1
						},
						plugins: {
							type: Array,
							default: null
						},
						beforeLoad: {
							type: Function,
							default: null
						},
						beforeMount: {
							type: Function,
							default: null
						},
						afterMount: {
							type: Function,
							default: null
						},
						beforeUnmount: {
							type: Function,
							default: null
						},
						afterUnmount: {
							type: Function,
							default: null
						},
						activated: {
							type: Function,
							default: null
						},
						deactivated: {
							type: Function,
							default: null
						},
						loadError: {
							type: Function,
							default: null
						}
					},
					data: function() {
						return {
							destroy: null,
							startAppQueue: Promise.resolve()
						}
					},
					mounted: function() {
						var t = this;
						Bn.$onAll(this.handleEmit), this.execStartApp(), this.$watch((function() {
							return t.name + t.url
						}), (function() {
							return t.execStartApp()
						}))
					},
					methods: {
						handleEmit: function(t) {
							for (var e = arguments.length, n = new Array(e > 1 ? e - 1 : 0), r = 1; r < e; r++) n[r - 1] = arguments[r];
							this.$emit.apply(this, [t].concat(n))
						},
						execStartApp: function() {
							var t = this;
							this.startAppQueue = this.startAppQueue.then(Yn(Qn().mark((function e() {
								return Qn().wrap((function(e) {
									while (1) switch (e.prev = e.next) {
										case 0:
											return e.prev = 0, e.next = 3, Gn({
												name: t.name,
												url: t.url,
												el: t.$refs.wujie,
												alive: t.alive,
												fetch: t.fetch,
												props: t.props,
												replace: t.replace,
												sync: t.sync,
												prefix: t.prefix,
												fiber: t.fiber,
												degrade: t.degrade,
												plugins: t.plugins,
												beforeLoad: t.beforeLoad,
												beforeMount: t.beforeMount,
												afterMount: t.afterMount,
												beforeUnmount: t.beforeUnmount,
												afterUnmount: t.afterUnmount,
												activated: t.activated,
												deactivated: t.deactivated,
												loadError: t.loadError
											});
										case 3:
											t.destroy = e.sent, e.next = 9;
											break;
										case 6:
											e.prev = 6, e.t0 = e["catch"](0), console.log(e.t0);
										case 9:
										case "end":
											return e.stop()
									}
								}), e, null, [
									[0, 6]
								])
							}))))
						}
					},
					beforeDestroy: function() {
						Bn.$offAll(this.handleEmit)
					},
					render: function(t) {
						var e = Vn ? r.h : t;
						return e("div", {
							style: {
								width: this.height,
								height: this.height
							},
							ref: "wujie"
						})
					}
				}, Kn = Vn ? (0, r.defineComponent)(Xn) : r["default"].extend(Xn);
			Kn.preloadApp = Hn, Kn.bus = Bn, Kn.destroyApp = $n, Kn.install = function(t) {
				t.component("WujieVue", Kn)
			};
			var tr = Kn
		},
		33581: function(t, e, n) {
			var r, o;

			function i(t) {
				return i = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, i(t)
			}! function(a, c) {
				"object" == i(e) ? t.exports = c() : (r = c, o = "function" === typeof r ? r.call(e, n, e, t) : r, void 0 === o || (t.exports = o))
			}(0, (function() {
				"use strict";
				var t = 1e3,
					e = 6e4,
					n = 36e5,
					r = "millisecond",
					o = "second",
					a = "minute",
					c = "hour",
					u = "day",
					s = "week",
					l = "month",
					f = "quarter",
					p = "year",
					d = "date",
					h = "Invalid Date",
					m = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,
					v = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,
					y = {
						name: "en",
						weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),
						months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"),
						ordinal: function(t) {
							var e = ["th", "st", "nd", "rd"],
								n = t % 100;
							return "[" + t + (e[(n - 20) % 10] || e[n] || e[0]) + "]"
						}
					}, g = function(t, e, n) {
						var r = String(t);
						return !r || r.length >= e ? t : "" + Array(e + 1 - r.length).join(n) + t
					}, w = {
						s: g,
						z: function(t) {
							var e = -t.utcOffset(),
								n = Math.abs(e),
								r = Math.floor(n / 60),
								o = n % 60;
							return (e <= 0 ? "+" : "-") + g(r, 2, "0") + ":" + g(o, 2, "0")
						},
						m: function t(e, n) {
							if (e.date() < n.date()) return -t(n, e);
							var r = 12 * (n.year() - e.year()) + (n.month() - e.month()),
								o = e.clone().add(r, l),
								i = n - o < 0,
								a = e.clone().add(r + (i ? -1 : 1), l);
							return +(-(r + (n - o) / (i ? o - a : a - o)) || 0)
						},
						a: function(t) {
							return t < 0 ? Math.ceil(t) || 0 : Math.floor(t)
						},
						p: function(t) {
							return {
								M: l,
								y: p,
								w: s,
								d: u,
								D: d,
								h: c,
								m: a,
								s: o,
								ms: r,
								Q: f
							}[t] || String(t || "").toLowerCase().replace(/s$/, "")
						},
						u: function(t) {
							return void 0 === t
						}
					}, b = "en",
					x = {};
				x[b] = y;
				var E = "$isDayjsObject",
					S = function(t) {
						return t instanceof j || !(!t || !t[E])
					}, k = function t(e, n, r) {
						var o;
						if (!e) return b;
						if ("string" == typeof e) {
							var i = e.toLowerCase();
							x[i] && (o = i), n && (x[i] = n, o = i);
							var a = e.split("-");
							if (!o && a.length > 1) return t(a[0])
						} else {
							var c = e.name;
							x[c] = e, o = c
						}
						return !r && o && (b = o), o || !r && b
					}, _ = function(t, e) {
						if (S(t)) return t.clone();
						var n = "object" == i(e) ? e : {};
						return n.date = t, n.args = arguments, new j(n)
					}, N = w;
				N.l = k, N.i = S, N.w = function(t, e) {
					return _(t, {
						locale: e.$L,
						utc: e.$u,
						x: e.$x,
						$offset: e.$offset
					})
				};
				var j = function() {
					function i(t) {
						this.$L = k(t.locale, null, !0), this.parse(t), this.$x = this.$x || t.x || {}, this[E] = !0
					}
					var y = i.prototype;
					return y.parse = function(t) {
						this.$d = function(t) {
							var e = t.date,
								n = t.utc;
							if (null === e) return new Date(NaN);
							if (N.u(e)) return new Date;
							if (e instanceof Date) return new Date(e);
							if ("string" == typeof e && !/Z$/i.test(e)) {
								var r = e.match(m);
								if (r) {
									var o = r[2] - 1 || 0,
										i = (r[7] || "0").substring(0, 3);
									return n ? new Date(Date.UTC(r[1], o, r[3] || 1, r[4] || 0, r[5] || 0, r[6] || 0, i)) : new Date(r[1], o, r[3] || 1, r[4] || 0, r[5] || 0, r[6] || 0, i)
								}
							}
							return new Date(e)
						}(t), this.init()
					}, y.init = function() {
						var t = this.$d;
						this.$y = t.getFullYear(), this.$M = t.getMonth(), this.$D = t.getDate(), this.$W = t.getDay(), this.$H = t.getHours(), this.$m = t.getMinutes(), this.$s = t.getSeconds(), this.$ms = t.getMilliseconds()
					}, y.$utils = function() {
						return N
					}, y.isValid = function() {
						return !(this.$d.toString() === h)
					}, y.isSame = function(t, e) {
						var n = _(t);
						return this.startOf(e) <= n && n <= this.endOf(e)
					}, y.isAfter = function(t, e) {
						return _(t) < this.startOf(e)
					}, y.isBefore = function(t, e) {
						return this.endOf(e) < _(t)
					}, y.$g = function(t, e, n) {
						return N.u(t) ? this[e] : this.set(n, t)
					}, y.unix = function() {
						return Math.floor(this.valueOf() / 1e3)
					}, y.valueOf = function() {
						return this.$d.getTime()
					}, y.startOf = function(t, e) {
						var n = this,
							r = !! N.u(e) || e,
							i = N.p(t),
							f = function(t, e) {
								var o = N.w(n.$u ? Date.UTC(n.$y, e, t) : new Date(n.$y, e, t), n);
								return r ? o : o.endOf(u)
							}, h = function(t, e) {
								return N.w(n.toDate()[t].apply(n.toDate("s"), (r ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(e)), n)
							}, m = this.$W,
							v = this.$M,
							y = this.$D,
							g = "set" + (this.$u ? "UTC" : "");
						switch (i) {
							case p:
								return r ? f(1, 0) : f(31, 11);
							case l:
								return r ? f(1, v) : f(0, v + 1);
							case s:
								var w = this.$locale().weekStart || 0,
									b = (m < w ? m + 7 : m) - w;
								return f(r ? y - b : y + (6 - b), v);
							case u:
							case d:
								return h(g + "Hours", 0);
							case c:
								return h(g + "Minutes", 1);
							case a:
								return h(g + "Seconds", 2);
							case o:
								return h(g + "Milliseconds", 3);
							default:
								return this.clone()
						}
					}, y.endOf = function(t) {
						return this.startOf(t, !1)
					}, y.$set = function(t, e) {
						var n, i = N.p(t),
							s = "set" + (this.$u ? "UTC" : ""),
							f = (n = {}, n[u] = s + "Date", n[d] = s + "Date", n[l] = s + "Month", n[p] = s + "FullYear", n[c] = s + "Hours", n[a] = s + "Minutes", n[o] = s + "Seconds", n[r] = s + "Milliseconds", n)[i],
							h = i === u ? this.$D + (e - this.$W) : e;
						if (i === l || i === p) {
							var m = this.clone().set(d, 1);
							m.$d[f](h), m.init(), this.$d = m.set(d, Math.min(this.$D, m.daysInMonth())).$d
						} else f && this.$d[f](h);
						return this.init(), this
					}, y.set = function(t, e) {
						return this.clone().$set(t, e)
					}, y.get = function(t) {
						return this[N.p(t)]()
					}, y.add = function(r, i) {
						var f, d = this;
						r = Number(r);
						var h = N.p(i),
							m = function(t) {
								var e = _(d);
								return N.w(e.date(e.date() + Math.round(t * r)), d)
							};
						if (h === l) return this.set(l, this.$M + r);
						if (h === p) return this.set(p, this.$y + r);
						if (h === u) return m(1);
						if (h === s) return m(7);
						var v = (f = {}, f[a] = e, f[c] = n, f[o] = t, f)[h] || 1,
							y = this.$d.getTime() + r * v;
						return N.w(y, this)
					}, y.subtract = function(t, e) {
						return this.add(-1 * t, e)
					}, y.format = function(t) {
						var e = this,
							n = this.$locale();
						if (!this.isValid()) return n.invalidDate || h;
						var r = t || "YYYY-MM-DDTHH:mm:ssZ",
							o = N.z(this),
							i = this.$H,
							a = this.$m,
							c = this.$M,
							u = n.weekdays,
							s = n.months,
							l = n.meridiem,
							f = function(t, n, o, i) {
								return t && (t[n] || t(e, r)) || o[n].slice(0, i)
							}, p = function(t) {
								return N.s(i % 12 || 12, t, "0")
							}, d = l || function(t, e, n) {
								var r = t < 12 ? "AM" : "PM";
								return n ? r.toLowerCase() : r
							};
						return r.replace(v, (function(t, r) {
							return r || function(t) {
								switch (t) {
									case "YY":
										return String(e.$y).slice(-2);
									case "YYYY":
										return N.s(e.$y, 4, "0");
									case "M":
										return c + 1;
									case "MM":
										return N.s(c + 1, 2, "0");
									case "MMM":
										return f(n.monthsShort, c, s, 3);
									case "MMMM":
										return f(s, c);
									case "D":
										return e.$D;
									case "DD":
										return N.s(e.$D, 2, "0");
									case "d":
										return String(e.$W);
									case "dd":
										return f(n.weekdaysMin, e.$W, u, 2);
									case "ddd":
										return f(n.weekdaysShort, e.$W, u, 3);
									case "dddd":
										return u[e.$W];
									case "H":
										return String(i);
									case "HH":
										return N.s(i, 2, "0");
									case "h":
										return p(1);
									case "hh":
										return p(2);
									case "a":
										return d(i, a, !0);
									case "A":
										return d(i, a, !1);
									case "m":
										return String(a);
									case "mm":
										return N.s(a, 2, "0");
									case "s":
										return String(e.$s);
									case "ss":
										return N.s(e.$s, 2, "0");
									case "SSS":
										return N.s(e.$ms, 3, "0");
									case "Z":
										return o
								}
								return null
							}(t) || o.replace(":", "")
						}))
					}, y.utcOffset = function() {
						return 15 * -Math.round(this.$d.getTimezoneOffset() / 15)
					}, y.diff = function(r, i, d) {
						var h, m = this,
							v = N.p(i),
							y = _(r),
							g = (y.utcOffset() - this.utcOffset()) * e,
							w = this - y,
							b = function() {
								return N.m(m, y)
							};
						switch (v) {
							case p:
								h = b() / 12;
								break;
							case l:
								h = b();
								break;
							case f:
								h = b() / 3;
								break;
							case s:
								h = (w - g) / 6048e5;
								break;
							case u:
								h = (w - g) / 864e5;
								break;
							case c:
								h = w / n;
								break;
							case a:
								h = w / e;
								break;
							case o:
								h = w / t;
								break;
							default:
								h = w
						}
						return d ? h : N.a(h)
					}, y.daysInMonth = function() {
						return this.endOf(l).$D
					}, y.$locale = function() {
						return x[this.$L]
					}, y.locale = function(t, e) {
						if (!t) return this.$L;
						var n = this.clone(),
							r = k(t, e, !0);
						return r && (n.$L = r), n
					}, y.clone = function() {
						return N.w(this.$d, this)
					}, y.toDate = function() {
						return new Date(this.valueOf())
					}, y.toJSON = function() {
						return this.isValid() ? this.toISOString() : null
					}, y.toISOString = function() {
						return this.$d.toISOString()
					}, y.toString = function() {
						return this.$d.toUTCString()
					}, i
				}(),
					O = j.prototype;
				return _.prototype = O, [
					["$ms", r],
					["$s", o],
					["$m", a],
					["$H", c],
					["$W", u],
					["$M", l],
					["$y", p],
					["$D", d]
				].forEach((function(t) {
					O[t[1]] = function(e) {
						return this.$g(e, t[0], t[1])
					}
				})), _.extend = function(t, e) {
					return t.$i || (t(e, j, _), t.$i = !0), _
				}, _.locale = k, _.isDayjs = S, _.unix = function(t) {
					return _(1e3 * t)
				}, _.en = x[b], _.Ls = x, _.p = {}, _
			}))
		},
		65202: function(t, e, n) {
			"use strict";
			n.d(e, {
				$o: function() {
					return Xt
				},
				P6: function() {
					return Vt
				},
				ZQ: function() {
					return te
				},
				cA: function() {
					return Kt
				},
				vV: function() {
					return Yt
				},
				wR: function() {
					return ee
				}
			});
			var r = n(59841);

			function o() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
				o = function() {
					return t
				};
				var t = {}, e = Object.prototype,
					n = e.hasOwnProperty,
					r = "function" == typeof Symbol ? Symbol : {}, a = r.iterator || "@@iterator",
					c = r.asyncIterator || "@@asyncIterator",
					u = r.toStringTag || "@@toStringTag";

				function s(t, e, n) {
					return Object.defineProperty(t, e, {
						value: n,
						enumerable: !0,
						configurable: !0,
						writable: !0
					}), t[e]
				}
				try {
					s({}, "")
				} catch (O) {
					s = function(t, e, n) {
						return t[e] = n
					}
				}
				function l(t, e, n, r) {
					var o = e && e.prototype instanceof d ? e : d,
						i = Object.create(o.prototype),
						a = new _(r || []);
					return i._invoke = function(t, e, n) {
						var r = "suspendedStart";
						return function(o, i) {
							if ("executing" === r) throw new Error("Generator is already running");
							if ("completed" === r) {
								if ("throw" === o) throw i;
								return j()
							}
							for (n.method = o, n.arg = i;;) {
								var a = n.delegate;
								if (a) {
									var c = E(a, n);
									if (c) {
										if (c === p) continue;
										return c
									}
								}
								if ("next" === n.method) n.sent = n._sent = n.arg;
								else if ("throw" === n.method) {
									if ("suspendedStart" === r) throw r = "completed", n.arg;
									n.dispatchException(n.arg)
								} else "return" === n.method && n.abrupt("return", n.arg);
								r = "executing";
								var u = f(t, e, n);
								if ("normal" === u.type) {
									if (r = n.done ? "completed" : "suspendedYield", u.arg === p) continue;
									return {
										value: u.arg,
										done: n.done
									}
								}
								"throw" === u.type && (r = "completed", n.method = "throw", n.arg = u.arg)
							}
						}
					}(t, n, a), i
				}
				function f(t, e, n) {
					try {
						return {
							type: "normal",
							arg: t.call(e, n)
						}
					} catch (O) {
						return {
							type: "throw",
							arg: O
						}
					}
				}
				t.wrap = l;
				var p = {};

				function d() {}
				function h() {}
				function m() {}
				var v = {};
				s(v, a, (function() {
					return this
				}));
				var y = Object.getPrototypeOf,
					g = y && y(y(N([])));
				g && g !== e && n.call(g, a) && (v = g);
				var w = m.prototype = d.prototype = Object.create(v);

				function b(t) {
					["next", "throw", "return"].forEach((function(e) {
						s(t, e, (function(t) {
							return this._invoke(e, t)
						}))
					}))
				}
				function x(t, e) {
					function r(o, a, c, u) {
						var s = f(t[o], t, a);
						if ("throw" !== s.type) {
							var l = s.arg,
								p = l.value;
							return p && "object" == i(p) && n.call(p, "__await") ? e.resolve(p.__await).then((function(t) {
								r("next", t, c, u)
							}), (function(t) {
								r("throw", t, c, u)
							})) : e.resolve(p).then((function(t) {
								l.value = t, c(l)
							}), (function(t) {
								return r("throw", t, c, u)
							}))
						}
						u(s.arg)
					}
					var o;
					this._invoke = function(t, n) {
						function i() {
							return new e((function(e, o) {
								r(t, n, e, o)
							}))
						}
						return o = o ? o.then(i, i) : i()
					}
				}
				function E(t, e) {
					var n = t.iterator[e.method];
					if (void 0 === n) {
						if (e.delegate = null, "throw" === e.method) {
							if (t.iterator.
							return &&(e.method = "return", e.arg = void 0, E(t, e), "throw" === e.method)) return p;
							e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
						}
						return p
					}
					var r = f(n, t.iterator, e.arg);
					if ("throw" === r.type) return e.method = "throw", e.arg = r.arg, e.delegate = null, p;
					var o = r.arg;
					return o ? o.done ? (e[t.resultName] = o.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = void 0), e.delegate = null, p) : o : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, p)
				}
				function S(t) {
					var e = {
						tryLoc: t[0]
					};
					1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
				}
				function k(t) {
					var e = t.completion || {};
					e.type = "normal", delete e.arg, t.completion = e
				}
				function _(t) {
					this.tryEntries = [{
						tryLoc: "root"
					}], t.forEach(S, this), this.reset(!0)
				}
				function N(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length;) if (n.call(t, r)) return e.value = t[r], e.done = !1, e;
									return e.value = void 0, e.done = !0, e
								};
							return o.next = o
						}
					}
					return {
						next: j
					}
				}
				function j() {
					return {
						value: void 0,
						done: !0
					}
				}
				return h.prototype = m, s(w, "constructor", m), s(m, "constructor", h), h.displayName = s(m, u, "GeneratorFunction"), t.isGeneratorFunction = function(t) {
					var e = "function" == typeof t && t.constructor;
					return !!e && (e === h || "GeneratorFunction" === (e.displayName || e.name))
				}, t.mark = function(t) {
					return Object.setPrototypeOf ? Object.setPrototypeOf(t, m) : (t.__proto__ = m, s(t, u, "GeneratorFunction")), t.prototype = Object.create(w), t
				}, t.awrap = function(t) {
					return {
						__await: t
					}
				}, b(x.prototype), s(x.prototype, c, (function() {
					return this
				})), t.AsyncIterator = x, t.async = function(e, n, r, o, i) {
					void 0 === i && (i = Promise);
					var a = new x(l(e, n, r, o), i);
					return t.isGeneratorFunction(n) ? a : a.next().then((function(t) {
						return t.done ? t.value : a.next()
					}))
				}, b(w), s(w, u, "Generator"), s(w, a, (function() {
					return this
				})), s(w, "toString", (function() {
					return "[object Generator]"
				})), t.keys = function(t) {
					var e = [];
					for (var n in t) e.push(n);
					return e.reverse(),
					function n() {
						for (; e.length;) {
							var r = e.pop();
							if (r in t) return n.value = r, n.done = !1, n
						}
						return n.done = !0, n
					}
				}, t.values = N, _.prototype = {
					constructor: _,
					reset: function(t) {
						if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = !1, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(k), !t) for (var e in this) "t" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = void 0)
					},
					stop: function() {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval
					},
					dispatchException: function(t) {
						if (this.done) throw t;
						var e = this;

						function r(n, r) {
							return a.type = "throw", a.arg = t, e.next = n, r && (e.method = "next", e.arg = void 0), !! r
						}
						for (var o = this.tryEntries.length - 1; o >= 0; --o) {
							var i = this.tryEntries[o],
								a = i.completion;
							if ("root" === i.tryLoc) return r("end");
							if (i.tryLoc <= this.prev) {
								var c = n.call(i, "catchLoc"),
									u = n.call(i, "finallyLoc");
								if (c && u) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0);
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								} else if (c) {
									if (this.prev < i.catchLoc) return r(i.catchLoc, !0)
								} else {
									if (!u) throw new Error("try statement without catch or finally");
									if (this.prev < i.finallyLoc) return r(i.finallyLoc)
								}
							}
						}
					},
					abrupt: function(t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var o = this.tryEntries[r];
							if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) {
								var i = o;
								break
							}
						}
						i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);
						var a = i ? i.completion : {};
						return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, p) : this.complete(a)
					},
					complete: function(t, e) {
						if ("throw" === t.type) throw t.arg;
						return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), p
					},
					finish: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), k(n), p
						}
					},
					catch: function(t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var n = this.tryEntries[e];
							if (n.tryLoc === t) {
								var r = n.completion;
								if ("throw" === r.type) {
									var o = r.arg;
									k(n)
								}
								return o
							}
						}
						throw new Error("illegal catch attempt")
					},
					delegateYield: function(t, e, n) {
						return this.delegate = {
							iterator: N(t),
							resultName: e,
							nextLoc: n
						}, "next" === this.method && (this.arg = void 0), p
					}
				}, t
			}
			function i(t) {
				return i = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, i(t)
			}
			var a = new Map;

			function c(t, e) {
				return m(t) ? "pt".concat(e) : l(t) ? t + e : h(t) ? "uk".concat(e) : N(t) ? "ft".concat(e) : O(t) ? "fu".concat(e) : L(t) ? "jj".concat(e) : T(t) ? "bc".concat(e) : ["sz", "sh", "hk", "us"][t] + e
			}
			function u(t) {
				var e = t.slice(0, 2),
					n = ["sz", "sh", "hk", "us"].indexOf(e);
				return {
					market: -1 === n ? e : "".concat(n),
					scode: t.slice(2)
				}
			}
			function s(t) {
				return ["usDJI", "usINX", "usIXIC", "usNDX", "usHXC"].includes(t) ? "us.".concat(t.slice(2)) : t
			}
			function l(t) {
				return "bj" === t || "nq" === t
			}
			function f(t) {
				return 0 === +t || 1 === +t
			}
			function p(t) {
				return 2 === +t
			}
			function d(t) {
				return 3 === +t
			}
			function h(t) {
				return "uk" === t
			}
			function m(t) {
				return "p" === t || "pt" === t
			}
			function v(t) {
				return "GP-B" === t
			}
			function y(t) {
				return /^ZS/.test(t) || "INDEX" === t || /^FT/.test(t)
			}
			function g(t) {
				return "ZS-ZQ" === t
			}
			function w(t) {
				return "ZS-JW" === t
			}
			function b(t) {
				return "GP-A-CYB" === t
			}
			function x(t) {
				return "GP-A-KCB" === t
			}
			function E(t) {
				var e = ["FJ", "FJ-CX", "KJ", "LOF", "ETF", "QDII-LOF/ETF"];
				return e.includes(t)
			}
			function S(t) {
				return "ZQ-NHG" === t
			}
			function k(t) {
				return "ZQ-KZZ" === t
			}
			function _(t) {
				return /^QZ/.test(t)
			}
			function N(t) {
				return "ft" === t
			}
			function j(t) {
				return "ZS-FT_DE" === t || "FT_DE" === t
			}
			function O(t) {
				return "fu" === t
			}
			function P(t) {
				return "FU_SGXS" === t
			}
			function L(t) {
				return "jj" === t
			}
			function T(t) {
				return "bc" === t
			}
			function A(t) {
				return (t || "").replace(/(\.N|\.OQ|\.AM|\.PS|\.OTC)/g, "")
			}
			function I(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
					n = t.split(",");
				return n.map((function(t) {
					var n = t.substr(0, t.indexOf(":"));
					if (!isNaN(n) || m(n)) {
						var r = c(n, t.substr(t.indexOf(":") + 1));
						return e ? s(r) : r
					}
					return t.replace(/:/g, "")
				}))
			}
			function C(t) {
				var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
					n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 2,
					r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0,
					o = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 1;
				if (isNaN(t) || "" === t) return "" === t ? "--" : t;
				var i = parseFloat(t || 0);
				return i = i < 1e4 * o ? i.toFixed(r) : i >= 1e4 * o && i < 1e8 ? "".concat((i / 1e4).toFixed(n), "万") : i >= 1e8 && i < 1e11 ? "".concat((i / 1e8).toFixed(n), "亿") : i >= 1e11 && i < 1e12 ? "".concat((i / 1e11).toFixed(n), "千亿") : i >= 1e12 && i < 1e16 ? "".concat((i / 1e12).toFixed(n), "万亿") : "".concat((i / 1e16).toFixed(n), "兆"), i + e
			}
			function U(t) {
				var e = t < 10 ? 4 : void 0,
					n = new Intl.NumberFormat(void 0, {
						style: "currency",
						currency: "HKD",
						currencyDisplay: "narrowSymbol",
						minimumSignificantDigits: e,
						maximumSignificantDigits: e
					});
				return n.format(t)
			}
			var D = Object.freeze({
				__proto__: null,
				typeMap: a,
				getSymbol: c,
				splitSymbol: u,
				hackUSSymbol: s,
				isBJMarket: l,
				isHSMarket: f,
				isHKMarket: p,
				isUSMarket: d,
				isUKMarket: h,
				isHSPlate: m,
				isBMarket: v,
				isIndex: y,
				isDebtIndex: g,
				isGuoZhengHK: w,
				isChuangYeStock: b,
				isKeChuangStock: x,
				isFund: E,
				isDebt: S,
				isTransferableDebt: k,
				isWarrants: _,
				isFTIndex: N,
				isGermanFTIndex: j,
				isFutures: O,
				isSGFutures: P,
				isJJMarket: L,
				isBCCurrency: T,
				trimScode: A,
				formatStock: I,
				bigNumberToText: C,
				formatCurrency: U
			});

			function z(t, e) {
				var n = Object.keys(t);
				if (Object.getOwnPropertySymbols) {
					var r = Object.getOwnPropertySymbols(t);
					e && (r = r.filter((function(e) {
						return Object.getOwnPropertyDescriptor(t, e).enumerable
					}))), n.push.apply(n, r)
				}
				return n
			}
			function M(t) {
				for (var e = 1; e < arguments.length; e++) {
					var n = null != arguments[e] ? arguments[e] : {};
					e % 2 ? z(Object(n), !0).forEach((function(e) {
						H(t, e, n[e])
					})) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : z(Object(n)).forEach((function(e) {
						Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
					}))
				}
				return t
			}
			function R(t) {
				return R = "function" === typeof Symbol && "symbol" === typeof Symbol.iterator ? function(t) {
					return typeof t
				} : function(t) {
					return t && "function" === typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
				}, R(t)
			}
			function q(t, e, n, r, o, i, a) {
				try {
					var c = t[i](a),
						u = c.value
				} catch (s) {
					return void n(s)
				}
				c.done ? e(u) : Promise.resolve(u).then(r, o)
			}
			function W(t) {
				return function() {
					var e = this,
						n = arguments;
					return new Promise((function(r, o) {
						var i = t.apply(e, n);

						function a(t) {
							q(i, r, o, a, c, "next", t)
						}
						function c(t) {
							q(i, r, o, a, c, "throw", t)
						}
						a(void 0)
					}))
				}
			}
			function F(t, e) {
				if (!(t instanceof e)) throw new TypeError("Cannot call a class as a function")
			}
			function B(t, e) {
				for (var n = 0; n < e.length; n++) {
					var r = e[n];
					r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(t, r.key, r)
				}
			}
			function G(t, e, n) {
				return e && B(t.prototype, e), n && B(t, n), t
			}
			function H(t, e, n) {
				return e in t ? Object.defineProperty(t, e, {
					value: n,
					enumerable: !0,
					configurable: !0,
					writable: !0
				}) : t[e] = n, t
			}
			function $(t, e) {
				if ("function" !== typeof e && null !== e) throw new TypeError("Super expression must either be null or a function");
				t.prototype = Object.create(e && e.prototype, {
					constructor: {
						value: t,
						writable: !0,
						configurable: !0
					}
				}), e && Q(t, e)
			}
			function J(t) {
				return J = Object.setPrototypeOf ? Object.getPrototypeOf : function(t) {
					return t.__proto__ || Object.getPrototypeOf(t)
				}, J(t)
			}
			function Q(t, e) {
				return Q = Object.setPrototypeOf || function(t, e) {
					return t.__proto__ = e, t
				}, Q(t, e)
			}
			function Z() {
				if ("undefined" === typeof Reflect || !Reflect.construct) return !1;
				if (Reflect.construct.sham) return !1;
				if ("function" === typeof Proxy) return !0;
				try {
					return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {}))), !0
				} catch (t) {
					return !1
				}
			}
			function Y(t) {
				if (void 0 === t) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
				return t
			}
			function V(t, e) {
				if (e && ("object" === i(e) || "function" === typeof e)) return e;
				if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined");
				return Y(t)
			}
			function X(t) {
				var e = Z();
				return function() {
					var n, r = J(t);
					if (e) {
						var o = J(this).constructor;
						n = Reflect.construct(r, arguments, o)
					} else n = r.apply(this, arguments);
					return V(this, n)
				}
			}
			function K(t, e) {
				while (!Object.prototype.hasOwnProperty.call(t, e)) if (t = J(t), null === t) break;
				return t
			}
			function tt(t, e, n) {
				return tt = "undefined" !== typeof Reflect && Reflect.get ? Reflect.get : function(t, e, n) {
					var r = K(t, e);
					if (r) {
						var o = Object.getOwnPropertyDescriptor(r, e);
						return o.get ? o.get.call(n) : o.value
					}
				}, tt(t, e, n || t)
			}
			function et(t, e) {
				if (t) {
					if ("string" === typeof t) return nt(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return "Object" === n && t.constructor && (n = t.constructor.name), "Map" === n || "Set" === n ? Array.from(t) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? nt(t, e) : void 0
				}
			}
			function nt(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r
			}
			function rt(t, e) {
				var n = "undefined" !== typeof Symbol && t[Symbol.iterator] || t["@@iterator"];
				if (!n) {
					if (Array.isArray(t) || (n = et(t)) || e && t && "number" === typeof t.length) {
						n && (t = n);
						var r = 0,
							o = function() {};
						return {
							s: o,
							n: function() {
								return r >= t.length ? {
									done: !0
								} : {
									done: !1,
									value: t[r++]
								}
							},
							e: function(t) {
								throw t
							},
							f: o
						}
					}
					throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
				}
				var i, a = !0,
					c = !1;
				return {
					s: function() {
						n = n.call(t)
					},
					n: function() {
						var t = n.next();
						return a = t.done, t
					},
					e: function(t) {
						c = !0, i = t
					},
					f: function() {
						try {
							a || null == n.
							return ||n.
							return ()
						} finally {
							if (c) throw i
						}
					}
				}
			}
			var ot = {
				adaptBJ: function(t) {
					return t
				},
				adaptHS: function(t) {
					return t
				},
				adaptPT: function(t) {
					return t
				},
				adaptHK: function(t) {
					return t
				},
				adaptUS: function(t) {
					return t
				},
				adaptUK: function(t) {
					return t
				},
				adaptFT: function(t) {
					return t
				},
				adaptFU: function(t) {
					return t
				},
				adaptBC: function(t) {
					return t
				},
				adaptTransDebt: function(t) {
					return t
				}
			}, it = {
				retcode: "0",
				has_follow: "deprecated",
				market_state: "deprecated"
			};

			function at(t) {
				if (!t) return Date.now() / 1e3;
				var e = / /.test(t) ? t.replace(/-/g, "/") : "".concat(t.slice(0, 4), "/").concat(t.slice(4, 6), "/").concat(t.slice(6, 8), " ").concat(t.slice(8, 10), ":").concat(t.slice(10, 12), ":").concat(t.slice(12));
				return "".concat(new Date(e).getTime() / 1e3)
			}
			var ct = {
				adaptBJ: function(t) {
					var e = {
						61: "nq",
						62: "bj"
					}[t[0]];
					return M(M({}, it), {}, {
						five_trans: {
							mcjg1: t[19],
							mcjg2: t[21],
							mcjg3: t[23],
							mcjg4: t[25],
							mcjg5: t[27],
							mcsl1: t[20],
							mcsl2: t[22],
							mcsl3: t[24],
							mcsl4: t[26],
							mcsl5: t[28],
							mrjg1: t[9],
							mrjg2: t[11],
							mrjg3: t[13],
							mrjg4: t[15],
							mrjg5: t[17],
							mrsl1: t[10],
							mrsl2: t[12],
							mrsl3: t[14],
							mrsl4: t[16],
							mrsl5: t[18]
						},
						secu_info: {
							market: e,
							nczj: t[62],
							price_ceiling: t[47],
							price_floor: t[48],
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[61],
							symbol: c(e, t[2]),
							zdf_day20: t[70],
							zdf_day5: t[63]
						},
						secu_quote: {
							amplitude: t[43],
							avg_price: t[51],
							cje: t[37],
							cjl: t[6],
							code: t[2],
							dqj: t[3],
							dynamic_ratio: t[52],
							hsl: t[38],
							jkj: t[5],
							lsacle: t[49],
							ltz: t[44],
							lyr_ratio: t[53],
							market: e,
							market_maker: t[83],
							npl: t[8],
							sjl: t[46],
							syl: t[39],
							utime: at(t[30]),
							week52zdj: t[68],
							week52zgj: t[67],
							wpl: t[7],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45]
						}
					})
				},
				adaptHS: function(t) {
					var e = {
						51: "0",
						1: "1"
					}[t[0]];
					return M(M({}, it), {}, {
						five_trans: {
							mcjg1: t[19],
							mcjg2: t[21],
							mcjg3: t[23],
							mcjg4: t[25],
							mcjg5: t[27],
							mcsl1: t[20],
							mcsl2: t[22],
							mcsl3: t[24],
							mcsl4: t[26],
							mcsl5: t[28],
							mrjg1: t[9],
							mrjg2: t[11],
							mrjg3: t[13],
							mrjg4: t[15],
							mrjg5: t[17],
							mrsl1: t[10],
							mrsl2: t[12],
							mrsl3: t[14],
							mrsl4: t[16],
							mrsl5: t[18]
						},
						secu_info: {
							beta: t[56],
							ltgb: t[72],
							ltgb_tz: t[76],
							market: e,
							nczj: t[62],
							price_ceiling: t[47],
							price_floor: t[48],
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[61],
							symbol: c(e, t[2]),
							zdf_day20: t[70],
							zdf_day5: t[63],
							zgb: t[73]
						},
						secu_quote: {
							amplitude: t[43],
							avg_price: t[51],
							cje: t[57],
							cjl: t[36],
							code: t[2],
							dqj: t[3],
							dynamic_ratio: t[52],
							gxl: t[64],
							hsl: t[38],
							iopv: t[78],
							jkj: t[5],
							jz: t[81],
							lsacle: t[49],
							ltz: t[44],
							lyr_ratio: t[53],
							market: e,
							mins: t[35],
							npl: t[8],
							phcje: t[58],
							phcjl: t[59],
							share: t[72],
							sjl: t[46],
							syl: t[39],
							utime: at(t[30]),
							wbcale: t[74],
							week52zdj: t[68],
							week52zgj: t[67],
							wpl: t[7],
							yzl: t[77],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45]
						}
					})
				},
				adaptPT: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							market: "p",
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[58],
							symbol: c("p", t[2])
						},
						secu_quote: {
							amplitude: t[43],
							cje: t[37],
							cjl: t[36],
							code: t[2],
							dqj: t[3],
							hsl: t[38],
							jkj: t[5],
							lsacle: t[49],
							ltz: t[44],
							market: "p",
							sjl: t[46],
							syl: t[39],
							utime: at(t[30]),
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45]
						}
					})
				},
				adaptHK: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							gx: t[72],
							ltgb: t[70],
							market: "2",
							nczj: t[61],
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[63],
							symbol: c("2", t[2]),
							trd_unit: t[60],
							zdf_day5: t[62],
							zdf_day20: t[67],
							zgb: t[69]
						},
						secu_quote: {
							amplitude: t[43],
							avg_price: t[73],
							cje: t[37],
							cjl: t[6],
							code: t[2],
							dqj: t[3],
							dynamic_ratio: t[71],
							hsl: t[59],
							jkj: t[5],
							lsacle: t[50],
							ltz: t[44],
							market: "2",
							sjl: t[58],
							syl: t[39],
							ttm_ratio: t[57],
							utime: at(t[30]),
							wbcale: t[51],
							week52zdj: t[49],
							week52zgj: t[48],
							weekratio: t[47],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45],
							hblx: t[75]
						}
					})
				},
				adaptUS: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							gx: t[66],
							ltgb: t[63],
							market: "3",
							nczj: t[54],
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[56],
							symbol: c("3", t[2]),
							timeliness: t[0],
							zdf_day20: t[60],
							zdf_day5: t[55],
							zgb: t[62]
						},
						secu_quote: {
							amplitude: t[43],
							avg_price: t[67],
							cje: t[37],
							cjl: t[36],
							code: t[2],
							dqj: t[3],
							dynamic_ratio: t[65],
							gxl: t[52],
							hsl: t[38],
							jkj: t[5],
							lsacle: t[64],
							ltz: t[44],
							lyr_ratio: t[41],
							market: "3",
							mgsy: t[47],
							npl: t[8],
							sjl: t[51],
							syl: t[39],
							utime: at(t[30]),
							week52zdj: t[49],
							week52zgj: t[48],
							wpl: t[7],
							xszgb: t[69],
							xszsz: t[68],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45],
							hblx: t[35]
						}
					})
				},
				adaptUK: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							market: "uk",
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[56],
							symbol: c("uk", t[2])
						},
						secu_quote: {
							amplitude: t[43],
							dqj: t[3],
							jkj: t[5],
							market: "uk",
							utime: at(t[30]),
							week52zdj: t[49],
							week52zgj: t[48],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4]
						}
					})
				},
				adaptFT: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							market: "ft",
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[56],
							symbol: c("ft", t[2])
						},
						secu_quote: {
							amplitude: t[43],
							cje: t[37],
							dqj: t[3],
							jkj: t[5],
							market: "ft",
							utime: at(t[30]),
							week52zdj: t[49],
							week52zgj: t[48],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4]
						}
					})
				},
				adaptFU: function(t) {
					return M(M({}, it), {}, {
						five_trans: {
							mcjg1: t[19],
							mcjg2: t[21],
							mcjg3: t[23],
							mcjg4: t[25],
							mcjg5: t[27],
							mcsl1: t[20],
							mcsl2: t[22],
							mcsl3: t[24],
							mcsl4: t[26],
							mcsl5: t[28],
							mrjg1: t[9],
							mrjg2: t[11],
							mrjg3: t[13],
							mrjg4: t[15],
							mrjg5: t[17],
							mrsl1: t[10],
							mrsl2: t[12],
							mrsl3: t[14],
							mrsl4: t[16],
							mrsl5: t[18]
						},
						secu_info: {
							market: "fu",
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							stocktype: t[56],
							symbol: c("fu", t[2]),
							timeliness: t[0]
						},
						secu_quote: {
							avg_price: t[45],
							ccl: t[38],
							cjl: t[36],
							dqj: t[3],
							jkj: t[5],
							market: "fu",
							rzc: t[39],
							utime: at(t[30]),
							week52zdj: t[49],
							week52zgj: t[48],
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zjj: t[41],
							zsj: t[4]
						}
					})
				},
				adaptBC: function(t) {
					return M(M({}, it), {}, {
						secu_info: {
							market: "bc",
							secu_code: t[2],
							secu_name: t[1],
							status: "" === t[40] ? "0" : t[40],
							stk_name: t[1],
							symbol: c("bc", t[2])
						},
						secu_quote: {
							cje: t[37],
							cjl: t[6],
							dqj: t[3],
							jkj: t[5],
							market: "bc",
							utime: at(t[30]),
							zde: t[31],
							zdf: t[32],
							zdj: t[34],
							zgj: t[33],
							zsj: t[4],
							zsz: t[45],
							hblx: t[82]
						}
					})
				},
				adaptTransDebt: function(t) {
					return {
						callPrice: t[26],
						callTriggerPrice: t[27],
						convertible: t[21],
						convertPremium: t[5],
						convertPrice: t[3],
						convertDate: t[22],
						convertValue: t[4],
						duration: t[19],
						maturityDate: t[10],
						maturityPrice: t[11],
						maturityYield: t[24],
						putDate: t[23],
						putPrice: t[28],
						remainSize: t[12],
						remainTime: t[20],
						rate: t[7],
						scale: t[25],
						stockPB: t[29]
					}
				}
			}, ut = {
				DEFAULT: {
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom",
					SQT: "/domain_upstream/sqtgtimgcn"
				},
				DAFENG: {
					PROXY_QQ: "/proxyfinanceqqcom",
					SQT: "/sqtgtimgcn"
				},
				GUOSEN: {
					PROXY_QQ: "/domain_upstream/proxyfinanceqqcom",
					SQT: "/domain_upstream/sqtgtimgcn"
				},
				ZHONGXINJIANTOU: {
					PROXY_QQ: "https://zxgcloud.csc108.com/domain_upstream/proxyfinanceqqcom",
					SQT: "https://zxgcloud.csc108.com/domain_upstream/sqtgtimgcn"
				}
			}, st = {
				DEFAULT: "wzq",
				DAFENG: "df",
				GUOSEN: "oem_guosen",
				ZHONGXINJIANTOU: "oem_jiantou"
			}, lt = {
				df: "34C0A93DF3AD73D4307E468317380146"
			}, ft = "wzq",
				pt = "https://proxy.finance.qq.com",
				dt = "https://sqt.gtimg.cn",
				ht = function() {
					function t(e) {
						var n, r, o;
						F(this, t), this.ajaxGet = e || this.defaultAjaxGet, this.oem = null === (n = window) || void 0 === n || null === (r = n.$broker) || void 0 === r ? void 0 : r.id, this.origin = ft, this.host = pt, this.qtHost = dt;
						var i = !1;
						try {
							i = !1
						} catch (s) {}
						if (this.oem) {
							var a, c;
							this.origin = st[this.oem] || (null === (a = window) || void 0 === a || null === (c = a.$broker) || void 0 === c ? void 0 : c.appSource) || st.DEFAULT;
							var u = ut[this.oem] || ut.DEFAULT;
							this.host = u.PROXY_QQ, this.qtHost = u.SQT
						} else null !== (o = window) && void 0 !== o && o.$app && /^(h5|quick)_.*/.test(window.$app) ? this.origin = window.$app : i && (this.origin = "zxg_xcx")
					}
					return G(t, [{
						key: "defaultAjaxGet",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return t.prev = 0, t.next = 3, fetch(e);
										case 3:
											return n = t.sent, t.abrupt("return", n.json());
										case 7:
											return t.prev = 7, t.t0 = t["catch"](0), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 10:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[0, 7]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "defaultErrorHandler",
						value: function() {
							return {}
						}
					}, {
						key: "getSign",
						value: function(t) {
							for (var e = [], n = 0, o = Object.keys(t); n < o.length; n++) {
								var i = o[n];
								e.push("".concat(i, "=").concat(t[i]))
							}
							return e.push("key=".concat(lt[this.origin])), {
								"x-appid": this.origin,
								"x-sa-v": 1,
								"x-sa-sign": r(e.join("&")).toLowerCase(),
								"x-timestamp": parseInt((new Date).getTime() / 1e3, 10)
							}
						}
					}, {
						key: "getQTAdapter",
						value: function(t) {
							return "undefined" === typeof t ? ot : "string" === typeof t ? "stockinfo" === t ? ct : ot : "object" === R(t) ? t : void 0
						}
					}, {
						key: "getQTs",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, c, s, l, f, h, m, v, y, g, w, b, x, E, S, k, _, N = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											n = N.length > 1 && void 0 !== N[1] ? N[1] : {}, r = n.getAll, i = void 0 !== r && r, a = n.encode, c = void 0 === a ? "" : a, s = i ? "" : "s_", l = [], f = rt(e);
											try {
												for (f.s(); !(h = f.n()).done;) m = h.value, v = u(m), y = v.market, g = v.scode, p(y) ? l.push("".concat(s, "r_hk").concat(g)) : d(y) ? l.push("t_".concat(s, "us").concat(A(g).replace(".", "__"))) : l.push("".concat(s).concat(m))
											} catch (o) {
												f.e(o)
											} finally {
												f.f()
											}
											return w = "".concat(this.qtHost, "/").concat(c, "/?q=").concat(l.join(","), "&fmt=json"), t.prev = 7, t.next = 10, this.ajaxGet(w);
										case 10:
											for (b = t.sent, x = {}, E = 0, S = Object.keys(b); E < S.length; E++) k = S[E], _ = k.replace(/s_|r_|t_/g, ""), x[_] = b[k];
											return t.abrupt("return", x);
										case 16:
											return t.prev = 16, t.t0 = t["catch"](7), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 19:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[7, 16]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}]), t
				}();

			function mt(t, e) {
				return f(t) ? e[61] : m(t) ? e[58] : p(t) ? e[63] : d(t) || h(t) || N(t) || O(t) ? e[56] : void 0
			}
			function vt(t, e) {
				return x(e) ? 1 : k(e) ? 10 : l(t) || f(t) ? 100 : 1
			}
			function yt(t, e) {
				return x(e) ? 1 : l(t) || f(t) || m(t) ? 100 : 1
			}
			function gt(t, e) {
				var n = e.market,
					r = e.scode;
				if (T(n)) {
					for (var o = t.data, i = o.fs.fsList[0].pointList, a = [], u = 0; u < i.length; u++) {
						var l = i[u],
							f = {
								time: l.tm,
								price: l.pix
							};
						a.push(f)
					}
					return {
						chartData: a,
						raw: o
					}
				}
				var p = c(n, r);
				p = d(n) ? s(p) : p;
				for (var h = t.data[p], m = mt(n, h.qt[p]), v = vt(n, m), y = [], g = 0, w = 0; w < h.data.data.length; w++) {
					var b = h.data.data[w];
					if (b) {
						var x = b.split(" "),
							E = 0 === +x[2] ? 0 : x[2] - g,
							S = {
								time: x[0],
								price: x[1],
								volume: E,
								amount: x[1] * E * v,
								totalVolume: x[2] * v,
								totalAmount: x[3]
							};
						h.data.extData && (S.iopv = h.data.extData[w].iopv), y.push(S), g = x[2]
					}
				}
				return {
					chartData: y,
					raw: h
				}
			}
			function wt(t, e) {
				var n, r = e.market,
					o = e.stockType,
					i = e.dType,
					a = vt(r, o),
					c = t.data["".concat(i, "List")] || [],
					u = {
						dType: i,
						items: [],
						max: 0,
						min: Number.MAX_SAFE_INTEGER,
						maxVol: 0
					}, s = rt(c);
				try {
					for (s.s(); !(n = s.n()).done;) {
						var l = n.value,
							p = void 0;
						"sec" === i ? (p = 60 * (l.tm.slice(2, 4) - 15) + +l.tm.slice(4), l.p = l.b1p, l.b1v = Math.round(l.b1v / a), l.b2v = Math.round(l.b2v / a), l.s2v = Math.round(l.s2v / a), u.maxVol = Math.max(u.maxVol, +l.b1v + +l.b2v + +l.s2v)) : "min" === i && (p = l.tm.slice(2, 4) - (f(r) ? 15 : 0)), u.items[p] = l, u.max = Math.max(u.max, l.p), u.min = Math.min(u.min, l.p)
					}
				} catch (d) {
					s.e(d)
				} finally {
					s.f()
				}
				return u
			}
			function bt(t, e) {
				var n, r = e.market,
					o = e.stockType,
					i = e.isHKOrZsOrFundOrNhg,
					a = vt(r, o),
					c = {
						items: [],
						preClose: +t.data.preClose,
						avgPrice: 0
					}, u = 0,
					s = 0,
					l = rt(t.data.minList);
				try {
					for (l.s(); !(n = l.n()).done;) {
						var f = n.value;
						if (f) {
							var p = f.split(" "),
								d = 0 === +p[2] ? 0 : p[2] - u,
								h = p[1] * d * a;
							c.items.push({
								time: p[0],
								price: p[1],
								volume: d,
								amount: h,
								totalVolume: p[2] * a,
								totalAmount: p[3]
							}), u = p[2], s += h
						}
					}
				} catch (v) {
					l.e(v)
				} finally {
					l.f()
				}
				var m = c.items[c.items.length - 1];
				return i || "undefined" === typeof m.totalAmount ? c.avgPrice = 0 === +u ? +m.price : s / u / a : c.avgPrice = 0 === +m.totalVolume ? +m.price : m.totalAmount / m.totalVolume, c
			}
			function xt(t, e) {
				var n = e.market,
					r = e.scode;
				if (T(n)) {
					var o, i = t.data,
						a = i.fs.fsList.reverse(),
						u = {
							items: [],
							labels: [],
							preClose: a[0].preClose
						}, v = rt(a);
					try {
						for (v.s(); !(o = v.n()).done;) {
							for (var y = o.value, w = y.pointList, b = 0; b < w.length; b++) {
								var x = w[b],
									E = b % 4 === 0;
								if (E) {
									var k = {
										time: x.tm,
										price: x.pix
									};
									u.items.push(k)
								}
							}
							u.labels.push("".concat(y.date.slice(4, 6), "/").concat(y.date.slice(6)))
						}
					} catch (B) {
						v.e(B)
					} finally {
						v.f()
					}
					return {
						chartData: u,
						raw: i
					}
				}
				var _ = c(n, r);
				_ = d(n) ? s(_) : _;
				var N, O = t.data[_],
					L = mt(n, O.qt[_]),
					A = vt(n, L),
					I = O.data.reverse(),
					C = {
						items: [],
						labels: [],
						preClose: I[0].prec
					}, U = rt(I);
				try {
					for (U.s(); !(N = U.n()).done;) {
						for (var D = N.value, z = D.data, M = 0, R = 0; R < z.length; R++) if (z[R]) {
							var q = z[R].split(" "),
								W = !1;
							if (S(L) || g(L) ? W = R <= 120 && R % 4 === 0 || R > 123 && R % 4 === 0 || 271 === R : l(n) || f(n) || m(n) ? W = R <= 120 && R % 4 === 0 || R > 123 && R % 4 === 0 || 241 === R : p(n) ? W = R <= 150 && R % 4 === 0 || R >= 151 && (R + 1) % 4 === 0 || 330 === R : d(n) ? W = R % 4 === 0 || 390 === R : h(n) || j(L) ? W = R % 4 === 0 || 510 === R : P(L) && (W = R <= 735 && R % 4 === 0 || 735 === R || R >= 736 && R % 4 === 0 || 1186 === R), W) {
								var F = 0 === +q[2] ? 0 : q[2] - M;
								C.items.push({
									date: D.date,
									time: q[0],
									price: q[1],
									volume: F,
									amount: q[1] * F * A,
									totalVolume: q[2] * A,
									totalAmount: q[3]
								})
							}
							M = q[2]
						}
						C.labels.push("".concat(D.date.slice(4, 6), "/").concat(D.date.slice(6)))
					}
				} catch (B) {
					U.e(B)
				} finally {
					U.f()
				}
				return {
					chartData: C,
					raw: O
				}
			}
			function Et(t, e) {
				for (var n = e.market, r = e.type, o = /^m\d/.test(r), i = t.data, a = yt(n, i.qt.fields[61]), c = i.nodes, u = i.opPoints, s = [], l = 0; l < c.length; l++) {
					var f = c[l],
						p = c[l - 1],
						d = f.date;
					o || /-/.test(d) || (d = "".concat(d.slice(0, 4), "-").concat(d.slice(4, 6), "-").concat(d.slice(6, 8)));
					var h = {
						quoteTime: d,
						time: d,
						open: +f.open,
						close: +f.last,
						high: +f.high,
						low: +f.low,
						volume: f.volume / a,
						preClose: p ? +p.last : +i.prec || +f.open,
						fh: {
							FHcontent: f.dividend,
							since_add_zdf: f.addZdf
						},
						hsl: +f.exchange,
						cje: f.amount / 1e4,
						oi: +f.oi
					};
					f.tradeDays && !isNaN(f.tradeDays) && (h.tradeDays = +f.tradeDays), isNaN(f.exchangeRaw) || (h.exchangeRaw = f.exchangeRaw);
					var m = u && u[l];
					if (m) {
						var v = m.signal,
							y = m.firstKprice,
							g = m.secondKprice;
						h.opData = {
							signal: v,
							firstKprice: y,
							secondKprice: g
						}
					}
					s.push(h)
				}
				return {
					chartData: s,
					raw: i
				}
			}
			function St(t, e) {
				var n = e.market,
					r = e.scode,
					o = e.type;
				if (m(n) || d(n)) {
					for (var i = t.data, a = i.kline, u = [], s = 0; s < a.length; s++) {
						var l = a[s],
							f = a[s - 1];
						u.push({
							quoteTime: l.dt,
							time: l.dt,
							open: +l.op,
							close: +l.cp,
							high: +l.hp,
							low: +l.lp,
							volume: +l.vol,
							preClose: f ? +f.cp : +i.prec || +l.op,
							cje: +l.amt
						})
					}
					return {
						chartData: u,
						raw: i
					}
				}
				for (var p = t.data[c(n, r)] || t.data, h = p[o] || p.data, v = [], y = 0; y < h.length; y++) {
					var g = h[y],
						w = h[y - 1];
					v.push({
						quoteTime: g[0],
						time: g[0],
						open: +g[1],
						close: +g[2],
						high: +g[3],
						low: +g[4],
						volume: +g[5],
						preClose: w ? +w[2] : +p.prec || +g[1],
						fh: g[6] || {},
						hsl: +g[7] / 100
					})
				}
				return {
					chartData: v,
					raw: p
				}
			}
			function kt(t, e) {
				var n = e.market,
					r = [];
				if (l(n)) {
					var o = t.data.data;
					r = "" === o ? [] : o.split("|").reverse()
				} else r = t.data.details || [], O(n) && r.reverse();
				var i = r.map((function(t) {
					var e = t.split("/");
					return {
						id: e[0],
						time: e[1],
						price: e[2],
						zdf: e[3],
						amount: e[4],
						volume: e[5],
						type: e[6]
					}
				}));
				return {
					list: i,
					page: t.data.page
				}
			}
			function _t(t, e, n) {
				var r = 2,
					o = parseInt(t || 0, 10);
				return l(e) ? C(o, "手", r) : p(e) || d(e) ? C(o, "股", r) : O(e) ? C(o, "", r) : C(o, x(n) ? "股" : "手", r)
			}
			function Nt(t) {
				var e = Object.assign({}, t),
					n = Object.assign({}, t.secu_quote),
					r = Object.assign({}, t.secu_info),
					o = Object.assign({}, t.five_trans);
				n.price_ceiling = r.price_ceiling, n.price_floor = r.price_floor, n.zgb = r.zgb, n.ltgb = r.ltgb, n.ltgb_tz = r.ltgb_tz, n.gx = r.gx, n.beta = r.beta, n.trd_unit = r.trd_unit, n.zdf_day5 = r.zdf_day5, n.zdf_day20 = r.zdf_day20, n.nczj = r.nczj, e.status = {
					C: "代码变更",
					D: "退市",
					S: "停牌",
					Z: "暂停上市",
					U: "待上市",
					I: "待发行",
					P: "申购日"
				}[r.status] || "", n.zde > 0 && (n.zde = "+".concat(n.zde)), n.zdf > 0 && (n.zdf = "+".concat(n.zdf)), 0 === +n.zgj && (n.zgj = "--"), 0 === +n.zdj && (n.zdj = "--"), 0 === +n.jkj && (n.jkj = "--"), 0 === +n.zsj && (n.zsj = "--"), "undefined" !== typeof n.cjl && (n.cjl = _t(n.cjl, n.market, r.stocktype)), "undefined" !== typeof n.phcjl && (n.phcjl = _t(n.phcjl, n.market, r.stocktype)), l(r.market) || f(r.market) || m(r.market) || p(r.market) && y(r.stocktype) ? ("undefined" !== typeof n.cje && (!isNaN(n.cje) && n.cje >= 1e7 ? n.cje = "".concat((n.cje / 1e4).toFixed(2), "亿") : n.cje = C(1e4 * n.cje)), "undefined" !== typeof n.phcje && (!isNaN(n.phcje) && n.phcje >= 1e7 ? n.phcje = "".concat((n.phcje / 1e4).toFixed(2), "亿") : n.phcje = C(1e4 * n.phcje))) : ("undefined" !== typeof n.cje && (!isNaN(n.cje) && n.cje >= 1e11 ? n.cje = "".concat((n.cje / 1e8).toFixed(2), "亿") : !isNaN(n.cje) && n.cje < 1e4 ? n.cje = (n.cje / 1).toFixed(0) : n.cje = C(n.cje)), "undefined" !== typeof n.phcje && (!isNaN(n.phcje) && n.phcje >= 1e11 ? n.phcje = "".concat((n.phcje / 1e8).toFixed(2), "亿") : !isNaN(n.phcje) && n.phcje < 1e4 ? n.phcje = (n.phcje / 1).toFixed(0) : n.phcje = C(n.phcje, "", 2, 2, 1e-4))), "undefined" !== typeof n.npl && (n.npl = C(n.npl)), "undefined" !== typeof n.wpl && (n.wpl = C(n.wpl)), "undefined" !== typeof n.amplitude && ("" === n.amplitude || 0 === +n.amplitude ? n.amplitude = "0.00%" : n.amplitude = C(n.amplitude, "%", 2, 2)), "undefined" !== typeof n.hsl && (n.hsl = C(n.hsl, "%", 2, 2)), n.originalZsz = n.zsz, "undefined" !== typeof n.zsz && ("" === n.zsz || 0 === +n.zsz ? n.zsz = "--" : !isNaN(n.zsz) && n.zsz >= 1e4 ? n.zsz = "".concat((1 * n.zsz).toFixed(0), "亿") : !isNaN(n.zsz) && n.zsz < 1 ? n.zsz = "".concat((1e4 * n.zsz).toFixed(2), "万") : n.zsz = C(n.zsz, "亿", 2, 2)), n.xszsz && (n.xszsz >= 1e4 ? n.xszsz = "".concat((1 * n.xszsz).toFixed(0), "亿") : n.xszsz < 1 ? n.xszsz = "".concat((1e4 * n.xszsz).toFixed(2), "万") : n.xszsz = C(n.xszsz, "亿", 2, 2)), "undefined" !== typeof n.ltz && ("" === n.ltz || 0 === +n.ltz ? n.ltz = "--" : !isNaN(n.ltz) && n.ltz >= 1e4 ? n.ltz = "".concat((1 * n.ltz).toFixed(0), "亿") : !isNaN(n.ltz) && n.ltz < 1 ? n.ltz = "".concat((1e4 * n.ltz).toFixed(2), "万") : n.ltz = C(n.ltz, "亿", 2, 2)), "undefined" !== typeof n.zgb && ("" === n.zgb || 0 === +n.zgb ? n.zgb = "--" : n.zgb = C(n.zgb)), n.xszgb && (n.xszgb = C(n.xszgb)), "undefined" !== typeof n.ltgb && ("" === n.ltgb || 0 === +n.ltgb ? n.ltgb = "--" : n.ltgb = C(n.ltgb)), "undefined" !== typeof n.ltgb_tz && ("" === n.ltgb_tz || 0 === +n.ltgb_tz ? n.ltgb_tz = "--" : n.ltgb_tz = C(n.ltgb_tz)), n.share = n.ltgb, n.gx || t.websocket || (n.gx = "0.00"), n.gxl ? n.gxl += "%" : t.websocket || (n.gxl = "0.00%"), n.weekratio ? n.weekratio += "%" : t.websocket || (n.weekratio = "0.00%"), 0 === +n.mgsy && (n.mgsy = "--"), 0 === +n.syl ? n.syl = "--" : n.syl < 0 && (n.syl = "亏损"), 0 === +n.dynamic_ratio ? n.dynamic_ratio = "--" : n.dynamic_ratio < 0 && (n.dynamic_ratio = "亏损"), 0 === +n.ttm_ratio ? n.ttm_ratio = "--" : n.ttm_ratio < 0 && (n.ttm_ratio = "亏损"), 0 === +n.lyr_ratio ? n.lyr_ratio = "--" : n.lyr_ratio < 0 && (n.lyr_ratio = "亏损"), 0 === +n.sjl ? n.sjl = "--" : n.sjl < 0 && (n.sjl = "亏损"), 0 === +n.lsacle && (n.lsacle = "--"), n.wbcale && (0 === +n.wbcale ? n.wbcale = "0.00%" : n.wbcale += "%"), -1 === +n.price_ceiling && (n.price_ceiling = "不限"), -1 === +n.price_floor && (n.price_floor = "不限"), "" !== n.avg_price && 0 !== +n.avg_price || (n.avg_price = "--"), 0 === +n.week52zgj && (n.week52zgj = "--"), 0 === +n.week52zdj && (n.week52zdj = "--"), n.zdf_day5 && (n.zdf_day5 += "%"), n.zdf_day20 && (n.zdf_day20 += "%"), n.nczj && (n.nczj += "%"), n.yzl && (n.yzl += "%"), n.mins && (n.mins = n.mins.split("/")), n.ccl && (n.ccl = C(n.ccl)), n.rzc && (n.rzc = C(n.rzc));
				for (var i = 0, a = 0, c = 1; c <= 5; c++) 0 === +o["mcsl".concat(c)] ? o["mcsl".concat(c)] = "-" : a += 1 * o["mcsl".concat(c)], o["mcsl".concat(c)] = C(o["mcsl".concat(c)], "", 1), 0 === +o["mrsl".concat(c)] ? o["mrsl".concat(c)] = "-" : i += 1 * o["mrsl".concat(c)], o["mrsl".concat(c)] = C(o["mrsl".concat(c)], "", 1), 0 === +o["mcjg".concat(c)] && (o["mcjg".concat(c)] = "-"), 0 === +o["mrjg".concat(c)] && (o["mrjg".concat(c)] = "-");
				var u = i + a;
				return n.wscale = u > 0 ? "".concat(((i - a) / u * 100).toFixed(2), "%") : "", e.secu_quote = n, e.secu_info = r, e.five_trans = o, e.processed = !0, e
			}
			function jt(t) {
				var e = Object.assign({}, t);
				return "undefined" !== typeof e.convertible && (e.convertible = "Y" === e.convertible ? "是" : "否"), e.convertPremium && (e.convertPremium += "%"), e.duration && (e.duration = "".concat(parseInt(e.duration, 10), "年")), e.maturityYield && (e.maturityYield += "%"), e.remainSize && (e.remainSize = C(1e4 * e.remainSize)), e.remainTime && (e.remainTime = "".concat((+e.remainTime).toFixed(1), "年")), e.scale && (e.scale = C(1e4 * e.scale)), e
			}
			var Ot, Pt = "10.0",
				Lt = function(t) {
					$(n, t);
					var e = X(n);

					function n(t) {
						var r;
						return F(this, n), r = e.call(this, t), r.path = "".concat(r.host, "/ifzqgtimg/appstock/app"), r
					}
					return G(n, [{
						key: "ajaxGetWithOrigin",
						value: function(t) {
							return this.ajaxGet("".concat(t, "&app=").concat(this.origin, "&t=").concat((new Date).getTime()))
						}
					}, {
						key: "judgeAdded",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l, f, p, d, h, m = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = m.length > 1 && void 0 !== m[1] ? m[1] : {}, r = e.market, i = e.scode, a = e.app, u = e.openId, s = e.fsKey, l = e.check, f = c(r, i), p = "".concat(this.host, "/newstock/stockapp/zixuangu/stockAdd"), d = "", a && "wzq" !== a ? "xcx" === a || "mp" === a ? d = "".concat(p, "?stocks=").concat(f, "&app=plus&appid=wx9cf8c670ebd68ce4&check=11&openid=").concat(u, "&fskey=").concat(s) : /^(h5|quick)_.*/.test(a) && (d = "".concat(p, "?stocks=").concat(f, "&app=").concat(a, "&check=").concat(l, "&openid=").concat(u, "&fskey=").concat(s)) : d = "".concat(p, "?stocks=").concat(f, "&app=wzq&appid=wx9cf8c670ebd68ce4&check=11&openid=").concat(u, "&fskey=").concat(s), t.prev = 6, t.next = 9, this.ajaxGet(d);
										case 9:
											return h = t.sent, t.abrupt("return", n.needProcess ? Boolean(+h.data[f]) : h);
										case 13:
											return t.prev = 13, t.t0 = t["catch"](6), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 16:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[6, 13]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getQT",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, u, s, v, y, g, w, b, x, E, S = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											if (n = S.length > 1 && void 0 !== S[1] ? S[1] : {}, r = e.market, i = e.scode, u = e.currency, s = e.encode, v = void 0 === s ? "" : s, y = "", y = p(r) ? "r_hk".concat(i) : d(r) ? "t_us".concat(A(i).replace(".", "__")) : T(r) ? "bc".concat(i, "-").concat(u) : c(r, i), "fuCN" !== y) {
												t.next = 6;
												break
											}
											return t.abrupt("return", this.hackFUCN(e, n));
										case 6:
											return g = "".concat(this.qtHost, "/").concat(v, "/?q=").concat(y, "&fmt=json"), t.prev = 7, t.next = 10, this.ajaxGetWithOrigin(g);
										case 10:
											if (b = t.sent, w = b[y], w) {
												t.next = 14;
												break
											}
											throw new Error;
										case 14:
											t.next = 22;
											break;
										case 16:
											t.prev = 16, t.t0 = t["catch"](7), w = new Array(100).fill(""), w[3] = "0.00", w[31] = "0.00", w[32] = "0.00";
										case 22:
											if (t.prev = 22, x = this.getQTAdapter(n.adapterType), !l(r)) {
												t.next = 28;
												break
											}
											E = x.adaptBJ(w), t.next = 60;
											break;
										case 28:
											if (!f(r)) {
												t.next = 33;
												break
											}
											E = x.adaptHS(w), a.set(y, w[61]), t.next = 60;
											break;
										case 33:
											if (!m(r)) {
												t.next = 37;
												break
											}
											E = x.adaptPT(w), t.next = 60;
											break;
										case 37:
											if (!p(r)) {
												t.next = 41;
												break
											}
											E = x.adaptHK(w), t.next = 60;
											break;
										case 41:
											if (!d(r)) {
												t.next = 45;
												break
											}
											E = x.adaptUS(w), t.next = 60;
											break;
										case 45:
											if (!h(r)) {
												t.next = 49;
												break
											}
											E = x.adaptUK(w), t.next = 60;
											break;
										case 49:
											if (!N(r)) {
												t.next = 53;
												break
											}
											E = x.adaptFT(w), t.next = 60;
											break;
										case 53:
											if (!O(r)) {
												t.next = 57;
												break
											}
											E = x.adaptFU(w), t.next = 60;
											break;
										case 57:
											if (!T(r)) {
												t.next = 60;
												break
											}
											return E = x.adaptBC(w), t.abrupt("return", E);
										case 60:
											return t.abrupt("return", "stockinfo" === n.adapterType && n.needProcess ? Nt(E) : E);
										case 63:
											return t.prev = 63, t.t1 = t["catch"](22), t.abrupt("return", this.defaultErrorHandler(t.t1));
										case 66:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[7, 16],
									[22, 63]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getTransDebtQT",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l, f, p, d, h = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = h.length > 1 && void 0 !== h[1] ? h[1] : {}, r = e.market, i = e.scode, a = e.encode, u = void 0 === a ? "" : a, s = "z_".concat(c(r, i)), l = "".concat(this.qtHost, "/").concat(u, "/?q=").concat(s, "&fmt=json"), t.prev = 4, t.next = 7, this.ajaxGetWithOrigin(l);
										case 7:
											return f = t.sent, p = this.getQTAdapter(n.adapterType), d = p.adaptTransDebt(f[s]), t.abrupt("return", "stockinfo" === n.adapterType && n.needProcess ? jt(d) : d);
										case 13:
											return t.prev = 13, t.t0 = t["catch"](4), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 16:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[4, 13]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getMarketState",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, c, u, s, l = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = l.length > 1 && void 0 !== l[1] ? l[1] : {}, r = e.market, i = e.encode, a = void 0 === i ? "" : i, c = O(r) ? "globalCommodityStat" : "marketStat", u = "".concat(this.qtHost, "/").concat(a, "/?q=").concat(c, "&fmt=json"), t.prev = 4, t.next = 7, this.ajaxGetWithOrigin(u);
										case 7:
											return s = t.sent, t.abrupt("return", n.needProcess ? s[c][0] : s);
										case 11:
											return t.prev = 11, t.t0 = t["catch"](4), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 14:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[4, 11]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getPlate",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = l.length > 1 && void 0 !== l[1] ? l[1] : {}, r = e.market, i = e.scode, a = c(r, i), u = "", f(r) ? u = "".concat(this.path, "/stockinfo/plate?code=").concat(a, "&zdf=1") : p(r) ? u = "".concat(this.host, "/ifzqgtimg/stock/corp/hkmoney/plate?symbol=").concat(a) : d(r) && (u = "".concat(this.host, "/ifzqgtimg/appstock/us/introduce/plate?symbol=").concat(a)), t.prev = 5, t.next = 8, this.ajaxGetWithOrigin(u);
										case 8:
											return s = t.sent, t.abrupt("return", n.needProcess ? s.data : s);
										case 12:
											return t.prev = 12, t.t0 = t["catch"](5), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 15:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[5, 12]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getTradeDetail",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, f, p, d, h, m = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = m.length > 1 && void 0 !== m[1] ? m[1] : {}, r = e.market, i = e.scode, a = e.start, u = e.page, s = e.openId, f = void 0 === s ? "" : s, p = c(r, i), d = "", d = l(r) ? "".concat(this.host, "/cgi/cgi-bin/detail/bj?code=").concat(p, "&page=").concat(u) : "".concat(this.host, "/cgi/cgi-bin/detail/all?code=").concat(p, "&start=").concat(a, "&openId=").concat(f), t.prev = 5, t.next = 8, this.ajaxGetWithOrigin(d);
										case 8:
											return h = t.sent, t.abrupt("return", n.needProcess ? kt(h, e) : h);
										case 12:
											return t.prev = 12, t.t0 = t["catch"](5), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 15:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[5, 12]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getBigDeal",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l, f, p, d, h = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = h.length > 1 && void 0 !== h[1] ? h[1] : {}, r = e.market, i = e.scode, a = e.need, u = void 0 === a ? 100 : a, s = e.start, l = void 0 === s ? "" : s, f = c(r, i), p = "".concat(this.host, "/cgi/cgi-bin/yidong/getDadan?code=").concat(f, "&need=").concat(u, "&start=").concat(l, "&_appver=").concat(Pt), t.prev = 4, t.next = 7, this.ajaxGetWithOrigin(p);
										case 7:
											return d = t.sent, t.abrupt("return", n.needProcess ? d.data : d);
										case 11:
											return t.prev = 11, t.t0 = t["catch"](4), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 14:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[4, 11]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getMins",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, v, y, g, w, b, x, E, S = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											if (n = S.length > 1 && void 0 !== S[1] ? S[1] : {}, r = e.market, i = e.scode, a = e.currency, u = e.needIOPV, v = e.openId, y = void 0 === v ? "" : v, g = c(r, i), "fuCN" !== g || !this.fuCNRes) {
												t.next = 5;
												break
											}
											return t.abrupt("return", n.needProcess ? gt(this.fuCNRes, e) : this.fuCNRes);
										case 5:
											if (w = "", !l(r)) {
												t.next = 10;
												break
											}
											w = "".concat(this.path, "/minute/query?code=").concat(g), t.next = 41;
											break;
										case 10:
											if (!f(r) && !m(r)) {
												t.next = 14;
												break
											}
											w = "".concat(this.path, "/minute/query?code=").concat(g, "&cyb=1").concat(u ? "&extData=iopv" : "", "&_appver=").concat(Pt), t.next = 41;
											break;
										case 14:
											if (!p(r)) {
												t.next = 18;
												break
											}
											w = "".concat(this.path, "/HkMinute/query?code=").concat(g), t.next = 41;
											break;
										case 18:
											if (!d(r)) {
												t.next = 22;
												break
											}
											w = "".concat(this.path, "/UsMinute/query?code=").concat(s(g)), t.next = 41;
											break;
										case 22:
											if (!h(r)) {
												t.next = 26;
												break
											}
											w = "".concat(this.host, "/cgi/cgi-bin/Minuteuk/query?code=").concat(g), t.next = 41;
											break;
										case 26:
											if (!N(r)) {
												t.next = 30;
												break
											}
											w = "".concat(this.host, "/cgi/cgi-bin/Minuteftse/query?code=").concat(g), t.next = 41;
											break;
										case 30:
											if (!O(r)) {
												t.next = 34;
												break
											}
											w = "".concat(this.host, "/cgi/cgi-bin/Minutefu/query?code=").concat(g, "&openId=").concat(y), t.next = 41;
											break;
										case 34:
											if (!T(r)) {
												t.next = 41;
												break
											}
											return w = "".concat(this.host, "/cgi-bin/stockinfoquery/fs/app/get"), b = {
												app: this.origin,
												code: g,
												currency: a,
												"fs.type": "d1",
												needDataType: "fs,qt",
												t: (new Date).getTime()
											}, t.next = 39, this.ajaxGet(w, "GET", b, {
												headers: this.getSign(b)
											});
										case 39:
											return x = t.sent, t.abrupt("return", n.needProcess ? gt(x, e) : x);
										case 41:
											return t.prev = 41, t.next = 44, this.ajaxGetWithOrigin(w);
										case 44:
											return E = t.sent, t.abrupt("return", n.needProcess ? gt(E, e) : E);
										case 48:
											return t.prev = 48, t.t0 = t["catch"](41), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 51:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[41, 48]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getAuctionMins",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l, f, p = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = p.length > 1 && void 0 !== p[1] ? p[1] : {}, r = e.market, i = e.scode, a = e.stockType, u = c(r, i), s = "GP-A" === a || x(a) || b(a) ? "sec" : "min", l = "".concat(this.host, "/cgi/cgi-bin/callAuction/get?stockCode=").concat(u, "&dType=").concat(s), t.prev = 5, t.next = 8, this.ajaxGetWithOrigin(l);
										case 8:
											return f = t.sent, t.abrupt("return", n.needProcess ? wt(f, M(M({}, e), {}, {
												dType: s
											})) : f);
										case 12:
											return t.prev = 12, t.t0 = t["catch"](5), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 15:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[5, 12]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getHistoryMins",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, i, a, u, s, l, f, p, d, h = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = h.length > 1 && void 0 !== h[1] ? h[1] : {}, i = e.market, a = e.scode, u = e.date, s = c(i, a), l = "date=".concat(u, "&stockCode=").concat(s), f = r(l).toLowerCase(), p = "".concat(this.host, "/cgi/cgi-bin/generalminute/history/minute?").concat(l, "&sign=").concat(f), t.prev = 6, t.next = 9, this.ajaxGetWithOrigin(p);
										case 9:
											return d = t.sent, t.abrupt("return", n.needProcess ? bt(d, e) : d);
										case 13:
											return t.prev = 13, t.t0 = t["catch"](6), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 16:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[6, 13]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getFmins",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, l, f, p, m, v, y, g = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											if (n = g.length > 1 && void 0 !== g[1] ? g[1] : {}, r = e.market, i = e.scode, a = e.currency, u = e.openId, l = void 0 === u ? "" : u, f = c(r, i), p = "", !d(r)) {
												t.next = 8;
												break
											}
											p = "".concat(this.path, "/dayus/query?code=").concat(s(f)), t.next = 30;
											break;
										case 8:
											if (!h(r)) {
												t.next = 12;
												break
											}
											p = "".concat(this.host, "/cgi/cgi-bin/Dayuk/query?code=").concat(f), t.next = 30;
											break;
										case 12:
											if (!N(r)) {
												t.next = 16;
												break
											}
											p = "".concat(this.host, "/cgi/cgi-bin/Dayftse/query?code=").concat(f), t.next = 30;
											break;
										case 16:
											if (!O(r)) {
												t.next = 20;
												break
											}
											p = "".concat(this.host, "/cgi/cgi-bin/Dayfu/query?code=").concat(f, "&openId=").concat(l), t.next = 30;
											break;
										case 20:
											if (!T(r)) {
												t.next = 29;
												break
											}
											return p = "".concat(this.host, "/cgi-bin/stockinfoquery/fs/app/get"), m = {
												app: this.origin,
												code: f,
												currency: a,
												"fs.type": "d5",
												needDataType: "fs,qt",
												t: (new Date).getTime()
											}, t.next = 25, this.ajaxGet(p, "GET", m, {
												headers: this.getSign(m)
											});
										case 25:
											return v = t.sent, t.abrupt("return", n.needProcess ? xt(v, e) : v);
										case 29:
											p = "".concat(this.path, "/day/query?code=").concat(f, "&_appver=").concat(Pt);
										case 30:
											return t.prev = 30, t.next = 33, this.ajaxGetWithOrigin(p);
										case 33:
											return y = t.sent, t.abrupt("return", n.needProcess ? xt(y, e) : y);
										case 37:
											return t.prev = 37, t.t0 = t["catch"](30), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 40:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[30, 37]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getKline",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i, a, u, s, l, f, p, d, h, m, v, y, g, w, b, x, E, S, k, _, N = arguments;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = N.length > 1 && void 0 !== N[1] ? N[1] : {}, r = e.market, i = e.scode, a = e.currency, u = e.fq, s = e.type, l = e.kline, f = e.end, p = e.added, d = e.openId, h = e.limit, m = void 0 === h ? 370 : h, v = e.opPoints, y = c(r, i), g = s || ["", "day", "week", "month", "season", "year"][l], w = ["", "qfq", "hfq", ""][u], b = "", f && (T(r) ? b = "&endTime=".concat(f.replace(/-/g, "")) : /^m\d/.test(s) ? b = "&endTime=".concat(f) : (x = /-/.test(f) ? f : "".concat(f.slice(0, 4), "-").concat(f.slice(4, 6), "-").concat(f.slice(6, 8)), b = "&toDate=".concat(x))), E = "".concat(a ? "&currency=".concat(a) : "").concat(p ? "&added=true" : "").concat(v ? "&op_points=true" : ""), S = "?code=".concat(y, "&ktype=").concat(g, "&fqtype=").concat(w).concat(b, "&limit=").concat(m, "&openid=").concat(d).concat(E), k = "".concat(this.host, "/cgi/cgi-bin/stockinfoquery/kline/app/get").concat(S), t.prev = 10, t.next = 13, this.ajaxGetWithOrigin(k);
										case 13:
											return _ = t.sent, t.abrupt("return", n.needProcess ? Et(_, e) : _);
										case 17:
											return t.prev = 17, t.t0 = t["catch"](10), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 20:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[10, 17]
								])
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getMinKline",
						value: function() {
							var t = W(o().mark((function t(e, n) {
								var r, i, a, u, s, l, h, v, y;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											if (r = e.market, i = e.scode, a = e.type, u = e.end, s = e.limit, l = void 0 === s ? 320 : s, !T(r)) {
												t.next = 3;
												break
											}
											return t.abrupt("return", this.getKline(e, n));
										case 3:
											return h = c(r, i), v = "", f(r) ? v = "".concat(this.path, "/kline/mkline?param=").concat(h, ",").concat(a, ",").concat(u, ",").concat(l) : m(r) ? v = "".concat(this.host, "/cgi/cgi-bin/kline/getPtMink?param=").concat(h, ",").concat(a, ",").concat(u, ",").concat(l) : p(r) ? v = "".concat(this.host, "/cgi/cgi-bin/kline/getHkMink?param=").concat(h, ",").concat(a, ",").concat(u, ",").concat(l) : d(r) && (v = "".concat(this.host, "/cgi/cgi-bin/kline/getUsMink?param=").concat(h, ",").concat(a, ",").concat(u, ",").concat(l)), t.prev = 6, t.next = 9, this.ajaxGetWithOrigin(v);
										case 9:
											return y = t.sent, t.abrupt("return", n.needProcess ? St(y, e) : y);
										case 13:
											return t.prev = 13, t.t0 = t["catch"](6), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 16:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[6, 13]
								])
							})));

							function e(e, n) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "hackFUCN",
						value: function() {
							var t = W(o().mark((function t(e, n) {
								var r, i, a;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return this.fuCNRes = null, t.prev = 1, t.next = 4, this.getMins(e);
										case 4:
											return this.fuCNRes = t.sent, r = this.fuCNRes.data.fuCN.qt.fuCN, i = this.getQTAdapter(n.adapterType), a = i.adaptFU(r), t.abrupt("return", "stockinfo" === n.adapterType && n.needProcess ? Nt(a) : a);
										case 11:
											return t.prev = 11, t.t0 = t["catch"](1), t.abrupt("return", this.defaultErrorHandler(t.t0));
										case 14:
										case "end":
											return t.stop()
									}
								}), t, this, [
									[1, 11]
								])
							})));

							function e(e, n) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}]), n
				}(ht),
				Tt = function() {
					function t() {
						var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
						F(this, t), H(this, "options", {}), H(this, "adapter", ot), this.options = e;
						var n = this.options.adapterType;
						"string" === typeof n ? "stockinfo" === n && (this.adapter = ct) : "object" === R(n) && (this.adapter = n)
					}
					return G(t, [{
						key: "getTag",
						value: function() {
							var t = this.options,
								e = t.topic,
								n = t.tag,
								r = t.stockList;
							if (Array.isArray(n)) return n;
							if ("quote_qt" === e) {
								if ("simple" === n) return ["3", "31", "32", "45"];
								if ("detail" === n) {
									var o = u(r[0]),
										i = o.market;
									if (f(i)) return ["3", "4", "5", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "38", "39", "40", "43", "44", "45", "46", "47", "48", "49", "51", "52", "53", "56", "57", "58", "59", "62", "63", "64", "67", "68", "70", "72", "74", "77", "78"];
									if (d(i)) return ["3", "4", "5", "7", "8", "30", "31", "32", "33", "34", "36", "37", "38", "39", "40", "41", "43", "44", "45", "47", "48", "49", "51", "52", "54", "55", "60", "64", "65", "66", "67", "68"]
								}
							}
						}
					}, {
						key: "handleData",
						value: function(t) {
							if (/^z_/.test(t.code)) return this.handleTransDebt(t);
							var e = this.options,
								n = e.adapterType,
								r = e.needProcess,
								o = u(t.code),
								i = o.market,
								c = {};
							return f(i) ? c = this.adapter.adaptHS(t.kv) : d(i) && (c = this.adapter.adaptUS(t.kv)), c.websocket = !0, "stockinfo" === n ? (c.secu_info.market = i, c.secu_quote.market = i, c.secu_info.symbol = t.code, c.secu_info.stocktype = a.get(t.code), r ? Nt(c) : c) : c
						}
					}, {
						key: "handleTransDebt",
						value: function(t) {
							var e = this.options,
								n = e.adapterType,
								r = e.needProcess,
								o = this.adapter.adaptTransDebt(t.kv);
							return "stockinfo" === n && r ? jt(o) : o
						}
					}]), t
				}(),
				At = function() {
					function t() {
						var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
						F(this, t), H(this, "options", {}), H(this, "retryCount", 0), H(this, "online", !0), H(this, "pullMode", !1), H(this, "pullTime", 5e3);
						var n = e.host || "proxy.finance.qq.com";
						this.baseUrl = "wss://".concat(n, "/peasy/pgw/push/web"), this.options = e, this.qtHelper = new Tt(this.options)
					}
					return G(t, [{
						key: "close",
						value: function() {
							clearInterval(this.heartInterval), clearInterval(this.ensureInterval), clearInterval(this.pullInterval)
						}
					}, {
						key: "send",
						value: function() {}
					}, {
						key: "sendHeartBeat",
						value: function() {
							var t = this;
							this.heartInterval = setInterval((function() {
								var e = {
									cmdId: 103,
									topic: "",
									topicDataJson: ""
								};
								t.send(JSON.stringify(e))
							}), 55e3)
						}
					}, {
						key: "sendMessage",
						value: function() {
							var t = this.options,
								e = t.topic,
								n = t.stockList,
								r = {
									cmdId: 101,
									topic: e,
									topicDataJson: JSON.stringify({
										subList: [{
											code: n || [],
											tag: this.qtHelper.getTag() || []
										}]
									})
								};
							this.send(JSON.stringify(r))
						}
					}, {
						key: "getMessage",
						value: function(t) {
							if (201 === t.cmdId && (this.ensureData(), "quote_qt" === t.topic)) {
								var e, n = [],
									r = t.topicDataJson.qt,
									o = rt(r);
								try {
									for (o.s(); !(e = o.n()).done;) {
										var i = e.value;
										n.push({
											topic: t.topic,
											symbol: i.code,
											data: this.qtHelper.handleData(i)
										})
									}
								} catch (a) {
									o.e(a)
								} finally {
									o.f()
								}
								this.handleData(n)
							}
						}
					}, {
						key: "ensureData",
						value: function() {
							var t = this;
							clearInterval(this.ensureInterval), this.options.ensure && (this.ensureInterval = setInterval((function() {
								t.pull({
									ensure: !0
								})
							}), 2e4))
						}
					}, {
						key: "changeStockList",
						value: function(t) {
							this.ws && (this.options.stockList = t, this.sendMessage())
						}
					}, {
						key: "changePullTime",
						value: function(t) {
							var e = this;
							this.pullTime = t, this.pullMode && (clearInterval(this.pullInterval), this.pullInterval = setInterval((function() {
								e.pull()
							}), this.pullTime))
						}
					}, {
						key: "handleData",
						value: function() {}
					}, {
						key: "pull",
						value: function() {}
					}]), t
				}(),
				It = function(t) {
					$(n, t);
					var e = X(n);

					function n() {
						var t, r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
						return F(this, n), t = e.call(this, r), t.onNetStateChangeBind = t.onNetStateChange.bind(Y(t)), window.addEventListener("online", t.onNetStateChangeBind), window.addEventListener("offline", t.onNetStateChangeBind), t.open(), t
					}
					return G(n, [{
						key: "getUrl",
						value: function() {
							var t, e = this.options.auth || {}, n = e.appName,
								r = e.openId,
								o = e.token;
							return t = "wzq" === n ? "".concat(this.baseUrl, "?_appName=wzq&appid=wx9cf8c670ebd68ce4&check=11&openid=").concat(r, "&fskey=").concat(o) : "mini" === n ? "".concat(this.baseUrl, "?_appName=mini") : this.baseUrl, t
						}
					}, {
						key: "open",
						value: function() {
							var t = this;
							this.ws || (this.ws = new WebSocket(this.getUrl()), this.ws.onopen = function() {
								var e, n;
								t.pullMode = !1, (null === (e = t.ws) || void 0 === e ? void 0 : e.readyState) === (null === (n = t.ws) || void 0 === n ? void 0 : n.OPEN) && (t.sendMessage(), t.sendHeartBeat(), t.ensureData())
							}, this.ws.onmessage = function(e) {
								var n = JSON.parse(e.data);
								n.topicDataJson = JSON.parse(n.topicDataJson), t.getMessage(n)
							}, this.ws.onclose = function(e) {
								t.ws = null, 1e3 !== e.code && t.online && (clearInterval(t.heartInterval), t.retryCount += 1, t.retryCount <= 3 ? t.open() : (clearInterval(t.ensureInterval), clearInterval(t.pullInterval), t.pullMode = !0, t.pullInterval = setInterval((function() {
									t.pull()
								}), t.pullTime)))
							})
						}
					}, {
						key: "close",
						value: function(t) {
							var e;
							t || (window.removeEventListener("online", this.onNetStateChangeBind), window.removeEventListener("offline", this.onNetStateChangeBind)), tt(J(n.prototype), "close", this).call(this), null === (e = this.ws) || void 0 === e || e.close(1e3), this.ws = null
						}
					}, {
						key: "send",
						value: function(t) {
							var e, n, r;
							(null === (e = this.ws) || void 0 === e ? void 0 : e.readyState) === (null === (n = this.ws) || void 0 === n ? void 0 : n.OPEN) && (null === (r = this.ws) || void 0 === r || r.send(t))
						}
					}, {
						key: "onNetStateChange",
						value: function(t) {
							this.online = "online" === t.type, this.online ? (this.pull({
								online: !0
							}), this.open()) : this.close(!0)
						}
					}]), n
				}(At),
				Ct = function(t) {
					$(n, t);
					var e = X(n);

					function n() {
						var t, r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, o = arguments.length > 1 ? arguments[1] : void 0;
						return F(this, n), t = e.call(this, r), t.open(o), t
					}
					return G(n, [{
						key: "getUrl",
						value: function() {
							var t = this.options.auth || {}, e = t.openId,
								n = t.token;
							return "".concat(this.baseUrl, "?_appName=xcx&appid=wx9cf8c670ebd68ce4&check=11&openid=").concat(e, "&fskey=").concat(n)
						}
					}, {
						key: "open",
						value: function(t) {
							var e = this;
							this.ws || (this.ws = t({
								url: this.getUrl()
							}), this.ws.onOpen((function() {
								e.pullMode = !1, e.sendMessage(), e.sendHeartBeat(), e.ensureData()
							})), this.ws.onMessage((function(t) {
								var n = JSON.parse(t.data);
								n.topicDataJson = JSON.parse(n.topicDataJson), e.getMessage(n)
							})), this.ws.onClose((function(n) {
								e.ws = null, 1e3 !== n.code && (clearInterval(e.heartInterval), e.retryCount += 1, e.retryCount <= 3 ? e.open(t) : (clearInterval(e.ensureInterval), clearInterval(e.pullInterval), e.pullMode = !0, e.pullInterval = setInterval((function() {
									e.pull()
								}), e.pullTime)))
							})))
						}
					}, {
						key: "close",
						value: function() {
							var t;
							tt(J(n.prototype), "close", this).call(this), null === (t = this.ws) || void 0 === t || t.close({
								code: 1e3
							}), this.ws = null
						}
					}, {
						key: "send",
						value: function(t) {
							var e;
							null === (e = this.ws) || void 0 === e || e.send({
								data: t
							})
						}
					}]), n
				}(At);

			function Ut(t) {
				var e, n, r, o = [],
					i = [],
					a = [],
					c = [],
					u = [],
					s = [],
					l = [],
					f = {};
				if (t && t.index) {
					o = Mt(t.index);
					var p = t.index.find((function(t) {
						return "sh000001" === t.code
					})),
						d = t.index.find((function(t) {
							return "sz399001" === t.code
						}));
					p && d && (e = +p.upn + +d.upn, n = +p.downn + +d.downn, r = +p.equaln + +d.equaln)
				}
				return null !== t && void 0 !== t && t.board && (i = Rt(t.board)), t && t.industry && (a = qt(t.industry)), t && t.industryCard && (c = Wt(t.industryCard)), t && t.concept && (u = qt(t.concept)), t && t.etf && (s = Gt(t.etf)), t && t.rankings && (l = Ht(t.rankings)), t && t.rankingV2 && (f = $t(t.rankingV2)), {
					mlist: o,
					raiseNum: e,
					fallNum: n,
					holdNum: r,
					hotPlate: i,
					industry: a,
					industryCard: c,
					concept: u,
					hsEtf: s,
					stock: l,
					stockPage: f
				}
			}
			function Dt(t) {
				var e = [],
					n = [],
					r = [];
				return t && t.index && (e = Mt(t.index)), t && t.industry && (n = qt(t.industry)), t && t.rankings && (r = Ht(t.rankings)), {
					mlist: e,
					industry: n,
					stock: r
				}
			}
			function zt(t) {
				var e = [],
					n = [],
					r = [],
					o = [];
				return t && t.index && (e = Mt(t.index)), t && t.etfBoard && (n = qt(t.etfBoard, "etfBoard")), t && t.industry && (r = qt(t.industry)), t && t.rankings && (o = Ht(t.rankings)), {
					mlist: e,
					etf: n,
					industry: r,
					stock: o
				}
			}
			function Mt(t) {
				var e = [];
				return t.map((function(t) {
					var n = t.code,
						r = t.name,
						o = t.last,
						i = t.ad,
						a = t.adp,
						c = u(n),
						s = c.market,
						l = c.scode;
					e.push({
						m: s,
						c: l,
						n: r,
						price: o,
						zde: +i > 0 ? "+".concat(i) : i,
						zdf: +a > 0 ? "+".concat(a) : a
					})
				})), e
			}
			function Rt(t) {
				var e = [],
					n = ["领涨板块", "领跌板块", "资金净流入最多"];
				return t.map((function(t, r) {
					var o = t.code,
						i = t.name,
						a = t.adp;
					e.push({
						code: o,
						name: i,
						zdf: +a > 0 ? "+".concat(a) : a,
						desc: n[r]
					})
				})), e
			}
			function qt(t, e) {
				var n = [];
				return t.map((function(t) {
					var r = t.code,
						o = t.name,
						i = t.adp,
						a = t.leadStock,
						c = t.leadStocks,
						u = ("h5Industry" === e ? c && c[0] : a) || {};
					n.push({
						code: "etfBoard" === e ? r.slice(2) : "hs" !== Ot ? r.slice(4) : r,
						name: o,
						zdf: "etfBoard" === e ? "" : +i > 0 ? "+".concat(i) : i,
						fn: u.name,
						fzjcj: u.last,
						fzdf: +u.adp > 0 ? "+".concat(u.adp) : u.adp
					})
				})), n
			}
			function Wt(t) {
				var e = {
					today: {},
					twentyDay: [],
					year: []
				}, n = t.advance,
					r = t.advance20day,
					o = t.advanceThisYear;
				return e.today.firstPlate = Ft(n[0]), e.today.followPlate = qt(n.slice(1, 4), "h5Industry"), e.twentyDay = Bt(r, "twentyDay"), e.year = Bt(o, "year"), e
			}
			function Ft(t) {
				var e = t.code,
					n = t.name,
					r = t.adp,
					o = t.tags,
					i = t.leadStocks,
					a = [];
				i.map((function(t) {
					var e = t.code,
						n = t.name,
						r = t.adp;
					a.push({
						code: e,
						name: n,
						zdf: +r > 0 ? "+".concat(r) : r
					})
				}));
				var c = {
					code: e,
					name: n,
					zdf: +r > 0 ? "+".concat(r) : r,
					tags: o,
					leadStock: a
				};
				return c
			}
			function Bt(t, e) {
				var n = [];
				return t.map((function(t) {
					var r = t.name,
						o = t.code,
						i = t.adp20,
						a = t.adpy;
					n.push({
						name: r,
						code: o,
						zdf: "twentyDay" === e ? i : a
					})
				})), n
			}
			function Gt(t) {
				var e = [];
				return t.map((function(t) {
					var n = t.code,
						r = t.name,
						o = t.adp,
						i = t.adpy,
						a = t.last,
						c = t.scale,
						u = t.labels;
					c = c ? C(c) : "--", e.push({
						code: n,
						name: r,
						zdf: +o > 0 ? "+".concat(o) : o,
						zdfy: +i > 0 ? "+".concat(i) : i,
						zxj: a,
						scale: c,
						labels: u
					})
				})), e
			}
			function Ht(t) {
				return Object.keys(t).map((function(e) {
					Object.keys(t[e]).map((function(n) {
						var r = [];
						"rank" === n && t[e][n] ? r = t[e][n].data : "rank" !== n && (r = t[e][n]);
						var o = [];
						r.map((function(t) {
							var e = t.code,
								n = t.name,
								r = t.last,
								i = t.type,
								a = t.ad,
								c = t.adp,
								s = t.amount,
								l = t.exchange,
								p = t.ads,
								d = t.amplitude,
								h = t.volratio,
								m = t.comment,
								v = u(e),
								y = v.market,
								g = v.scode;
							o.push({
								market: y,
								code: g,
								name: n,
								zjcj: f(y) ? (+r).toFixed(2) : r,
								stock_type: i,
								zde: a > 0 ? "+".concat(a) : a,
								zdf: c > 0 ? "+".concat(c) : c,
								cjje: s,
								hsl: l,
								zs: p,
								zf: d,
								lb: h,
								delay: "0",
								comment: m && m[0] ? m[0] : ""
							})
						})), t[e][n] = o
					}))
				})), t
			}
			function $t(t) {
				if (t && t.data) {
					var e = t.data,
						n = [];
					return Array.isArray(e) && e.map((function(t) {
						var e = t.code,
							r = t.name,
							o = t.last,
							i = t.type,
							a = t.ad,
							c = t.adp,
							s = t.amount,
							l = t.exchange,
							p = t.ads,
							d = t.amplitude,
							h = t.volratio,
							m = t.comment,
							v = t.labels,
							y = t.netmainin,
							g = u(e),
							w = g.market,
							b = g.scode;
						n.push({
							market: w,
							code: b,
							name: r,
							zjcj: f(w) ? (+o).toFixed(2) : o,
							stock_type: i,
							zde: a > 0 ? "+".concat(a) : a,
							zdf: c > 0 ? "+".concat(c) : c,
							cjje: s,
							hsl: l,
							zs: p,
							zf: d,
							lb: h,
							delay: "0",
							comment: m && m[0] ? m[0] : "",
							labels: v,
							netmainin: y
						})
					})), {
						data: n,
						offset: t.offset,
						total: t.total
					}
				}
			}
			function Jt(t, e) {
				var n = null;
				return Ot = t, "hs" === t ? n = Ut(e) : "hk" === t ? n = Dt(e) : "us" === t && (n = zt(e)), n
			}
			var Qt = function(t) {
				$(n, t);
				var e = X(n);

				function n() {
					return F(this, n), e.apply(this, arguments)
				}
				return G(n, [{
					key: "getMarketData",
					value: function() {
						var t = W(o().mark((function t() {
							var e, n, r, i, a, c, u, s, l, f, p = arguments;
							return o().wrap((function(t) {
								while (1) switch (t.prev = t.next) {
									case 0:
										return e = p.length > 0 && void 0 !== p[0] ? p[0] : {}, n = e.market, r = e.rankOnly, i = e.offset, a = e.count, c = e.rank_type, u = n.toLowerCase(), s = "".concat(this.host, "/cgi/cgi-bin/stockinfoquery/quote/app/get?market=").concat(u), l = null, "HS" !== n || this.oem || (s = c ? "".concat(s, "&comment=true&appid=zxg&offset=").concat(i, "&count=").concat(a, "&rank_type=").concat(c) : "".concat(s, "&comment=true&appid=zxg")), r && (s = "".concat(s, "&rankOnly=true")), t.next = 9, this.ajaxGet("".concat(s, "&app=").concat(this.origin));
									case 9:
										return f = t.sent, f && 0 === f.code && f.data && (l = Jt(u, f.data)), t.abrupt("return", l);
									case 12:
									case "end":
										return t.stop()
								}
							}), t, this)
						})));

						function e() {
							return t.apply(this, arguments)
						}
						return e
					}()
				}]), n
			}(ht),
				Zt = function(t) {
					$(n, t);
					var e = X(n);

					function n() {
						return F(this, n), e.apply(this, arguments)
					}
					return G(n, [{
						key: "batchUpdateStock",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = "".concat(this.host, "/newstock/stockapp/Updstock/operseq?app=").concat(this.origin), t.next = 3, this.ajaxGet(M({
												url: n,
												method: "post"
											}, e));
										case 3:
											return r = t.sent, t.abrupt("return", r);
										case 5:
										case "end":
											return t.stop()
									}
								}), t, this)
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "queryUserStock",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r, i;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = "".concat(this.host, "/newstock/stockapp/zixuangu/stocklist?app=").concat(this.origin), t.next = 3, this.ajaxGet({
												url: n,
												params: e
											});
										case 3:
											return r = t.sent, i = this.formatList(r), t.abrupt("return", i);
										case 6:
										case "end":
											return t.stop()
									}
								}), t, this)
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}, {
						key: "getMarket",
						value: function(t) {
							var e = {
								sz: "0",
								sh: "1",
								hk: "2",
								us: "3",
								pt: "p",
								bj: "bj"
							};
							return e[t] || t
						}
					}, {
						key: "mappingStock",
						value: function(t) {
							var e = t.qt,
								n = void 0 === e ? {} : e,
								r = t.market,
								o = t.star,
								i = n.name,
								a = n.isdelay,
								c = n.type,
								u = n.symbol,
								s = n.zd,
								l = n.zdf,
								f = n.zxj,
								p = n.sz,
								d = n.wzq_cls,
								h = n.wzq_usable,
								m = n.state,
								v = n.jnzdf,
								y = this.getMarket(r);
							return {
								name: i,
								star: o,
								zde: +s > 0 ? "+".concat(s) : s,
								zsz: p,
								zdf: +l > 0 ? "+".concat(l) : l,
								zjcj: f,
								cls: d,
								scode: 0 === (null === u || void 0 === u ? void 0 : u.indexOf(".")) ? u.substr(1) : u,
								delay: a,
								type: y,
								stock_type: c,
								usable: h,
								susp_flag: "S" === m,
								status: m || "0",
								chooseSymbol: t.symbol,
								jnzdf: +v > 0 ? "+".concat(v) : v
							}
						}
					}, {
						key: "formatList",
						value: function(t) {
							var e = this,
								n = t.data,
								r = n.newUser,
								o = n.groups,
								i = n.grouplist,
								a = n.wzq_recommend,
								c = void 0 === a ? [] : a,
								u = i.map((function(t) {
									var n = t.groupinfo,
										r = t.stocklist;
									return {
										groupinfo: n,
										stocklist: r.map((function(t) {
											return e.mappingStock(t)
										}))
									}
								})),
								s = c.map((function(t) {
									return e.mappingStock(t)
								}));
							return {
								retcode: t.code,
								retmsg: t.msg,
								groups: o,
								new_user: r,
								grouplist: u,
								recommends: s
							}
						}
					}, {
						key: "queryStockGroups",
						value: function() {
							var t = W(o().mark((function t(e) {
								var n, r;
								return o().wrap((function(t) {
									while (1) switch (t.prev = t.next) {
										case 0:
											return n = "".concat(this.host, "/newstock/stockapp/zixuangu/stockGroups?app=").concat(this.origin), t.next = 3, this.ajaxGet(M({
												url: n
											}, e));
										case 3:
											return r = t.sent, t.abrupt("return", r);
										case 5:
										case "end":
											return t.stop()
									}
								}), t, this)
							})));

							function e(e) {
								return t.apply(this, arguments)
							}
							return e
						}()
					}]), n
				}(ht),
				Yt = Lt,
				Vt = D,
				Xt = It,
				Kt = Ct,
				te = Qt,
				ee = Zt
		},
		16212: function(t, e) {
			"use strict";
			/*! js-cookie v3.0.1 | MIT */
			function n(t) {
				for (var e = 1; e < arguments.length; e++) {
					var n = arguments[e];
					for (var r in n) t[r] = n[r]
				}
				return t
			}
			var r = {
				read: function(t) {
					return '"' === t[0] && (t = t.slice(1, -1)), t.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent)
				},
				write: function(t) {
					return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent)
				}
			};

			function o(t, e) {
				function r(r, o, i) {
					if ("undefined" !== typeof document) {
						i = n({}, e, i), "number" === typeof i.expires && (i.expires = new Date(Date.now() + 864e5 * i.expires)), i.expires && (i.expires = i.expires.toUTCString()), r = encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
						var a = "";
						for (var c in i) i[c] && (a += "; " + c, !0 !== i[c] && (a += "=" + i[c].split(";")[0]));
						return document.cookie = r + "=" + t.write(o, r) + a
					}
				}
				function i(e) {
					if ("undefined" !== typeof document && (!arguments.length || e)) {
						for (var n = document.cookie ? document.cookie.split("; ") : [], r = {}, o = 0; o < n.length; o++) {
							var i = n[o].split("="),
								a = i.slice(1).join("=");
							try {
								var c = decodeURIComponent(i[0]);
								if (r[c] = t.read(a, c), e === c) break
							} catch (u) {}
						}
						return e ? r[e] : r
					}
				}
				return Object.create({
					set: r,
					get: i,
					remove: function(t, e) {
						r(t, "", n({}, e, {
							expires: -1
						}))
					},
					withAttributes: function(t) {
						return o(this.converter, n({}, this.attributes, t))
					},
					withConverter: function(t) {
						return o(n({}, this.converter, t), this.attributes)
					}
				}, {
					attributes: {
						value: Object.freeze(e)
					},
					converter: {
						value: Object.freeze(t)
					}
				})
			}
			var i = o(r, {
				path: "/"
			});
			e.Z = i
		},
		70398: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return u
				}
			});
			var r = n(94545),
				o = n(59700);

			function i(t, e) {
				return function(n, r) {
					if (null == n) return n;
					if (!(0, o.Z)(n)) return t(n, r);
					var i = n.length,
						a = e ? i : -1,
						c = Object(n);
					while (e ? a-- : ++a < i) if (!1 === r(c[a], a, c)) break;
					return n
				}
			}
			var a = i,
				c = a(r.Z),
				u = c
		},
		87349: function(t, e) {
			"use strict";

			function n(t, e, n, r) {
				var o = t.length,
					i = n + (r ? 1 : -1);
				while (r ? i-- : ++i < o) if (e(t[i], i, t)) return i;
				return -1
			}
			e.Z = n
		},
		94545: function(t, e, n) {
			"use strict";

			function r(t) {
				return function(e, n, r) {
					var o = -1,
						i = Object(e),
						a = r(e),
						c = a.length;
					while (c--) {
						var u = a[t ? c : ++o];
						if (!1 === n(i[u], u, i)) break
					}
					return e
				}
			}
			n.d(e, {
				Z: function() {
					return s
				}
			});
			var o = r,
				i = o(),
				a = i,
				c = n(88068);

			function u(t, e) {
				return t && a(t, e, c.Z)
			}
			var s = u
		},
		38357: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return s
				}
			});
			var r = n(87349);

			function o(t) {
				return t !== t
			}
			var i = o;

			function a(t, e, n) {
				var r = n - 1,
					o = t.length;
				while (++r < o) if (t[r] === e) return r;
				return -1
			}
			var c = a;

			function u(t, e, n) {
				return e === e ? c(t, e, n) : (0, r.Z)(t, i, n)
			}
			var s = u
		},
		57952: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return u
				}
			});
			var r = /\s/;

			function o(t) {
				var e = t.length;
				while (e-- && r.test(t.charAt(e)));
				return e
			}
			var i = o,
				a = /^\s+/;

			function c(t) {
				return t ? t.slice(0, i(t) + 1).replace(a, "") : t
			}
			var u = c
		},
		80324: function(t, e, n) {
			"use strict";
			var r = n(38357),
				o = n(43011),
				i = Math.max;

			function a(t, e, n) {
				var a = null == t ? 0 : t.length;
				if (!a) return -1;
				var c = null == n ? 0 : (0, o.Z)(n);
				return c < 0 && (c = i(a + c, 0)), (0, r.Z)(t, e, c)
			}
			e.Z = a
		},
		40566: function(t, e) {
			"use strict";
			var n = Array.prototype,
				r = n.join;

			function o(t, e) {
				return null == t ? "" : r.call(t, e)
			}
			e.Z = o
		},
		10617: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return f
				}
			});
			var r = n(14450),
				o = n(91617),
				i = n(70398),
				a = n(59700);

			function c(t, e) {
				var n = -1,
					r = (0, a.Z)(t) ? Array(t.length) : [];
				return (0, i.Z)(t, (function(t, o, i) {
					r[++n] = e(t, o, i)
				})), r
			}
			var u = c,
				s = n(19115);

			function l(t, e) {
				var n = (0, s.Z)(t) ? r.Z : u;
				return n(t, (0, o.Z)(e, 3))
			}
			var f = l
		},
		43011: function(t, e, n) {
			"use strict";
			n.d(e, {
				Z: function() {
					return s
				}
			});
			var r = n(25280),
				o = 1 / 0,
				i = 17976931348623157e292;

			function a(t) {
				if (!t) return 0 === t ? t : 0;
				if (t = (0, r.Z)(t), t === o || t === -o) {
					var e = t < 0 ? -1 : 1;
					return e * i
				}
				return t === t ? t : 0
			}
			var c = a;

			function u(t) {
				var e = c(t),
					n = e % 1;
				return e === e ? n ? e - n : e : 0
			}
			var s = u
		},
		25280: function(t, e, n) {
			"use strict";
			var r = n(57952),
				o = n(3422),
				i = n(97496),
				a = NaN,
				c = /^[-+]0x[0-9a-f]+$/i,
				u = /^0b[01]+$/i,
				s = /^0o[0-7]+$/i,
				l = parseInt;

			function f(t) {
				if ("number" == typeof t) return t;
				if ((0, i.Z)(t)) return a;
				if ((0, o.Z)(t)) {
					var e = "function" == typeof t.valueOf ? t.valueOf() : t;
					t = (0, o.Z)(e) ? e + "" : e
				}
				if ("string" != typeof t) return 0 === t ? t : +t;
				t = (0, r.Z)(t);
				var n = u.test(t);
				return n || s.test(t) ? l(t.slice(2), n ? 2 : 8) : c.test(t) ? a : +t
			}
			e.Z = f
		}
	}]);
//# sourceMappingURL=5355.7679a84f.js.map