#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中信建投交易示例脚本

展示如何使用cookies直接调用trade.py进行买入、卖出和撤单操作，无需登录
"""

import json
import time
import logging
import requests
import httpx
import asyncio
from typing import Dict, Any
import urllib3

from citic_trader.trade import TradeClient
from citic_trader.async_trade import AsyncTradeClient
from citic_trader.utils import setup_logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """
    从文件加载cookies
    
    Args:
        cookies_file: cookies文件路径，JSON格式
        
    Returns:
        Dict: cookies字典
    """
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载cookies失败: {e}")
        return {}

async def setup_async_session(cookies: Dict[str, str]) -> httpx.AsyncClient:
    """
    设置异步会话，添加cookies
    
    Args:
        cookies: cookies字典
        
    Returns:
        httpx.AsyncClient: 异步会话对象
    """
    async_session = httpx.AsyncClient(verify=False)
    
    # 添加cookies到会话
    for key, value in cookies.items():
        async_session.cookies.set(key, value)
    
    # 添加微信浏览器标识
    async_session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',
        'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html'
    })
    
    return async_session

def setup_user_info() -> Dict[str, Any]:
    """
    设置用户信息
    
    Returns:
        Dict: 用户信息字典
    """
    # 这里需要填写您的账户信息
    return {
        "account_id": "您的账户ID",  # 请替换为实际账户ID
        "stockholder_code": "**********",  # 请替换为实际股东代码
        "psw_session": "您的密码会话",  # 通常从cookies中获取
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def speed_test_trade(trade_client: TradeClient, stock_code: str, price: float, volume: int, stock_info: Dict[str, Any], num_trades: int = 10, interval: int = 1, logger: logging.Logger = None):
    """
    对交易下单进行测速。

    Args:
        trade_client: 交易客户端实例。
        stock_code: 股票代码。
        price: 交易价格。
        volume: 交易数量。
        stock_info: 股票信息字典。
        num_trades: 交易次数。
        interval: 每单之间的间隔时间（秒）。
        logger: 日志记录器。
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    logger.info(f"\n===== 开始交易测速 (共 {num_trades} 单，每单间隔 {interval} 秒) =====")

    total_time = 0
    successful_trades = 0

    for i in range(num_trades):
        start_time = time.time()
        logger.info(f"正在执行第 {i+1}/{num_trades} 单买入: {stock_code}, 价格: {price}, 数量: {volume}")
        buy_result = trade_client.buy(stock_code, price, volume, stock_info)
        end_time = time.time()
        elapsed_time = end_time - start_time

        logger.info(f"第 {i+1} 单买入结果: {buy_result}. 耗时: {elapsed_time:.4f} 秒")

        
        successful_trades += 1
        total_time += elapsed_time

        
        if i < num_trades - 1:
            time.sleep(interval)

    avg_time = total_time / successful_trades if successful_trades > 0 else 0
    logger.info(f"\n===== 交易测速完成 =====")
    logger.info(f"总交易单数: {num_trades}")
    logger.info(f"成功交易单数: {successful_trades}")
    logger.info(f"总耗时: {total_time:.4f} 秒")
    logger.info(f"平均每单耗时: {avg_time:.4f} 秒 (仅计算成功交易)")

async def speed_test_trade_async(trade_client: AsyncTradeClient, stock_code: str, price: float, volume: int, stock_info: Dict[str, Any], num_trades: int = 10, interval: int = 1, logger: logging.Logger = None):
    """
    对异步交易下单进行测速。

    Args:
        trade_client: 异步交易客户端实例。
        stock_code: 股票代码。
        price: 交易价格。
        volume: 交易数量。
        stock_info: 股票信息字典。
        num_trades: 交易次数。
        interval: 每单之间的间隔时间（秒）。
        logger: 日志记录器。
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    logger.info(f"\n===== 开始异步交易测速 (共 {num_trades} 单，每单间隔 {interval} 秒) =====")

    total_time = 0
    successful_trades = 0

    for i in range(num_trades):
        start_time = time.time()
        logger.info(f"正在执行第 {i+1}/{num_trades} 单买入: {stock_code}, 价格: {price}, 数量: {volume}")
        buy_result = await trade_client.buy_async(stock_code, price, volume, stock_info)
        end_time = time.time()
        elapsed_time = end_time - start_time

        logger.info(f"第 {i+1} 单买入结果: {buy_result}. 耗时: {elapsed_time:.4f} 秒")

        successful_trades += 1
        total_time += elapsed_time
        
        if i < num_trades - 1:
            await asyncio.sleep(interval)

    avg_time = total_time / successful_trades if successful_trades > 0 else 0
    logger.info(f"\n===== 异步交易测速完成 =====")
    logger.info(f"总交易单数: {num_trades}")
    logger.info(f"成功交易单数: {successful_trades}")
    logger.info(f"总耗时: {total_time:.4f} 秒")
    logger.info(f"平均每单耗时: {avg_time:.4f} 秒 (仅计算成功交易)")

async def async_main():
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    logger.info("开始执行异步交易示例")
    
    # 加载cookies (请替换为您的cookies文件路径)
    cookies_file = "cookies.json"
    cookies = load_cookies(cookies_file)

    if not cookies:
        logger.error("未找到有效的cookies，请先保存cookies到文件")
        return
    
    # 设置用户信息
    user_info = setup_user_info()

    # --- 同步客户端设置与测速 ---
    logger.info("正在初始化同步交易客户端...")
    trade_session = requests.Session()
    for key, value in cookies.items():
        trade_session.cookies.set(key, value)
    trade_session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',
        'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html'
    })
    sync_trade_client = TradeClient(trade_session, user_info, logger)
    logger.info("同步交易客户端创建成功")

    # 获取股票信息（同步）
    print(f"\n===== 获取股票信息示例 (同步) =====")
    stock_code_to_test = "518880"
    print(f"正在获取股票信息: {stock_code_to_test}")
    sync_stock_info_result = sync_trade_client.get_stock_info(stock_code_to_test) # 假设TradeClient有get_stock_info方法
    if sync_stock_info_result.get("status") == "success":
        stock_name = sync_stock_info_result["data"].get("name", "")
        stock_price = sync_stock_info_result["data"].get("price", "0")
        print(f"股票名称: {stock_name}, 当前价格: {stock_price}")
    else:
        logger.error(f"无法获取股票 {stock_code_to_test} 的信息（同步），请检查！")
        return

    # 同步交易测速
    num_trades = 5
    interval = 0.1
    speed_test_trade(sync_trade_client, stock_code_to_test, 7.500, 100, sync_stock_info_result, num_trades=num_trades, interval=interval, logger=logger)

    # 保存同步会话的cookies
    with open(cookies_file, 'w', encoding='utf-8') as f:
        cookies_to_save = {}
        for cookie in trade_session.cookies:
            cookies_to_save[cookie.name] = cookie.value
        json.dump(cookies_to_save, f, indent=2)
    logger.info("同步会话的Cookies已成功保存到cookies.json文件")

    # --- 异步客户端设置与测速 ---
    # 重新加载cookies，确保同步操作带来的任何更新都被捕捉到
    cookies_reloaded = load_cookies(cookies_file)
    if not cookies_reloaded:
        logger.error("重新加载cookies失败，无法初始化异步客户端")
        return

    logger.info("正在初始化异步交易客户端...")
    async with await setup_async_session(cookies_reloaded) as async_session:
        async_trade_client = AsyncTradeClient(async_session, user_info, logger)
        await async_trade_client.initialize()
        logger.info("异步交易客户端创建成功")

        # 获取股票信息（异步）
        print(f"\n===== 获取股票信息示例 (异步) =====")
        print(f"正在获取股票信息: {stock_code_to_test}")
        async_stock_info_result = await async_trade_client.get_stock_info_async(stock_code_to_test)
        if async_stock_info_result.get("status") == "success":
            stock_name = async_stock_info_result["data"].get("name", "")
            stock_price = async_stock_info_result["data"].get("price", "0")
            print(f"股票名称: {stock_name}, 当前价格: {stock_price}")
        else:
            logger.error(f"无法获取股票 {stock_code_to_test} 的信息（异步），请检查！")
            return

        # 异步交易测速
        await speed_test_trade_async(async_trade_client, stock_code_to_test, 7.500, 100, async_stock_info_result, num_trades=num_trades, interval=interval, logger=logger)

    logger.info("所有交易示例执行完毕")
    # 将session的cookies保存到cookies.json文件
    cookies_to_save = {}
    for cookie in async_session.cookies.jar:
        cookies_to_save[cookie.name] = cookie.value
    with open('cookies.json', 'w', encoding='utf-8') as f:
        json.dump(cookies_to_save, f, indent=2)
    logger.info("Cookies已成功保存到cookies.json文件")


if __name__ == "__main__":
    asyncio.run(async_main()) 