#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证模块测试脚本

用于测试中信建投认证模块的登录和会话管理功能
"""

import os
import sys
import logging
import json
import getpass
from citic_trader.utils import setup_logger
from citic_trader.auth import AuthClient

# 设置日志
logger = setup_logger(name="auth_test", log_to_console=True, log_level=logging.INFO)

def test_login(username=None, password=None):
    """
    测试登录功能
    
    Args:
        username: 用户名（资金账号）
        password: 密码
    """
    logger.info("开始测试认证模块")
    
    # 如果未提供用户名和密码，从控制台请求输入
    if not username:
        username = input("请输入资金账号：")
    if not password:
        password = getpass.getpass("请输入密码：")
    
    # 创建认证客户端
    auth_client = AuthClient(logger)
    
    # 测试登录
    logger.info(f"正在使用账号 {username} 尝试登录...")
    session, user_info = auth_client.login(username, password)
    
    if session and user_info:
        logger.info("登录成功！")
        logger.info(f"账户信息: {json.dumps(user_info, ensure_ascii=False, indent=2)}")
        
        # 测试登出
        logger.info("正在测试登出功能...")
        logout_success = auth_client.logout(session)
        if logout_success:
            logger.info("登出成功！")
        else:
            logger.error("登出失败！")
        
        return True
    else:
        logger.error("登录失败！")
        return False

def test_cookie_persistence():
    """
    测试cookie持久化和会话恢复功能
    """
    logger.info("开始测试cookie持久化和会话恢复")
    
    # 创建认证客户端
    auth_client = AuthClient(logger)
    
    # 检查cookies文件是否存在
    cookies_file = auth_client.cookies_file
    if os.path.exists(cookies_file):
        logger.info(f"发现cookies文件: {cookies_file}")
        
        # 尝试使用cookies进行自动登录
        logger.info("尝试使用cookies进行自动登录...")
        # 这里我们需要传递一个假的用户名和密码，但是认证客户端应该优先使用cookies
        session, user_info = auth_client.login("test", "test")
        
        if session and user_info:
            logger.info("使用cookies自动登录成功！")
            logger.info(f"账户信息: {json.dumps(user_info, ensure_ascii=False, indent=2)}")
            
            # 测试登出
            logger.info("正在测试登出功能...")
            logout_success = auth_client.logout(session)
            if logout_success:
                logger.info("登出成功！")
            else:
                logger.error("登出失败！")
            
            return True
        else:
            logger.warning("使用cookies自动登录失败，cookies可能已过期")
            return False
    else:
        logger.warning(f"未找到cookies文件: {cookies_file}，需要先执行正常登录")
        return False

if __name__ == "__main__":
    # 根据命令行参数执行测试
    if len(sys.argv) > 1:
        if sys.argv[1] == "cookie":
            test_cookie_persistence()
        elif sys.argv[1] == "login" and len(sys.argv) >= 4:
            test_login(sys.argv[2], sys.argv[3])
        else:
            print("用法: python test_auth.py [login username password | cookie]")
    else:
        # 默认运行完整测试
        if not test_cookie_persistence():
            print("\n")
            print("=" * 50)
            print("未能使用cookies自动登录，请输入账号密码进行测试")
            print("=" * 50)
            test_login() 