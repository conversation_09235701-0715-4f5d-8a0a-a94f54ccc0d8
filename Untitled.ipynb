{"cells": [{"cell_type": "code", "execution_count": 1, "id": "daea9005", "metadata": {"ExecuteTime": {"end_time": "2025-06-10T02:49:13.910836Z", "start_time": "2025-06-10T02:49:12.961836Z"}}, "outputs": [], "source": ["import requests\n", "import time\n", "import numpy"]}, {"cell_type": "code", "execution_count": 2, "id": "a03a0798", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T06:25:34.490808Z", "start_time": "2025-06-03T06:25:34.478807Z"}}, "outputs": [{"data": {"text/plain": ["1752221434828"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["int(time.time()*1000)"]}, {"cell_type": "code", "execution_count": 3, "id": "4250c5bf", "metadata": {"ExecuteTime": {"end_time": "2025-06-10T04:59:32.235603Z", "start_time": "2025-06-10T04:59:29.564581Z"}}, "outputs": [], "source": ["cookies = {\n", "  \"h_delisted\": \"0|1751332792\",\n", "  \"h_dsxpl\": \"1|20250711\",\n", "  \"h_hrepo\": \"0\",\n", "  \"h_pg\": \"0\",\n", "  \"h_spec\": \"0\",\n", "  \"h_ttime\": \"1752213197\",\n", "  \"h_xg\": \"0\",\n", "  \"h_xgzq\": \"0\",\n", "  \"h_yerk\": \"0\",\n", "  \"oem://wzq_channel\": \"fm_wzq_wx_v1_unknow_01..\",\n", "  \"qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"qlskey\": \"080a752219cd128c202*bH5ccbdbJH\",\n", "  \"qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\",\n", "  \"qq_logtype\": \"16\",\n", "  \"qs_openid\": \"o4NN4jlQfrkl5xecDR9NKp2wFN04\",\n", "  \"qs_unionid\": \"oURPMwqW34OdDH0smHSUJPgLnaAI\",\n", "  \"trade_in_cmschina\": \"1\",\n", "  \"trade_in_partner\": \"1\",\n", "  \"wx_session_time\": \"1751332780000\",\n", "  \"wzq_dealer\": \"11100\",\n", "  \"wzq_qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"wzq_qlskey\": \"080a752219cd128c202*bH5ccbdbJH\",\n", "  \"wzq_qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\"\n", "}\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "params = {\n", "    't': str(int(time.time()*1000)),\n", "}\n", "\n", "data = {\n", "    'scene': 'trade',\n", "    'come_from': '0',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = requests.post(\n", "    'https://wzq.csc108.com/cgi-bin/tradeprepare.cgi',\n", "    params=params,\n", "    cookies=cookies,\n", "    headers=headers,\n", "    data=data,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "77596a18", "metadata": {"ExecuteTime": {"end_time": "2025-06-10T03:05:02.398747Z", "start_time": "2025-06-10T03:05:02.355746Z"}}, "outputs": [{"data": {"text/plain": ["'{\"retcode\":\"0\",\"retmsg\":\"OK\",\"encrypt_method\":\"0\",\"gm_check_sign_switch\":\"0\",\"key\":\"C0CEE4B6914866965BE0D3D3F155D85FA296CBBF13956EDBB32146C8C61E36B164BDD8F399CFEF37B60AB650BABBB1F62EFA679119B375E0E1A0272D7CD84DD6DE8D2C618753A7C2D79AEFBC2249F23A8797A4AC885CE795CBA8DB27F8BEBCC0DADCC155ED216BAE6A2923A98E45A7CD1BE1F330CB1B66054B2FC9A117522633\",\"key_front_and_broker\":\"****************************************************************************************************************************************************************************************************************************************************************\",\"needcheck\":\"1\",\"timeseed\":\"17522214712483723904\"}\\n'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["response.text"]}, {"cell_type": "code", "execution_count": 5, "id": "65ad3a12", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T07:28:08.496762Z", "start_time": "2025-06-03T07:28:08.476759Z"}, "scrolled": true}, "outputs": [{"data": {"text/plain": ["{'retcode': '0',\n", " 'retmsg': 'OK',\n", " 'encrypt_method': '0',\n", " 'gm_check_sign_switch': '0',\n", " 'key': 'C0CEE4B6914866965BE0D3D3F155D85FA296CBBF13956EDBB32146C8C61E36B164BDD8F399CFEF37B60AB650BABBB1F62EFA679119B375E0E1A0272D7CD84DD6DE8D2C618753A7C2D79AEFBC2249F23A8797A4AC885CE795CBA8DB27F8BEBCC0DADCC155ED216BAE6A2923A98E45A7CD1BE1F330CB1B66054B2FC9A117522633',\n", " 'key_front_and_broker': '****************************************************************************************************************************************************************************************************************************************************************',\n", " 'needcheck': '1',\n", " 'timeseed': '17522214712483723904'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": 53, "id": "252502d1", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:20:00.492323Z", "start_time": "2025-06-03T09:20:00.274320Z"}}, "outputs": [], "source": ["cookies = {\n", "    'qs_openid': 'o4NN4jlQfrkl5xecDR9NKp2wFN04',\n", "    'qs_unionid': 'oURPMwqW34OdDH0smHSUJPgLnaAI',\n", "    'trade_in_cmschina': '1',\n", "    'trade_in_partner': '1',\n", "    'wzq_dealer': '11100',\n", "    'oem://wzq_channel': 'fm_wzq_wx_v1_unknow_01..',\n", "    'qlappid': 'wx9cf8c670ebd68ce4',\n", "    'qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qq_logtype': '16',\n", "    'wx_session_time': '1748936554000',\n", "    'wzq_qlappid': 'wx9cf8c670ebd68ce4',\n", "    'wzq_qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'h_xgzq': '0',\n", "    'qlskey': '110a7527ab704ae7101*bH4IJ4b6H4',\n", "    'wzq_qlskey': '110a7527ab704ae7101*bH4IJ4b6H4',\n", "    'h_dsxpl': '0|20250603',\n", "    'h_ttime': '1748941675',\n", "}\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    # 'Cookie': 'qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wzq_dealer=11100; oem://wzq_channel=fm_wzq_wx_v1_unknow_01..; qlappid=wx9cf8c670ebd68ce4; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; wx_session_time=1748936554000; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; h_xgzq=0; qlskey=110a7527ab704ae7101*bH4IJ4b6H4; wzq_qlskey=110a7527ab704ae7101*bH4IJ4b6H4; h_dsxpl=0|20250603; h_ttime=1748941675',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "data = {\n", "    'market': '0',\n", "    'stock_code': '',\n", "    'stockholder_code': '',\n", "    'retry_time': '0',\n", "    'come_from': '0',\n", "    'gm_flag': '0',\n", "}\n", "params = {\n", "    't': str(int(time.time()*1000)),\n", "}\n", "response = requests.post(\n", "    'https://wzq.csc108.com/cgi-bin/trade_order_no.fcgi',\n", "    params=params,\n", "    cookies=cookies,\n", "    headers=headers,\n", "    data=data,\n", ")"]}, {"cell_type": "code", "execution_count": 54, "id": "19426a75", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:20:00.876327Z", "start_time": "2025-06-03T09:20:00.854328Z"}}, "outputs": [{"data": {"text/plain": ["{'retcode': '0',\n", " 'retmsg': 'OK',\n", " 'trade_order_no': '01202506031720000307c400754b7aec',\n", " 'trade_set_domain': 'https://wzq.csc108.com'}"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": 58, "id": "5dff416e", "metadata": {"ExecuteTime": {"end_time": "2025-06-04T06:13:59.824572Z", "start_time": "2025-06-04T06:13:59.548575Z"}}, "outputs": [], "source": ["import requests\n", "\n", "cookies = {\n", "    'qs_openid': 'o4NN4jlQfrkl5xecDR9NKp2wFN04',\n", "    'qs_unionid': 'oURPMwqW34OdDH0smHSUJPgLnaAI',\n", "    'trade_in_cmschina': '1',\n", "    'trade_in_partner': '1',\n", "    'wzq_dealer': '11100',\n", "    'oem://wzq_channel': 'fm_wzq_wx_v1_unknow_01..',\n", "    'h_delisted': '0|1748999894',\n", "    'h_xgzq': '0',\n", "    'h_ttime': '1749014887',\n", "    'h_dsxpl': '1|20250604',\n", "    'qlappid': 'wx9cf8c670ebd68ce4',\n", "    'qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qq_logtype': '16',\n", "    'wx_session_time': '1749016209000',\n", "    'wzq_qlappid': 'wx9cf8c670ebd68ce4',\n", "    'wzq_qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qlskey': '110a75276555d9f3101*bH4JabH5Ib',\n", "    'wzq_qlskey': '110a75276555d9f3101*bH4JabH5Ib',\n", "}\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    # 'Cookie': 'qlappid=wx9cf8c670ebd68ce4; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wx_session_time=1748999319000; wzq_dealer=11100; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; oem://wzq_channel=fm_wzq_wx_v1_unknow_01..; h_delisted=0|1748999894; h_xgzq=0; h_dsxpl=0|20250604; qlskey=110a752759372042100*bH4Jab4IIH; wzq_qlskey=110a752759372042100*bH4Jab4IIH; h_ttime=1749014887',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    'market': '0',\n", "    'code': '161130',\n", "    'needquote': '1',\n", "    'needfive': '1',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = requests.post('https://wzq.csc108.com/cgi-bin/stock_info.fcgi', cookies=cookies, headers=headers, data=data)"]}, {"cell_type": "code", "execution_count": 62, "id": "aab2f9fd", "metadata": {"ExecuteTime": {"end_time": "2025-06-04T06:14:59.930461Z", "start_time": "2025-06-04T06:14:59.912462Z"}}, "outputs": [{"data": {"text/plain": ["{'class': '5',\n", " 'market': '0',\n", " 'name': '纳斯达克100LOF',\n", " 'price_ceiling': '3.821',\n", " 'price_floor': '3.127',\n", " 'product_status': '',\n", " 'secu_code': '161130',\n", " 'spread': '0.001',\n", " 'status': '0',\n", " 'susp_flag': '0',\n", " 'trd_ceiling': '99999999',\n", " 'trd_floor': '1',\n", " 'trd_unit': '100'}"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json().get('info')"]}, {"cell_type": "code", "execution_count": 68, "id": "9a1c7ae6", "metadata": {"ExecuteTime": {"end_time": "2025-06-04T07:10:30.534430Z", "start_time": "2025-06-04T07:10:29.793425Z"}}, "outputs": [], "source": ["\n", "cookies ={\n", "  \"h_delisted\": \"0|1748999894\",\n", "  \"h_dsxpl\": \"0|20250604\",\n", "  \"h_ttime\": \"1749020648\",\n", "  \"h_xgzq\": \"0\",\n", "  \"oem://wzq_channel\": \"fm_wzq_wx_v1_unknow_01..\",\n", "  \"qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"qlskey\": \"110a7527ad85cb0a100*bH4Jaca64H\",\n", "  \"qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\",\n", "  \"qq_logtype\": \"16\",\n", "  \"qs_openid\": \"o4NN4jlQfrkl5xecDR9NKp2wFN04\",\n", "  \"qs_unionid\": \"oURPMwqW34OdDH0smHSUJPgLnaAI\",\n", "  \"trade_in_cmschina\": \"1\",\n", "  \"trade_in_partner\": \"1\",\n", "  \"wx_session_time\": \"1749016209000\",\n", "  \"wzq_dealer\": \"11100\",\n", "  \"wzq_qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"wzq_qlskey\": \"110a7527ad85cb0a100*bH4Jaca64H\",\n", "  \"wzq_qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\"\n", "}\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    # 'Cookie': 'qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wzq_dealer=11100; oem://wzq_channel=fm_wzq_wx_v1_unknow_01..; h_delisted=0|1748999894; h_xgzq=0; qlappid=wx9cf8c670ebd68ce4; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; wx_session_time=1749016209000; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; h_dsxpl=0|20250604; h_ttime=1749018501; qlskey=110a7528c6e08d52100*bH4JabIJcc; wzq_qlskey=110a7528c6e08d52100*bH4JabIJcc',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    '_appver': '7.0.20',\n", "    '_osVer': 'Windows1064',\n", "    '_buildh5ver': '************',\n", "    'psw': '',\n", "    'id': '',\n", "    'contract_no': 'VJ00000005',\n", "    'trade_time': '2025-06-04 17:19:36',\n", "    'scode': '161130',\n", "    'action': '1',\n", "    'market': '0',\n", "    'come_from': '0',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = requests.post(\n", "    'https://wzq.csc108.com/cgi-bin/tradecancel.cgi',\n", "    params=params,\n", "    cookies=cookies,\n", "    headers=headers,\n", "    data=data,\n", ")"]}, {"cell_type": "code", "execution_count": 69, "id": "b0709432", "metadata": {"ExecuteTime": {"end_time": "2025-06-04T07:10:31.285437Z", "start_time": "2025-06-04T07:10:31.269437Z"}}, "outputs": [{"data": {"text/plain": ["'{\"retcode\":\"0\",\"retmsg\":\"OK\"}\\n'"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["response.text"]}, {"cell_type": "code", "execution_count": 73, "id": "516132ec", "metadata": {"ExecuteTime": {"end_time": "2025-06-05T00:56:48.393876Z", "start_time": "2025-06-05T00:56:48.374874Z"}}, "outputs": [], "source": ["import akshare as ak\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 107, "id": "a745feb2", "metadata": {"ExecuteTime": {"end_time": "2025-06-06T08:06:05.194103Z", "start_time": "2025-06-06T08:06:05.029101Z"}}, "outputs": [], "source": ["cookies = {\n", "    'FundTradeLoginTab': '0',\n", "    'FundTradeLoginCard': '0',\n", "    'FundTradeLoginProtocol': 'Fri%20May%2009%202025%2013%3A28%3A37%20GMT%2B0800%20(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)',\n", "    'ASP.NET_SessionId': 'ygblgvhohexllevky0pinvom',\n", "    'st_si': '72984034441568',\n", "    'st_asi': 'delete',\n", "    'VipLevel': '0',\n", "    'FundTradeLoginUser': 'ELyz1Jq0qnqJfIsGIFrLz4wmm7PwuDyLI8lrzOKHSg+1ksm6ft1m2NOtcsNI6bqlyMZsdaYD',\n", "    'fund_trade_cn': 'Xs5mWVACcZOe7l3ClVKI1PoBmy4rXeqcMIOhvSPFnInIvl6UjeYiOGAebwjJAYRicx1zo6NIRIPkC3BsYDYXo+asMgpoTp6rfPpxvglHoiTv39xufao=',\n", "    'fund_trade_name': 'Z+LLBOA0D4kDoZNO6a15NXwYT0zwd3v6CFbXW97iqa4yU1M5gPMnVadVTMiwbP9moPR/GUWQ',\n", "    'fund_trade_visitor': 'xSu2UqF8vYU36K8lFCWnnvKzdKXx4pGuzvFTrmzvfBFQmHaB/NEQFaQsXqjkPqVVKVBD4gL+',\n", "    'fund_trade_risk': 'NKFcE429lqfsLt8pCsXTbcULKv+idhDZSsqNmS8OQt709qESnTTybji/Q6HUbJdRvzr5kZxj',\n", "    'fund_trade_gps': '14',\n", "    'TradeLoginToken': 'f261e5b2c0974d44b51de3d8c288854d',\n", "    'UTOKEN': 'UGaGvXf3Pkive7gyEQO4EIrlyA601YPqwESOH3lpN/vlMwqc/nNJ7d68dldakJSAiJ1FgEtG+CywDrTRq+AW2pMZ16Ak8MEfefpM8KGui8UISKOdn5Q=',\n", "    'LToken': '1637de1e5cc340ffae38c8ce2ef0bd8a',\n", "    'fund_trade_trackid': 'TOHlaiJJcf7SkJPcbFJ8O3/uJZkoRcJcD9rrgCyqo20IMp+D/VWf4LpbnxTad1DdV63j/sZ2P2HJS3p5QrFvaA==',\n", "    'b_p_log_key': 'OR6XY69IQ+BXOfsKxNDB9TKi0GuMHRSb49l848NpjocL9T2qNHfkQpCeDWDGfQE5lcxJ3lKHUi3krZSHQ3LVSDgCYe7Qd0HKYJk5EX+x+YShNYGa/mA=',\n", "    'st_pvi': '34743709332097',\n", "    'st_sp': '2024-12-04%2015%3A31%3A21',\n", "    'st_inirUrl': 'http%3A%2F%2Fhelp.1234567.com.cn%2F',\n", "    'st_sn': '8',\n", "    'st_psi': '20250606142030316-0-0570042797',\n", "    'fund_trade_token': 'k2q3pnckMJs5EWHZcwG0EDDZWIYFy0U-hYIuuv_p4lhEqgtd7sjid2znWyusYC3100z7jdNMtAq6cGhafinaymOD7wTXnQCOt3Vt_iP6RnjlwvzKxHKETkQK2QJhBkFapRbocOPL38VV_4-IxUAK9AL5x81DAWlde5t6C2btSzdTl97C9YhuyYa01SnNadQuotqInXweDwqG7jMfKhbHqUYSKCIv_xJNdBtLlVsWwoXpKiF1B_eQRTv7PeyLZ0SPgKvEfQfDl1OcSzpfqnmTyDcDRSpzv7uhDHgvDnL5q7zlUYKzx2WmlEwgWiiaEOw1P1MZFpQ0x447CSxZreMOtBL8JSjT_qHuP5RD1GUqB1fW8wOt30mYEx^^',\n", "}\n", "\n", "headers = {\n", "    'Accept': 'application/json, text/javascript, */*; q=0.01',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',\n", "    'Connection': 'keep-alive',\n", "    'Content-Type': 'application/json; charset=UTF-8',\n", "    'Referer': 'https://trade.1234567.com.cn/FundtradePage/default2.aspx?fc=111111',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203',\n", "    'X-Requested-With': 'XMLHttpRequest',\n", "    'sec-ch-ua': '\"Not/A)Brand\";v=\"99\", \"Microsoft Edge\";v=\"115\", \"Chromium\";v=\"115\"',\n", "    'sec-ch-ua-mobile': '?0',\n", "    'sec-ch-ua-platform': '\"Windows\"',\n", "}\n", "\n", "data = \"{'code' : '000123'}\"\n", "\n", "response = requests.post(\n", "    'https://trade.1234567.com.cn/FundtradePage/default2.aspx/FundTypeGet',\n", "    cookies=cookies,\n", "    headers=headers,\n", "    data=data,\n", ")"]}, {"cell_type": "code", "execution_count": 108, "id": "ed17c948", "metadata": {"ExecuteTime": {"end_time": "2025-06-06T08:06:05.660168Z", "start_time": "2025-06-06T08:06:05.645169Z"}}, "outputs": [{"data": {"text/plain": ["'{\"Succeed\":false,\"Message\":\"请先进行登录!\"}'"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()['d']"]}, {"cell_type": "code", "execution_count": 106, "id": "5abd5174", "metadata": {"ExecuteTime": {"end_time": "2025-06-06T07:05:04.456249Z", "start_time": "2025-06-06T07:04:42.574609Z"}}, "outputs": [{"ename": "ConnectionError", "evalue": "HTTPSConnectionPool(host='pdf.dfcfw.com', port=443): Max retries exceeded with url: /pdf/H2_AN202407031637495189_1.pdf?1719998097000.pdf (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002381CDCC880>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTimeoutError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:174\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    173\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 174\u001b[0m     conn \u001b[38;5;241m=\u001b[39m connection\u001b[38;5;241m.\u001b[39mcreate_connection(\n\u001b[0;32m    175\u001b[0m         (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dns_host, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mport), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mextra_kw\n\u001b[0;32m    176\u001b[0m     )\n\u001b[0;32m    178\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout:\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\connection.py:95\u001b[0m, in \u001b[0;36mcreate_connection\u001b[1;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[0;32m     94\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m err \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m---> 95\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m err\n\u001b[0;32m     97\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m socket\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgetaddrinfo returns an empty list\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\connection.py:85\u001b[0m, in \u001b[0;36mcreate_connection\u001b[1;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[0;32m     84\u001b[0m     sock\u001b[38;5;241m.\u001b[39mbind(source_address)\n\u001b[1;32m---> 85\u001b[0m \u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msa\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     86\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m sock\n", "\u001b[1;31mTimeoutError\u001b[0m: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mNewConnectionError\u001b[0m                        <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:716\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    715\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[1;32m--> 716\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    722\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    723\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    724\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    726\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[0;32m    728\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[0;32m    729\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:404\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[1;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[0;32m    403\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 404\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    405\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    406\u001b[0m     \u001b[38;5;66;03m# Py2 raises this as a BaseSSLError, Py3 raises it as socket timeout.\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:1061\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[1;34m(self, conn)\u001b[0m\n\u001b[0;32m   1060\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(conn, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msock\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m):  \u001b[38;5;66;03m# AppE<PERSON><PERSON> might not have  `.sock`\u001b[39;00m\n\u001b[1;32m-> 1061\u001b[0m     \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1063\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_verified:\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:363\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    361\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    362\u001b[0m     \u001b[38;5;66;03m# Add certificate verification\u001b[39;00m\n\u001b[1;32m--> 363\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    364\u001b[0m     hostname \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:186\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    185\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m--> 186\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NewConnectionError(\n\u001b[0;32m    187\u001b[0m         \u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to establish a new connection: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m e\n\u001b[0;32m    188\u001b[0m     )\n\u001b[0;32m    190\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m conn\n", "\u001b[1;31mNewConnectionError\u001b[0m: <urllib3.connection.HTTPSConnection object at 0x000002381CDCC880>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:802\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    800\u001b[0m     e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n\u001b[1;32m--> 802\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    803\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m    804\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    805\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\retry.py:594\u001b[0m, in \u001b[0;36mRetry.increment\u001b[1;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[0;32m    593\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m new_retry\u001b[38;5;241m.\u001b[39mis_exhausted():\n\u001b[1;32m--> 594\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause))\n\u001b[0;32m    596\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[1;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='pdf.dfcfw.com', port=443): Max retries exceeded with url: /pdf/H2_AN202407031637495189_1.pdf?1719998097000.pdf (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002381CDCC880>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mConnectionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Input \u001b[1;32mIn [106]\u001b[0m, in \u001b[0;36m<cell line: 19>\u001b[1;34m()\u001b[0m\n\u001b[0;32m      1\u001b[0m headers \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      2\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAccept\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mapplication/json, text/javascript, */*; q=0.01\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAccept-Language\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mzh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     14\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msec-ch-ua-platform\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWindows\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     15\u001b[0m }\n\u001b[0;32m     17\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m{\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcode\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m : \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m000123\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m}\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m---> 19\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     20\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mhttps://pdf.dfcfw.com/pdf/H2_AN202407031637495189_1.pdf?1719998097000.pdf\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m     21\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcookies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcookies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     22\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     23\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     24\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[1;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[0;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[0;32m    105\u001b[0m \n\u001b[0;32m    106\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    112\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m request(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url, data\u001b[38;5;241m=\u001b[39mdata, json\u001b[38;5;241m=\u001b[39mjson, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[1;34m(method, url, **kwargs)\u001b[0m\n\u001b[0;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[0;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[1;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m session\u001b[38;5;241m.\u001b[39mrequest(method\u001b[38;5;241m=\u001b[39mmethod, url\u001b[38;5;241m=\u001b[39murl, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[0;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[0;32m    587\u001b[0m }\n\u001b[0;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(prep, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msend_kwargs)\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[0;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[1;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m adapter\u001b[38;5;241m.\u001b[39msend(request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[0;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\adapters.py:700\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    696\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _SSLError):\n\u001b[0;32m    697\u001b[0m         \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[0;32m    698\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m--> 700\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    702\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    703\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n", "\u001b[1;31mConnectionError\u001b[0m: HTTPSConnectionPool(host='pdf.dfcfw.com', port=443): Max retries exceeded with url: /pdf/H2_AN202407031637495189_1.pdf?1719998097000.pdf (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002381CDCC880>: Failed to establish a new connection: [WinError 10060] 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。'))"]}], "source": ["headers = {\n", "    'Accept': 'application/json, text/javascript, */*; q=0.01',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',\n", "    'Connection': 'keep-alive',\n", "    'Content-Type': 'application/json; charset=UTF-8',\n", "    'Referer': 'https://trade.1234567.com.cn/FundtradePage/default2.aspx?fc=111111',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203',\n", "    'X-Requested-With': 'XMLHttpRequest',\n", "    'sec-ch-ua': '\"Not/A)Brand\";v=\"99\", \"Microsoft Edge\";v=\"115\", \"Chromium\";v=\"115\"',\n", "    'sec-ch-ua-mobile': '?0',\n", "    'sec-ch-ua-platform': '\"Windows\"',\n", "}\n", "\n", "data = \"{'code' : '000123'}\"\n", "\n", "response = requests.post(\n", "    'https://pdf.dfcfw.com/pdf/H2_AN202407031637495189_1.pdf?1719998097000.pdf',\n", "    cookies=cookies,\n", "    headers=headers,\n", "    data=data,\n", ")"]}, {"cell_type": "code", "execution_count": 20, "id": "b58058c6", "metadata": {"ExecuteTime": {"end_time": "2025-06-10T04:59:10.481596Z", "start_time": "2025-06-10T04:58:55.439813Z"}}, "outputs": [{"ename": "ProxyError", "evalue": "HTTPSConnectionPool(host='wzq.csc108.com', port=443): Max retries exceeded with url: /cgi-bin/stock_info.fcgi (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000014684254A00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mConnectionRefusedError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:174\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    173\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 174\u001b[0m     conn \u001b[38;5;241m=\u001b[39m connection\u001b[38;5;241m.\u001b[39mcreate_connection(\n\u001b[0;32m    175\u001b[0m         (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dns_host, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mport), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mextra_kw\n\u001b[0;32m    176\u001b[0m     )\n\u001b[0;32m    178\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout:\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\connection.py:95\u001b[0m, in \u001b[0;36mcreate_connection\u001b[1;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[0;32m     94\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m err \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m---> 95\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m err\n\u001b[0;32m     97\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m socket\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgetaddrinfo returns an empty list\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\connection.py:85\u001b[0m, in \u001b[0;36mcreate_connection\u001b[1;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[0;32m     84\u001b[0m     sock\u001b[38;5;241m.\u001b[39mbind(source_address)\n\u001b[1;32m---> 85\u001b[0m \u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msa\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     86\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m sock\n", "\u001b[1;31mConnectionRefusedError\u001b[0m: [WinError 10061] 由于目标计算机积极拒绝，无法连接。", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mNewConnectionError\u001b[0m                        <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:713\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    712\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_new_proxy_conn \u001b[38;5;129;01mand\u001b[39;00m http_tunnel_required:\n\u001b[1;32m--> 713\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_prepare_proxy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    715\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:1015\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._prepare_proxy\u001b[1;34m(self, conn)\u001b[0m\n\u001b[0;32m   1013\u001b[0m     conn\u001b[38;5;241m.\u001b[39mtls_in_tls_required \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m-> 1015\u001b[0m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:363\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    361\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    362\u001b[0m     \u001b[38;5;66;03m# Add certificate verification\u001b[39;00m\n\u001b[1;32m--> 363\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    364\u001b[0m     hostname \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connection.py:186\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    185\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m--> 186\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NewConnectionError(\n\u001b[0;32m    187\u001b[0m         \u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to establish a new connection: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m e\n\u001b[0;32m    188\u001b[0m     )\n\u001b[0;32m    190\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m conn\n", "\u001b[1;31mNewConnectionError\u001b[0m: <urllib3.connection.HTTPSConnection object at 0x0000014684254A00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:830\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    827\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    828\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    829\u001b[0m     )\n\u001b[1;32m--> 830\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    831\u001b[0m         method,\n\u001b[0;32m    832\u001b[0m         url,\n\u001b[0;32m    833\u001b[0m         body,\n\u001b[0;32m    834\u001b[0m         headers,\n\u001b[0;32m    835\u001b[0m         retries,\n\u001b[0;32m    836\u001b[0m         redirect,\n\u001b[0;32m    837\u001b[0m         assert_same_host,\n\u001b[0;32m    838\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    839\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    840\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    841\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    842\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    843\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw\n\u001b[0;32m    844\u001b[0m     )\n\u001b[0;32m    846\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:830\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    827\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    828\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    829\u001b[0m     )\n\u001b[1;32m--> 830\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    831\u001b[0m         method,\n\u001b[0;32m    832\u001b[0m         url,\n\u001b[0;32m    833\u001b[0m         body,\n\u001b[0;32m    834\u001b[0m         headers,\n\u001b[0;32m    835\u001b[0m         retries,\n\u001b[0;32m    836\u001b[0m         redirect,\n\u001b[0;32m    837\u001b[0m         assert_same_host,\n\u001b[0;32m    838\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    839\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    840\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    841\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    842\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    843\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw\n\u001b[0;32m    844\u001b[0m     )\n\u001b[0;32m    846\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:830\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    827\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    828\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    829\u001b[0m     )\n\u001b[1;32m--> 830\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    831\u001b[0m         method,\n\u001b[0;32m    832\u001b[0m         url,\n\u001b[0;32m    833\u001b[0m         body,\n\u001b[0;32m    834\u001b[0m         headers,\n\u001b[0;32m    835\u001b[0m         retries,\n\u001b[0;32m    836\u001b[0m         redirect,\n\u001b[0;32m    837\u001b[0m         assert_same_host,\n\u001b[0;32m    838\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    839\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    840\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    841\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    842\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    843\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw\n\u001b[0;32m    844\u001b[0m     )\n\u001b[0;32m    846\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py:802\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    800\u001b[0m     e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n\u001b[1;32m--> 802\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    803\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m    804\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    805\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\util\\retry.py:594\u001b[0m, in \u001b[0;36mRetry.increment\u001b[1;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[0;32m    593\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m new_retry\u001b[38;5;241m.\u001b[39mis_exhausted():\n\u001b[1;32m--> 594\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause))\n\u001b[0;32m    596\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[1;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='wzq.csc108.com', port=443): Max retries exceeded with url: /cgi-bin/stock_info.fcgi (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000014684254A00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mProxyError\u001b[0m                                Traceback (most recent call last)", "Input \u001b[1;32mIn [20]\u001b[0m, in \u001b[0;36m<cell line: 60>\u001b[1;34m()\u001b[0m\n\u001b[0;32m     27\u001b[0m session\u001b[38;5;241m.\u001b[39mcookies\u001b[38;5;241m.\u001b[39mupdate({\n\u001b[0;32m     28\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mqlappid\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwx9cf8c670ebd68ce4\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     29\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mqluin\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mos-ppuF7XdUGL-hJG43C1yFNbBm8\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     50\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mh_ttime\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m1749524316\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     51\u001b[0m })\n\u001b[0;32m     52\u001b[0m data \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     53\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmarket\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m0\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     54\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcode\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m161130\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     57\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgm_flag\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m0\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     58\u001b[0m }\n\u001b[1;32m---> 60\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     61\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mhttps://wzq.csc108.com/cgi-bin/stock_info.fcgi\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m     62\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\n\u001b[0;32m     63\u001b[0m \u001b[43m)\u001b[49m\n\u001b[0;32m     65\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mjson())  \u001b[38;5;66;03m# 输出响应内容\u001b[39;00m\n\u001b[0;32m     66\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m使用的协议版本:\u001b[39m\u001b[38;5;124m\"\u001b[39m, response\u001b[38;5;241m.\u001b[39mraw\u001b[38;5;241m.\u001b[39mversion)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py:637\u001b[0m, in \u001b[0;36mSession.post\u001b[1;34m(self, url, data, json, **kwargs)\u001b[0m\n\u001b[0;32m    626\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\u001b[38;5;28mself\u001b[39m, url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m    627\u001b[0m     \u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[0;32m    628\u001b[0m \n\u001b[0;32m    629\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    634\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[0;32m    635\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 637\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPOST\u001b[39m\u001b[38;5;124m\"\u001b[39m, url, data\u001b[38;5;241m=\u001b[39mdata, json\u001b[38;5;241m=\u001b[39mjson, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[0;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[0;32m    587\u001b[0m }\n\u001b[0;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(prep, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msend_kwargs)\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[0;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[1;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m adapter\u001b[38;5;241m.\u001b[39msend(request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[0;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\adapters.py:694\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    691\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RetryError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    693\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _ProxyError):\n\u001b[1;32m--> 694\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ProxyError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    696\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _SSLError):\n\u001b[0;32m    697\u001b[0m     \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[0;32m    698\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n", "\u001b[1;31mProxyError\u001b[0m: HTTPSConnectionPool(host='wzq.csc108.com', port=443): Max retries exceeded with url: /cgi-bin/stock_info.fcgi (Caused by ProxyError('Cannot connect to proxy.', NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000014684254A00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))"]}], "source": ["import requests\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "\n", "class HTTP1Adapter(HTTPAdapter):\n", "    def init_poolmanager(, *args, **kwargs):\n", "        kwargs['socket_options'] = [\n", "            (6, 1, 1)  # 强制 HTTP/1.1 (SOL_SOCKET=6, SO_PROTOCOL=1, HTTP/1.1=1)\n", "        ]\n", "        super().init_poolmanager(*args, **kwargs)\n", "\n", "# 创建会话并挂载适配器\n", "session = requests.Session()\n", "retry = Retry(total=3, backoff_factor=1)\n", "adapter = HTTP1Adapter(max_retries=retry)\n", "session.mount(\"https://\", adapter)\n", "session.mount(\"http://\", adapter)\n", "\n", "\n", "session.headers.update({\n", "    'Host': 'wzq.csc108.com',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "})\n", "session.cookies.update({\n", "    'qlappid': 'wx9cf8c670ebd68ce4',\n", "    'qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qq_logtype': '16',\n", "    'qs_openid': 'o4NN4jlQfrkl5xecDR9NKp2wFN04',\n", "    'qs_unionid': 'oURPMwqW34OdDH0smHSUJPgLnaAI',\n", "    'trade_in_cmschina': '1',\n", "    'trade_in_partner': '1',\n", "    'wx_session_time': '1749523402000',\n", "    'wzq_dealer': '11100',\n", "    'wzq_qlappid': 'wx9cf8c670ebd68ce4',\n", "    'wzq_qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'oem://wzq_channel': 'fm_wzq_wx_v1_unknow_01..',\n", "    'h_spec': '0',\n", "    'h_hrepo': '0',\n", "    'h_pg': '0',\n", "    'h_xg': '0',\n", "    'h_yerk': '0',\n", "    'h_delisted': '0|1749523410',\n", "    'h_xgzq': '0',\n", "    'qlskey': '110a752762b055bb101*bH4J5c4db6',\n", "    'wzq_qlskey': '110a752762b055bb101*bH4J5c4db6',\n", "    'h_dsxpl': '0|20250610',\n", "    'h_ttime': '1749524316',\n", "})\n", "data = {\n", "    'market': '0',\n", "    'code': '161130',\n", "    'needquote': '1',\n", "    'needfive': '1',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = session.post(\n", "    'https://wzq.csc108.com/cgi-bin/stock_info.fcgi',\n", "    data=data\n", ")\n", "\n", "print(response.json())  # 输出响应内容\n", "print(\"使用的协议版本:\", response.raw.version)  # 检查是否为 HTTP/1.1"]}, {"cell_type": "code", "execution_count": 44, "id": "b9f2d0e6", "metadata": {"ExecuteTime": {"end_time": "2025-06-11T10:41:15.178953Z", "start_time": "2025-06-11T10:41:15.026952Z"}}, "outputs": [{"data": {"text/plain": ["'{\"code\":0, \"message\":\"\", \"data\":{\"stockCode\":\"sz161130\", \"nodes\":[{\"open\":\"3.328\", \"last\":\"3.447\", \"high\":\"3.458\", \"low\":\"3.314\", \"volume\":\"27821500.00\", \"amount\":\"94620557.00\", \"exchange\":\"29.13\", \"exchangeRaw\":\"29.1300\", \"date\":\"2025-05-16\", \"oi\":\"0.000\", \"tradeDays\":\"5\", \"dividend\":\"\", \"addZdf\":\"\", \"finance\":null}, {\"open\":\"3.429\", \"last\":\"3.417\", \"high\":\"3.463\", \"low\":\"3.409\", \"volume\":\"10281900.00\", \"amount\":\"35320485.00\", \"exchange\":\"11.06\", \"exchangeRaw\":\"11.0600\", \"date\":\"2025-05-23\", \"oi\":\"0.000\", \"tradeDays\":\"5\", \"dividend\":\"\", \"addZdf\":\"\", \"finance\":null}, {\"open\":\"3.430\", \"last\":\"3.467\", \"high\":\"3.531\", \"low\":\"3.417\", \"volume\":\"12156000.00\", \"amount\":\"42275411.00\", \"exchange\":\"14.04\", \"exchangeRaw\":\"14.0400\", \"date\":\"2025-05-30\", \"oi\":\"0.000\", \"tradeDays\":\"5\", \"dividend\":\"\", \"addZdf\":\"\", \"finance\":null}, {\"open\":\"3.482\", \"last\":\"3.498\", \"high\":\"3.525\", \"low\":\"3.473\", \"volume\":\"5989700.00\", \"amount\":\"20929244.00\", \"exchange\":\"6.98\", \"exchangeRaw\":\"6.9800\", \"date\":\"2025-06-06\", \"oi\":\"0.000\", \"tradeDays\":\"4\", \"dividend\":\"\", \"addZdf\":\"\", \"finance\":null}, {\"open\":\"3.507\", \"last\":\"3.533\", \"high\":\"3.544\", \"low\":\"3.504\", \"volume\":\"4431700.00\", \"amount\":\"15622210.00\", \"exchange\":\"5.18\", \"exchangeRaw\":\"5.1800\", \"date\":\"2025-06-11\", \"oi\":\"0.000\", \"tradeDays\":\"3\", \"dividend\":\"\", \"addZdf\":\"\", \"finance\":null}], \"qt\":{\"fields\":[\"51\", \"纳斯达克100LOF\", \"161130\", \"3.533\", \"3.522\", \"3.531\", \"10047\", \"4007\", \"6040\", \"3.532\", \"45\", \"3.531\", \"51\", \"3.530\", \"30\", \"3.528\", \"150\", \"3.527\", \"1\", \"3.533\", \"58\", \"3.534\", \"83\", \"3.535\", \"139\", \"3.536\", \"383\", \"3.537\", \"230\", \"\", \"20250611161430\", \"0.011\", \"0.31\", \"3.539\", \"3.529\", \"3.533/10047/3550850\", \"10047\", \"355\", \"1.18\", \"\", \"\", \"3.539\", \"3.529\", \"0.28\", \"3.02\", \"3.02\", \"0.00\", \"3.874\", \"3.170\", \"0.64\", \"-616\", \"3.534\", \"\", \"\", \"\", \"\", \"\", \"355.0850\", \"0.0000\", \"0\", \" \", \"ETF\", \"-1.86\", \"0.91\", \"\", \"\", \"\", \"3.728\", \"2.798\", \"2.58\", \"5.06\", \"11.91\", \"85482615\", \"85482615\", \"-52.65\", \"15.42\", \"85482615\", \"-1.00\", \"\", \"13.31\", \"0.03\", \"3.5686\", \"CNY\", \"0\", \"\", \"3.538\", \"-1303\"], \"market\":\"2025-06-11 18:41:13|HK_close_已收盘|SH_close_已收盘|SZ_close_已收盘|US_close_未开盘|SQ_close_已休市|DS_close_已休市|ZS_close_已休市|NEWSH_close_已收盘|NEWSZ_close_已收盘|NEWHK_close_已收盘|NEWUS_close_未开盘|REPO_close_已收盘|UK_open_交易中|KCB_close_已收盘|IT_open_交易中|MY_close_已收盘|EU_open_交易中|AH_close_已收盘|DE_open_交易中|JW_close_已收盘|CYB_close_已收盘|USA_close_未开盘|USB_open_盘前交易|ZQ_close_已收盘\", \"zhishu\":[]}, \"fundQt\":null, \"cvbondQt\":null, \"prec\":\"3.272\", \"fsStartDate\":\"20201009\", \"pandata\":null, \"introduce\":\"\", \"funddata\":null, \"warrantInfo\":null, \"fqtype\":\"\", \"attribute\":null, \"fs\":null, \"opPoints\":[], \"jtInfo\":null}}'"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["session = requests.Session()\n", "session.trust_env = False\n", "session.proxies = {'http': None, 'https': None}\n", "headers = {\n", "    'Host': 'zxgcloud.csc108.com',\n", "    'accept': 'application/json, text/plain, */*',\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'origin': 'https://wzq.csc108.com',\n", "    'sec-fetch-site': 'same-site',\n", "    'sec-fetch-mode': 'cors',\n", "    'sec-fetch-dest': 'empty',\n", "    'referer': 'https://wzq.csc108.com/',\n", "    'accept-language': 'zh-CN,zh;q=0.9',\n", "    'priority': 'u=1, i',\n", "}\n", "\n", "params = {\n", "    'code': 'sz161130',\n", "    'ktype': 'week',\n", "    'fqtype': 'qfq',\n", "    'toDate': '',\n", "    'limit': '5',\n", "    'openid': 'stockfe',\n", "    'app': 'oem_ji<PERSON><PERSON>',\n", "    't': str(int(time.time()*1000)),\n", "}\n", "\n", "response = session.get(\n", "    'https://zxgcloud.csc108.com/domain_upstream/proxyfinanceqqcom/cgi/cgi-bin/stockinfoquery/kline/app/get',\n", "    params=params,\n", "    headers=headers,\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": 51, "id": "b3fa53c8", "metadata": {"ExecuteTime": {"end_time": "2025-06-11T10:41:56.834238Z", "start_time": "2025-06-11T10:41:56.669238Z"}}, "outputs": [{"data": {"text/plain": ["'{\"code\":0,\"msg\":\"\",\"data\":{\"sz161130\":{\"qt\":{\"sz161130\":[\"51\",\"\\\\u7eb3\\\\u65af\\\\u8fbe\\\\u514b100LOF\",\"161130\",\"3.533\",\"3.522\",\"3.531\",\"10047\",\"4007\",\"6040\",\"3.532\",\"45\",\"3.531\",\"51\",\"3.530\",\"30\",\"3.528\",\"150\",\"3.527\",\"1\",\"3.533\",\"58\",\"3.534\",\"83\",\"3.535\",\"139\",\"3.536\",\"383\",\"3.537\",\"230\",\"\",\"20250611161430\",\"0.011\",\"0.31\",\"3.539\",\"3.529\",\"3.533\\\\/10047\\\\/3550850\",\"10047\",\"355\",\"1.18\",\"\",\"\",\"3.539\",\"3.529\",\"0.28\",\"3.02\",\"3.02\",\"0.00\",\"3.874\",\"3.170\",\"0.64\",\"-616\",\"3.534\",\"\",\"\",\"\",\"\",\"\",\"355.0850\",\"0.0000\",\"0\",\" \",\"ETF\",\"-1.86\",\"0.91\",\"\",\"\",\"\",\"3.728\",\"2.798\",\"2.58\",\"5.06\",\"11.91\",\"85482615\",\"85482615\",\"-52.65\",\"15.42\",\"85482615\",\"-1.00\",\"\",\"13.31\",\"0.03\",\"3.5686\",\"CNY\",\"0\",\"\",\"3.538\",\"-1303\"],\"market\":[\"2025-06-11 18:41:55|HK_close_\\\\u5df2\\\\u6536\\\\u76d8|SH_close_\\\\u5df2\\\\u6536\\\\u76d8|SZ_close_\\\\u5df2\\\\u6536\\\\u76d8|US_close_\\\\u672a\\\\u5f00\\\\u76d8|SQ_close_\\\\u5df2\\\\u4f11\\\\u5e02|DS_close_\\\\u5df2\\\\u4f11\\\\u5e02|ZS_close_\\\\u5df2\\\\u4f11\\\\u5e02|NEWSH_close_\\\\u5df2\\\\u6536\\\\u76d8|NEWSZ_close_\\\\u5df2\\\\u6536\\\\u76d8|NEWHK_close_\\\\u5df2\\\\u6536\\\\u76d8|NEWUS_close_\\\\u672a\\\\u5f00\\\\u76d8|REPO_close_\\\\u5df2\\\\u6536\\\\u76d8|UK_open_\\\\u4ea4\\\\u6613\\\\u4e2d|KCB_close_\\\\u5df2\\\\u6536\\\\u76d8|IT_open_\\\\u4ea4\\\\u6613\\\\u4e2d|MY_close_\\\\u5df2\\\\u6536\\\\u76d8|EU_open_\\\\u4ea4\\\\u6613\\\\u4e2d|AH_close_\\\\u5df2\\\\u6536\\\\u76d8|DE_open_\\\\u4ea4\\\\u6613\\\\u4e2d|JW_close_\\\\u5df2\\\\u6536\\\\u76d8|CYB_close_\\\\u5df2\\\\u6536\\\\u76d8|USA_close_\\\\u672a\\\\u5f00\\\\u76d8|USB_open_\\\\u76d8\\\\u524d\\\\u4ea4\\\\u6613|ZQ_close_\\\\u5df2\\\\u6536\\\\u76d8\"],\"zjlx\":[]},\"m120\":[[\"202506091500\",\"3.507\",\"3.508\",\"3.512\",\"3.504\",\"6284.010\",{},\"73.3931\"],[\"202506101130\",\"3.523\",\"3.540\",\"3.544\",\"3.523\",\"8032.250\",{},\"93.9630\"],[\"202506101500\",\"3.538\",\"3.522\",\"3.540\",\"3.519\",\"13876.470\",{},\"162.3299\"],[\"202506111130\",\"3.531\",\"3.534\",\"3.539\",\"3.531\",\"5663.000\",{},\"66.252\"],[\"202506111500\",\"3.533\",\"3.533\",\"3.535\",\"3.529\",\"4384.000\",{},\"51.281\"]],\"prec\":\"3.522\"}}}'"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    'Host': 'zxgcloud.csc108.com',\n", "    'accept': 'application/json, text/plain, */*',\n", "    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'origin': 'https://wzq.csc108.com',\n", "    'sec-fetch-site': 'same-site',\n", "    'sec-fetch-mode': 'cors',\n", "    'sec-fetch-dest': 'empty',\n", "    'referer': 'https://wzq.csc108.com/',\n", "    'accept-language': 'zh-CN,zh;q=0.9',\n", "    'priority': 'u=1, i',\n", "}\n", "\n", "params = {\n", "    'param': 'sz161130,m120,,5',\n", "    'app': 'oem_ji<PERSON><PERSON>',\n", "    't': '1749521215610',\n", "}\n", "\n", "response = session.get(\n", "    'https://zxgcloud.csc108.com/domain_upstream/proxyfinanceqqcom/ifzqgtimg/appstock/app/kline/mkline',\n", "    params=params,\n", "    headers=headers,\n", ")\n", "response.text"]}, {"cell_type": "code", "execution_count": 55, "id": "59fe41c5", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T07:07:34.608592Z", "start_time": "2025-06-12T07:07:34.510593Z"}}, "outputs": [], "source": ["cookies = {\n", "    'qs_openid': 'o4NN4jlQfrkl5xecDR9NKp2wFN04',\n", "    'qs_unionid': 'oURPMwqW34OdDH0smHSUJPgLnaAI',\n", "    'trade_in_cmschina': '1',\n", "    'trade_in_partner': '1',\n", "    'wzq_dealer': '11100',\n", "    'oem://wzq_channel': 'fm_wzq_wx_v1_unknow_01..',\n", "    'h_xgzq': '0',\n", "    'qlappid': 'wx9cf8c670ebd68ce4',\n", "    'qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qq_logtype': '16',\n", "    'wx_session_time': '1749693089000',\n", "    'wzq_qlappid': 'wx9cf8c670ebd68ce4',\n", "    'wzq_qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'sajssdk_2015_cross_new_user': '1',\n", "    'sensorsdata2015jssdkcross': '%7B%22distinct_id%22%3A%22oURPMwqW34OdDH0smHSUJPgLnaAI%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219761e532f7268-0972c2d3aecf78-7f4c1349-2073600-19761e532f8177%22%7D',\n", "    'h_spec': '0',\n", "    'h_hrepo': '0',\n", "    'h_pg': '0',\n", "    'h_yerk': '0',\n", "    'qlskey': '110a752763fdd89f101*bH4JHbbH6a',\n", "    'wzq_qlskey': '110a752763fdd89f101*bH4JHbbH6a',\n", "    'h_dsxpl': '0|20250612',\n", "    'h_ttime': '1749711760',\n", "}\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    # 'Cookie': 'qlappid=wx9cf8c670ebd68ce4; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wx_session_time=1749519366000; wzq_dealer=11100; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; oem://wzq_channel=fm_wzq_wx_v1_unknow_01..; h_spec=0; h_dsxpl=1|20250610; h_hrepo=0; h_pg=0; h_xg=0; h_yerk=0; h_delisted=0|1749519385; h_xgzq=0; qlskey=110a7529288b0157101*bH4J5bJJII; wzq_qlskey=110a7529288b0157101*bH4J5bJJII',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    'market': '0',\n", "    'code': '161130',\n", "    'needquote': '1',\n", "    'needfive': '1',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = session.post('https://wzq.csc108.com/cgi-bin/stock_info.fcgi', cookies=cookies, headers=headers, data=data)"]}, {"cell_type": "code", "execution_count": 56, "id": "4ab84a66", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T07:07:35.179593Z", "start_time": "2025-06-12T07:07:35.161595Z"}}, "outputs": [{"data": {"text/plain": ["{'retcode': '0',\n", " 'retmsg': 'OK',\n", " 'market_state': '6',\n", " 'refresh_time': '3',\n", " 'stock_cls': 'F',\n", " 'five_trans': {'mcjg1': '3.523',\n", "  'mcjg2': '3.524',\n", "  'mcjg3': '3.525',\n", "  'mcjg4': '3.526',\n", "  'mcjg5': '3.527',\n", "  'mcsl1': '23',\n", "  'mcsl2': '11',\n", "  'mcsl3': '2',\n", "  'mcsl4': '40',\n", "  'mcsl5': '50',\n", "  'mrjg1': '3.522',\n", "  'mrjg2': '3.521',\n", "  'mrjg3': '3.520',\n", "  'mrjg4': '3.519',\n", "  'mrjg5': '3.518',\n", "  'mrsl1': '91',\n", "  'mrsl2': '12',\n", "  'mrsl3': '1',\n", "  'mrsl4': '4',\n", "  'mrsl5': '327'},\n", " 'info': {'class': '5',\n", "  'market': '0',\n", "  'name': '纳斯达克100LOF',\n", "  'price_ceiling': '3.886',\n", "  'price_floor': '3.180',\n", "  'product_status': '',\n", "  'secu_code': '161130',\n", "  'spread': '0.001',\n", "  'status': '0',\n", "  'susp_flag': '0',\n", "  'trd_ceiling': '99999999',\n", "  'trd_floor': '1',\n", "  'trd_unit': '100'},\n", " 'quote': {'cje': '3172868.00',\n", "  'cjl': '902000',\n", "  'dqj': '3.522',\n", "  'hsl': '',\n", "  'jkj': '3.519',\n", "  'ltz': '',\n", "  'npl': '',\n", "  'syl': '--',\n", "  'utime': '1749712035',\n", "  'wpl': '',\n", "  'zde': '-0.011',\n", "  'zdf': '-0.31',\n", "  'zdj': '3.511',\n", "  'zgj': '3.524',\n", "  'zsj': '3.533',\n", "  'zsz': '2.99'}}"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": 3, "id": "7828b4de", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'retcode': '0',\n", " 'retmsg': 'OK',\n", " 'refresh_time': '1000',\n", " 'stop_refresh': '1',\n", " 'info': {'can_cancel': '0',\n", "  'code': '518850',\n", "  'contract_no': '0500994192',\n", "  'end_time': '2025-06-16 13:19:24',\n", "  'market': '1',\n", "  'match_num': '1300',\n", "  'match_price': '7.620',\n", "  'name': '黄金9999',\n", "  'order_num': '1300',\n", "  'order_price': '7.620',\n", "  'stock_type': '0',\n", "  'total_agree': '9906.00',\n", "  'trade_avg_price': '7.620',\n", "  'trade_brokerage': '1.98',\n", "  'trade_cost': '0.00',\n", "  'trade_state': '2',\n", "  'trade_tax': '0.00',\n", "  'trade_time': '2025-06-16 13:19:24',\n", "  'trade_total_amount': '9,904.02',\n", "  'trade_total_fee': '1.98',\n", "  'trade_type': '2',\n", "  'trans_id': ''}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "\n", "cookies = {\n", "    'qlappid': 'wx9cf8c670ebd68ce4',\n", "    'qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'qq_logtype': '16',\n", "    'qs_openid': 'o4NN4jlQfrkl5xecDR9NKp2wFN04',\n", "    'qs_unionid': 'oURPMwqW34OdDH0smHSUJPgLnaAI',\n", "    'trade_in_cmschina': '1',\n", "    'trade_in_partner': '1',\n", "    'wx_session_time': '1750640307000',\n", "    'wzq_dealer': '11100',\n", "    'wzq_qlappid': 'wx9cf8c670ebd68ce4',\n", "    'wzq_qluin': 'os-ppuF7XdUGL-hJG43C1yFNbBm8',\n", "    'oem://wzq_channel': 'fm_wzq_wx_v1_unknow_01..',\n", "    'qlskey': '110a7527ca2e89bd101*bH5a64a4J6',\n", "    'wzq_qlskey': '110a7527ca2e89bd101*bH5a64a4J6',\n", "}{\n", "  \"h_delisted\": \"0|1751332792\",\n", "  \"h_dsxpl\": \"1|20250717\",\n", "  \"h_hrepo\": \"0\",\n", "  \"h_pg\": \"0\",\n", "  \"h_spec\": \"0\",\n", "  \"h_ttime\": \"1752735021\",\n", "  \"h_xg\": \"0\",\n", "  \"h_xgzq\": \"0\",\n", "  \"h_yerk\": \"0\",\n", "  \"oem://wzq_channel\": \"fm_wzq_wx_v1_unknow_01..\",\n", "  \"qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"qlskey\": \"110a7527a8256ec2100*bH5cHd5aca\",\n", "  \"qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\",\n", "  \"qq_logtype\": \"16\",\n", "  \"qs_openid\": \"o4NN4jlQfrkl5xecDR9NKp2wFN04\",\n", "  \"qs_unionid\": \"oURPMwqW34OdDH0smHSUJPgLnaAI\",\n", "  \"trade_in_cmschina\": \"1\",\n", "  \"trade_in_partner\": \"1\",\n", "  \"wx_session_time\": \"1751332780000\",\n", "  \"wzq_dealer\": \"11100\",\n", "  \"wzq_qlappid\": \"wx9cf8c670ebd68ce4\",\n", "  \"wzq_qlskey\": \"110a7527a8256ec2100*bH5cHd5aca\",\n", "  \"wzq_qluin\": \"os-ppuF7XdUGL-hJG43C1yFNbBm8\"\n", "}\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    # 'Cookie': 'qlappid=wx9cf8c670ebd68ce4; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wx_session_time=1750640307000; wzq_dealer=11100; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; oem://wzq_channel=fm_wzq_wx_v1_unknow_01..; qlskey=110a7527ca2e89bd101*bH5a64a4J6; wzq_qlskey=110a7527ca2e89bd101*bH5a64a4J6',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    'id': '',\n", "    'contract_no': '0500994192',\n", "    'trade_time': '2025-06-16 13:19:24',\n", "    'istiming': '0',\n", "    'gm_flag': '0',\n", "}\n", "\n", "response = requests.post('https://wzq.csc108.com/cgi-bin/tradequery.cgi', cookies=cookies, headers=headers, data=data)\n", "response.json()"]}, {"cell_type": "code", "execution_count": 19, "id": "1b1c5d4e", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import time\n", "import json\n", "import logging\n", "import requests\n", "import argparse\n", "from datetime import datetime, timedelta\n", "from typing import Dict, Any, Optional, Tuple\n", "from colorama import init, Fore, Style\n", "from pytdx.hq import TdxHq_API\n", "import pickle\n", "\n", "# 导入自定义模块\n", "from realtime_signal import RealTimeSignalCalculator, TDX_SERVERS\n", "from citic_trader.trade import TradeClient\n", "from citic_trader.utils import setup_logger, format_stock_code\n", "from citic_trader.query import QueryClient\n", "\n", "\n", "def load_cookies() -> Dict[str, str]:\n", "    \"\"\"\n", "    从文件加载cookies\n", "    \n", "    可以支持两种格式：\n", "    1. JSON格式的字典：{\"key1\": \"value1\", \"key2\": \"value2\"}\n", "    2. 原始字符串格式： \"key1=value1; key2=value2\"\n", "    \n", "    Returns:\n", "        Dict: cookies字典\n", "    \"\"\"\n", "    try:\n", "        if not os.path.exists(cookies_file):\n", "            logger.error(f\"Cookies文件不存在: {cookies_file}\")\n", "            return {}\n", "            \n", "        with open(cookies_file, 'r', encoding='utf-8') as f:\n", "            content = f.read().strip()\n", "            \n", "            # 尝试解析为JSON格式\n", "            try:\n", "                cookies = json.loads(content)\n", "                logger.info(f\"成功从 {cookies_file} 加载JSON格式的cookies\")\n", "                return cookies\n", "            except json.JSONDecodeError:\n", "                # 如果不是JSON格式，尝试解析为原始字符串格式\n", "                logger.info(f\"尝试从 {cookies_file} 加载原始字符串格式的cookies\")\n", "                cookies = {}\n", "                parts = content.split(';')\n", "                for part in parts:\n", "                    if '=' in part:\n", "                        key, value = part.split('=', 1)\n", "                        cookies[key.strip()] = value.strip()\n", "                if cookies:\n", "                    logger.info(f\"成功解析原始字符串格式的cookies\")\n", "                    return cookies\n", "                else:\n", "                    logger.error(f\"无法解析cookies文件内容为有效的JSON或原始字符串格式: {cookies_file}\")\n", "                    return {}\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"加载cookies失败: {e}\")\n", "        return {}\n", "\n", "def setup_trade_client() -> bool:\n", "    \"\"\"\n", "    设置交易客户端\n", "    \n", "    Returns:\n", "        bool: 是否设置成功\n", "    \"\"\"\n", "    try:\n", "        # 加载cookies\n", "        cookies = load_cookies()\n", "        if not cookies:\n", "            logger.error(\"未找到有效的cookies，无法初始化交易客户端\")\n", "            return False\n", "        \n", "        # 创建会话并添加cookies\n", "        session = requests.Session()\n", "        # 禁用代理\n", "        session.trust_env = False\n", "        session.proxies = {'http': None, 'https': None}\n", "        for key, value in cookies.items():\n", "            session.cookies.set(key, value)\n", "        \n", "        # 设置用户信息 (这里需要根据实际情况修改)\n", "        user_info = {\n", "            \"account_id\": \"\",  # 从cookies中获取或手动设置\n", "            \"stockholder_code\": \"\",  # 从交易接口获取或手动设置\n", "            \"psw_session\": \"\",  # 从cookies中获取或手动设置\n", "            \"app_info\": {\n", "                \"_appver\": \"7.0.20\",\n", "                \"_osVer\": \"Windows1064\",\n", "                \"_buildh5ver\": \"************\"\n", "            }\n", "        }\n", "        \n", "        # 创建交易客户端\n", "        trade_client = TradeClient(session, user_info, logger)\n", "        # 创建查询客户端\n", "        query_client = QueryClient(session, user_info, logger)\n", "        logger.info(\"交易客户端和查询客户端创建成功\")\n", "        return trade_client, query_client\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"设置交易客户端失败: {e}\")\n", "        return False\n", "\n", "def get_account_info() -> bool:\n", "    \"\"\"\n", "    获取账户持仓和资金信息\n", "    \n", "    Returns:\n", "        bool: 是否获取成功\n", "    \"\"\"\n", "    try:\n", "        if not query_client:\n", "            logger.error(\"查询客户端未初始化\")\n", "            return False\n", "            \n", "        # 获取账户信息（新版接口）\n", "        logger.info(\"获取账户持仓和资金信息...\")\n", "        result = query_client.query_account()\n", "        \n", "        if result.get(\"status\") == \"success\":\n", "            account_info = result\n", "            \n", "            # 更新可用资金\n", "            available_cash = float(result[\"data\"][\"funds\"].get(\"available_cash\", 0))\n", "            \n", "            # 清空旧的持仓信息\n", "            etf_positions = {base_etf: 0, target_etf: 0}\n", "            etf_can_sell_qty = {base_etf: 0, target_etf: 0}\n", "            \n", "            # 更新两个ETF的持仓\n", "            for position in result[\"data\"][\"positions\"]:\n", "                code = position[\"code\"]\n", "                if code == base_etf or code == target_etf:\n", "                    etf_positions[code] = int(position.get(\"hold_num\", 0))\n", "                    etf_can_sell_qty[code] = int(position.get(\"can_use\", 0))\n", "            \n", "            logger.info(f\"账户信息获取成功\")\n", "            logger.info(f\"可用资金: {available_cash}元\")\n", "            logger.info(f\"当前 {base_etf} 持仓: {etf_positions[base_etf]}份, 可卖出: {etf_can_sell_qty[base_etf]}份\")\n", "            logger.info(f\"当前 {target_etf} 持仓: {etf_positions[target_etf]}份, 可卖出: {etf_can_sell_qty[target_etf]}份\")\n", "            \n", "            return True\n", "        else:\n", "            logger.error(f\"获取账户信息失败: {result.get('message', '未知错误')}\")\n", "            return False\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"获取账户信息过程中出错: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 59, "id": "3812c406", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-18 09:14:52] [INFO] [*********.py:43] 成功从 cookies.json 加载JSON格式的cookies\n", "[2025-07-18 09:14:52] [INFO] [trade.py:885] 股东代码获取成功: {'0': '0583744886', '1': 'F259313185'}\n", "[2025-07-18 09:14:52] [INFO] [trade.py:85] 交易客户端初始化完成\n", "[2025-07-18 09:14:52] [ERROR] [trade.py:614] 获取交易订单号失败: 交易请先登录\n", "[2025-07-18 09:14:52] [ERROR] [trade.py:614] 获取交易订单号失败: 交易请先登录\n", "[2025-07-18 09:14:52] [INFO] [trade.py:87] trade_order_no:{'0': '', '1': ''}\n", "[2025-07-18 09:14:52] [INFO] [query.py:71] 查询客户端初始化完成\n", "[2025-07-18 09:14:52] [INFO] [query.py:71] 查询客户端初始化完成\n", "[2025-07-18 09:14:52] [INFO] [2813857427.py:35] 交易客户端和查询客户端创建成功\n"]}], "source": ["logger = setup_logger()\n", "cookies_file = \"cookies.json\" \n", "base_etf = \"518890\"\n", "target_etf = \"518880\"\n", "try:\n", "    # 加载cookies\n", "    cookies = load_cookies()\n", "    if not cookies:\n", "        logger.error(\"未找到有效的cookies，无法初始化交易客户端\")\n", "    \n", "    # 创建会话并添加cookies\n", "    session = requests.Session()\n", "    # 禁用代理\n", "    session.trust_env = False\n", "    session.proxies = {'http': None, 'https': None}\n", "    for key, value in cookies.items():\n", "        session.cookies.set(key, value)\n", "    \n", "    # 设置用户信息 (这里需要根据实际情况修改)\n", "    user_info = {\n", "        \"account_id\": \"\",  # 从cookies中获取或手动设置\n", "        \"stockholder_code\": \"\",  # 从交易接口获取或手动设置\n", "        \"psw_session\": \"\",  # 从cookies中获取或手动设置\n", "        \"app_info\": {\n", "            \"_appver\": \"7.0.20\",\n", "            \"_osVer\": \"Windows1064\",\n", "            \"_buildh5ver\": \"************\"\n", "        }\n", "    }\n", "    \n", "    # 创建交易客户端\n", "    trade_client = TradeClient(session, user_info, logger)\n", "    # 创建查询客户端\n", "    query_client = QueryClient(session, user_info, logger)\n", "    logger.info(\"交易客户端和查询客户端创建成功\")\n", "  \n", "except Exception as e:\n", "    logger.error(f\"设置交易客户端失败: {e}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "054d6c9e", "metadata": {}, "outputs": [], "source": ["trade_client._prepare_trade(\"1\")"]}, {"cell_type": "code", "execution_count": 3, "id": "63700fc2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-14 12:37:09] [INFO] [*********.py:123] 获取账户持仓和资金信息...\n", "[2025-07-14 12:37:09] [INFO] [query.py:141] 正在查询账户信息...\n", "[2025-07-14 12:37:09] [INFO] [query.py:159] 查询账户信息成功\n", "[2025-07-14 12:37:09] [INFO] [*********.py:143] 账户信息获取成功\n", "[2025-07-14 12:37:09] [INFO] [*********.py:144] 可用资金: 323.02元\n", "[2025-07-14 12:37:09] [INFO] [*********.py:145] 当前 518890 持仓: 0份, 可卖出: 0份\n", "[2025-07-14 12:37:09] [INFO] [*********.py:146] 当前 518880 持仓: 7900份, 可卖出: 7900份\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["get_account_infoTradeClient()"]}, {"cell_type": "code", "execution_count": 3, "id": "78cb37c0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-14 12:41:16] [INFO] [query.py:435] 正在刷新交易订单状态...\n", "[2025-07-14 12:41:16] [INFO] [query.py:451] 刷新交易信息成功\n", "[2025-07-14 12:41:16] [INFO] [query.py:506] 刷新交易订单状态成功\n"]}, {"data": {"text/plain": ["{'status': 'success',\n", " 'message': '刷新交易信息成功',\n", " 'data': {'funds': {'total_assets': '58943.020',\n", "   'available_cash': '323.02',\n", "   'withdrawable_cash': '185.24',\n", "   'frozen_cash': '0.00',\n", "   'market_value': '58618.000',\n", "   'total_profit': '526.070',\n", "   'today_profit': '360.980'},\n", "  'positions': [{'code': '518880',\n", "    'name': '黄金ETF',\n", "    'hold_num': '7900',\n", "    'can_use': '7900',\n", "    'new_price': '7.420',\n", "    'hold_cost': '7.425',\n", "    'hold_val': '58618.000',\n", "    'earn_val': '-41.660',\n", "    'earn_per': '-0.07',\n", "    'earn_val_day': '-41.660',\n", "    'stockholder_code': 'F259313185'},\n", "   {'code': '518890',\n", "    'name': '中银黄金',\n", "    'hold_num': '0',\n", "    'can_use': '0',\n", "    'new_price': '7.422',\n", "    'hold_cost': '--',\n", "    'hold_val': '0.000',\n", "    'earn_val': '567.730',\n", "    'earn_per': '+0.97',\n", "    'earn_val_day': '402.640',\n", "    'stockholder_code': 'F259313185'}],\n", "  'orders': {'order_num_all': '42', 'order_num_undone': '0'},\n", "  'bulletin': [],\n", "  'activityinfo': {},\n", "  'aics': {},\n", "  'balanceinfo': {}}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["query_client.trade_refresh()"]}, {"cell_type": "code", "execution_count": 31, "id": "6fdbfd17", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-14 12:33:07] [INFO] [trade.py:791] 正在获取账户持仓和资金信息...\n", "[2025-07-14 12:33:07] [INFO] [trade.py:833] 账户信息获取成功\n", "[2025-07-14 12:33:07] [INFO] [trade.py:834] 可用资金: 323.02元\n", "[2025-07-14 12:33:07] [INFO] [trade.py:835] 持仓市值: 58618.000元\n", "[2025-07-14 12:33:07] [INFO] [trade.py:836] 持仓数量: 2支股票/基金\n"]}, {"data": {"text/plain": ["{'status': 'success',\n", " 'message': '获取账户信息成功',\n", " 'data': {'funds': {'total_assets': '58935.120',\n", "   'available_cash': '323.02',\n", "   'withdrawable_cash': '185.24',\n", "   'frozen_cash': '0.00',\n", "   'market_value': '58618.000',\n", "   'total_profit': '526.07',\n", "   'today_profit': '360.980'},\n", "  'positions': [{'can_use': '7900',\n", "    'code': '518880',\n", "    'earn_per': '-0.07',\n", "    'earn_per_day': '-0.07',\n", "    'earn_val': '-41.660',\n", "    'earn_val_day': '-41.660',\n", "    'hold_cost': '7.425',\n", "    'hold_cost_day': '58757.866',\n", "    'hold_num': '7900',\n", "    'hold_open_value': '',\n", "    'hold_val': '58618.000',\n", "    'market': '1',\n", "    'name': '黄金ETF',\n", "    'new_price': '7.420',\n", "    'stockholder_code': 'F259313185'},\n", "   {'can_use': '0',\n", "    'code': '518890',\n", "    'earn_per': '+0.97',\n", "    'earn_per_day': '+0.69',\n", "    'earn_val': '567.730',\n", "    'earn_val_day': '402.640',\n", "    'hold_cost': '--',\n", "    'hold_cost_day': '58396.800',\n", "    'hold_num': '0',\n", "    'hold_open_value': '58396.80',\n", "    'hold_val': '0.000',\n", "    'market': '1',\n", "    'name': '中银黄金',\n", "    'new_price': '7.422',\n", "    'stockholder_code': 'F259313185'}],\n", "  'orders': [{'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:59',\n", "    'code': '518880',\n", "    'contract_no': '0500170248',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.424',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2969.60',\n", "    'trade_avg_price': '7.424',\n", "    'trade_income': '',\n", "    'trade_money': '2969.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:59',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:57',\n", "    'code': '518890',\n", "    'contract_no': '0500169254',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:57',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:55',\n", "    'code': '518880',\n", "    'contract_no': '0500168639',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.424',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2969.60',\n", "    'trade_avg_price': '7.424',\n", "    'trade_income': '',\n", "    'trade_money': '2969.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:55',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:54',\n", "    'code': '518890',\n", "    'contract_no': '0500168330',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:54',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:52',\n", "    'code': '518880',\n", "    'contract_no': '0500167447',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:52',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:50',\n", "    'code': '518890',\n", "    'contract_no': '0500166523',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:50',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:48',\n", "    'code': '518880',\n", "    'contract_no': '0500165927',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:48',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:47',\n", "    'code': '518890',\n", "    'contract_no': '0500165628',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:47',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:45',\n", "    'code': '518880',\n", "    'contract_no': '0500164773',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:45',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:44',\n", "    'code': '518890',\n", "    'contract_no': '0500164501',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:44',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:42',\n", "    'code': '518880',\n", "    'contract_no': '0500163652',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:42',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:41',\n", "    'code': '518890',\n", "    'contract_no': '0500163302',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:41',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:36',\n", "    'code': '518880',\n", "    'contract_no': '0500161290',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.424',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2969.60',\n", "    'trade_avg_price': '7.424',\n", "    'trade_income': '',\n", "    'trade_money': '2969.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:36',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:34',\n", "    'code': '518890',\n", "    'contract_no': '0500160660',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:34',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:33',\n", "    'code': '518880',\n", "    'contract_no': '0500160020',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:33',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:32',\n", "    'code': '518890',\n", "    'contract_no': '0500159733',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:32',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:30',\n", "    'code': '518880',\n", "    'contract_no': '0500158880',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:30',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:29',\n", "    'code': '518890',\n", "    'contract_no': '0500158611',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:29',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:27',\n", "    'code': '518880',\n", "    'contract_no': '0500157712',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:27',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:26',\n", "    'code': '518890',\n", "    'contract_no': '0500157161',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:26',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:24',\n", "    'code': '518880',\n", "    'contract_no': '0500156508',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:24',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:23',\n", "    'code': '518890',\n", "    'contract_no': '0500155995',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:23',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:21',\n", "    'code': '518880',\n", "    'contract_no': '0500155301',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:21',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:20',\n", "    'code': '518890',\n", "    'contract_no': '0500154994',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:20',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:18',\n", "    'code': '518880',\n", "    'contract_no': '0500154069',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.426',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:18',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:17',\n", "    'code': '518890',\n", "    'contract_no': '0500153538',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:17',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:15',\n", "    'code': '518880',\n", "    'contract_no': '0500152822',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.426',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.426',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.40',\n", "    'trade_avg_price': '7.426',\n", "    'trade_income': '',\n", "    'trade_money': '2970.40',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:15',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:14',\n", "    'code': '518890',\n", "    'contract_no': '0500152552',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:14',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:12',\n", "    'code': '518880',\n", "    'contract_no': '0500151628',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:12',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:11',\n", "    'code': '518890',\n", "    'contract_no': '0500151340',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:11',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:09',\n", "    'code': '518880',\n", "    'contract_no': '0500150498',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:09',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:08',\n", "    'code': '518890',\n", "    'contract_no': '0500149995',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:08',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:06',\n", "    'code': '518880',\n", "    'contract_no': '0500149353',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '300',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '300',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2227.50',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2227.50',\n", "    'trade_num': '300',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:06',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:05',\n", "    'code': '518890',\n", "    'contract_no': '0500149043',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '300',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '300',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2233.20',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2233.20',\n", "    'trade_num': '300',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:05',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:04',\n", "    'code': '518880',\n", "    'contract_no': '0500148420',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:04',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:02',\n", "    'code': '518890',\n", "    'contract_no': '0500147783',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.444',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.60',\n", "    'trade_avg_price': '7.444',\n", "    'trade_income': '',\n", "    'trade_money': '2977.60',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:02',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:01',\n", "    'code': '518880',\n", "    'contract_no': '0500147077',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:01',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:32:00',\n", "    'code': '518890',\n", "    'contract_no': '0500146791',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:32:00',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '400',\n", "    'cancel_time': '2025-07-14 09:31:46',\n", "    'code': '518890',\n", "    'contract_no': '0500141259',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '0',\n", "    'match_price': '',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '',\n", "    'trade_avg_price': '',\n", "    'trade_income': '',\n", "    'trade_money': '0.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '8',\n", "    'trade_time': '2025-07-14 09:31:46',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '400',\n", "    'cancel_time': '2025-07-14 09:31:45',\n", "    'code': '518890',\n", "    'contract_no': '0500140463',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '0',\n", "    'match_price': '',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.444',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '',\n", "    'trade_avg_price': '',\n", "    'trade_income': '',\n", "    'trade_money': '0.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '8',\n", "    'trade_time': '2025-07-14 09:31:45',\n", "    'trade_type': '2',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:31:42',\n", "    'code': '518880',\n", "    'contract_no': '0500139560',\n", "    'dqj': '7.420',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.425',\n", "    'name': '黄金ETF',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.425',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2970.00',\n", "    'trade_avg_price': '7.425',\n", "    'trade_income': '',\n", "    'trade_money': '2970.00',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:31:42',\n", "    'trade_type': '1',\n", "    'trans_id': ''},\n", "   {'can_cancel': '0',\n", "    'cancel_num': '',\n", "    'cancel_time': '2025-07-14 09:31:41',\n", "    'code': '518890',\n", "    'contract_no': '0500139074',\n", "    'dqj': '7.422',\n", "    'end_time': '',\n", "    'market': '1',\n", "    'match_num': '400',\n", "    'match_price': '7.443',\n", "    'name': '中银黄金',\n", "    'not_cancel_num': '',\n", "    'not_trade_num': '',\n", "    'order_num': '400',\n", "    'order_price': '7.443',\n", "    'pgcode': '',\n", "    'pgname': '',\n", "    'stock_type': '0',\n", "    'total_agree': '2977.20',\n", "    'trade_avg_price': '7.443',\n", "    'trade_income': '',\n", "    'trade_money': '2977.20',\n", "    'trade_num': '400',\n", "    'trade_rate': '',\n", "    'trade_state': '2',\n", "    'trade_time': '2025-07-14 09:31:41',\n", "    'trade_type': '2',\n", "    'trans_id': ''}],\n", "  'limits': {'max_buy_money': '323.02', 'max_sell_qty': '0'},\n", "  'stockholder_code': '',\n", "  'raw_response': {'retcode': '0',\n", "   'retmsg': 'OK',\n", "   'market_state_bj': '3',\n", "   'market_state_h': '3',\n", "   'market_state_hk': '3',\n", "   'market_state_kc_after_h': '3',\n", "   'market_state_nq': '3',\n", "   'market_state_repo_h': '3',\n", "   'market_state_repo_s': '3',\n", "   'market_state_s': '3',\n", "   'market_state_trade_after_h': '3',\n", "   'max_buy_money': '323.02',\n", "   'max_buy_qty_switch': '0',\n", "   'needcheck': '1',\n", "   'refresh_btn': '0',\n", "   'refresh_time': '10',\n", "   'trade_order_no': '01202507141233070306a5007560da23',\n", "   'trade_set_domain': 'https://wzq.csc108.com',\n", "   'bulletin': [{'bulletin_status': '0'}],\n", "   'holdstock': [{'can_use': '7900',\n", "     'code': '518880',\n", "     'earn_per': '-0.07',\n", "     'earn_per_day': '-0.07',\n", "     'earn_val': '-41.660',\n", "     'earn_val_day': '-41.660',\n", "     'hold_cost': '7.425',\n", "     'hold_cost_day': '58757.866',\n", "     'hold_num': '7900',\n", "     'hold_open_value': '',\n", "     'hold_val': '58618.000',\n", "     'market': '1',\n", "     'name': '黄金ETF',\n", "     'new_price': '7.420',\n", "     'stockholder_code': 'F259313185'},\n", "    {'can_use': '0',\n", "     'code': '518890',\n", "     'earn_per': '+0.97',\n", "     'earn_per_day': '+0.69',\n", "     'earn_val': '567.730',\n", "     'earn_val_day': '402.640',\n", "     'hold_cost': '--',\n", "     'hold_cost_day': '58396.800',\n", "     'hold_num': '0',\n", "     'hold_open_value': '58396.80',\n", "     'hold_val': '0.000',\n", "     'market': '1',\n", "     'name': '中银黄金',\n", "     'new_price': '7.422',\n", "     'stockholder_code': 'F259313185'}],\n", "   'orderlist': [{'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:59',\n", "     'code': '518880',\n", "     'contract_no': '0500170248',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.424',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2969.60',\n", "     'trade_avg_price': '7.424',\n", "     'trade_income': '',\n", "     'trade_money': '2969.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:59',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:57',\n", "     'code': '518890',\n", "     'contract_no': '0500169254',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:57',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:55',\n", "     'code': '518880',\n", "     'contract_no': '0500168639',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.424',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2969.60',\n", "     'trade_avg_price': '7.424',\n", "     'trade_income': '',\n", "     'trade_money': '2969.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:55',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:54',\n", "     'code': '518890',\n", "     'contract_no': '0500168330',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:54',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:52',\n", "     'code': '518880',\n", "     'contract_no': '0500167447',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:52',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:50',\n", "     'code': '518890',\n", "     'contract_no': '0500166523',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:50',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:48',\n", "     'code': '518880',\n", "     'contract_no': '0500165927',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:48',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:47',\n", "     'code': '518890',\n", "     'contract_no': '0500165628',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:47',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:45',\n", "     'code': '518880',\n", "     'contract_no': '0500164773',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:45',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:44',\n", "     'code': '518890',\n", "     'contract_no': '0500164501',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:44',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:42',\n", "     'code': '518880',\n", "     'contract_no': '0500163652',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:42',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:41',\n", "     'code': '518890',\n", "     'contract_no': '0500163302',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:41',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:36',\n", "     'code': '518880',\n", "     'contract_no': '0500161290',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.424',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2969.60',\n", "     'trade_avg_price': '7.424',\n", "     'trade_income': '',\n", "     'trade_money': '2969.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:36',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:34',\n", "     'code': '518890',\n", "     'contract_no': '0500160660',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:34',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:33',\n", "     'code': '518880',\n", "     'contract_no': '0500160020',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:33',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:32',\n", "     'code': '518890',\n", "     'contract_no': '0500159733',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:32',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:30',\n", "     'code': '518880',\n", "     'contract_no': '0500158880',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:30',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:29',\n", "     'code': '518890',\n", "     'contract_no': '0500158611',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:29',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:27',\n", "     'code': '518880',\n", "     'contract_no': '0500157712',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:27',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:26',\n", "     'code': '518890',\n", "     'contract_no': '0500157161',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:26',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:24',\n", "     'code': '518880',\n", "     'contract_no': '0500156508',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:24',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:23',\n", "     'code': '518890',\n", "     'contract_no': '0500155995',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:23',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:21',\n", "     'code': '518880',\n", "     'contract_no': '0500155301',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:21',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:20',\n", "     'code': '518890',\n", "     'contract_no': '0500154994',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:20',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:18',\n", "     'code': '518880',\n", "     'contract_no': '0500154069',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.426',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:18',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:17',\n", "     'code': '518890',\n", "     'contract_no': '0500153538',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:17',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:15',\n", "     'code': '518880',\n", "     'contract_no': '0500152822',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.426',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.426',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.40',\n", "     'trade_avg_price': '7.426',\n", "     'trade_income': '',\n", "     'trade_money': '2970.40',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:15',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:14',\n", "     'code': '518890',\n", "     'contract_no': '0500152552',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:14',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:12',\n", "     'code': '518880',\n", "     'contract_no': '0500151628',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:12',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:11',\n", "     'code': '518890',\n", "     'contract_no': '0500151340',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:11',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:09',\n", "     'code': '518880',\n", "     'contract_no': '0500150498',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:09',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:08',\n", "     'code': '518890',\n", "     'contract_no': '0500149995',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:08',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:06',\n", "     'code': '518880',\n", "     'contract_no': '0500149353',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '300',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '300',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2227.50',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2227.50',\n", "     'trade_num': '300',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:06',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:05',\n", "     'code': '518890',\n", "     'contract_no': '0500149043',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '300',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '300',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2233.20',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2233.20',\n", "     'trade_num': '300',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:05',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:04',\n", "     'code': '518880',\n", "     'contract_no': '0500148420',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:04',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:02',\n", "     'code': '518890',\n", "     'contract_no': '0500147783',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.444',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.60',\n", "     'trade_avg_price': '7.444',\n", "     'trade_income': '',\n", "     'trade_money': '2977.60',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:02',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:01',\n", "     'code': '518880',\n", "     'contract_no': '0500147077',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:01',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:32:00',\n", "     'code': '518890',\n", "     'contract_no': '0500146791',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:32:00',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '400',\n", "     'cancel_time': '2025-07-14 09:31:46',\n", "     'code': '518890',\n", "     'contract_no': '0500141259',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '0',\n", "     'match_price': '',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '',\n", "     'trade_avg_price': '',\n", "     'trade_income': '',\n", "     'trade_money': '0.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '8',\n", "     'trade_time': '2025-07-14 09:31:46',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '400',\n", "     'cancel_time': '2025-07-14 09:31:45',\n", "     'code': '518890',\n", "     'contract_no': '0500140463',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '0',\n", "     'match_price': '',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.444',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '',\n", "     'trade_avg_price': '',\n", "     'trade_income': '',\n", "     'trade_money': '0.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '8',\n", "     'trade_time': '2025-07-14 09:31:45',\n", "     'trade_type': '2',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:31:42',\n", "     'code': '518880',\n", "     'contract_no': '0500139560',\n", "     'dqj': '7.420',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.425',\n", "     'name': '黄金ETF',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.425',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2970.00',\n", "     'trade_avg_price': '7.425',\n", "     'trade_income': '',\n", "     'trade_money': '2970.00',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:31:42',\n", "     'trade_type': '1',\n", "     'trans_id': ''},\n", "    {'can_cancel': '0',\n", "     'cancel_num': '',\n", "     'cancel_time': '2025-07-14 09:31:41',\n", "     'code': '518890',\n", "     'contract_no': '0500139074',\n", "     'dqj': '7.422',\n", "     'end_time': '',\n", "     'market': '1',\n", "     'match_num': '400',\n", "     'match_price': '7.443',\n", "     'name': '中银黄金',\n", "     'not_cancel_num': '',\n", "     'not_trade_num': '',\n", "     'order_num': '400',\n", "     'order_price': '7.443',\n", "     'pgcode': '',\n", "     'pgname': '',\n", "     'stock_type': '0',\n", "     'total_agree': '2977.20',\n", "     'trade_avg_price': '7.443',\n", "     'trade_income': '',\n", "     'trade_money': '2977.20',\n", "     'trade_num': '400',\n", "     'trade_rate': '',\n", "     'trade_state': '2',\n", "     'trade_time': '2025-07-14 09:31:41',\n", "     'trade_type': '2',\n", "     'trans_id': ''}],\n", "   'stock_setting': [{'is_open': '1', 'order_id': '1', 'stock_shift': '1'},\n", "    {'is_open': '1', 'order_id': '2', 'stock_shift': '1/2'},\n", "    {'is_open': '1', 'order_id': '3', 'stock_shift': '1/3'},\n", "    {'is_open': '1', 'order_id': '4', 'stock_shift': '1/4'},\n", "    {'is_open': '0', 'order_id': '5', 'stock_shift': '3/4'},\n", "    {'is_open': '0', 'order_id': '6', 'stock_shift': '1/5'},\n", "    {'is_open': '0', 'order_id': '7', 'stock_shift': '2/5'},\n", "    {'is_open': '0', 'order_id': '8', 'stock_shift': '3/5'},\n", "    {'is_open': '0', 'order_id': '9', 'stock_shift': '4/5'}],\n", "   'fundsinfo': {'can_draw': '185.24',\n", "    'can_trade': '323.02',\n", "    'earn_val': '526.07',\n", "    'earn_val_today': '360.980',\n", "    'freeze_money': '0.00',\n", "    'hold_val': '58618.000',\n", "    'total_money': '58935.120'},\n", "   'orderinfo': {'order_num_all': '42', 'order_num_undone': '0'}}}}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["trade_client.trade_show()"]}, {"cell_type": "code", "execution_count": 56, "id": "f4f81909", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'retcode': '103421018', 'retmsg': '交易密码解密失败'}"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "# 创建会话并添加cookies\n", "\n", "\n", "headers = {\n", "    'Host': 'wzq.csc108.com',\n", "    'Accept': 'application/json, text/plain, */*',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue',\n", "    'Origin': 'https://wzq.csc108.com',\n", "    'Sec-Fetch-Site': 'same-origin',\n", "    'Sec-Fetch-Mode': 'cors',\n", "    'Se<PERSON>-<PERSON><PERSON>-Dest': 'empty',\n", "    'Referer': 'https://wzq.csc108.com/mp/zhongxinjiantou_oem/trade/index.f7254210.html',\n", "    'Accept-Language': 'zh-CN,zh;q=0.9',\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "}\n", "\n", "data = {\n", "    't': str(int(time.time()*1000)),\n", "    \"scene\": \"trade\",\n", "    \"come_from\": \"0\",\n", "    \"gm_flag\": \"0\"\n", "}\n", "\n", "response = session.post('https://wzq.csc108.com/cgi-bin/tradeprepare.cgi?', headers=headers, data=data)\n", "\n", "psw = ed.encrypt_password(response.json(), \"315717\")\n", "data = {\n", "    't': str(int(time.time()*1000)),\n", "    \"_appver\": \"7.0.20\",\n", "    \"_osVer\": \"Windows1064\",\n", "    \"_buildh5ver\": \"************\",\n", "    \"action\": \"2\",\n", "    \"psw\": psw,\n", "    \"is_trade\" : \"\" ,\n", "    \"cosign_pk\" : \"\" ,\n", "    \"cosign\" : \"\" ,\n", "    \"come_from\": \"0\",\n", "    \"gm_flag\": \"0\"\n", "}\n", "\n", "response = session.post('https://wzq.csc108.com/cgi-bin/tradepasswd.cgi?', headers=headers, data=data)\n", "response.json()"]}, {"cell_type": "code", "execution_count": 58, "id": "d5b0a7a3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-07-18 09:14:43] [INFO] [177070519.py:5] Cookies已保存到 cookies.json\n"]}], "source": ["if session:\n", "    try:\n", "        with open(cookies_file, 'w', encoding='utf-8') as f:\n", "            json.dump(session.cookies.get_dict(), f, indent=2)\n", "        logger.info(f\"Cookies已保存到 {cookies_file}\")\n", "    except Exception as e:\n", "        logger.error(f\"保存cookies失败: {e}\")"]}, {"cell_type": "code", "execution_count": 57, "id": "640be429", "metadata": {}, "outputs": [{"data": {"text/plain": ["<RequestsCookieJar[<PERSON><PERSON>(version=0, name='h_delisted', value='0|1751332792', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), <PERSON><PERSON>(version=0, name='h_dsxpl', value='1|20250717', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), <PERSON><PERSON>(version=0, name='h_hrepo', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_pg', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_spec', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_ttime', value='1752735021', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_xg', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_xgzq', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='h_yerk', value='0', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='oem://wzq_channel', value='fm_wzq_wx_v1_unknow_01..', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qlappid', value='wx9cf8c670ebd68ce4', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qlskey', value='110a7527a8256ec2100*bH5cHd5aca', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qluin', value='os-ppuF7XdUGL-hJG43C1yFNbBm8', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qq_logtype', value='16', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qs_openid', value='o4NN4jlQfrkl5xecDR9NKp2wFN04', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qs_unionid', value='oURPMwqW34OdDH0smHSUJPgLnaAI', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='trade_in_cmschina', value='1', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='trade_in_partner', value='1', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='wx_session_time', value='1751332780000', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='wzq_dealer', value='11100', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='wzq_qlappid', value='wx9cf8c670ebd68ce4', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='wzq_qlskey', value='110a7527a8256ec2100*bH5cHd5aca', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='wzq_qluin', value='os-ppuF7XdUGL-hJG43C1yFNbBm8', port=None, port_specified=False, domain='', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=None, discard=True, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='qlappid', value='wx9cf8c670ebd68ce4', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False), Cookie(version=0, name='qlskey', value='110a7527a8256ec2100*bH5cIabcaa', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False), Cookie(version=0, name='qluin', value='os-ppuF7XdUGL-hJG43C1yFNbBm8', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False), Cookie(version=0, name='wzq_qlappid', value='wx9cf8c670ebd68ce4', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False), Cookie(version=0, name='wzq_qlskey', value='110a7527a8256ec2100*bH5cIabcaa', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False), Cookie(version=0, name='wzq_qluin', value='os-ppuF7XdUGL-hJG43C1yFNbBm8', port=None, port_specified=False, domain='.csc108.com', domain_specified=True, domain_initial_dot=False, path='/', path_specified=True, secure=False, expires=1752887600, discard=False, comment=None, comment_url=None, rest={}, rfc2109=False)]>"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["session.cookies"]}, {"cell_type": "code", "execution_count": 46, "id": "1eedf6e9", "metadata": {}, "outputs": [], "source": ["from encrypt_password import TimeSeedHelper\n", "encrypt = TimeSeedHelper(17527450902791083689)"]}, {"cell_type": "code", "execution_count": 50, "id": "80f4e79a", "metadata": {}, "outputs": [], "source": ["import encrypt_password as ed"]}, {"cell_type": "code", "execution_count": 51, "id": "ac0a8dff", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'timeseed'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[51]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43med\u001b[49m\u001b[43m.\u001b[49m\u001b[43mencrypt_password\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mjson\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m315717\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Downloads\\PythonProjects\\cursor-ai-project\\auto-trading\\encrypt_password.py:88\u001b[39m, in \u001b[36mencrypt_password\u001b[39m\u001b[34m(config, password)\u001b[39m\n\u001b[32m     84\u001b[39m ts_helper = TimeSeedHelper(config[\u001b[33m\"\u001b[39m\u001b[33mtimeseed\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m     86\u001b[39m \u001b[38;5;66;03m# 构造待加密明文:\u001b[39;00m\n\u001b[32m     87\u001b[39m \u001b[38;5;66;03m# 格式: _transTimeSeed(time_seed_prefix) + time_code + password\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m88\u001b[39m time_seed_prefix = ts_helper.get()  \u001b[38;5;66;03m# 前20字符\u001b[39;00m\n\u001b[32m     89\u001b[39m time_code = ts_helper.get_time_code()  \u001b[38;5;66;03m# MD5前14字符\u001b[39;00m\n\u001b[32m     91\u001b[39m \u001b[38;5;66;03m# 转换时间种子前缀 (16进制字符串 → 原始字符串)\u001b[39;00m\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'timeseed'"]}], "source": ["ed.encrypt_password(response.json(), \"315717\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}