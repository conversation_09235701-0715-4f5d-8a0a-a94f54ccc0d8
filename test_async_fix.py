#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试异步交易客户端修复
"""

import json
import asyncio
import httpx
import logging
from citic_trader.async_trade import AsyncTradeClient
from citic_trader.utils import setup_logger

async def test_async_client():
    """测试异步客户端"""
    
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    
    # 加载cookies
    try:
        with open("cookies.json", 'r', encoding='utf-8') as f:
            cookies = json.load(f)
    except Exception as e:
        print(f"加载cookies失败: {e}")
        return
    
    # 创建异步会话
    async with httpx.AsyncClient(verify=False) as session:
        # 添加cookies
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        # 用户信息
        user_info = {
            "account_id": "",
            "stockholder_code": "",
            "psw_session": "",
            "app_info": {
                "_appver": "7.0.20",
                "_osVer": "Windows1064",
                "_buildh5ver": "************"
            }
        }
        
        # 创建异步交易客户端
        async_client = AsyncTradeClient(session, user_info, logger)
        
        # 初始化客户端
        await async_client.initialize()
        
        # 测试获取股票信息
        print("测试获取股票信息...")
        stock_info = await async_client.get_stock_info_async("518880")
        print(f"股票信息状态: {stock_info.get('status')}")
        
        if stock_info.get('status') == 'success':
            print(f"股票名称: {stock_info['data'].get('name')}")
            print(f"当前价格: {stock_info['data'].get('price')}")
        else:
            print(f"获取股票信息失败: {stock_info.get('message')}")
        
        print("异步客户端测试完成")

if __name__ == "__main__":
    asyncio.run(test_async_client())
