"""
价格风险控制模块

提供价格风险控制功能，包括止损止盈、波动监控和价格偏离检测
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import os
import numpy as np
from collections import deque

class PriceRiskController:
    """
    价格风险控制器
    
    控制价格相关的风险，包括止损止盈、波动监控和价格偏离检测
    """
    
    def __init__(self, config: Dict[str, Any] = None, logger: Optional[logging.Logger] = None):
        """
        初始化价格风险控制器
        
        Args:
            config: 配置字典，包含风险控制参数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "stop_loss_ratio": 0.05,           # 止损比例 (相对成本价)
            "take_profit_ratio": 0.10,         # 止盈比例 (相对成本价)
            "volatility_window": 20,           # 波动性计算窗口大小
            "max_volatility": 0.03,            # 最大允许波动率
            "price_deviation_threshold": 0.05, # 价格偏离阈值 (相对参考价)
            "enable_stop_loss": True,          # 是否启用止损
            "enable_take_profit": True,        # 是否启用止盈
            "enable_volatility_control": True, # 是否启用波动性控制
            "enable_deviation_control": True   # 是否启用价格偏离控制
        }
        
        # 合并配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化价格历史数据
        self.price_history = {}                 # 股票价格历史 {code: deque([price, timestamp])}
        self.stock_positions = {}               # 持仓信息 {code: {"amount": int, "cost": float}}
        self.stock_custom_settings = {}         # 股票自定义设置 {code: {"stop_loss": float, "take_profit": float}}
        self.reference_prices = {}              # 参考价格 {code: float}
        
        self.logger.info(f"价格风险控制器初始化完成, 配置: {json.dumps(self.config, ensure_ascii=False)}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        self.config.update(new_config)
        self.logger.info(f"价格风险控制器配置已更新: {json.dumps(new_config, ensure_ascii=False)}")
    
    def update_positions(self, positions: Dict[str, Dict[str, float]]):
        """
        更新持仓信息
        
        Args:
            positions: 持仓信息，格式: {code: {'amount': amount, 'cost': cost}}
        """
        self.stock_positions = positions
        self.logger.info(f"持仓信息已更新，共 {len(positions)} 个股票")
    
    def update_price(self, code: str, price: float, timestamp: Optional[datetime] = None):
        """
        更新股票价格
        
        Args:
            code: 股票代码
            price: 当前价格
            timestamp: 时间戳，默认为当前时间
        """
        if timestamp is None:
            timestamp = datetime.now()
            
        # 初始化价格历史队列
        if code not in self.price_history:
            self.price_history[code] = deque(maxlen=self.config["volatility_window"])
        
        # 添加价格记录
        self.price_history[code].append((price, timestamp))
        
        # 如果没有参考价格，设置当前价格为参考价格
        if code not in self.reference_prices:
            self.reference_prices[code] = price
            
        # 日志记录，但不是每次都记录以避免日志过多
        if len(self.price_history[code]) == 1 or len(self.price_history[code]) % 10 == 0:
            self.logger.debug(f"更新 {code} 价格: {price}")
    
    def set_custom_thresholds(self, code: str, stop_loss: Optional[float] = None, take_profit: Optional[float] = None):
        """
        为特定股票设置自定义止损止盈阈值
        
        Args:
            code: 股票代码
            stop_loss: 止损价格，如果为None则使用全局配置
            take_profit: 止盈价格，如果为None则使用全局配置
        """
        if code not in self.stock_custom_settings:
            self.stock_custom_settings[code] = {}
            
        if stop_loss is not None:
            self.stock_custom_settings[code]["stop_loss"] = stop_loss
            self.logger.info(f"设置 {code} 自定义止损价格: {stop_loss}")
            
        if take_profit is not None:
            self.stock_custom_settings[code]["take_profit"] = take_profit
            self.logger.info(f"设置 {code} 自定义止盈价格: {take_profit}")
    
    def set_reference_price(self, code: str, price: float):
        """
        设置股票参考价格，用于价格偏离检测
        
        Args:
            code: 股票代码
            price: 参考价格
        """
        self.reference_prices[code] = price
        self.logger.info(f"设置 {code} 参考价格: {price}")
    
    def calculate_volatility(self, code: str) -> float:
        """
        计算股票价格波动率
        
        Args:
            code: 股票代码
            
        Returns:
            float: 波动率
        """
        if code not in self.price_history or len(self.price_history[code]) < 2:
            return 0.0
            
        # 提取价格历史
        prices = [item[0] for item in self.price_history[code]]
        
        if len(prices) < 2:
            return 0.0
            
        # 计算收益率
        returns = np.diff(prices) / prices[:-1]
        
        # 计算标准差
        volatility = np.std(returns)
        
        return volatility
    
    def check_risk(self, trade_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        检查价格风险
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
            
        Returns:
            Tuple[bool, Dict]: (是否通过风险检查, 风险详情)
        """
        # 提取交易信息
        code = trade_info.get('code', '')
        price = float(trade_info.get('price', 0))
        direction = trade_info.get('direction', 'buy')  # 'buy' 或 'sell'
        
        # 初始化风险检查结果
        risk_details = {
            "stop_loss": {
                "passed": True,
                "threshold": 0.0,
                "actual": price,
                "message": ""
            },
            "take_profit": {
                "passed": True,
                "threshold": 0.0,
                "actual": price,
                "message": ""
            },
            "volatility": {
                "passed": True,
                "threshold": self.config["max_volatility"],
                "actual": 0.0,
                "message": ""
            },
            "price_deviation": {
                "passed": True,
                "threshold": self.config["price_deviation_threshold"],
                "actual": 0.0,
                "message": ""
            }
        }
        
        # 1. 检查止损止盈（仅对卖出订单且有持仓）
        if direction == 'sell' and code in self.stock_positions:
            position = self.stock_positions[code]
            cost = position.get('cost', 0.0)
            
            if cost > 0:
                # 计算价格变动比例
                price_change_ratio = (price - cost) / cost
                
                # 检查止损条件
                if self.config["enable_stop_loss"] and price_change_ratio < 0:
                    # 获取止损阈值（优先使用自定义设置）
                    stop_loss_threshold = self.stock_custom_settings.get(code, {}).get("stop_loss")
                    if stop_loss_threshold is None:
                        stop_loss_threshold = cost * (1 - self.config["stop_loss_ratio"])
                    
                    risk_details["stop_loss"]["threshold"] = stop_loss_threshold
                    
                    # 检查是否触发止损
                    if price < stop_loss_threshold:
                        risk_details["stop_loss"]["passed"] = False
                        risk_details["stop_loss"]["message"] = f"价格 {price:.2f} 低于止损阈值 {stop_loss_threshold:.2f} (成本价 {cost:.2f})"
                        self.logger.warning(risk_details["stop_loss"]["message"])
                
                # 检查止盈条件
                if self.config["enable_take_profit"] and price_change_ratio > 0:
                    # 获取止盈阈值（优先使用自定义设置）
                    take_profit_threshold = self.stock_custom_settings.get(code, {}).get("take_profit")
                    if take_profit_threshold is None:
                        take_profit_threshold = cost * (1 + self.config["take_profit_ratio"])
                    
                    risk_details["take_profit"]["threshold"] = take_profit_threshold
                    
                    # 检查是否触发止盈
                    if price > take_profit_threshold:
                        risk_details["take_profit"]["passed"] = False
                        risk_details["take_profit"]["message"] = f"价格 {price:.2f} 高于止盈阈值 {take_profit_threshold:.2f} (成本价 {cost:.2f})"
                        self.logger.info(risk_details["take_profit"]["message"])
        
        # 2. 检查价格波动（对所有订单）
        if self.config["enable_volatility_control"]:
            volatility = self.calculate_volatility(code)
            risk_details["volatility"]["actual"] = volatility
            
            if volatility > self.config["max_volatility"]:
                risk_details["volatility"]["passed"] = False
                risk_details["volatility"]["message"] = f"股票 {code} 波动率 {volatility:.4f} 超过阈值 {self.config['max_volatility']:.4f}"
                self.logger.warning(risk_details["volatility"]["message"])
        
        # 3. 检查价格偏离（对所有订单）
        if self.config["enable_deviation_control"] and code in self.reference_prices:
            reference_price = self.reference_prices[code]
            
            if reference_price > 0:
                # 计算价格偏离比例
                deviation_ratio = abs(price - reference_price) / reference_price
                risk_details["price_deviation"]["actual"] = deviation_ratio
                
                if deviation_ratio > self.config["price_deviation_threshold"]:
                    risk_details["price_deviation"]["passed"] = False
                    risk_details["price_deviation"]["message"] = (
                        f"股票 {code} 当前价格 {price:.2f} 与参考价格 {reference_price:.2f} "
                        f"偏离 {deviation_ratio:.2%}，超过阈值 {self.config['price_deviation_threshold']:.2%}"
                    )
                    self.logger.warning(risk_details["price_deviation"]["message"])
        
        # 综合所有风险检查结果
        # 注意：止盈不是真正的风险，触发止盈时不应该阻止交易
        all_passed = (
            risk_details["stop_loss"]["passed"] and 
            risk_details["volatility"]["passed"] and 
            risk_details["price_deviation"]["passed"]
        )
        
        if all_passed:
            self.logger.info(f"价格风险检查通过: {code}, {price}")
        else:
            self.logger.warning(f"价格风险检查未通过: {code}, {price}")
        
        return all_passed, risk_details
    
    def get_stop_loss_price(self, code: str) -> Optional[float]:
        """
        获取股票的止损价格
        
        Args:
            code: 股票代码
            
        Returns:
            Optional[float]: 止损价格，如果没有持仓则返回None
        """
        if code not in self.stock_positions:
            return None
            
        # 优先使用自定义止损价格
        if code in self.stock_custom_settings and "stop_loss" in self.stock_custom_settings[code]:
            return self.stock_custom_settings[code]["stop_loss"]
        
        # 否则根据成本价和止损比例计算
        cost = self.stock_positions[code].get('cost', 0.0)
        if cost > 0:
            return cost * (1 - self.config["stop_loss_ratio"])
        
        return None
    
    def get_take_profit_price(self, code: str) -> Optional[float]:
        """
        获取股票的止盈价格
        
        Args:
            code: 股票代码
            
        Returns:
            Optional[float]: 止盈价格，如果没有持仓则返回None
        """
        if code not in self.stock_positions:
            return None
            
        # 优先使用自定义止盈价格
        if code in self.stock_custom_settings and "take_profit" in self.stock_custom_settings[code]:
            return self.stock_custom_settings[code]["take_profit"]
        
        # 否则根据成本价和止盈比例计算
        cost = self.stock_positions[code].get('cost', 0.0)
        if cost > 0:
            return cost * (1 + self.config["take_profit_ratio"])
        
        return None
    
    def get_volatility_info(self, code: str = None) -> Dict[str, Any]:
        """
        获取股票波动率信息
        
        Args:
            code: 股票代码，如果为None则返回所有股票
            
        Returns:
            Dict: 波动率信息
        """
        if code:
            return {
                "code": code,
                "volatility": self.calculate_volatility(code),
                "window": self.config["volatility_window"],
                "max_volatility": self.config["max_volatility"],
                "price_count": len(self.price_history.get(code, []))
            }
        else:
            # 返回所有股票的波动率信息
            volatility_info = {}
            for stock_code in self.price_history:
                volatility_info[stock_code] = {
                    "volatility": self.calculate_volatility(stock_code),
                    "window": self.config["volatility_window"],
                    "max_volatility": self.config["max_volatility"],
                    "price_count": len(self.price_history[stock_code])
                }
            return volatility_info
    
    def get_price_monitoring_info(self, code: str) -> Dict[str, Any]:
        """
        获取股票价格监控信息
        
        Args:
            code: 股票代码
            
        Returns:
            Dict: 价格监控信息
        """
        info = {
            "code": code,
            "current_price": None,
            "reference_price": self.reference_prices.get(code),
            "stop_loss": self.get_stop_loss_price(code),
            "take_profit": self.get_take_profit_price(code),
            "volatility": self.calculate_volatility(code),
            "position": self.stock_positions.get(code)
        }
        
        # 获取最新价格
        if code in self.price_history and self.price_history[code]:
            info["current_price"] = self.price_history[code][-1][0]
            
        return info 