#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
市场数据模块测试脚本

用于测试通达信行情数据源和实时数据管理器的功能
"""

import sys
import time
import logging
import pandas as pd
import matplotlib.pyplot as plt
from citic_trader.utils import setup_logger, load_config
from market_data.tdx_data_source import TdxDataSource

# from market_data import (
#     get_real_time_data_manager,
#     connect_market_data_sources,
#     disconnect_market_data_sources
# )

# 设置日志
logger = setup_logger(log_to_file=True, log_level=logging.INFO)

def test_connection():
    """
    测试市场数据源的连接功能
    """
    logger.info("开始测试市场数据源连接")
    
    # 加载配置
    try:
        config = load_config("config.json")
        tdx_config = config.get("tdx_config", {})
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return False

    data_source = None
    try:
        # 初始化TdxDataSource，它将根据配置自动选择最优服务器
        logger.info("初始化 TdxDataSource...")
        data_source = TdxDataSource(tdx_config=tdx_config, logger=logger, auto_connect=True)
        
        if data_source.connected:
            logger.info(f"市场数据源连接成功，当前连接到: {data_source.current_server.get('name', '')} ({data_source.current_server['ip']}:{data_source.current_server['port']})")
            return True
        else:
            logger.error("市场数据源连接失败")
            return False
    except Exception as e:
        logger.error(f"测试市场数据源连接时发生错误: {e}")
        return False
    finally:
        if data_source and data_source.connected:
            logger.info("正在测试断开连接...")
            if data_source.disconnect():
                logger.info("市场数据源断开连接成功")
            else:
                logger.error("市场数据源断开连接失败")

def test_realtime_price(symbols=None):
    """
    测试实时价格获取功能
    
    Args:
        symbols: 要测试的证券代码列表
    """
    if symbols is None:
        # 默认测试股票和ETF
        symbols = ["600000", "000001", "518880", "510050"]
    
    logger.info(f"开始测试实时价格获取，证券代码: {symbols}")
    
    # 连接市场数据源
    if not test_connection():
        logger.error("市场数据源连接失败，无法测试实时价格获取")
        return False
    
    try:
        # 获取实时数据管理器
        rtd_manager = get_real_time_data_manager()
        
        # 获取并显示实时价格
        for symbol in symbols:
            logger.info(f"获取 {symbol} 的实时价格...")
            price_data = rtd_manager.get_price(symbol)
            logger.info(f"实时价格数据: {price_data}")
        
        return True
    except Exception as e:
        logger.error(f"测试实时价格获取时发生错误: {e}")
        return False
    finally:
        # 确保断开连接
        disconnect_market_data_sources()

def test_k_data(symbol="518880", period=5, count=100):
    """
    测试K线数据获取功能
    
    Args:
        symbol: 证券代码
        period: K线周期（分钟）
        count: 获取的K线数量
    """
    logger.info(f"开始测试K线数据获取, 证券代码: {symbol}, 周期: {period}分钟, 数量: {count}")
    
    # 连接市场数据源
    if not test_connection():
        logger.error("市场数据源连接失败，无法测试K线数据获取")
        return False
    
    try:
        # 获取实时数据管理器
        rtd_manager = get_real_time_data_manager()
        
        # 获取K线数据
        logger.info(f"获取 {symbol} 的K线数据...")
        k_data = rtd_manager.get_k_data(symbol, period, count)
        
        # 显示K线数据
        if isinstance(k_data, pd.DataFrame) and not k_data.empty:
            logger.info(f"成功获取 {len(k_data)} 条K线数据")
            logger.info(f"K线数据头部:\n{k_data.head()}")
            logger.info(f"K线数据尾部:\n{k_data.tail()}")
            
            # 绘制K线图
            try:
                plt.figure(figsize=(12, 6))
                plt.plot(k_data.index, k_data['close'])
                plt.title(f"{symbol} 收盘价 ({period}分钟)")
                plt.xlabel("时间")
                plt.ylabel("价格")
                plt.grid(True)
                plt.tight_layout()
                plt.savefig(f"{symbol}_{period}m_kline.png")
                logger.info(f"K线图已保存为 {symbol}_{period}m_kline.png")
            except Exception as e:
                logger.warning(f"绘制K线图时发生错误: {e}")
            
            return True
        else:
            logger.error(f"获取 {symbol} 的K线数据失败或数据为空")
            return False
    except Exception as e:
        logger.error(f"测试K线数据获取时发生错误: {e}")
        return False
    finally:
        # 确保断开连接
        disconnect_market_data_sources()

def test_auto_update():
    """
    测试自动更新功能
    """
    logger.info("开始测试实时数据自动更新功能")
    
    # 连接市场数据源
    if not test_connection():
        logger.error("市场数据源连接失败，无法测试自动更新功能")
        return False
    
    try:
        # 获取实时数据管理器
        rtd_manager = get_real_time_data_manager()
        
        # 设置要监控的股票列表
        symbols = ["600000", "000001", "518880", "510050"]
        logger.info(f"设置监控股票列表: {symbols}")
        
        # 启动自动更新
        logger.info("启动自动更新...")
        rtd_manager.start_auto_update(symbols, interval=5)
        
        # 等待并每秒获取一次价格
        logger.info("等待数据更新 (30秒)...")
        for i in range(30):
            time.sleep(1)
            
            # 每5秒获取一次所有股票的价格
            if i % 5 == 0:
                logger.info(f"第 {i+1} 秒:")
                for symbol in symbols:
                    price_data = rtd_manager.get_price(symbol)
                    logger.info(f"  {symbol}: {price_data}")
        
        # 停止自动更新
        logger.info("停止自动更新...")
        rtd_manager.stop_auto_update()
        
        return True
    except Exception as e:
        logger.error(f"测试自动更新功能时发生错误: {e}")
        return False
    finally:
        # 确保断开连接
        disconnect_market_data_sources()

if __name__ == "__main__":
    # 根据命令行参数执行不同的测试
    if len(sys.argv) > 1:
        if sys.argv[1] == "connection":
            test_connection()
        elif sys.argv[1] == "price":
            symbols = sys.argv[2:] if len(sys.argv) > 2 else None
            test_realtime_price(symbols)
        elif sys.argv[1] == "kdata":
            symbol = sys.argv[2] if len(sys.argv) > 2 else "518880"
            period = int(sys.argv[3]) if len(sys.argv) > 3 else 5
            count = int(sys.argv[4]) if len(sys.argv) > 4 else 100
            test_k_data(symbol, period, count)
        elif sys.argv[1] == "auto":
            test_auto_update()
        else:
            print("用法: python test_market_data.py [connection|price [symbols...]|kdata [symbol [period [count]]]|auto]")
    else:
        # 默认运行连接测试和实时价格测试
        if test_connection():
            test_realtime_price() 