"""
执行风险控制模块

提供执行风险控制功能，包括交易验证、错误处理和紧急停止功能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime, timedelta
import json
import os
import threading
import time

class ExecutionRiskController:
    """
    执行风险控制器
    
    控制与交易执行相关的风险，提供交易验证、错误处理和紧急停止功能
    """
    
    def __init__(self, config: Dict[str, Any] = None, logger: Optional[logging.Logger] = None):
        """
        初始化执行风险控制器
        
        Args:
            config: 配置字典，包含风险控制参数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "max_consecutive_errors": 3,       # 最大连续错误次数
            "retry_interval": 5,               # 重试间隔 (秒)
            "max_retry_count": 3,              # 最大重试次数
            "error_cool_down": 300,            # 错误冷却时间 (秒)
            "enable_verification": True,       # 是否启用交易验证
            "enable_emergency_stop": True,     # 是否启用紧急停止
            "enable_retry_mechanism": True     # 是否启用重试机制
        }
        
        # 合并配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化执行风险控制状态
        self.emergency_stop = False             # 紧急停止标志
        self.error_counts = {}                  # 各类错误计数 {error_type: count}
        self.consecutive_errors = 0             # 连续错误次数
        self.last_error_time = None             # 最后错误时间
        self.last_trade_verification = {}       # 最后交易验证结果 {order_id: result}
        self.error_records = []                 # 错误记录
        self.callbacks = {}                     # 回调函数 {event_type: [callback1, callback2, ...]}
        
        self.logger.info(f"执行风险控制器初始化完成, 配置: {json.dumps(self.config, ensure_ascii=False)}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        self.config.update(new_config)
        self.logger.info(f"执行风险控制器配置已更新: {json.dumps(new_config, ensure_ascii=False)}")
    
    def register_callback(self, event_type: str, callback: Callable):
        """
        注册回调函数
        
        Args:
            event_type: 事件类型，如 'emergency_stop', 'error', 'verification_failed'
            callback: 回调函数
        """
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        
        self.callbacks[event_type].append(callback)
        self.logger.info(f"已为事件 {event_type} 注册回调函数")
    
    def trigger_callbacks(self, event_type: str, data: Dict[str, Any] = None):
        """
        触发回调函数
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type not in self.callbacks:
            return
            
        for callback in self.callbacks[event_type]:
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"执行回调函数时出错: {e}")
    
    def emergency_stop_trading(self, reason: str):
        """
        紧急停止交易
        
        Args:
            reason: 停止原因
        """
        if not self.emergency_stop:
            self.emergency_stop = True
            self.logger.critical(f"紧急停止交易: {reason}")
            
            # 触发紧急停止回调
            self.trigger_callbacks("emergency_stop", {"reason": reason, "timestamp": datetime.now().isoformat()})
    
    def reset_emergency_stop(self):
        """
        重置紧急停止状态
        """
        if self.emergency_stop:
            self.emergency_stop = False
            self.logger.info("紧急停止状态已重置")
            
            # 触发紧急停止重置回调
            self.trigger_callbacks("emergency_stop_reset", {"timestamp": datetime.now().isoformat()})
    
    def is_emergency_stop_active(self) -> bool:
        """
        检查紧急停止是否激活
        
        Returns:
            bool: 紧急停止是否激活
        """
        return self.emergency_stop
    
    def record_error(self, error_type: str, error_message: str, trade_info: Optional[Dict[str, Any]] = None):
        """
        记录错误
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            trade_info: 交易信息
        """
        # 更新错误计数
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        self.consecutive_errors += 1
        self.last_error_time = datetime.now()
        
        # 记录错误
        error_record = {
            "timestamp": self.last_error_time.isoformat(),
            "error_type": error_type,
            "error_message": error_message,
            "trade_info": trade_info
        }
        self.error_records.append(error_record)
        
        # 日志记录
        self.logger.error(f"交易错误: {error_type} - {error_message}")
        
        # 触发错误回调
        self.trigger_callbacks("error", error_record)
        
        # 检查是否需要紧急停止
        if self.config["enable_emergency_stop"] and self.consecutive_errors >= self.config["max_consecutive_errors"]:
            self.emergency_stop_trading(f"连续 {self.consecutive_errors} 次错误: {error_type}")
    
    def record_success(self):
        """
        记录成功
        """
        # 重置连续错误计数
        self.consecutive_errors = 0
    
    def should_retry(self, error_type: str) -> bool:
        """
        判断是否应该重试
        
        Args:
            error_type: 错误类型
            
        Returns:
            bool: 是否应该重试
        """
        if not self.config["enable_retry_mechanism"]:
            return False
            
        # 如果紧急停止已激活，不再重试
        if self.emergency_stop:
            return False
            
        # 检查错误次数是否超过最大重试次数
        if self.error_counts.get(error_type, 0) > self.config["max_retry_count"]:
            self.logger.warning(f"错误 {error_type} 已达到最大重试次数 {self.config['max_retry_count']}")
            return False
            
        # 检查是否在冷却期内
        if self.last_error_time:
            cool_down_end = self.last_error_time + timedelta(seconds=self.config["error_cool_down"])
            if datetime.now() > cool_down_end:
                # 冷却期已过，重置错误计数
                self.error_counts[error_type] = 0
        
        return True
    
    def verify_trade(self, order_info: Dict[str, Any], verification_result: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证交易
        
        Args:
            order_info: 订单信息
            verification_result: 验证结果
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 验证消息)
        """
        if not self.config["enable_verification"]:
            return True, "交易验证已禁用"
            
        # 提取订单信息
        order_id = order_info.get('order_id', '')
        
        # 保存验证结果
        self.last_trade_verification[order_id] = verification_result
        
        # 检查验证结果
        is_verified = verification_result.get('verified', False)
        verify_message = verification_result.get('message', '')
        
        if not is_verified:
            self.logger.warning(f"交易验证失败: {order_id} - {verify_message}")
            
            # 触发验证失败回调
            self.trigger_callbacks("verification_failed", {
                "order_id": order_id,
                "verification_result": verification_result,
                "timestamp": datetime.now().isoformat()
            })
        else:
            self.logger.info(f"交易验证通过: {order_id}")
        
        return is_verified, verify_message
    
    def wait_and_retry(self, retry_function: Callable, error_type: str, max_retries: int = None) -> Any:
        """
        等待并重试函数
        
        Args:
            retry_function: 重试函数
            error_type: 错误类型
            max_retries: 最大重试次数，如果为None则使用配置中的值
            
        Returns:
            Any: 函数返回值
        """
        if max_retries is None:
            max_retries = self.config["max_retry_count"]
            
        retries = 0
        last_error = None
        
        while retries < max_retries:
            try:
                result = retry_function()
                self.record_success()
                return result
            except Exception as e:
                last_error = e
                retries += 1
                self.record_error(error_type, str(e))
                
                if not self.should_retry(error_type) or retries >= max_retries:
                    break
                    
                # 等待重试间隔
                self.logger.info(f"等待 {self.config['retry_interval']} 秒后重试, 当前重试次数: {retries}/{max_retries}")
                time.sleep(self.config["retry_interval"])
        
        # 如果所有重试都失败，抛出最后一个错误
        if last_error:
            raise last_error
        
        return None
    
    def check_risk(self) -> Tuple[bool, Dict[str, Any]]:
        """
        检查执行风险
        
        Returns:
            Tuple[bool, Dict]: (是否通过风险检查, 风险详情)
        """
        # 初始化风险检查结果
        risk_details = {
            "emergency_stop": {
                "passed": not self.emergency_stop,
                "message": "紧急停止已激活" if self.emergency_stop else ""
            },
            "consecutive_errors": {
                "passed": self.consecutive_errors < self.config["max_consecutive_errors"],
                "limit": self.config["max_consecutive_errors"],
                "actual": self.consecutive_errors,
                "message": f"连续错误次数 {self.consecutive_errors} 已达到阈值 {self.config['max_consecutive_errors']}" if self.consecutive_errors >= self.config["max_consecutive_errors"] else ""
            }
        }
        
        # 综合所有风险检查结果
        all_passed = all(detail["passed"] for detail in risk_details.values())
        
        if all_passed:
            self.logger.info("执行风险检查通过")
        else:
            self.logger.warning("执行风险检查未通过")
        
        return all_passed, risk_details
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息
        
        Returns:
            Dict: 错误统计信息
        """
        return {
            "total_errors": sum(self.error_counts.values()),
            "consecutive_errors": self.consecutive_errors,
            "error_types": dict(self.error_counts),
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None,
            "emergency_stop": self.emergency_stop
        }
    
    def save_error_records(self, file_path: str):
        """
        保存错误记录到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.error_records, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"错误记录已保存到 {file_path}")
        except Exception as e:
            self.logger.error(f"保存错误记录失败: {e}")
    
    def start_monitoring(self, monitor_interval: int = 60):
        """
        启动风险监控线程
        
        Args:
            monitor_interval: 监控间隔 (秒)
        """
        def monitor_thread():
            self.logger.info("风险监控线程已启动")
            while True:
                try:
                    # 检查风险
                    self.check_risk()
                    
                    # 等待下次检查
                    time.sleep(monitor_interval)
                except Exception as e:
                    self.logger.error(f"风险监控线程发生错误: {e}")
                    time.sleep(monitor_interval)
        
        # 创建并启动监控线程
        thread = threading.Thread(target=monitor_thread, daemon=True)
        thread.start()
        self.logger.info(f"风险监控线程已启动，监控间隔: {monitor_interval}秒")
        
        return thread 