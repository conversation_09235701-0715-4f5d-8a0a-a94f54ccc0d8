#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中信建投交易账户信息查询示例脚本

展示如何使用trade_show方法获取账户持仓和资金信息
"""

import json
import time
import logging
import requests
from typing import Dict, Any

from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """
    从文件加载cookies
    
    Args:
        cookies_file: cookies文件路径，JSON格式
        
    Returns:
        Dict: cookies字典
    """
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """
    设置会话，添加cookies
    
    Args:
        cookies: cookies字典
        
    Returns:
        requests.Session: 会话对象
    """
    session = requests.Session()
    
    # 添加cookies到会话
    for key, value in cookies.items():
        session.cookies.set(key, value)
    
    return session

def setup_user_info() -> Dict[str, Any]:
    """
    设置用户信息
    
    Returns:
        Dict: 用户信息字典
    """
    # 这里需要填写您的账户信息
    return {
        "account_id": "您的账户ID",  # 请替换为实际账户ID
        "stockholder_code": "",  # 会自动从trade_show接口获取
        "psw_session": "",  # 通常从cookies中获取
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def print_position_info(position: Dict[str, Any]) -> None:
    """
    打印持仓信息
    
    Args:
        position: 持仓信息字典
    """
    print(f"代码: {position.get('code')}, 名称: {position.get('name')}")
    print(f"  持仓数量: {position.get('hold_num')}份")
    print(f"  可用数量: {position.get('can_use')}份")
    print(f"  最新价格: {position.get('new_price')}元")
    print(f"  持仓成本: {position.get('hold_cost')}元")
    print(f"  持仓市值: {position.get('hold_val')}元")
    print(f"  持仓盈亏: {position.get('earn_val')}元")
    print(f"  盈亏比例: {position.get('earn_per')}%")
    print(f"  今日盈亏: {position.get('earn_val_day')}元")
    print(f"  股东代码: {position.get('stockholder_code')}")

def print_order_info(order: Dict[str, Any]) -> None:
    """
    打印委托信息
    
    Args:
        order: 委托信息字典
    """
    # 交易类型: 1-买入, 2-卖出
    trade_type = "买入" if order.get('trade_type') == "1" else "卖出" if order.get('trade_type') == "2" else "未知"
    
    # 交易状态: 0-未报, 1-待报, 2-已报, 3-已报待撤, 4-部成待撤, 5-部撤, 6-已撤, 7-部成, 8-已成, 9-废单
    trade_state_map = {
        "0": "未报", "1": "待报", "2": "已报", "3": "已报待撤", 
        "4": "部成待撤", "5": "部撤", "6": "已撤", "7": "部成", "8": "已成", "9": "废单"
    }
    trade_state = trade_state_map.get(order.get('trade_state'), "未知")
    
    print(f"合同编号: {order.get('contract_no')}")
    print(f"  代码: {order.get('code')}, 名称: {order.get('name')}")
    print(f"  交易类型: {trade_type}")
    print(f"  委托价格: {order.get('order_price')}元")
    print(f"  委托数量: {order.get('order_num')}份")
    print(f"  成交数量: {order.get('match_num')}份")
    print(f"  成交价格: {order.get('match_price')}元")
    print(f"  成交金额: {order.get('trade_money')}元")
    print(f"  交易状态: {trade_state}")
    print(f"  交易时间: {order.get('trade_time')}")
    print(f"  撤单时间: {order.get('cancel_time')}")

def main():
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    logger.info("开始执行账户信息查询示例")
    
    # 加载cookies (请替换为您的cookies文件路径)
    cookies_file = "cookies.json"
    cookies = load_cookies(cookies_file)

    if not cookies:
        logger.error("未找到有效的cookies，请先保存cookies到文件")
        return
    
    # 设置会话
    session = requests.Session()
    for key, value in cookies.items():
        session.cookies.set(key, value)
    
    # 设置用户信息
    user_info = setup_user_info()
    
    # 创建交易客户端
    trade_client = TradeClient(session, user_info, logger)
    logger.info("交易客户端创建成功")
    
    try:
        # 获取账户信息
        print("\n===== 获取账户信息 =====")
        account_info = trade_client.trade_show()
        
        if account_info.get("status") == "success":
            # 打印资金信息
            print("\n----- 资金信息 -----")
            funds = account_info["data"]["funds"]
            print(f"总资产: {funds['total_assets']}元")
            print(f"可用资金: {funds['available_cash']}元")
            print(f"可取资金: {funds['withdrawable_cash']}元")
            print(f"冻结资金: {funds['frozen_cash']}元")
            print(f"持仓市值: {funds['market_value']}元")
            print(f"总盈亏: {funds['total_profit']}元")
            print(f"今日盈亏: {funds['today_profit']}元")
            
            # 打印交易限制
            print("\n----- 交易限制 -----")
            limits = account_info["data"]["limits"]
            print(f"最大可买入金额: {limits['max_buy_money']}元")
            print(f"最大可卖出数量: {limits['max_sell_qty']}份")
            
            # 打印持仓信息
            print("\n----- 持仓信息 -----")
            positions = account_info["data"]["positions"]
            if positions:
                for position in positions:
                    print_position_info(position)
                    print("---")
            else:
                print("无持仓")
            
            # 打印委托信息
            print("\n----- 委托信息 -----")
            orders = account_info["data"]["orders"]
            if orders:
                # 只显示最近5条委托记录
                for order in orders[:5]:
                    print_order_info(order)
                    print("---")
                
                if len(orders) > 5:
                    print(f"... 还有 {len(orders) - 5} 条委托记录未显示")
            else:
                print("无委托记录")
            
            # 获取股东代码
            print("\n----- 股东代码 -----")
            stockholder_code = account_info["data"]["stockholder_code"]
            print(f"股东代码: {stockholder_code}")
            
        else:
            logger.error(f"获取账户信息失败: {account_info.get('message', '未知错误')}")
            
        # 查询特定股票的持仓
        print("\n===== 查询特定股票持仓 =====")
        stock_code = "161130"  # 纳指LOF
        stock_info = trade_client.trade_show(stock_code)
        
        if stock_info.get("status") == "success":
            positions = stock_info["data"]["positions"]
            if positions:
                for position in positions:
                    if position.get("code") == stock_code:
                        print_position_info(position)
                        break
                else:
                    print(f"未持有股票: {stock_code}")
            else:
                print(f"未持有股票: {stock_code}")
        else:
            logger.error(f"查询股票持仓失败: {stock_info.get('message', '未知错误')}")
        
    except Exception as e:
        logger.error(f"查询过程中发生错误: {e}")
    
    logger.info("账户信息查询示例执行完毕")
    # 将session的cookies保存到cookies.json文件
    with open('cookies.json', 'w') as f:
        json.dump(session.cookies.get_dict(), f, indent=2)
    logger.info("Cookies已成功保存到cookies.json文件")

if __name__ == "__main__":
    main() 