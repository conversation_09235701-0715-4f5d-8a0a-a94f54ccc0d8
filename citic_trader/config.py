import json
import os
import logging
from typing import Dict, Tuple, Optional, Any
# 获取日志记录器
logger = logging.getLogger(__name__)

CONFIG_FILE = "config.json"

class Config:
    """
    配置管理类，用于加载和访问config.json中的配置。
    采用单例模式，确保配置只被加载一次。
    """
    _instance = None
    _config_data = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._load_config()
        return cls._instance

    def _load_config(self):
        """
        加载config.json文件。
        """
        script_dir = os.path.dirname(__file__)
        config_path = os.path.join(script_dir, '''..''', CONFIG_FILE) # config.json在项目根目录

        if not os.path.exists(config_path):
            logger.warning(f"配置文件 {config_path} 不存在，将使用默认配置。")
            self._config_data = self._get_default_config()
            return

        try:
            with open(config_path, '''r''', encoding='''utf-8''') as f:
                self._config_data = json.load(f)
            logger.info(f"成功加载配置文件: {config_path}")
        except json.JSONDecodeError as e:
            logger.error(f"配置文件 {config_path} 格式错误: {e}，将使用默认配置。")
            self._config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件 {config_path} 时发生未知错误: {e}，将使用默认配置。")
            self._config_data = self._get_default_config()

    def _get_default_config(self):
        """
        提供一个默认的配置。
        """
        return {
            "trading": {
                "base_etf": "518880",
                "target_etf": "518890",
                "trade_etf": "518850",
                "window": 120,
                "std_dev_mult": 1.2,
                "max_pos_size": 1.0,
                "trade_volume": 100,
                "check_interval": 5
            },
            "auth": {
                "cookies_file": "cookies.json",
                "save_cookies": True
            },
            "logging": {
                "level": "INFO",
                "file": "trading.log"
            },
            "risk_control": {
                "max_daily_loss": 1000,
                "max_position_value": 50000,
                "max_trades_per_day": 10,
                "stop_loss_pct": 2.0,
                "take_profit_pct": 5.0,
                "volatility_threshold": 3.0
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值。
        支持通过点号分隔的键来访问嵌套配置，例如 "trading.trade_volume"。
        """
        keys = key.split('.')
        value = self._config_data
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

def get_config() -> Config:
    """
    获取Config类的单例实例。
    """
    return Config()

# 初始化日志，确保在Config类中使用
from .utils import setup_logger
setup_logger(log_to_file=True) 