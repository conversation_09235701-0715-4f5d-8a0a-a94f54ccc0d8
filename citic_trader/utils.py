"""
工具函数模块

提供项目中使用的各种辅助功能和工具函数。
"""

import json
import logging
import os
import base64
from datetime import datetime, time
import pytz
from typing import Dict, Any, Optional, Tuple
import requests

def setup_logger(log_level: int = logging.INFO, log_to_file: bool = True) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        log_level: 日志级别，默认为INFO
        log_to_file: 是否将日志写入文件，默认为True
        
    Returns:
        logging.Logger: 日志记录器对象
    """
    # 创建日志记录器
    logger = logging.getLogger("citic_trader")
    logger.setLevel(log_level)
    logger.handlers = []  # 清除已有的处理器，避免重复
    
    # 创建格式化器
    formatter = logging.Formatter(
        "[%(asctime)s] [%(levelname)s] [%(filename)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果需要，创建文件处理器
    if log_to_file:
        # 确保logs目录存在
        if not os.path.exists("logs"):
            os.makedirs("logs")
        
        # 创建日志文件名，格式为YYYYMMDD.log
        log_file = os.path.join("logs", f"{datetime.now().strftime('%Y%m%d')}.log")
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def extract_response_data(response: requests.Response) -> Dict[str, Any]:
    """
    从响应对象中提取数据
    
    Args:
        response: 响应对象
        
    Returns:
        Dict: 响应数据字典
    """
    try:
        # 尝试解析响应数据
        # 中信建投的响应通常是Base64编码的JSON
        decoded = base64.b64decode(response.text).decode('utf-8')
        return json.loads(decoded)
    except Exception as e:
        # 如果解析失败，尝试直接解析JSON
        try:
            return response.json()
        except Exception:
            # 如果仍然失败，则返回原始响应文本
            return {
                "retcode": "-1",
                "retmsg": f"解析响应数据失败: {str(e)}",
                "data": response.text
            }

def format_stock_code(stock_code: str) -> str:
    """
    格式化股票代码，添加交易所前缀
    
    Args:
        stock_code: 原始股票代码
        
    Returns:
        str: 格式化后的股票代码
    """
    # 如果已经包含交易所前缀，则直接返回
    if stock_code.startswith(("SH", "SZ", "sh", "sz")):
        return stock_code.upper()
    
    # 根据股票代码判断交易所
    if stock_code.startswith(("6", "5", "7", "9")):
        return f"SH{stock_code}"
    elif stock_code.startswith(("0", "1", "2", "3")):
        return f"SZ{stock_code}"
    else:
        # 默认使用上交所
        return f"SH{stock_code}"

def parse_trade_response(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    解析交易响应
    
    Args:
        response_data: 原始响应数据
        
    Returns:
        Dict: 解析后的响应数据
    """
    try:
        if response_data.get("retcode") == "0":
            # 成功响应
            return {
                "status": "success",
                "message": response_data.get("retmsg", "成功"),
                "data": response_data
            }
        else:
            # 失败响应
            return {
                "status": "failed",
                "message": response_data.get("retmsg", "未知错误"),
                "data": response_data
            }
    except Exception as e:
        # 解析异常
        return {
            "status": "error",
            "message": f"解析响应失败: {str(e)}",
            "data": response_data
        }

def get_stock_name(stock_code: str, session: Optional[requests.Session] = None) -> str:
    """
    获取股票名称
    
    Args:
        stock_code: 股票代码
        session: 可选的会话对象
        
    Returns:
        str: 股票名称，如果获取失败则返回空字符串
    """
    # 格式化股票代码
    formatted_code = format_stock_code(stock_code)
    clean_code = formatted_code.replace("SH", "").replace("SZ", "")
    
    # 创建用于请求的会话
    if session is None:
        session = requests.Session()
    
    try:
        # 请求股票信息
        api_url = f"https://wzq.csc108.com/cgi-bin/stock_info.fcgi?t={int(datetime.now().timestamp() * 1000)}"
        data = {
            "market": "1" if formatted_code.startswith("SH") else "2",
            "code": clean_code,
            "come_from": "0",
            "gm_flag": "0"
        }
        
        response = session.post(api_url, data=data)
        if response.status_code != 200:
            return ""
        
        # 解析响应
        try:
            content = base64.b64decode(response.text).decode('utf-8')
            stock_info = json.loads(content)
            if stock_info.get("retcode") == "0":
                return stock_info.get("name", "")
        except:
            pass
        
        return ""
    except Exception:
        return ""

def is_trading_time() -> Tuple[bool, str]:
    """
    检查当前是否为A股交易时间
    
    Returns:
        Tuple[bool, str]: (是否为交易时间, 说明信息)
    """
    # 获取中国时区的当前时间
    tz = pytz.timezone('Asia/Shanghai')
    now = datetime.now(tz)
    
    # 如果是周末，不是交易日
    if now.weekday() >= 5:  # 5和6分别是周六和周日
        return False, "当前为周末非交易日"
    
    # 当前时间
    current_time = now.time()
    
    # 上午交易时段：9:30 - 11:30
    morning_start = time(9, 30)
    morning_end = time(11, 30)
    
    # 下午交易时段：13:00 - 15:00
    afternoon_start = time(13, 0)
    afternoon_end = time(15, 0)
    
    # 判断是否在交易时间内
    if (morning_start <= current_time <= morning_end) or (afternoon_start <= current_time <= afternoon_end):
        return True, "当前为交易时间"
    elif current_time < morning_start:
        return False, "当前为盘前时间"
    elif morning_end < current_time < afternoon_start:
        return False, "当前为午休时间"
    else:
        return False, "当前为盘后时间"

def format_amount(amount: float) -> str:
    """
    格式化金额为字符串
    
    Args:
        amount: 金额
        
    Returns:
        str: 格式化后的金额字符串，保留两位小数
    """
    return f"{amount:.2f}"

def get_timestamp_ms() -> int:
    """
    获取毫秒级时间戳
    
    Returns:
        int: 毫秒级时间戳
    """
    return int(datetime.now().timestamp() * 1000)

def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件。

    Args:
        config_path: 配置文件路径。

    Returns:
        配置字典。

    Raises:
        FileNotFoundError: 如果配置文件不存在。
        json.JSONDecodeError: 如果配置文件格式不正确。
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件未找到: {config_path}")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"配置已从 {config_path} 加载。")
        return config
    except json.JSONDecodeError as e:
        logging.error(f"配置文件 {config_path} 格式错误: {e}")
        raise
    except Exception as e:
        logging.error(f"加载配置文件 {config_path} 时发生未知错误: {e}")
        raise