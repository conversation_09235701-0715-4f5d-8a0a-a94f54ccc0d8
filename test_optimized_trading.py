#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化交易函数测试脚本

对比原始交易函数和优化后交易函数的性能差异
"""

import os
import sys
import time
import json
import logging
import requests
from datetime import datetime
from typing import Dict, Any, Optional
from colorama import init, Fore, Style

# 导入交易模块
from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger

# 初始化colorama
init(autoreset=True)

class TradingPerformanceTest:
    """交易性能测试类"""
    
    def __init__(self, cookies_file: str = "cookies.json"):
        """
        初始化测试类
        
        Args:
            cookies_file: cookies文件路径
        """
        self.cookies_file = cookies_file
        self.logger = setup_logger(log_level=logging.INFO)
        self.session = None
        self.trade_client = None
        
        # 测试参数
        self.test_stock_code = "518880"  # 黄金ETF
        self.test_price = 7.500  # 测试价格
        self.test_volume = 100   # 测试数量
        
        # 性能统计
        self.performance_stats = {
            "original_buy": [],
            "optimized_buy": [],
            "ultra_optimized_buy": [],
            "original_sell": [],
            "optimized_sell": [],
            "ultra_optimized_sell": []
        }
    
    def load_cookies(self) -> Dict[str, str]:
        """加载cookies"""
        try:
            if not os.path.exists(self.cookies_file):
                self.logger.error(f"Cookies文件不存在: {self.cookies_file}")
                return {}
                
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
                try:
                    cookies = json.loads(content)
                    self.logger.info(f"成功加载cookies")
                    return cookies
                except json.JSONDecodeError:
                    self.logger.error(f"无法解析cookies文件")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"加载cookies失败: {e}")
            return {}
    
    def setup_trade_client(self) -> bool:
        """设置交易客户端"""
        try:
            # 加载cookies
            cookies = self.load_cookies()
            if not cookies:
                self.logger.error("未找到有效的cookies")
                return False
            
            # 创建会话
            self.session = requests.Session()
            self.session.trust_env = False
            self.session.proxies = {'http': None, 'https': None}
            for key, value in cookies.items():
                self.session.cookies.set(key, value)
            
            # 用户信息
            user_info = {
                "account_id": "",
                "stockholder_code": "",
                "psw_session": "",
                "app_info": {
                    "_appver": "7.0.20",
                    "_osVer": "Windows1064",
                    "_buildh5ver": "************"
                }
            }
            
            # 创建交易客户端
            self.trade_client = TradeClient(self.session, user_info, self.logger)
            self.logger.info("交易客户端创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设置交易客户端失败: {e}")
            return False
    
    def measure_execution_time(self, func, *args, **kwargs) -> tuple:
        """
        测量函数执行时间
        
        Returns:
            tuple: (执行时间(秒), 函数返回值)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return execution_time, result
    
    def test_buy_functions(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        测试买入函数性能
        
        Args:
            dry_run: 是否为模拟测试（不实际交易）
            
        Returns:
            Dict: 测试结果
        """
        results = {}
        
        print(Fore.CYAN + Style.BRIGHT + "=" * 60)
        print(Fore.CYAN + Style.BRIGHT + "测试买入函数性能")
        print(Fore.CYAN + Style.BRIGHT + "=" * 60)
        
        # 获取股票信息（所有函数都需要）
        stock_info = self.trade_client.get_stock_info(self.test_stock_code)
        
        if dry_run:
            print(Fore.YELLOW + "注意：这是模拟测试，不会实际执行交易")
        
        # 测试原始买入函数
        print(Fore.GREEN + "\n1. 测试原始买入函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.buy,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            stock_info=stock_info,
            order_type="specified"
        )
        results["original_buy"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        time.sleep(1)
        # 测试优化买入函数
        print(Fore.GREEN + "\n2. 测试优化买入函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.buy_optimized,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            stock_info=stock_info,
            order_type="specified"
        )
        results["optimized_buy"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        time.sleep(1)
        # 测试超级优化买入函数
        print(Fore.GREEN + "\n3. 测试超级优化买入函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.buy_ultra_optimized,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            order_type="specified"
        )
        results["ultra_optimized_buy"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        
        return results
    
    def test_sell_functions(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        测试卖出函数性能
        
        Args:
            dry_run: 是否为模拟测试
            
        Returns:
            Dict: 测试结果
        """
        results = {}
        
        print(Fore.CYAN + Style.BRIGHT + "\n" + "=" * 60)
        print(Fore.CYAN + Style.BRIGHT + "测试卖出函数性能")
        print(Fore.CYAN + Style.BRIGHT + "=" * 60)
        
        # 获取股票信息
        stock_info = self.trade_client.get_stock_info(self.test_stock_code)
        
        if dry_run:
            print(Fore.YELLOW + "注意：这是模拟测试，不会实际执行交易")
        
        # 测试原始卖出函数
        print(Fore.GREEN + "\n1. 测试原始卖出函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.sell,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            stock_info=stock_info
        )
        results["original_sell"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        time.sleep(1)       
        # 测试优化卖出函数
        print(Fore.GREEN + "\n2. 测试优化卖出函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.sell_optimized,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            stock_info=stock_info,
            order_type="specified"
        )
        results["optimized_sell"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        time.sleep(1)
        # 测试超级优化卖出函数
        print(Fore.GREEN + "\n3. 测试超级优化卖出函数...")
        exec_time, result = self.measure_execution_time(
            self.trade_client.sell_ultra_optimized,
            stock_code=self.test_stock_code,
            price=self.test_price,
            volume=self.test_volume,
            order_type="specified"
        )
        results["ultra_optimized_sell"] = {
            "execution_time": exec_time,
            "result": result,
            "success": result.get("status") == "success"
        }
        print(f"执行时间: {exec_time:.3f}秒")
        print(f"结果: {result.get('status', 'unknown')}")
        
        return results
    
    def analyze_performance(self, buy_results: Dict[str, Any], sell_results: Dict[str, Any]) -> None:
        """分析性能结果"""
        print(Fore.MAGENTA + Style.BRIGHT + "\n" + "=" * 60)
        print(Fore.MAGENTA + Style.BRIGHT + "性能分析结果")
        print(Fore.MAGENTA + Style.BRIGHT + "=" * 60)
        
        # 买入函数性能对比
        print(Fore.YELLOW + "\n买入函数性能对比:")
        original_time = buy_results["original_buy"]["execution_time"]
        optimized_time = buy_results["optimized_buy"]["execution_time"]
        ultra_time = buy_results["ultra_optimized_buy"]["execution_time"]
        
        print(f"原始买入函数:     {original_time:.3f}秒")
        print(f"优化买入函数:     {optimized_time:.3f}秒 (提升: {((original_time - optimized_time) / original_time * 100):.1f}%)")
        print(f"超级优化买入函数: {ultra_time:.3f}秒 (提升: {((original_time - ultra_time) / original_time * 100):.1f}%)")
        
        # 卖出函数性能对比
        print(Fore.YELLOW + "\n卖出函数性能对比:")
        original_time = sell_results["original_sell"]["execution_time"]
        optimized_time = sell_results["optimized_sell"]["execution_time"]
        ultra_time = sell_results["ultra_optimized_sell"]["execution_time"]
        
        print(f"原始卖出函数:     {original_time:.3f}秒")
        print(f"优化卖出函数:     {optimized_time:.3f}秒 (提升: {((original_time - optimized_time) / original_time * 100):.1f}%)")
        print(f"超级优化卖出函数: {ultra_time:.3f}秒 (提升: {((original_time - ultra_time) / original_time * 100):.1f}%)")
    
    def run_performance_test(self, dry_run: bool = True) -> None:
        """运行性能测试"""
        print(Fore.BLUE + Style.BRIGHT + "=" * 80)
        print(Fore.BLUE + Style.BRIGHT + "交易函数性能测试")
        print(Fore.BLUE + Style.BRIGHT + f"测试股票: {self.test_stock_code}")
        print(Fore.BLUE + Style.BRIGHT + f"测试价格: {self.test_price}")
        print(Fore.BLUE + Style.BRIGHT + f"测试数量: {self.test_volume}")
        print(Fore.BLUE + Style.BRIGHT + "=" * 80)
        
        # 设置交易客户端
        if not self.setup_trade_client():
            print(Fore.RED + "无法设置交易客户端，测试终止")
            return
        
        # 测试买入函数
        buy_results = self.test_buy_functions(dry_run)
        
        # 测试卖出函数
        sell_results = self.test_sell_functions(dry_run)
        
        # 分析性能
        self.analyze_performance(buy_results, sell_results)
        
        # 保存测试结果
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "test_parameters": {
                "stock_code": self.test_stock_code,
                "price": self.test_price,
                "volume": self.test_volume,
                "dry_run": dry_run
            },
            "buy_results": buy_results,
            "sell_results": sell_results
        }
        
        with open("performance_test_results.json", "w", encoding="utf-8") as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        print(Fore.GREEN + Style.BRIGHT + f"\n测试结果已保存到 performance_test_results.json")
            # 保存cookies
        if self.session:
            try:
                with open(self.cookies_file, 'w', encoding='utf-8') as f:
                    json.dump(self.session.cookies.get_dict(), f, indent=2)
                self.logger.info(f"Cookies已保存到 {self.cookies_file}")
            except Exception as e:
                self.logger.error(f"保存cookies失败: {e}")
def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='交易函数性能测试')
    parser.add_argument('--cookies', type=str, default="cookies.json", help='cookies文件路径')
    parser.add_argument('--stock', type=str, default="518850", help='测试股票代码')
    parser.add_argument('--price', type=float, default=8.179, help='测试价格')
    parser.add_argument('--volume', type=int, default=100, help='测试数量')
    parser.add_argument('--real', action='store_true', help='执行真实交易（默认为模拟测试）')
    
    args = parser.parse_args()
    
    # 创建测试实例
    test = TradingPerformanceTest(args.cookies)
    test.test_stock_code = args.stock
    test.test_price = args.price
    test.test_volume = args.volume
    
    # 运行测试
    test.run_performance_test(dry_run=not args.real)

if __name__ == "__main__":
    main()
