# 量化交易系统

这是一个基于Python的完整量化交易系统，结合了实时行情数据、信号计算策略和证券交易接口，可以自动执行股票和ETF的买入、卖出以及持仓管理操作。

## 功能特点

- **用户认证与会话管理**：支持中信建投交易账户的登录和会话维护
- **实时行情数据**：自动连接行情数据服务器，获取股票和ETF的实时价格
- **信号计算策略**：内置配对交易等多种量化策略，实时计算交易信号
- **交易执行功能**：
  - 买入下单
  - 卖出下单
  - 撤单操作
  - 委托查询
- **持仓与资金查询**：
  - 实时查询账户资金状态
  - 查询持仓明细
  - 查询历史成交记录
  - 查询当日委托记录
- **自动化交易**：基于信号自动执行交易，支持参数配置和风控设置
- **风险控制系统**：内置多层次风险控制机制，保障交易安全
- **数据分析与可视化**：支持交易结果分析和图表展示

## 系统架构

系统主要由以下几个核心模块组成：

1. **认证模块** (`auth`)：处理用户登录和会话管理
2. **行情模块** (`market_data`)：获取实时价格和市场数据
3. **信号计算模块** (`signal`)：基于市场数据计算交易信号
4. **交易执行模块** (`trade`)：执行买入、卖出和撤单操作
5. **查询模块** (`query`)：查询账户资金、持仓和委托信息
6. **自动交易模块** (`auto_trade`)：集成所有模块，实现自动化交易
7. **风控模块** (`risk_control`)：提供多层次风险控制机制
8. **数据分析模块** (`analysis`)：分析交易结果并可视化

## 安装依赖

在使用前，请确保已安装以下依赖库：

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 认证与登录

```python
from citic_trader.auth import AuthClient
from citic_trader.utils import setup_logger

# 设置日志
logger = setup_logger()

# 创建认证客户端
auth_client = AuthClient(logger)

# 登录账户
session, user_info = auth_client.login(username, password)
```

### 2. 查询账户信息

```python
from citic_trader.query import QueryClient

# 创建查询客户端
query_client = QueryClient(session, user_info, logger)

# 查询账户资金
account_info = query_client.query_account()
print(f"可用资金：{account_info['data']['funds']['available_cash']}元")

# 查询持仓
positions = query_client.query_positions()
for position in positions['data']:
    print(f"持仓：{position['name']}({position['code']})，数量：{position['hold_num']}份")

# 查询当日委托
orders = query_client.query_orders()
for order in orders['data']:
    print(f"委托：{order['name']}，状态：{order['trade_state']}")
```

### 3. 交易操作

```python
from citic_trader.trade import TradeClient

# 创建交易客户端
trade_client = TradeClient(session, user_info, logger)

# 获取股票信息
stock_info = trade_client.get_stock_info("000001")

# 买入操作
result = trade_client.buy("000001", 10.0, 100, stock_info['data'])
if result['status'] == 'success':
    print(f"买入委托成功，委托编号：{result['entrust_no']}")

# 卖出操作
result = trade_client.sell("000001", 10.5, 100, stock_info['data'])
if result['status'] == 'success':
    print(f"卖出委托成功，委托编号：{result['entrust_no']}")

# 撤单操作
result = trade_client.cancel(entrust_no, trade_time, "000001", "0", "2", stock_info['data'])
if result['status'] == 'success':
    print(f"撤单成功")
```

### 4. 自动交易

```bash
python auto_trade.py --target 518890 --trade 518850 --window 120 --std 1.2 --volume 200
```

## 命令行工具

### 账户信息查询工具

```bash
python query_tool.py --action account
python query_tool.py --action positions
python query_tool.py --action orders
```

### 交易执行工具

```bash
python trade_tool.py --action buy --code 000001 --price 10.0 --volume 100
python trade_tool.py --action sell --code 000001 --price 10.5 --volume 100
python trade_tool.py --action cancel --entrust-no 123456
```

## 配置选项

系统支持通过命令行参数和配置文件进行灵活配置：

### 命令行参数

- `--target`: 目标ETF代码（用于计算信号）
- `--trade`: 实际交易的ETF代码
- `--window`: 滚动窗口大小，单位分钟
- `--std`: 标准差倍数
- `--max-pos`: 最大持仓比例
- `--volume`: 交易数量，单位股
- `--interval`: 检查信号间隔，单位秒
- `--cookies`: cookies文件路径

### 配置文件

系统还支持通过`config.json`文件进行配置：

```json
{
  "trading": {
    "base_etf": "518880",
    "target_etf": "518890",
    "trade_etf": "518850",
    "window": 120,
    "std_dev_mult": 1.2,
    "max_pos_size": 1.0,
    "trade_volume": 100,
    "check_interval": 5
  },
  "auth": {
    "cookies_file": "cookies.json",
    "save_cookies": true
  },
  "logging": {
    "level": "INFO",
    "file": "trading.log"
  },
  "risk_control": {
    "max_daily_loss": 1000,
    "max_position_value": 50000,
    "max_trades_per_day": 10,
    "stop_loss_pct": 2.0,
    "take_profit_pct": 5.0,
    "volatility_threshold": 3.0
  }
}
```

## 高级功能

### 策略开发

系统支持自定义交易策略，只需继承`BaseStrategy`类并实现`calculate_signal`方法：

```python
from strategies.base_strategy import BaseStrategy

class MyCustomStrategy(BaseStrategy):
    def __init__(self, params):
        super().__init__(params)
        # 初始化策略参数
        
    def calculate_signal(self, market_data):
        # 实现信号计算逻辑
        return signal  # 返回 1(买入), -1(卖出), 0(不操作)
```

### 风险控制

系统内置了多层次风险控制机制，确保交易安全：

#### 1. 资金风险控制
- **单笔交易金额限制**：限制单笔交易的最大金额
- **日内亏损限制**：当日亏损达到阈值时自动停止交易
- **总持仓限制**：限制账户总持仓不超过设定阈值

#### 2. 交易频率控制
- **日内交易次数限制**：限制每日最大交易次数
- **交易间隔控制**：设定连续交易之间的最小时间间隔
- **高频交易检测**：检测并阻止异常的高频交易行为

#### 3. 价格风险控制
- **止损止盈**：自动设置止损和止盈价位
- **波动率监控**：监控市场波动率，在高波动环境下调整交易策略
- **价格偏离检测**：检测价格是否异常偏离，防止在非正常市场环境下交易

#### 4. 执行风险控制
- **交易验证**：每次交易前后进行验证，确保交易符合预期
- **错误处理**：完善的错误处理机制，防止系统因错误而崩溃
- **紧急停止**：提供紧急停止功能，可在任何时候中断自动交易

### 回测系统

系统还提供了历史数据回测功能：

```python
from backtest.engine import BacktestEngine
from strategies.my_strategy import MyCustomStrategy

# 创建策略实例
strategy = MyCustomStrategy(params)

# 创建回测引擎
engine = BacktestEngine(
    strategy=strategy,
    start_date="2023-01-01",
    end_date="2023-12-31",
    initial_capital=100000
)

# 运行回测
results = engine.run()

# 分析结果
engine.analyze()
```

### 数据分析与可视化

系统提供了强大的数据分析和可视化功能：

- **绩效分析**：计算各种绩效指标（夏普比率、最大回撤等）
- **交易记录分析**：分析交易记录，识别盈利/亏损模式
- **策略优化**：基于历史数据优化策略参数
- **可视化图表**：生成各类图表（价格走势、信号分布、盈亏分布等）

## 文件说明

- `citic_trader/`：交易接口核心模块
  - `auth.py`: 认证和会话管理
  - `trade.py`: 交易执行功能
  - `query.py`: 查询功能
  - `utils.py`: 通用工具函数
  - `encrypt.py`: 加密相关功能
- `strategies/`：交易策略
  - `base_strategy.py`: 策略基类
  - `pair_trading.py`: 配对交易策略
- `market_data/`：行情数据模块
  - `tdx_api.py`: 通达信行情接口
  - `data_source.py`: 数据源基类
  - `real_time_data.py`: 实时数据处理
- `risk_control/`：风险控制模块
  - `risk_manager.py`: 风险管理器
  - `risk_check.py`: 风险检查功能
  - `risk_limits.py`: 风险限制定义
- `analysis/`：数据分析模块
  - `performance.py`: 绩效分析
  - `visualization.py`: 数据可视化
  - `trade_analyzer.py`: 交易分析
- `auto_trade.py`: 自动交易控制模块
- `query_tool.py`: 账户查询工具
- `trade_tool.py`: 交易执行工具
- `backtest/`：回测系统
  - `engine.py`: 回测引擎
  - `analyzer.py`: 绩效分析
- `data/`：数据文件
  - `cookies.json`: 存储cookies的文件
  - `trade_records.json`: 交易记录文件

## 安全性说明

- 账户密码和身份信息采用加密存储
- 敏感信息不会明文保存
- 请勿与他人分享cookies或账户信息
- 建议定期更换密码和会话信息

## 免责声明

本系统仅供学习和研究使用，不构成投资建议。实盘交易产生的任何盈亏由用户自行承担，与本系统及其开发者无关。

## 开发计划

- [ ] 添加更多交易策略
- [ ] 开发Web管理界面
- [ ] 支持多账户并行交易
- [ ] 添加事件驱动架构
- [ ] 集成机器学习模型
