(window["webpackJsonp"] = window["webpackJsonp"] || []).push([
	["pages-account-about-company_pages-account-about-protocol_pages-account-auto-add-choose_pages-account_5ae25a28"], {
		df98: function(e, t, n) {
			"use strict";
			(function(e) {
				var a = n("d4ec"),
					o = n("bee2"),
					r = n("ade3"),
					i = n("8d81"),
					s = n.n(i),
					d = function() {
						function t(e) {
							Object(a["a"])(this, t), Object(r["a"])(this, "timeSeed", void 0), Object(r["a"])(this, "used", void 0), this.timeSeed = e, this.used = !1
						}
						return Object(o["a"])(t, [{
							key: "get",
							value: function() {
								return this.used = !0, this.timeSeed ? this.timeSeed.substring(0, 20) : ""
							}
						}, {
							key: "set",
							value: function(e) {
								this.used = !1, this.timeSeed = e
							}
						}, {
							key: "hasUsed",
							value: function() {
								return e.log("[crypt] get data-used is", this.used), this.used
							}
						}, {
							key: "getTimeCode",
							value: function() {
								var e = s()(this.timeSeed);
								return e.substring(0, 14)
							}
						}]), t
					}();
				t["a"] = d
			}).call(this, n("5a52")["default"])
		},
		fe28: function(e, t, n) {
			"use strict";
			n.d(t, "c", (function() {
				return se
			})), n.d(t, "a", (function() {
				return de
			})), n.d(t, "b", (function() {
				return ce
			})), n.d(t, "d", (function() {
				return le
			})), n.d(t, "e", (function() {
				return pe
			}));
			var a, o, r, i, s = n("bee2"),
				d = n("d4ec"),
				c = n("ade3"),
				l = {
					SM: "1",
					RSA: "0"
				}, p = Object(s["a"])((function e(t, n) {
					Object(d["a"])(this, e), Object(c["a"])(this, "key", void 0), Object(c["a"])(this, "encryptMethod", void 0), this.key = t, this.encryptMethod = n
				})),
				u = p,
				b = (n("99af"), n("fb6a"), n("a9e3"), n("e25e"), n("14d9"), n("a15b"), n("4de4"), n("d3b7"), n("a434"), 16),
				m = b,
				f = 65536,
				v = f >>> 1,
				h = f * f,
				w = f - 1;

			function g(e) {
				var t;
				for (a = e, o = new Array(a), t = 0; t < o.length; t++) o[t] = 0;
				r = new k, i = new k, i.digits[0] = 1
			}
			function k(e) {
				this.digits = "boolean" === typeof e && 1 == e ? null : o.slice(0), this.isNeg = !1
			}
			function y(e) {
				var t = new k(!0);
				return t.digits = e.digits.slice(0), t.isNeg = e.isNeg, t
			}
			function x(e) {
				var t, n = "";
				for (t = e.length - 1; t > -1; --t) n += e.charAt(t);
				return n
			}
			g(20);
			var _ = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"];

			function C(e, t) {
				var n = new k;
				n.digits[0] = t;
				var a = q(e, n),
					o = _[a[1].digits[0]];
				while (1 == K(a[0], r)) a = q(a[0], n), o += _[a[1].digits[0]];
				return (e.isNeg ? "-" : "") + x(o)
			}
			var E = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"];

			function S(e) {
				var t, n = 15,
					a = "";
				for (t = 0; t < 4; ++t) a += E[e & n], e >>>= 4;
				return x(a)
			}
			function P(e) {
				var t, n = "";
				for (t = j(e); t > -1; --t) n += S(e.digits[t]);
				return n
			}
			function O(e) {
				var t, n = 48,
					a = n + 9,
					o = 97,
					r = o + 25,
					i = 65,
					s = 90;
				return t = e >= n && e <= a ? e - n : e >= i && e <= s ? 10 + e - i : e >= o && e <= r ? 10 + e - o : 0, t
			}
			function A(e) {
				var t, n = 0,
					a = Math.min(e.length, 4);
				for (t = 0; t < a; ++t) n <<= 4, n |= O(e.charCodeAt(t));
				return n
			}
			function N(e) {
				var t, n, a = new k,
					o = e.length;
				for (t = o, n = 0; t > 0; t -= 4, ++n) a.digits[n] = A(e.substr(Math.max(t - 4, 0), Math.min(t, 4)));
				return a
			}
			function I(e, t) {
				var n, a, o, r = 0;
				if (e.isNeg != t.isNeg) t.isNeg = !t.isNeg, n = M(e, t), t.isNeg = !t.isNeg;
				else {
					for (n = new k, o = 0; o < e.digits.length; ++o) a = e.digits[o] + t.digits[o] + r, n.digits[o] = 65535 & a, r = Number(a >= f);
					n.isNeg = e.isNeg
				}
				return n
			}
			function M(e, t) {
				var n, a, o, r;
				if (e.isNeg != t.isNeg) t.isNeg = !t.isNeg, n = I(e, t), t.isNeg = !t.isNeg;
				else {
					for (n = new k, r = 0, a = 0; a < e.digits.length; ++a) o = e.digits[a] - t.digits[a] + r, n.digits[a] = 65535 & o, n.digits[a] < 0 && (n.digits[a] += f), r = 0 - Number(o < 0);
					if (-1 == r) {
						for (r = 0, a = 0; a < e.digits.length; ++a) o = 0 - n.digits[a] + r, n.digits[a] = 65535 & o, n.digits[a] < 0 && (n.digits[a] += f), r = 0 - Number(o < 0);
						n.isNeg = !e.isNeg
					} else n.isNeg = e.isNeg
				}
				return n
			}
			function j(e) {
				var t = e.digits.length - 1;
				while (t > 0 && 0 == e.digits[t])--t;
				return t
			}
			function $(e) {
				var t, n = j(e),
					a = e.digits[n],
					o = (n + 1) * m;
				for (t = o; t > o - m; --t) {
					if (0 != (32768 & a)) break;
					a <<= 1
				}
				return t
			}
			function B(e, t) {
				var n, a, o, r, i, s = new k,
					d = j(e),
					c = j(t);
				for (r = 0; r <= c; ++r) {
					for (n = 0, o = r, i = 0; i <= d; ++i, ++o) a = s.digits[o] + e.digits[i] * t.digits[r] + n, s.digits[o] = a & w, n = a >>> b;
					s.digits[r + d + 1] = n
				}
				return s.isNeg = e.isNeg != t.isNeg, s
			}
			function z(e, t) {
				var n, a, o, r, i = new k;
				for (n = j(e), a = 0, r = 0; r <= n; ++r) o = i.digits[r] + e.digits[r] * t + a, i.digits[r] = o & w, a = o >>> b;
				return i.digits[1 + n] = a, i
			}
			function T(e, t, n, a, o) {
				var r, i, s = Math.min(t + o, e.length);
				for (r = t, i = a; r < s; ++r, ++i) n[i] = e[r]
			}
			var R = [0, 32768, 49152, 57344, 61440, 63488, 64512, 65024, 65280, 65408, 65472, 65504, 65520, 65528, 65532, 65534, 65535];

			function U(e, t) {
				var n = Math.floor(t / m),
					a = new k;
				T(e.digits, 0, a.digits, n, a.digits.length - n);
				var o, r, i = t % m,
					s = m - i;
				for (o = a.digits.length - 1, r = o - 1; o > 0; --o, --r) a.digits[o] = a.digits[o] << i & w | (a.digits[r] & R[i]) >>> s;
				return a.digits[0] = a.digits[o] << i & w, a.isNeg = e.isNeg, a
			}
			var H = [0, 1, 3, 7, 15, 31, 63, 127, 255, 511, 1023, 2047, 4095, 8191, 16383, 32767, 65535];

			function L(e, t) {
				var n = Math.floor(t / m),
					a = new k;
				T(e.digits, n, a.digits, 0, e.digits.length - n);
				var o, r, i = t % m,
					s = m - i;
				for (o = 0, r = o + 1; o < a.digits.length - 1; ++o, ++r) a.digits[o] = a.digits[o] >>> i | (a.digits[r] & H[i]) << s;
				return a.digits[a.digits.length - 1] >>>= i, a.isNeg = e.isNeg, a
			}
			function D(e, t) {
				var n = new k;
				return T(e.digits, 0, n.digits, t, n.digits.length - t), n
			}
			function F(e, t) {
				var n = new k;
				return T(e.digits, t, n.digits, 0, n.digits.length - t), n
			}
			function W(e, t) {
				var n = new k;
				return T(e.digits, 0, n.digits, 0, t), n
			}
			function K(e, t) {
				var n;
				if (e.isNeg != t.isNeg) return 1 - 2 * Number(e.isNeg);
				for (n = e.digits.length - 1; n >= 0; --n) if (e.digits[n] != t.digits[n]) return e.isNeg ? 1 - 2 * Number(e.digits[n] > t.digits[n]) : 1 - 2 * Number(e.digits[n] < t.digits[n]);
				return 0
			}
			function q(e, t) {
				var n, a, o = $(e),
					r = $(t),
					s = t.isNeg;
				if (o < r) return e.isNeg ? (n = y(i), n.isNeg = !t.isNeg, e.isNeg = !1, t.isNeg = !1, a = M(t, e), e.isNeg = !0, t.isNeg = s) : (n = new k, a = y(e)), [n, a];
				n = new k, a = e;
				var d = Math.ceil(r / m) - 1,
					c = 0;
				while (t.digits[d] < v) t = U(t, 1), ++c, ++r, d = Math.ceil(r / m) - 1;
				a = U(a, c), o += c;
				var l = Math.ceil(o / m) - 1,
					p = D(t, l - d);
				while (-1 != K(a, p))++n.digits[l - d], a = M(a, p);
				for (var u = l; u > d; --u) {
					var b = u >= a.digits.length ? 0 : a.digits[u],
						g = u - 1 >= a.digits.length ? 0 : a.digits[u - 1],
						x = u - 2 >= a.digits.length ? 0 : a.digits[u - 2],
						_ = d >= t.digits.length ? 0 : t.digits[d],
						C = d - 1 >= t.digits.length ? 0 : t.digits[d - 1];
					n.digits[u - d - 1] = b == _ ? w : Math.floor((b * f + g) / _);
					var E = n.digits[u - d - 1] * (_ * f + C),
						S = b * h + (g * f + x);
					while (E > S)--n.digits[u - d - 1], E = n.digits[u - d - 1] * (_ * f | C), S = b * f * f + (g * f + x);
					p = D(t, u - d - 1), a = M(a, z(p, n.digits[u - d - 1])), a.isNeg && (a = I(a, p), --n.digits[u - d - 1])
				}
				return a = L(a, c), n.isNeg = e.isNeg != s, e.isNeg && (n = s ? I(n, i) : M(n, i), t = L(t, c), a = M(t, a)), 0 == a.digits[0] && 0 == j(a) && (a.isNeg = !1), [n, a]
			}
			function G(e, t) {
				return q(e, t)[0]
			}
			function Z(e) {
				this.modulus = y(e), this.k = j(this.modulus) + 1;
				var t = new k;
				t.digits[2 * this.k] = 1, this.mu = G(t, this.modulus), this.bkplus1 = new k, this.bkplus1.digits[this.k + 1] = 1, this.modulo = V, this.multiplyMod = X, this.powMod = Q
			}
			function V(e) {
				var t = F(e, this.k - 1),
					n = B(t, this.mu),
					a = F(n, this.k + 1),
					o = W(e, this.k + 1),
					r = B(a, this.modulus),
					i = W(r, this.k + 1),
					s = M(o, i);
				s.isNeg && (s = I(s, this.bkplus1));
				var d = K(s, this.modulus) >= 0;
				while (d) s = M(s, this.modulus), d = K(s, this.modulus) >= 0;
				return s
			}
			function X(e, t) {
				var n = B(e, t);
				return this.modulo(n)
			}
			function Q(e, t) {
				var n = new k;
				n.digits[0] = 1;
				var a = e,
					o = t;
				while (1) {
					if (0 != (1 & o.digits[0]) && (n = this.multiplyMod(n, a)), o = L(o, 1), 0 == o.digits[0] && 0 == j(o)) break;
					a = this.multiplyMod(a, a)
				}
				return n
			}
			function Y(e, t, n) {
				this.e = N(e), this.d = N(t), this.m = N(n), this.chunkSize = 2 * (j(this.m) + 1), this.radix = 16, this.barrett = new Z(this.m)
			}
			var J, ee, te, ne = {
				_genRandomValue: function() {
					var e = Math.ceil(255 * Math.random());
					return e
				},
				_transTimeSeed: function(e) {
					for (var t = e.length, n = "", a = 0; a < t; a += 2) n += String.fromCharCode(parseInt(e.substr(a, 2), 16));
					return n
				},
				_encrypt2: function(e, t) {
					var n = [],
						a = t.length,
						o = 0,
						r = e.chunkSize;
					while (o < a) n[o] = t.charCodeAt(o), o++;
					while (n.length % r != 0) n[o] = o == a || o == r - 1 ? 0 : o == r - 2 ? 2 : ne._genRandomValue(), o++;
					var i, s, d, c = n.length,
						l = "";
					for (o = 0; o < c; o += r) {
						for (d = new k, i = 0, s = o; s < o + r; ++i) d.digits[i] = n[s++], d.digits[i] += n[s++] << 8;
						var p = e.barrett.powMod(d, e.e),
							u = 16 == e.radix ? P(p) : C(p, e.radix);
						l += u + " "
					}
					return l.substring(0, l.length - 1)
				},
				_encrypt1: function(e, t) {
					var n = [],
						a = t.length,
						o = 0,
						r = e.chunkSize;
					for (i = a - 1; i >= 0; i--) n[o] = t.charCodeAt(i), o++;
					while (n.length % r != 0) n[o] = o == a || o == r - 1 ? 0 : o == r - 2 ? 2 : ne._genRandomValue(), o++;
					var i, s, d, c = n.length,
						l = "";
					for (o = 0; o < c; o += r) {
						for (d = new k, i = 0, s = o; s < o + r; ++i) d.digits[i] = n[s++], d.digits[i] += n[s++] << 8;
						var p = e.barrett.powMod(d, e.e),
							u = 16 == e.radix ? P(p) : C(p, e.radix);
						l += u + " "
					}
					return l.substring(0, l.length - 1)
				},
				_encryptLong: function(e, t) {
					try {
						var n = e.chunkSize,
							a = (8 * n + 7 >> 3) - 11,
							o = 0,
							r = [];
						while (o <= t.length - 1) {
							var i = t.charCodeAt(o);
							i < 128 ? r.push(t[o]) : i > 127 && i < 2048 ? r.push(null, t[o]) : r.push(null, null, t[o]), o++
						}
						if (r.length <= a) return ne._encrypt1(e, t);
						var s = "";
						while (r.length > 0) {
							var d = a;
							while (null === r[d - 1]) d -= 1;
							var c = r.slice(0, d).filter((function(e) {
								return null !== e
							})).join("");
							s += ne._encrypt1(e, c), r.splice(0, d)
						}
						return s
					} catch (l) {
						return ""
					}
				},
				encrypt: function(e, t, n) {
					var a = "";
					return a = !0 === n ? ne._encrypt1(e, t).toUpperCase() : ne._encrypt2(e, t).toUpperCase(), a
				},
				encrypt1: function(e, t, n, a) {
					var o = ne._transTimeSeed(n),
						r = o + t;
					return a && (r = o + a + t), ne.encrypt(e, r, !0)
				},
				encrypt2: function(e, t, n, a) {
					var o = ne._transTimeSeed(n),
						r = o + t;
					return !0 === a && (r = o + "00000000000000" + t), ne.encrypt(e, r, !1)
				},
				encryptLong: function(e, t, n, a) {
					var o = ne._transTimeSeed(n),
						r = o + t;
					return a && (r = o + a + t), ne._encryptLong(e, r)
				}
			}, ae = {
				setMaxDigits: g,
				RSAKeyPair: Y,
				encrypt: ne.encrypt,
				encrypt1: ne.encrypt1,
				encrypt2: ne.encrypt2,
				getTimeSeed: ne._transTimeSeed,
				encryptLong: ne.encryptLong
			}, oe = function() {
				function e(t) {
					Object(d["a"])(this, e), Object(c["a"])(this, "key", void 0), Object(c["a"])(this, "needSign", void 0), this.needSign = t, ae.setMaxDigits(131)
				}
				return Object(s["a"])(e, [{
					key: "setKey",
					value: function(e) {
						var t = "10001";
						if (t) {
							var n = new ae.RSAKeyPair(t, "", e);
							this.key = new u(n, l.RSA)
						}
					}
				}, {
					key: "encode",
					value: function(e, t) {
						var n = t.get(),
							a = t.getTimeCode(),
							o = "0000000000000000000000000000000000000000",
							r = ae.encrypt1(this.key.key, e, n, a);
						return "".concat(a, "000000").concat(o).concat(r)
					}
				}, {
					key: "encodeLong",
					value: function(e, t) {
						var n = t.get(),
							a = t.getTimeCode(),
							o = "0000000000000000000000000000000000000000",
							r = ae.encryptLong(this.key.key, e, n, a);
						return "".concat(a, "000000").concat(o).concat(r)
					}
				}]), e
			}(),
				re = oe,
				ie = n("df98");

			function se() {
				return J
			}
			function de() {
				return ee
			}
			function ce() {
				return te
			}
			function le(e) {
				var t = "1" === e.gm_check_sign_switch;
				ee = e.encrypt_method === l.SM ? new Sm2Encode(t) : new re(t), ee.setKey(e.key), e.key_front_and_broker && (te = e.encrypt_method === l.SM ? new Sm2Encode(t) : new re(t), te.setKey(e.key_front_and_broker))
			}
			function pe(e) {
				J = e ? new ie["a"](e) : null
			}
		}
	}]);
// 定义 s() 函数（MD5 实现）
function o(t, e) {
	var n = (65535 & t) + (65535 & e)
	  , r = (t >> 16) + (e >> 16) + (n >> 16);
	return r << 16 | 65535 & n
}
function a(t, e) {
	return t << e | t >>> 32 - e
}
function s(t, e, n, r, i, s) {
	return o(a(o(o(e, t), o(r, s)), i), n)
}
function c(t, e, n, r, i, o, a) {
	return s(e & n | ~e & r, t, e, i, o, a)
}
function u(t, e, n, r, i, o, a) {
	return s(e & r | n & ~r, t, e, i, o, a)
}
function f(t, e, n, r, i, o, a) {
	return s(e ^ n ^ r, t, e, i, o, a)
}
function l(t, e, n, r, i, o, a) {
	return s(n ^ (e | ~r), t, e, i, o, a)
}
function h(t, e) {
	var n, r, i, a, s;
	t[e >> 5] |= 128 << e % 32,
	t[14 + (e + 64 >>> 9 << 4)] = e;
	var h = 1732584193
	  , d = -271733879
	  , p = -1732584194
	  , v = 271733878;
	for (n = 0; n < t.length; n += 16)
		r = h,
		i = d,
		a = p,
		s = v,
		h = c(h, d, p, v, t[n], 7, -680876936),
		v = c(v, h, d, p, t[n + 1], 12, -389564586),
		p = c(p, v, h, d, t[n + 2], 17, 606105819),
		d = c(d, p, v, h, t[n + 3], 22, -1044525330),
		h = c(h, d, p, v, t[n + 4], 7, -176418897),
		v = c(v, h, d, p, t[n + 5], 12, 1200080426),
		p = c(p, v, h, d, t[n + 6], 17, -1473231341),
		d = c(d, p, v, h, t[n + 7], 22, -45705983),
		h = c(h, d, p, v, t[n + 8], 7, 1770035416),
		v = c(v, h, d, p, t[n + 9], 12, -1958414417),
		p = c(p, v, h, d, t[n + 10], 17, -42063),
		d = c(d, p, v, h, t[n + 11], 22, -1990404162),
		h = c(h, d, p, v, t[n + 12], 7, 1804603682),
		v = c(v, h, d, p, t[n + 13], 12, -40341101),
		p = c(p, v, h, d, t[n + 14], 17, -1502002290),
		d = c(d, p, v, h, t[n + 15], 22, 1236535329),
		h = u(h, d, p, v, t[n + 1], 5, -165796510),
		v = u(v, h, d, p, t[n + 6], 9, -1069501632),
		p = u(p, v, h, d, t[n + 11], 14, 643717713),
		d = u(d, p, v, h, t[n], 20, -373897302),
		h = u(h, d, p, v, t[n + 5], 5, -701558691),
		v = u(v, h, d, p, t[n + 10], 9, 38016083),
		p = u(p, v, h, d, t[n + 15], 14, -660478335),
		d = u(d, p, v, h, t[n + 4], 20, -405537848),
		h = u(h, d, p, v, t[n + 9], 5, 568446438),
		v = u(v, h, d, p, t[n + 14], 9, -1019803690),
		p = u(p, v, h, d, t[n + 3], 14, -187363961),
		d = u(d, p, v, h, t[n + 8], 20, 1163531501),
		h = u(h, d, p, v, t[n + 13], 5, -1444681467),
		v = u(v, h, d, p, t[n + 2], 9, -51403784),
		p = u(p, v, h, d, t[n + 7], 14, 1735328473),
		d = u(d, p, v, h, t[n + 12], 20, -1926607734),
		h = f(h, d, p, v, t[n + 5], 4, -378558),
		v = f(v, h, d, p, t[n + 8], 11, -2022574463),
		p = f(p, v, h, d, t[n + 11], 16, 1839030562),
		d = f(d, p, v, h, t[n + 14], 23, -35309556),
		h = f(h, d, p, v, t[n + 1], 4, -1530992060),
		v = f(v, h, d, p, t[n + 4], 11, 1272893353),
		p = f(p, v, h, d, t[n + 7], 16, -155497632),
		d = f(d, p, v, h, t[n + 10], 23, -1094730640),
		h = f(h, d, p, v, t[n + 13], 4, 681279174),
		v = f(v, h, d, p, t[n], 11, -358537222),
		p = f(p, v, h, d, t[n + 3], 16, -722521979),
		d = f(d, p, v, h, t[n + 6], 23, 76029189),
		h = f(h, d, p, v, t[n + 9], 4, -640364487),
		v = f(v, h, d, p, t[n + 12], 11, -421815835),
		p = f(p, v, h, d, t[n + 15], 16, 530742520),
		d = f(d, p, v, h, t[n + 2], 23, -995338651),
		h = l(h, d, p, v, t[n], 6, -198630844),
		v = l(v, h, d, p, t[n + 7], 10, 1126891415),
		p = l(p, v, h, d, t[n + 14], 15, -1416354905),
		d = l(d, p, v, h, t[n + 5], 21, -57434055),
		h = l(h, d, p, v, t[n + 12], 6, 1700485571),
		v = l(v, h, d, p, t[n + 3], 10, -1894986606),
		p = l(p, v, h, d, t[n + 10], 15, -1051523),
		d = l(d, p, v, h, t[n + 1], 21, -2054922799),
		h = l(h, d, p, v, t[n + 8], 6, 1873313359),
		v = l(v, h, d, p, t[n + 15], 10, -30611744),
		p = l(p, v, h, d, t[n + 6], 15, -1560198380),
		d = l(d, p, v, h, t[n + 13], 21, 1309151649),
		h = l(h, d, p, v, t[n + 4], 6, -145523070),
		v = l(v, h, d, p, t[n + 11], 10, -1120210379),
		p = l(p, v, h, d, t[n + 2], 15, 718787259),
		d = l(d, p, v, h, t[n + 9], 21, -343485551),
		h = o(h, r),
		d = o(d, i),
		p = o(p, a),
		v = o(v, s);
	return [h, d, p, v]
}
function d(t) {
	var e, n = "", r = 32 * t.length;
	for (e = 0; e < r; e += 8)
		n += String.fromCharCode(t[e >> 5] >>> e % 32 & 255);
	return n
}
function p(t) {
	var e, n = [];
	for (n[(t.length >> 2) - 1] = void 0,
	e = 0; e < n.length; e += 1)
		n[e] = 0;
	var r = 8 * t.length;
	for (e = 0; e < r; e += 8)
		n[e >> 5] |= (255 & t.charCodeAt(e / 8)) << e % 32;
	return n
}
function v(t) {
	return d(h(p(t), 8 * t.length))
}
function g(t, e) {
	var n, r, i = p(t), o = [], a = [];
	for (o[15] = a[15] = void 0,
	i.length > 16 && (i = h(i, 8 * t.length)),
	n = 0; n < 16; n += 1)
		o[n] = 909522486 ^ i[n],
		a[n] = 1549556828 ^ i[n];
	return r = h(o.concat(p(e)), 512 + 8 * e.length),
	d(h(a.concat(r), 640))
}
function m(t) {
	var e, n, r = "0123456789abcdef", i = "";
	for (n = 0; n < t.length; n += 1)
		e = t.charCodeAt(n),
		i += r.charAt(e >>> 4 & 15) + r.charAt(15 & e);
	return i
}
function b(t) {
	return unescape(encodeURIComponent(t))
}
function y(t) {
	return v(b(t))
}
function _(t) {
	return m(y(t))
}
function w(t, e) {
	return g(b(t), b(e))
}
function x(t, e) {
	return m(w(t, e))
}
function S(t, e, n) {
	return e ? n ? w(e, t) : x(e, t) : n ? y(t) : _(t)
}




// 定义 DClass
var DClass = (function() {
    function t(e) {
        this.timeSeed = e;
        this.used = false;
    }
    t.prototype.get = function() {
        this.used = true;
        return this.timeSeed ? this.timeSeed.substring(0, 20) : "";
    };
    t.prototype.getTimeCode = function() {
        var e = S(this.timeSeed);
        return e.substring(0, 14);
    };
    return t;
})();



var e = {
    encrypt_method: "0",
    gm_check_sign_switch: "0",
    key: "C0CEE4B6914866965BE0D3D3F155D85FA296CBBF13956EDBB32146C8C61E36B164BDD8F399CFEF37B60AB650BABBB1F62EFA679119B375E0E1A0272D7CD84DD6DE8D2C618753A7C2D79AEFBC2249F23A8797A4AC885CE795CBA8DB27F8BEBCC0DADCC155ED216BAE6A2923A98E45A7CD1BE1F330CB1B66054B2FC9A117522633",
    key_front_and_broker: "****************************************************************************************************************************************************************************************************************************************************************",
    needcheck: "1",
    retcode: 0,
    retmsg: "OK",
    timeseed: "17485820861926175275"
};
var encryptor = new re(false); // 假设 needSign 为 false
var keys=encryptor.setKey(e.key)
// 定义 i 对象
var i = {
    key: {
        key: keys // 替换为实际的密钥
    },
    encode: function(e, t) {
        var n = t.get();
        var a = t.getTimeCode();
        var o = "0000000000000000000000000000000000000000";
        var r = ae.encrypt1(keys, e, n, a);
        return "".concat(a, "000000").concat(o).concat(r);
    }
};

// 导出接口
function encode(n, o) {
    var t = new DClass(o);
    return i.encode(n, t);
}