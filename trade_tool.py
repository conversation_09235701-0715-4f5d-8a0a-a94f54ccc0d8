#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易执行工具

提供命令行界面，用于执行买入、卖出和撤单操作
"""

import os
import sys
import json
import logging
import argparse
import requests
from typing import Dict, Any

from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger, format_stock_code
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def load_cookies(cookies_file: str) -> Dict[str, str]:
    """
    从文件加载cookies
    
    Args:
        cookies_file: cookies文件路径，JSON格式
        
    Returns:
        Dict: cookies字典
    """
    try:
        if not os.path.exists(cookies_file):
            print(f"{Fore.RED}Cookies文件不存在: {cookies_file}")
            return {}
            
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
            print(f"{Fore.GREEN}成功从 {cookies_file} 加载cookies")
            return cookies
    except Exception as e:
        print(f"{Fore.RED}加载cookies失败: {e}")
        return {}

def setup_session(cookies: Dict[str, str]) -> requests.Session:
    """
    设置会话，添加cookies
    
    Args:
        cookies: cookies字典
        
    Returns:
        requests.Session: 会话对象
    """
    session = requests.Session()
    
    # 添加cookies到会话
    for key, value in cookies.items():
        session.cookies.set(key, value)
    
    return session

def setup_user_info() -> Dict[str, Any]:
    """
    设置用户信息
    
    Returns:
        Dict: 用户信息字典
    """
    return {
        "account_id": "",  # 会自动从交易接口获取
        "stockholder_code": "",  # 会自动从交易接口获取
        "psw_session": "",  # 通常从cookies中获取
        "app_info": {
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "************"
        }
    }

def execute_buy(trade_client: TradeClient, stock_code: str, price: float, volume: int) -> None:
    """
    执行买入操作
    
    Args:
        trade_client: 交易客户端
        stock_code: 股票代码
        price: 买入价格
        volume: 买入数量
    """
    print(f"{Fore.CYAN}{Style.BRIGHT}===== 执行买入操作 ====={Style.RESET_ALL}")
    print(f"{Fore.WHITE}股票代码: {Fore.YELLOW}{stock_code}")
    print(f"{Fore.WHITE}买入价格: {Fore.YELLOW}{price}元")
    print(f"{Fore.WHITE}买入数量: {Fore.YELLOW}{volume}份")
    
    # 获取股票信息
    stock_info_result = trade_client.get_stock_info(stock_code)
    if stock_info_result["status"] != "success":
        print(f"{Fore.RED}获取股票信息失败: {stock_info_result['message']}")
        return
    
    # 执行买入操作
    buy_result = trade_client.buy(stock_code, price, volume, stock_info_result["data"])
    
    if buy_result["status"] == "success":
        print(f"{Fore.GREEN}买入委托提交成功:")
        print(f"{Fore.WHITE}委托编号: {Fore.YELLOW}{buy_result['entrust_no']}")
        print(f"{Fore.WHITE}委托状态: {Fore.YELLOW}{buy_result['data'].get('trade_state', '未知')}")
        print(f"{Fore.WHITE}委托时间: {Fore.YELLOW}{buy_result['data'].get('trade_time', '未知')}")
    else:
        print(f"{Fore.RED}买入委托提交失败: {buy_result['message']}")

def execute_sell(trade_client: TradeClient, stock_code: str, price: float, volume: int) -> None:
    """
    执行卖出操作
    
    Args:
        trade_client: 交易客户端
        stock_code: 股票代码
        price: 卖出价格
        volume: 卖出数量
    """
    print(f"{Fore.CYAN}{Style.BRIGHT}===== 执行卖出操作 ====={Style.RESET_ALL}")
    print(f"{Fore.WHITE}股票代码: {Fore.YELLOW}{stock_code}")
    print(f"{Fore.WHITE}卖出价格: {Fore.YELLOW}{price}元")
    print(f"{Fore.WHITE}卖出数量: {Fore.YELLOW}{volume}份")
    
    # 获取股票信息
    stock_info_result = trade_client.get_stock_info(stock_code)
    if stock_info_result["status"] != "success":
        print(f"{Fore.RED}获取股票信息失败: {stock_info_result['message']}")
        return
    
    # 执行卖出操作
    sell_result = trade_client.sell(stock_code, price, volume, stock_info_result["data"])
    
    if sell_result["status"] == "success":
        print(f"{Fore.GREEN}卖出委托提交成功:")
        print(f"{Fore.WHITE}委托编号: {Fore.YELLOW}{sell_result['entrust_no']}")
        print(f"{Fore.WHITE}委托状态: {Fore.YELLOW}{sell_result['data'].get('trade_state', '未知')}")
        print(f"{Fore.WHITE}委托时间: {Fore.YELLOW}{sell_result['data'].get('trade_time', '未知')}")
    else:
        print(f"{Fore.RED}卖出委托提交失败: {sell_result['message']}")

def execute_cancel(trade_client: TradeClient, contract_no: str, trade_time: str, stock_code: str = "") -> None:
    """
    执行撤单操作
    
    Args:
        trade_client: 交易客户端
        contract_no: 合同编号
        trade_time: 交易时间
        stock_code: 股票代码
    """
    print(f"{Fore.CYAN}{Style.BRIGHT}===== 执行撤单操作 ====={Style.RESET_ALL}")
    print(f"{Fore.WHITE}合同编号: {Fore.YELLOW}{contract_no}")
    print(f"{Fore.WHITE}交易时间: {Fore.YELLOW}{trade_time}")
    
    if not stock_code:
        # 获取委托记录，查找对应的股票代码
        account_info = trade_client.trade_show()
        if account_info["status"] == "success":
            orders = account_info["data"].get("orders", [])
            for order in orders:
                if order.get("contract_no") == contract_no:
                    stock_code = order.get("code", "")
                    break
        
        if not stock_code:
            print(f"{Fore.RED}未找到合同编号 {contract_no} 对应的股票代码，请手动提供")
            return
    
    # 格式化股票代码
    formatted_code = format_stock_code(stock_code)
    market = "1" if formatted_code.startswith("SH") else "0"  # 1表示上海，0表示深圳
    clean_code = formatted_code.replace("SH", "").replace("SZ", "")
    
    # 获取股票信息
    stock_info_result = trade_client.get_stock_info(stock_code)
    if stock_info_result["status"] != "success":
        print(f"{Fore.RED}获取股票信息失败: {stock_info_result['message']}")
        return
    
    # 执行撤单操作
    # 1表示买入委托，2表示卖出委托，这里因为不确定是买入还是卖出，所以传空字符串让交易接口自动判断
    cancel_result = trade_client.cancel(contract_no, trade_time, clean_code, market, "", stock_info_result["data"])
    
    if cancel_result["status"] == "success":
        print(f"{Fore.GREEN}撤单请求提交成功")
        if "entrust_no" in cancel_result and cancel_result["entrust_no"]:
            print(f"{Fore.WHITE}撤单委托编号: {Fore.YELLOW}{cancel_result['entrust_no']}")
    else:
        print(f"{Fore.RED}撤单请求提交失败: {cancel_result['message']}")

def parse_arguments():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(description='交易执行工具')
    
    parser.add_argument('--action', choices=['buy', 'sell', 'cancel'], required=True,
                      help='交易动作: buy(买入), sell(卖出), cancel(撤单)')
    parser.add_argument('--code', type=str,
                      help='股票代码，买入和卖出操作必须提供')
    parser.add_argument('--price', type=float,
                      help='价格，买入和卖出操作必须提供')
    parser.add_argument('--volume', type=int,
                      help='数量，买入和卖出操作必须提供，必须为100的整数倍')
    parser.add_argument('--entrust-no', type=str,
                      help='委托编号，撤单操作必须提供')
    parser.add_argument('--trade-time', type=str,
                      help='交易时间，撤单操作必须提供')
    parser.add_argument('--cookies', type=str, default='cookies.json',
                      help='cookies文件路径')
                      
    return parser.parse_args()

def validate_arguments(args):
    """
    验证命令行参数
    
    Args:
        args: 命令行参数
        
    Returns:
        bool: 参数是否有效
    """
    if args.action in ['buy', 'sell']:
        if not args.code:
            print(f"{Fore.RED}错误: 买入和卖出操作必须提供股票代码")
            return False
        if not args.price:
            print(f"{Fore.RED}错误: 买入和卖出操作必须提供价格")
            return False
        if not args.volume:
            print(f"{Fore.RED}错误: 买入和卖出操作必须提供数量")
            return False
        if args.volume <= 0 or args.volume % 100 != 0:
            print(f"{Fore.RED}错误: 交易数量必须为正整数且为100的整数倍")
            return False
    elif args.action == 'cancel':
        if not args.entrust_no:
            print(f"{Fore.RED}错误: 撤单操作必须提供委托编号")
            return False
        if not args.trade_time:
            print(f"{Fore.RED}错误: 撤单操作必须提供交易时间")
            return False
    
    return True

def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 验证参数有效性
    if not validate_arguments(args):
        return
    
    # 设置日志
    logger = setup_logger(log_level=logging.INFO)
    logger.info("开始执行交易工具")
    
    # 加载cookies
    cookies = load_cookies(args.cookies)
    if not cookies:
        logger.error("未找到有效的cookies，请先保存cookies到文件")
        return
    
    # 设置会话
    session = setup_session(cookies)
    
    # 设置用户信息
    user_info = setup_user_info()
    
    # 创建交易客户端
    trade_client = TradeClient(session, user_info, logger)
    logger.info("交易客户端创建成功")
    
    try:
        # 执行对应的交易操作
        if args.action == 'buy':
            execute_buy(trade_client, args.code, args.price, args.volume)
        elif args.action == 'sell':
            execute_sell(trade_client, args.code, args.price, args.volume)
        elif args.action == 'cancel':
            execute_cancel(trade_client, args.entrust_no, args.trade_time, args.code)
        
    except Exception as e:
        logger.error(f"交易执行过程中发生错误: {e}")
        print(f"{Fore.RED}交易执行过程中发生错误: {e}")
    
    logger.info("交易工具执行完毕")
    # 将session的cookies保存到cookies.json文件
    with open(args.cookies, 'w') as f:
        json.dump(session.cookies.get_dict(), f, indent=2)
    logger.info(f"Cookies已成功保存到{args.cookies}文件")
    print(f"{Fore.GREEN}Cookies已成功保存到{args.cookies}文件")

if __name__ == "__main__":
    main() 