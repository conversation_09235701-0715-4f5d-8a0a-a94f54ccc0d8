{"log": {"version": "1.2", "creator": {"name": "<PERSON>", "version": "4.6.7"}, "entries": [{"startedDateTime": "2025-06-06T10:53:57.881+08:00", "time": 480, "request": {"method": "GET", "url": "https://wzq.csc108.com/cgi-bin/outwx_login.cgi?returl=%2Fmp%2Foem%2Findex.html%23%2Fwj_trade%2Fapply%2Findex%3Fqrcode%3Dgdpmfb", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "wzq.csc108.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "Sec-Fetch-Site", "value": "none"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}], "queryString": [{"name": "returl", "value": "/mp/oem/index.html#/wj_trade/apply/index?qrcode=gdpmfb"}], "headersSize": 716, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 302, "statusText": "Found", "httpVersion": "HTTP/1.1", "cookies": [{"name": "qlappid", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qlskey", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qluin", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qq_logtype", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wx_session_time", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qlappid", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qlskey", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qluin", "value": "", "path": "/", "domain": "csc108.com", "expires": "Thu, 01 Jan 1970 00:00:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}], "headers": [{"name": "Date", "value": "Fri, 06 Jun 2025 02:53:58 GMT"}, {"name": "Content-Type", "value": "text/html; charset=iso-8859-1"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Set-<PERSON><PERSON>", "value": "qlappid=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qlskey=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qluin=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qq_logtype=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wx_session_time=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qlappid=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qlskey=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qluin=; EXPIRES=Thu, 01 Jan 1970 00:00:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Location", "value": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx75e48a53a33093e0&redirect_uri=https%3A%2F%2Fwxgateway.csc108.com%2Fwx%2Fgate%2Foem%2Fbase_code%3FappId%3Dwx75e48a53a33093e0%26url%3Dhttps%253A%252F%252Fwzq.csc108.com%252Fcgi-bin%252Foutwx_login.cgi%253Fredirect_out_times%253D1%2526req_time%253D1749178438%2526returl%253D%25252Fmp%25252Foem%25252Findex.html%252523%25252Fwj_trade%25252Fapply%25252Findex%25253Fqrcode%25253Dgdpmfb&response_type=code&scope=snsapi_base&state=a#wechat_redirect"}, {"name": "x-via", "value": "1.1 PS-HET-018Sv41:34 (Cdn Cache Server V2.0)"}, {"name": "x-ws-request-id", "value": "68425846_PS-HET-018Sv41_45756-50650"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 710, "mimeType": "text/html; charset=iso-8859-1", "text": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>302 Found</title>\n</head><body>\n<h1>Found</h1>\n<p>The document has moved <a href=\"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx75e48a53a33093e0&amp;redirect_uri=https%3A%2F%2Fwxgateway.csc108.com%2Fwx%2Fgate%2Foem%2Fbase_code%3FappId%3Dwx75e48a53a33093e0%26url%3Dhttps%253A%252F%252Fwzq.csc108.com%252Fcgi-bin%252Foutwx_login.cgi%253Fredirect_out_times%253D1%2526req_time%253D1749178438%2526returl%253D%25252Fmp%25252Foem%25252Findex.html%252523%25252Fwj_trade%25252Fapply%25252Findex%25253Fqrcode%25253Dgdpmfb&amp;response_type=code&amp;scope=snsapi_base&amp;state=a#wechat_redirect\">here</a>.</p>\n</body></html>\n"}, "redirectURL": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx75e48a53a33093e0&redirect_uri=https%3A%2F%2Fwxgateway.csc108.com%2Fwx%2Fgate%2Foem%2Fbase_code%3FappId%3Dwx75e48a53a33093e0%26url%3Dhttps%253A%252F%252Fwzq.csc108.com%252Fcgi-bin%252Foutwx_login.cgi%253Fredirect_out_times%253D1%2526req_time%253D1749178438%2526returl%253D%25252Fmp%25252Foem%25252Findex.html%252523%25252Fwj_trade%25252Fapply%25252Findex%25253Fqrcode%25253Dgdpmfb&response_type=code&scope=snsapi_base&state=a#wechat_redirect", "headersSize": 0, "bodySize": 710}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": 71, "connect": 278, "ssl": 201, "send": 3, "wait": 77, "receive": 51}}, {"startedDateTime": "2025-06-06T10:53:59.321+08:00", "time": 99, "request": {"method": "GET", "url": "https://wxgateway.csc108.com/wx/gate/oem/base_code?appId=wx75e48a53a33093e0&url=https%3A%2F%2Fwzq.csc108.com%2Fcgi-bin%2Foutwx_login.cgi%3Fredirect_out_times%3D1%26req_time%3D1749178438%26returl%3D%252Fmp%252Foem%252Findex.html%2523%252Fwj_trade%252Fapply%252Findex%253Fqrcode%253Dgdpmfb&code=091nbo0w3EH73537Sp1w3ekoQ21nbo0R&state=a", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "wxgateway.csc108.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "Sec-Fetch-Site", "value": "none"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}], "queryString": [{"name": "appId", "value": "wx75e48a53a33093e0"}, {"name": "url", "value": "https://wzq.csc108.com/cgi-bin/outwx_login.cgi?redirect_out_times=1&req_time=1749178438&returl=%2Fmp%2Foem%2Findex.html%23%2Fwj_trade%2Fapply%2Findex%3Fqrcode%3Dgdpmfb"}, {"name": "code", "value": "091nbo0w3EH73537Sp1w3ekoQ21nbo0R"}, {"name": "state", "value": "a"}], "headersSize": 923, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 302, "statusText": "Found", "httpVersion": "HTTP/1.1", "cookies": [{"name": "HttpOnly", "value": "true", "path": null, "domain": null, "expires": null, "httpOnly": false, "secure": true, "comment": null, "_maxAge": null}], "headers": [{"name": "Date", "value": "Fri, 06 Jun 2025 02:53:59 GMT"}, {"name": "Content-Length", "value": "0"}, {"name": "Location", "value": "https://wzq.csc108.com/cgi-bin/outwx_login.cgi?redirect_out_times=1&req_time=1749178438&returl=%2Fmp%2Foem%2Findex.html%23%2Fwj_trade%2Fapply%2Findex%3Fqrcode%3Dgdpmfb&param=091nbo0w3EH73537Sp1w3ekoQ21nbo0R"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"name": "Set-<PERSON><PERSON>", "value": "HttpOnly=true;Secure=true"}, {"name": "x-via", "value": "1.1 PSbjzwdx5aa31:2 (Cdn Cache Server V2.0), 1.1 PS-HET-018Sv41:32 (Cdn Cache Server V2.0)"}, {"name": "x-ws-request-id", "value": "68425847_PS-HET-018Sv41_44461-56177"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 0, "mimeType": null, "text": "", "encoding": "base64"}, "redirectURL": "https://wzq.csc108.com/cgi-bin/outwx_login.cgi?redirect_out_times=1&req_time=1749178438&returl=%2Fmp%2Foem%2Findex.html%23%2Fwj_trade%2Fapply%2Findex%3Fqrcode%3Dgdpmfb&param=091nbo0w3EH73537Sp1w3ekoQ21nbo0R", "headersSize": 0, "bodySize": 0}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": 13, "connect": 48, "ssl": 34, "send": 2, "wait": 34, "receive": 2}}, {"startedDateTime": "2025-06-06T10:53:59.818+08:00", "time": 596, "request": {"method": "GET", "url": "https://wzq.csc108.com/cgi-bin/outwx_login.cgi?redirect_out_times=1&req_time=1749178438&returl=%2Fmp%2Foem%2Findex.html%23%2Fwj_trade%2Fapply%2Findex%3Fqrcode%3Dgdpmfb&param=091nbo0w3EH73537Sp1w3ekoQ21nbo0R", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "wzq.csc108.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "Sec-Fetch-Site", "value": "none"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}], "queryString": [{"name": "redirect_out_times", "value": "1"}, {"name": "req_time", "value": "1749178438"}, {"name": "returl", "value": "/mp/oem/index.html#/wj_trade/apply/index?qrcode=gdpmfb"}, {"name": "param", "value": "091nbo0w3EH73537Sp1w3ekoQ21nbo0R"}], "headersSize": 796, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 302, "statusText": "Found", "httpVersion": "HTTP/1.1", "cookies": [{"name": "qlappid", "value": "wx9cf8c670ebd68ce4", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qlskey", "value": "110a7528ddc89595101*bH4JbHI44a", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qluin", "value": "os-ppuF7XdUGL-hJG43C1yFNbBm8", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qq_logtype", "value": "16", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qs_openid", "value": "o4NN4jlQfrkl5xecDR9NKp2wFN04", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "qs_unionid", "value": "oURPMwqW34OdDH0smHSUJPgLnaAI", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "trade_in_cmschina", "value": "1", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "trade_in_partner", "value": "1", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wx_session_time", "value": "*************", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_dealer", "value": "11100", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qlappid", "value": "wx9cf8c670ebd68ce4", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qlskey", "value": "110a7528ddc89595101*bH4JbHI44a", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}, {"name": "wzq_qluin", "value": "os-ppuF7XdUGL-hJG43C1yFNbBm8", "path": "/", "domain": "csc108.com", "expires": "Wed, 11 Jun 25 02:54:00 GMT", "httpOnly": false, "secure": false, "comment": null, "_maxAge": null}], "headers": [{"name": "Date", "value": "Fri, 06 Jun 2025 02:54:00 GMT"}, {"name": "Content-Type", "value": "text/html; charset=iso-8859-1"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Set-<PERSON><PERSON>", "value": "qlappid=wx9cf8c670ebd68ce4; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qlskey=110a7528ddc89595101*bH4JbHI44a; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qq_logtype=16; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "trade_in_cmschina=1; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "trade_in_partner=1; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wx_session_time=*************; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_dealer=11100; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qlappid=wx9cf8c670ebd68ce4; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qlskey=110a7528ddc89595101*bH4JbHI44a; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Set-<PERSON><PERSON>", "value": "wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; EXPIRES=Wed, 11 Jun 25 02:54:00 GMT; PATH=/; DOMAIN=csc108.com;"}, {"name": "Location", "value": "https://wzq.csc108.com/mp/oem/index.html#/wj_trade/apply/index?_=*************&account_mode=0&login_jump_times=1&qrcode=gdpmfb&set_mode="}, {"name": "x-via", "value": "1.1 PS-HET-018Sv41:34 (Cdn Cache Server V2.0)"}, {"name": "x-ws-request-id", "value": "68425847_PS-HET-018Sv41_45756-50702"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 346, "mimeType": "text/html; charset=iso-8859-1", "text": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>302 Found</title>\n</head><body>\n<h1>Found</h1>\n<p>The document has moved <a href=\"https://wzq.csc108.com/mp/oem/index.html#/wj_trade/apply/index?_=*************&amp;account_mode=0&amp;login_jump_times=1&amp;qrcode=gdpmfb&amp;set_mode=\">here</a>.</p>\n</body></html>\n"}, "redirectURL": "https://wzq.csc108.com/mp/oem/index.html#/wj_trade/apply/index?_=*************&account_mode=0&login_jump_times=1&qrcode=gdpmfb&set_mode=", "headersSize": 0, "bodySize": 346}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 592, "receive": 3}}, {"startedDateTime": "2025-06-06T10:53:59.92+08:00", "time": 184, "request": {"method": "GET", "url": "https://wzq.csc108.com/sw.js?v=zhongxinjiantou_202409041640", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "wzq.csc108.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Cache-Control", "value": "max-age=0"}, {"name": "Accept", "value": "*/*"}, {"name": "Service-Worker", "value": "script"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "Sec-Fetch-Mode", "value": "same-origin"}, {"name": "Sec-Fetch-Dest", "value": "serviceworker"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://wzq.csc108.com/sw.js?v=zhongxinjiantou_202409041640"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}], "queryString": [{"name": "v", "value": "zhongxinjiantou_202409041640"}], "headersSize": 516, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Fri, 06 Jun 2025 02:54:00 GMT"}, {"name": "Content-Type", "value": "application/x-javascript"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Last-Modified", "value": "Wed, 04 Sep 2024 08:42:48 GMT"}, {"name": "ETag", "value": "W/\"66d81d88-2109e\""}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "x-via", "value": "1.1 PS-HET-018Sv41:2 (Cdn Cache Server V2.0)"}, {"name": "x-ws-request-id", "value": "68425848_PS-HET-018Sv41_31343-57917"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 135326, "compression": 112624, "mimeType": "application/x-javascript", "text": "KGZ1bmN0aW9uKCl7InVzZSBzdHJpY3QiO3ZhciB0PXszMDY6ZnVuY3Rpb24oKXt0cnl7c2VsZlsid29ya2JveDpjb3JlOjYuNS4zIl0mJl8oKX1jYXRjaCh0KXt9fSw0MDg6ZnVuY3Rpb24oKXt0cnl7c2VsZlsid29ya2JveDpwcmVjYWNoaW5nOjYuNS4zIl0mJl8oKX1jYXRjaCh0KXt9fSwxODg6ZnVuY3Rpb24oKXt0cnl7c2VsZlsid29ya2JveDpyb3V0aW5nOjYuNS4zIl0mJl8oKX1jYXRjaCh0KXt9fSw1Nzc6ZnVuY3Rpb24oKXt0cnl7c2VsZlsid29ya2JveDpzdHJhdGVnaWVzOjYuNS4zIl0mJl8oKX1jYXRjaCh0KXt9fX0sZT17fTtmdW5jdGlvbiByKG4pe3ZhciBvPWVbbl07aWYodm9pZCAwIT09bylyZXR1cm4gby5leHBvcnRzO3ZhciBpPWVbbl09e2V4cG9ydHM6e319O3JldHVybiB0W25dKGksaS5leHBvcnRzLHIpLGkuZXhwb3J0c30hZnVuY3Rpb24oKXtyKDMwNik7dmFyIHQ9bnVsbCxlPWZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10LHI9YXJndW1lbnRzLmxlbmd0aCxuPW5ldyBBcnJheShyPjE/ci0xOjApLG89MTtvPHI7bysrKW5bby0xXT1hcmd1bWVudHNbb107cmV0dXJuIG4ubGVuZ3RoPjAmJihlKz0iIDo6ICIuY29uY2F0KEpTT04uc3RyaW5naWZ5KG4pKSksZX0sbj1lO2Z1bmN0aW9uIG8odCl7cmV0dXJuIG89ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/ZnVuY3Rpb24odCl7cmV0dXJuIHR5cGVvZiB0fTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmdC5jb25zdHJ1Y3Rvcj09PVN5bWJvbCYmdCE9PVN5bWJvbC5wcm90b3R5cGU/InN5bWJvbCI6dHlwZW9mIHR9LG8odCl9ZnVuY3Rpb24gaSh0LGUpe2Zvcih2YXIgcj0wO3I8ZS5sZW5ndGg7cisrKXt2YXIgbj1lW3JdO24uZW51bWVyYWJsZT1uLmVudW1lcmFibGV8fCExLG4uY29uZmlndXJhYmxlPSEwLCJ2YWx1ZSJpbiBuJiYobi53cml0YWJsZT0hMCksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsbi5rZXksbil9fWZ1bmN0aW9uIGEodCxlLHIpe3JldHVybiBlJiZpKHQucHJvdG90eXBlLGUpLHImJmkodCxyKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSx0fWZ1bmN0aW9uIGModCxlKXtpZighKHQgaW5zdGFuY2VvZiBlKSl0aHJvdyBuZXcgVHlwZUVycm9yKCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb24iKX1mdW5jdGlvbiB1KHQsZSl7aWYoImZ1bmN0aW9uIiE9PXR5cGVvZiBlJiZudWxsIT09ZSl0aHJvdyBuZXcgVHlwZUVycm9yKCJTdXBlciBleHByZXNzaW9uIG11c3QgZWl0aGVyIGJlIG51bGwgb3IgYSBmdW5jdGlvbiIpO3QucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoZSYmZS5wcm90b3R5cGUse2NvbnN0cnVjdG9yOnt2YWx1ZTp0LHdyaXRhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMH19KSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSxlJiZkKHQsZSl9ZnVuY3Rpb24gcyh0KXt2YXIgZT15KCk7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIHIsbj1tKHQpO2lmKGUpe3ZhciBvPW0odGhpcykuY29uc3RydWN0b3I7cj1SZWZsZWN0LmNvbnN0cnVjdChuLGFyZ3VtZW50cyxvKX1lbHNlIHI9bi5hcHBseSh0aGlzLGFyZ3VtZW50cyk7cmV0dXJuIGYodGhpcyxyKX19ZnVuY3Rpb24gZih0LGUpe2lmKGUmJigib2JqZWN0Ij09PW8oZSl8fCJmdW5jdGlvbiI9PT10eXBlb2YgZSkpcmV0dXJuIGU7aWYodm9pZCAwIT09ZSl0aHJvdyBuZXcgVHlwZUVycm9yKCJEZXJpdmVkIGNvbnN0cnVjdG9ycyBtYXkgb25seSByZXR1cm4gb2JqZWN0IG9yIHVuZGVmaW5lZCIpO3JldHVybiBsKHQpfWZ1bmN0aW9uIGwodCl7aWYodm9pZCAwPT09dCl0aHJvdyBuZXcgUmVmZXJlbmNlRXJyb3IoInRoaXMgaGFzbid0IGJlZW4gaW5pdGlhbGlzZWQgLSBzdXBlcigpIGhhc24ndCBiZWVuIGNhbGxlZCIpO3JldHVybiB0fWZ1bmN0aW9uIGgodCl7dmFyIGU9ImZ1bmN0aW9uIj09PXR5cGVvZiBNYXA/bmV3IE1hcDp2b2lkIDA7cmV0dXJuIGg9ZnVuY3Rpb24odCl7aWYobnVsbD09PXR8fCF2KHQpKXJldHVybiB0O2lmKCJmdW5jdGlvbiIhPT10eXBlb2YgdCl0aHJvdyBuZXcgVHlwZUVycm9yKCJTdXBlciBleHByZXNzaW9uIG11c3QgZWl0aGVyIGJlIG51bGwgb3IgYSBmdW5jdGlvbiIpO2lmKCJ1bmRlZmluZWQiIT09dHlwZW9mIGUpe2lmKGUuaGFzKHQpKXJldHVybiBlLmdldCh0KTtlLnNldCh0LHIpfWZ1bmN0aW9uIHIoKXtyZXR1cm4gcCh0LGFyZ3VtZW50cyxtKHRoaXMpLmNvbnN0cnVjdG9yKX1yZXR1cm4gci5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZSh0LnByb3RvdHlwZSx7Y29uc3RydWN0b3I6e3ZhbHVlOnIsZW51bWVyYWJsZTohMSx3cml0YWJsZTohMCxjb25maWd1cmFibGU6ITB9fSksZChyLHQpfSxoKHQpfWZ1bmN0aW9uIHAodCxlLHIpe3JldHVybiBwPXkoKT9SZWZsZWN0LmNvbnN0cnVjdC5iaW5kKCk6ZnVuY3Rpb24odCxlLHIpe3ZhciBuPVtudWxsXTtuLnB1c2guYXBwbHkobixlKTt2YXIgbz1GdW5jdGlvbi5iaW5kLmFwcGx5KHQsbiksaT1uZXcgbztyZXR1cm4gciYmZChpLHIucHJvdG90eXBlKSxpfSxwLmFwcGx5KG51bGwsYXJndW1lbnRzKX1mdW5jdGlvbiB5KCl7aWYoInVuZGVmaW5lZCI9PT10eXBlb2YgUmVmbGVjdHx8IVJlZmxlY3QuY29uc3RydWN0KXJldHVybiExO2lmKFJlZmxlY3QuY29uc3RydWN0LnNoYW0pcmV0dXJuITE7aWYoImZ1bmN0aW9uIj09PXR5cGVvZiBQcm94eSlyZXR1cm4hMDt0cnl7cmV0dXJuIEJvb2xlYW4ucHJvdG90eXBlLnZhbHVlT2YuY2FsbChSZWZsZWN0LmNvbnN0cnVjdChCb29sZWFuLFtdLChmdW5jdGlvbigpe30pKSksITB9Y2F0Y2godCl7cmV0dXJuITF9fWZ1bmN0aW9uIHYodCl7cmV0dXJuLTEhPT1GdW5jdGlvbi50b1N0cmluZy5jYWxsKHQpLmluZGV4T2YoIltuYXRpdmUgY29kZV0iKX1mdW5jdGlvbiBkKHQsZSl7cmV0dXJuIGQ9T2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5zZXRQcm90b3R5cGVPZi5iaW5kKCk6ZnVuY3Rpb24odCxlKXtyZXR1cm4gdC5fX3Byb3RvX189ZSx0fSxkKHQsZSl9ZnVuY3Rpb24gbSh0KXtyZXR1cm4gbT1PYmplY3Quc2V0UHJvdG90eXBlT2Y/T2JqZWN0LmdldFByb3RvdHlwZU9mLmJpbmQoKTpmdW5jdGlvbih0KXtyZXR1cm4gdC5fX3Byb3RvX198fE9iamVjdC5nZXRQcm90b3R5cGVPZih0KX0sbSh0KX12YXIgZz1mdW5jdGlvbih0KXt1KHIsdCk7dmFyIGU9cyhyKTtmdW5jdGlvbiByKHQsbyl7dmFyIGk7Yyh0aGlzLHIpO3ZhciBhPW4odCxvKTtyZXR1cm4gaT1lLmNhbGwodGhpcyxhKSxpLm5hbWU9dCxpLmRldGFpbHM9byxpfXJldHVybiBhKHIpfShoKEVycm9yKSk7dmFyIHc9bmV3IFNldDt2YXIgYix4PXtnb29nbGVBbmFseXRpY3M6Imdvb2dsZUFuYWx5dGljcyIscHJlY2FjaGU6InByZWNhY2hlLXYyIixwcmVmaXg6Indvcmtib3giLHJ1bnRpbWU6InJ1bnRpbWUiLHN1ZmZpeDoidW5kZWZpbmVkIiE9PXR5cGVvZiByZWdpc3RyYXRpb24/cmVnaXN0cmF0aW9uLnNjb3BlOiIifSxMPWZ1bmN0aW9uKHQpe3JldHVyblt4LnByZWZpeCx0LHguc3VmZml4XS5maWx0ZXIoKGZ1bmN0aW9uKHQpe3JldHVybiB0JiZ0Lmxlbmd0aD4wfSkpLmpvaW4oIi0iKX0saz1mdW5jdGlvbih0KXtmb3IodmFyIGU9MCxyPU9iamVjdC5rZXlzKHgpO2U8ci5sZW5ndGg7ZSsrKXt2YXIgbj1yW2VdO3Qobil9fSxFPXt1cGRhdGVEZXRhaWxzOmZ1bmN0aW9uKHQpe2soKGZ1bmN0aW9uKGUpeyJzdHJpbmciPT09dHlwZW9mIHRbZV0mJih4W2VdPXRbZV0pfSkpfSxnZXRHb29nbGVBbmFseXRpY3NOYW1lOmZ1bmN0aW9uKHQpe3JldHVybiB0fHxMKHguZ29vZ2xlQW5hbHl0aWNzKX0sZ2V0UHJlY2FjaGVOYW1lOmZ1bmN0aW9uKHQpe3JldHVybiB0fHxMKHgucHJlY2FjaGUpfSxnZXRQcmVmaXg6ZnVuY3Rpb24oKXtyZXR1cm4geC5wcmVmaXh9LGdldFJ1bnRpbWVOYW1lOmZ1bmN0aW9uKHQpe3JldHVybiB0fHxMKHgucnVudGltZSl9LGdldFN1ZmZpeDpmdW5jdGlvbigpe3JldHVybiB4LnN1ZmZpeH19O2Z1bmN0aW9uIF8odCl7cmV0dXJuIF89ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/ZnVuY3Rpb24odCl7cmV0dXJuIHR5cGVvZiB0fTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmdC5jb25zdHJ1Y3Rvcj09PVN5bWJvbCYmdCE9PVN5bWJvbC5wcm90b3R5cGU/InN5bWJvbCI6dHlwZW9mIHR9LF8odCl9ZnVuY3Rpb24gTygpey8qISByZWdlbmVyYXRvci1ydW50aW1lIC0tIENvcHlyaWdodCAoYykgMjAxNC1wcmVzZW50LCBGYWNlYm9vaywgSW5jLiAtLSBsaWNlbnNlIChNSVQpOiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVnZW5lcmF0b3IvYmxvYi9tYWluL0xJQ0VOU0UgKi9PPWZ1bmN0aW9uKCl7cmV0dXJuIHR9O3ZhciB0PXt9LGU9T2JqZWN0LnByb3RvdHlwZSxyPWUuaGFzT3duUHJvcGVydHksbj0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sP1N5bWJvbDp7fSxvPW4uaXRlcmF0b3J8fCJAQGl0ZXJhdG9yIixpPW4uYXN5bmNJdGVyYXRvcnx8IkBAYXN5bmNJdGVyYXRvciIsYT1uLnRvU3RyaW5nVGFnfHwiQEB0b1N0cmluZ1RhZyI7ZnVuY3Rpb24gYyh0LGUscil7cmV0dXJuIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LGUse3ZhbHVlOnIsZW51bWVyYWJsZTohMCxjb25maWd1cmFibGU6ITAsd3JpdGFibGU6ITB9KSx0W2VdfXRyeXtjKHt9LCIiKX1jYXRjaChqKXtjPWZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdFtlXT1yfX1mdW5jdGlvbiB1KHQsZSxyLG4pe3ZhciBvPWUmJmUucHJvdG90eXBlIGluc3RhbmNlb2YgbD9lOmwsaT1PYmplY3QuY3JlYXRlKG8ucHJvdG90eXBlKSxhPW5ldyBrKG58fFtdKTtyZXR1cm4gaS5faW52b2tlPWZ1bmN0aW9uKHQsZSxyKXt2YXIgbj0ic3VzcGVuZGVkU3RhcnQiO3JldHVybiBmdW5jdGlvbihvLGkpe2lmKCJleGVjdXRpbmciPT09bil0aHJvdyBuZXcgRXJyb3IoIkdlbmVyYXRvciBpcyBhbHJlYWR5IHJ1bm5pbmciKTtpZigiY29tcGxldGVkIj09PW4pe2lmKCJ0aHJvdyI9PT1vKXRocm93IGk7cmV0dXJuIFMoKX1mb3Ioci5tZXRob2Q9byxyLmFyZz1pOzspe3ZhciBhPXIuZGVsZWdhdGU7aWYoYSl7dmFyIGM9YihhLHIpO2lmKGMpe2lmKGM9PT1mKWNvbnRpbnVlO3JldHVybiBjfX1pZigibmV4dCI9PT1yLm1ldGhvZClyLnNlbnQ9ci5fc2VudD1yLmFyZztlbHNlIGlmKCJ0aHJvdyI9PT1yLm1ldGhvZCl7aWYoInN1c3BlbmRlZFN0YXJ0Ij09PW4pdGhyb3cgbj0iY29tcGxldGVkIixyLmFyZztyLmRpc3BhdGNoRXhjZXB0aW9uKHIuYXJnKX1lbHNlInJldHVybiI9PT1yLm1ldGhvZCYmci5hYnJ1cHQoInJldHVybiIsci5hcmcpO249ImV4ZWN1dGluZyI7dmFyIHU9cyh0LGUscik7aWYoIm5vcm1hbCI9PT11LnR5cGUpe2lmKG49ci5kb25lPyJjb21wbGV0ZWQiOiJzdXNwZW5kZWRZaWVsZCIsdS5hcmc9PT1mKWNvbnRpbnVlO3JldHVybnt2YWx1ZTp1LmFyZyxkb25lOnIuZG9uZX19InRocm93Ij09PXUudHlwZSYmKG49ImNvbXBsZXRlZCIsci5tZXRob2Q9InRocm93IixyLmFyZz11LmFyZyl9fX0odCxyLGEpLGl9ZnVuY3Rpb24gcyh0LGUscil7dHJ5e3JldHVybnt0eXBlOiJub3JtYWwiLGFyZzp0LmNhbGwoZSxyKX19Y2F0Y2goail7cmV0dXJue3R5cGU6InRocm93Iixhcmc6an19fXQud3JhcD11O3ZhciBmPXt9O2Z1bmN0aW9uIGwoKXt9ZnVuY3Rpb24gaCgpe31mdW5jdGlvbiBwKCl7fXZhciB5PXt9O2MoeSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpO3ZhciB2PU9iamVjdC5nZXRQcm90b3R5cGVPZixkPXYmJnYodihFKFtdKSkpO2QmJmQhPT1lJiZyLmNhbGwoZCxvKSYmKHk9ZCk7dmFyIG09cC5wcm90b3R5cGU9bC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZSh5KTtmdW5jdGlvbiBnKHQpe1sibmV4dCIsInRocm93IiwicmV0dXJuIl0uZm9yRWFjaCgoZnVuY3Rpb24oZSl7Yyh0LGUsKGZ1bmN0aW9uKHQpe3JldHVybiB0aGlzLl9pbnZva2UoZSx0KX0pKX0pKX1mdW5jdGlvbiB3KHQsZSl7ZnVuY3Rpb24gbihvLGksYSxjKXt2YXIgdT1zKHRbb10sdCxpKTtpZigidGhyb3ciIT09dS50eXBlKXt2YXIgZj11LmFyZyxsPWYudmFsdWU7cmV0dXJuIGwmJiJvYmplY3QiPT1fKGwpJiZyLmNhbGwobCwiX19hd2FpdCIpP2UucmVzb2x2ZShsLl9fYXdhaXQpLnRoZW4oKGZ1bmN0aW9uKHQpe24oIm5leHQiLHQsYSxjKX0pLChmdW5jdGlvbih0KXtuKCJ0aHJvdyIsdCxhLGMpfSkpOmUucmVzb2x2ZShsKS50aGVuKChmdW5jdGlvbih0KXtmLnZhbHVlPXQsYShmKX0pLChmdW5jdGlvbih0KXtyZXR1cm4gbigidGhyb3ciLHQsYSxjKX0pKX1jKHUuYXJnKX12YXIgbzt0aGlzLl9pbnZva2U9ZnVuY3Rpb24odCxyKXtmdW5jdGlvbiBpKCl7cmV0dXJuIG5ldyBlKChmdW5jdGlvbihlLG8pe24odCxyLGUsbyl9KSl9cmV0dXJuIG89bz9vLnRoZW4oaSxpKTppKCl9fWZ1bmN0aW9uIGIodCxlKXt2YXIgcj10Lml0ZXJhdG9yW2UubWV0aG9kXTtpZih2b2lkIDA9PT1yKXtpZihlLmRlbGVnYXRlPW51bGwsInRocm93Ij09PWUubWV0aG9kKXtpZih0Lml0ZXJhdG9yLnJldHVybiYmKGUubWV0aG9kPSJyZXR1cm4iLGUuYXJnPXZvaWQgMCxiKHQsZSksInRocm93Ij09PWUubWV0aG9kKSlyZXR1cm4gZjtlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIlRoZSBpdGVyYXRvciBkb2VzIG5vdCBwcm92aWRlIGEgJ3Rocm93JyBtZXRob2QiKX1yZXR1cm4gZn12YXIgbj1zKHIsdC5pdGVyYXRvcixlLmFyZyk7aWYoInRocm93Ij09PW4udHlwZSlyZXR1cm4gZS5tZXRob2Q9InRocm93IixlLmFyZz1uLmFyZyxlLmRlbGVnYXRlPW51bGwsZjt2YXIgbz1uLmFyZztyZXR1cm4gbz9vLmRvbmU/KGVbdC5yZXN1bHROYW1lXT1vLnZhbHVlLGUubmV4dD10Lm5leHRMb2MsInJldHVybiIhPT1lLm1ldGhvZCYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLGUuZGVsZWdhdGU9bnVsbCxmKTpvOihlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIml0ZXJhdG9yIHJlc3VsdCBpcyBub3QgYW4gb2JqZWN0IiksZS5kZWxlZ2F0ZT1udWxsLGYpfWZ1bmN0aW9uIHgodCl7dmFyIGU9e3RyeUxvYzp0WzBdfTsxIGluIHQmJihlLmNhdGNoTG9jPXRbMV0pLDIgaW4gdCYmKGUuZmluYWxseUxvYz10WzJdLGUuYWZ0ZXJMb2M9dFszXSksdGhpcy50cnlFbnRyaWVzLnB1c2goZSl9ZnVuY3Rpb24gTCh0KXt2YXIgZT10LmNvbXBsZXRpb258fHt9O2UudHlwZT0ibm9ybWFsIixkZWxldGUgZS5hcmcsdC5jb21wbGV0aW9uPWV9ZnVuY3Rpb24gayh0KXt0aGlzLnRyeUVudHJpZXM9W3t0cnlMb2M6InJvb3QifV0sdC5mb3JFYWNoKHgsdGhpcyksdGhpcy5yZXNldCghMCl9ZnVuY3Rpb24gRSh0KXtpZih0KXt2YXIgZT10W29dO2lmKGUpcmV0dXJuIGUuY2FsbCh0KTtpZigiZnVuY3Rpb24iPT10eXBlb2YgdC5uZXh0KXJldHVybiB0O2lmKCFpc05hTih0Lmxlbmd0aCkpe3ZhciBuPS0xLGk9ZnVuY3Rpb24gZSgpe2Zvcig7KytuPHQubGVuZ3RoOylpZihyLmNhbGwodCxuKSlyZXR1cm4gZS52YWx1ZT10W25dLGUuZG9uZT0hMSxlO3JldHVybiBlLnZhbHVlPXZvaWQgMCxlLmRvbmU9ITAsZX07cmV0dXJuIGkubmV4dD1pfX1yZXR1cm57bmV4dDpTfX1mdW5jdGlvbiBTKCl7cmV0dXJue3ZhbHVlOnZvaWQgMCxkb25lOiEwfX1yZXR1cm4gaC5wcm90b3R5cGU9cCxjKG0sImNvbnN0cnVjdG9yIixwKSxjKHAsImNvbnN0cnVjdG9yIixoKSxoLmRpc3BsYXlOYW1lPWMocCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpLHQuaXNHZW5lcmF0b3JGdW5jdGlvbj1mdW5jdGlvbih0KXt2YXIgZT0iZnVuY3Rpb24iPT10eXBlb2YgdCYmdC5jb25zdHJ1Y3RvcjtyZXR1cm4hIWUmJihlPT09aHx8IkdlbmVyYXRvckZ1bmN0aW9uIj09PShlLmRpc3BsYXlOYW1lfHxlLm5hbWUpKX0sdC5tYXJrPWZ1bmN0aW9uKHQpe3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2Y/T2JqZWN0LnNldFByb3RvdHlwZU9mKHQscCk6KHQuX19wcm90b19fPXAsYyh0LGEsIkdlbmVyYXRvckZ1bmN0aW9uIikpLHQucHJvdG90eXBlPU9iamVjdC5jcmVhdGUobSksdH0sdC5hd3JhcD1mdW5jdGlvbih0KXtyZXR1cm57X19hd2FpdDp0fX0sZyh3LnByb3RvdHlwZSksYyh3LnByb3RvdHlwZSxpLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLHQuQXN5bmNJdGVyYXRvcj13LHQuYXN5bmM9ZnVuY3Rpb24oZSxyLG4sbyxpKXt2b2lkIDA9PT1pJiYoaT1Qcm9taXNlKTt2YXIgYT1uZXcgdyh1KGUscixuLG8pLGkpO3JldHVybiB0LmlzR2VuZXJhdG9yRnVuY3Rpb24ocik/YTphLm5leHQoKS50aGVuKChmdW5jdGlvbih0KXtyZXR1cm4gdC5kb25lP3QudmFsdWU6YS5uZXh0KCl9KSl9LGcobSksYyhtLGEsIkdlbmVyYXRvciIpLGMobSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLGMobSwidG9TdHJpbmciLChmdW5jdGlvbigpe3JldHVybiJbb2JqZWN0IEdlbmVyYXRvcl0ifSkpLHQua2V5cz1mdW5jdGlvbih0KXt2YXIgZT1bXTtmb3IodmFyIHIgaW4gdCllLnB1c2gocik7cmV0dXJuIGUucmV2ZXJzZSgpLGZ1bmN0aW9uIHIoKXtmb3IoO2UubGVuZ3RoOyl7dmFyIG49ZS5wb3AoKTtpZihuIGluIHQpcmV0dXJuIHIudmFsdWU9bixyLmRvbmU9ITEscn1yZXR1cm4gci5kb25lPSEwLHJ9fSx0LnZhbHVlcz1FLGsucHJvdG90eXBlPXtjb25zdHJ1Y3RvcjprLHJlc2V0OmZ1bmN0aW9uKHQpe2lmKHRoaXMucHJldj0wLHRoaXMubmV4dD0wLHRoaXMuc2VudD10aGlzLl9zZW50PXZvaWQgMCx0aGlzLmRvbmU9ITEsdGhpcy5kZWxlZ2F0ZT1udWxsLHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLmFyZz12b2lkIDAsdGhpcy50cnlFbnRyaWVzLmZvckVhY2goTCksIXQpZm9yKHZhciBlIGluIHRoaXMpInQiPT09ZS5jaGFyQXQoMCkmJnIuY2FsbCh0aGlzLGUpJiYhaXNOYU4oK2Uuc2xpY2UoMSkpJiYodGhpc1tlXT12b2lkIDApfSxzdG9wOmZ1bmN0aW9uKCl7dGhpcy5kb25lPSEwO3ZhciB0PXRoaXMudHJ5RW50cmllc1swXS5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuIHRoaXMucnZhbH0sZGlzcGF0Y2hFeGNlcHRpb246ZnVuY3Rpb24odCl7aWYodGhpcy5kb25lKXRocm93IHQ7dmFyIGU9dGhpcztmdW5jdGlvbiBuKHIsbil7cmV0dXJuIGEudHlwZT0idGhyb3ciLGEuYXJnPXQsZS5uZXh0PXIsbiYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLCEhbn1mb3IodmFyIG89dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO28+PTA7LS1vKXt2YXIgaT10aGlzLnRyeUVudHJpZXNbb10sYT1pLmNvbXBsZXRpb247aWYoInJvb3QiPT09aS50cnlMb2MpcmV0dXJuIG4oImVuZCIpO2lmKGkudHJ5TG9jPD10aGlzLnByZXYpe3ZhciBjPXIuY2FsbChpLCJjYXRjaExvYyIpLHU9ci5jYWxsKGksImZpbmFsbHlMb2MiKTtpZihjJiZ1KXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9ZWxzZSBpZihjKXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKX1lbHNle2lmKCF1KXRocm93IG5ldyBFcnJvcigidHJ5IHN0YXRlbWVudCB3aXRob3V0IGNhdGNoIG9yIGZpbmFsbHkiKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9fX19LGFicnVwdDpmdW5jdGlvbih0LGUpe2Zvcih2YXIgbj10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bj49MDstLW4pe3ZhciBvPXRoaXMudHJ5RW50cmllc1tuXTtpZihvLnRyeUxvYzw9dGhpcy5wcmV2JiZyLmNhbGwobywiZmluYWxseUxvYyIpJiZ0aGlzLnByZXY8by5maW5hbGx5TG9jKXt2YXIgaT1vO2JyZWFrfX1pJiYoImJyZWFrIj09PXR8fCJjb250aW51ZSI9PT10KSYmaS50cnlMb2M8PWUmJmU8PWkuZmluYWxseUxvYyYmKGk9bnVsbCk7dmFyIGE9aT9pLmNvbXBsZXRpb246e307cmV0dXJuIGEudHlwZT10LGEuYXJnPWUsaT8odGhpcy5tZXRob2Q9Im5leHQiLHRoaXMubmV4dD1pLmZpbmFsbHlMb2MsZik6dGhpcy5jb21wbGV0ZShhKX0sY29tcGxldGU6ZnVuY3Rpb24odCxlKXtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiJicmVhayI9PT10LnR5cGV8fCJjb250aW51ZSI9PT10LnR5cGU/dGhpcy5uZXh0PXQuYXJnOiJyZXR1cm4iPT09dC50eXBlPyh0aGlzLnJ2YWw9dGhpcy5hcmc9dC5hcmcsdGhpcy5tZXRob2Q9InJldHVybiIsdGhpcy5uZXh0PSJlbmQiKToibm9ybWFsIj09PXQudHlwZSYmZSYmKHRoaXMubmV4dD1lKSxmfSxmaW5pc2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIuZmluYWxseUxvYz09PXQpcmV0dXJuIHRoaXMuY29tcGxldGUoci5jb21wbGV0aW9uLHIuYWZ0ZXJMb2MpLEwociksZn19LGNhdGNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLnRyeUxvYz09PXQpe3ZhciBuPXIuY29tcGxldGlvbjtpZigidGhyb3ciPT09bi50eXBlKXt2YXIgbz1uLmFyZztMKHIpfXJldHVybiBvfX10aHJvdyBuZXcgRXJyb3IoImlsbGVnYWwgY2F0Y2ggYXR0ZW1wdCIpfSxkZWxlZ2F0ZVlpZWxkOmZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdGhpcy5kZWxlZ2F0ZT17aXRlcmF0b3I6RSh0KSxyZXN1bHROYW1lOmUsbmV4dExvYzpyfSwibmV4dCI9PT10aGlzLm1ldGhvZCYmKHRoaXMuYXJnPXZvaWQgMCksZn19LHR9ZnVuY3Rpb24gUyh0LGUscixuLG8saSxhKXt0cnl7dmFyIGM9dFtpXShhKSx1PWMudmFsdWV9Y2F0Y2gocyl7cmV0dXJuIHZvaWQgcihzKX1jLmRvbmU/ZSh1KTpQcm9taXNlLnJlc29sdmUodSkudGhlbihuLG8pfWZ1bmN0aW9uIGoodCl7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIGU9dGhpcyxyPWFyZ3VtZW50cztyZXR1cm4gbmV3IFByb21pc2UoKGZ1bmN0aW9uKG4sbyl7dmFyIGk9dC5hcHBseShlLHIpO2Z1bmN0aW9uIGEodCl7UyhpLG4sbyxhLGMsIm5leHQiLHQpfWZ1bmN0aW9uIGModCl7UyhpLG4sbyxhLGMsInRocm93Iix0KX1hKHZvaWQgMCl9KSl9fWZ1bmN0aW9uIFAodCxlKXt2YXIgcj0idW5kZWZpbmVkIiE9PXR5cGVvZiBTeW1ib2wmJnRbU3ltYm9sLml0ZXJhdG9yXXx8dFsiQEBpdGVyYXRvciJdO2lmKCFyKXtpZihBcnJheS5pc0FycmF5KHQpfHwocj1UKHQpKXx8ZSYmdCYmIm51bWJlciI9PT10eXBlb2YgdC5sZW5ndGgpe3ImJih0PXIpO3ZhciBuPTAsbz1mdW5jdGlvbigpe307cmV0dXJue3M6byxuOmZ1bmN0aW9uKCl7cmV0dXJuIG4+PXQubGVuZ3RoP3tkb25lOiEwfTp7ZG9uZTohMSx2YWx1ZTp0W24rK119fSxlOmZ1bmN0aW9uKHQpe3Rocm93IHR9LGY6b319dGhyb3cgbmV3IFR5cGVFcnJvcigiSW52YWxpZCBhdHRlbXB0IHRvIGl0ZXJhdGUgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfXZhciBpLGE9ITAsYz0hMTtyZXR1cm57czpmdW5jdGlvbigpe3I9ci5jYWxsKHQpfSxuOmZ1bmN0aW9uKCl7dmFyIHQ9ci5uZXh0KCk7cmV0dXJuIGE9dC5kb25lLHR9LGU6ZnVuY3Rpb24odCl7Yz0hMCxpPXR9LGY6ZnVuY3Rpb24oKXt0cnl7YXx8bnVsbD09ci5yZXR1cm58fHIucmV0dXJuKCl9ZmluYWxseXtpZihjKXRocm93IGl9fX19ZnVuY3Rpb24gVCh0LGUpe2lmKHQpe2lmKCJzdHJpbmciPT09dHlwZW9mIHQpcmV0dXJuIE4odCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT9OKHQsZSk6dm9pZCAwfX1mdW5jdGlvbiBOKHQsZSl7KG51bGw9PWV8fGU+dC5sZW5ndGgpJiYoZT10Lmxlbmd0aCk7Zm9yKHZhciByPTAsbj1uZXcgQXJyYXkoZSk7cjxlO3IrKyluW3JdPXRbcl07cmV0dXJuIG59ZnVuY3Rpb24gUih0LGUpe3ZhciByLG49bmV3IFVSTCh0KSxvPVAoZSk7dHJ5e2ZvcihvLnMoKTshKHI9by5uKCkpLmRvbmU7KXt2YXIgaT1yLnZhbHVlO24uc2VhcmNoUGFyYW1zLmRlbGV0ZShpKX19Y2F0Y2goYSl7by5lKGEpfWZpbmFsbHl7by5mKCl9cmV0dXJuIG4uaHJlZn1mdW5jdGlvbiBBKHQsZSxyLG4pe3JldHVybiBDLmFwcGx5KHRoaXMsYXJndW1lbnRzKX1mdW5jdGlvbiBDKCl7cmV0dXJuIEM9aihPKCkubWFyaygoZnVuY3Rpb24gdChlLHIsbixvKXt2YXIgaSxhLGMsdSxzLGYsbDtyZXR1cm4gTygpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDppZihpPVIoci51cmwsbiksci51cmwhPT1pKXt0Lm5leHQ9MzticmVha31yZXR1cm4gdC5hYnJ1cHQoInJldHVybiIsZS5tYXRjaChyLG8pKTtjYXNlIDM6cmV0dXJuIGE9T2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LG8pLHtpZ25vcmVTZWFyY2g6ITB9KSx0Lm5leHQ9NixlLmtleXMocixhKTtjYXNlIDY6Yz10LnNlbnQsdT1QKGMpLHQucHJldj04LHUucygpO2Nhc2UgMTA6aWYoKHM9dS5uKCkpLmRvbmUpe3QubmV4dD0xNzticmVha31pZihmPXMudmFsdWUsbD1SKGYudXJsLG4pLGkhPT1sKXt0Lm5leHQ9MTU7YnJlYWt9cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iLGUubWF0Y2goZixvKSk7Y2FzZSAxNTp0Lm5leHQ9MTA7YnJlYWs7Y2FzZSAxNzp0Lm5leHQ9MjI7YnJlYWs7Y2FzZSAxOTp0LnByZXY9MTksdC50MD10WyJjYXRjaCJdKDgpLHUuZSh0LnQwKTtjYXNlIDIyOnJldHVybiB0LnByZXY9MjIsdS5mKCksdC5maW5pc2goMjIpO2Nhc2UgMjU6cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iKTtjYXNlIDI2OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCxudWxsLFtbOCwxOSwyMiwyNV1dKX0pKSksQy5hcHBseSh0aGlzLGFyZ3VtZW50cyl9ZnVuY3Rpb24gSSgpe2lmKHZvaWQgMD09PWIpe3ZhciB0PW5ldyBSZXNwb25zZSgiIik7aWYoImJvZHkiaW4gdCl0cnl7bmV3IFJlc3BvbnNlKHQuYm9keSksYj0hMH1jYXRjaChlKXtiPSExfWI9ITF9cmV0dXJuIGJ9ZnVuY3Rpb24gRyh0LGUpe2Zvcih2YXIgcj0wO3I8ZS5sZW5ndGg7cisrKXt2YXIgbj1lW3JdO24uZW51bWVyYWJsZT1uLmVudW1lcmFibGV8fCExLG4uY29uZmlndXJhYmxlPSEwLCJ2YWx1ZSJpbiBuJiYobi53cml0YWJsZT0hMCksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsbi5rZXksbil9fWZ1bmN0aW9uIEYodCxlLHIpe3JldHVybiBlJiZHKHQucHJvdG90eXBlLGUpLHImJkcodCxyKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSx0fWZ1bmN0aW9uIFUodCxlKXtpZighKHQgaW5zdGFuY2VvZiBlKSl0aHJvdyBuZXcgVHlwZUVycm9yKCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb24iKX12YXIgcT1GKChmdW5jdGlvbiB0KCl7dmFyIGU9dGhpcztVKHRoaXMsdCksdGhpcy5wcm9taXNlPW5ldyBQcm9taXNlKChmdW5jdGlvbih0LHIpe2UucmVzb2x2ZT10LGUucmVqZWN0PXJ9KSl9KSk7ZnVuY3Rpb24gSyh0KXtyZXR1cm4gSz0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiYic3ltYm9sIj09dHlwZW9mIFN5bWJvbC5pdGVyYXRvcj9mdW5jdGlvbih0KXtyZXR1cm4gdHlwZW9mIHR9OmZ1bmN0aW9uKHQpe3JldHVybiB0JiYiZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiZ0LmNvbnN0cnVjdG9yPT09U3ltYm9sJiZ0IT09U3ltYm9sLnByb3RvdHlwZT8ic3ltYm9sIjp0eXBlb2YgdH0sSyh0KX1mdW5jdGlvbiBNKCl7LyohIHJlZ2VuZXJhdG9yLXJ1bnRpbWUgLS0gQ29weXJpZ2h0IChjKSAyMDE0LXByZXNlbnQsIEZhY2Vib29rLCBJbmMuIC0tIGxpY2Vuc2UgKE1JVCk6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWdlbmVyYXRvci9ibG9iL21haW4vTElDRU5TRSAqL009ZnVuY3Rpb24oKXtyZXR1cm4gdH07dmFyIHQ9e30sZT1PYmplY3QucHJvdG90eXBlLHI9ZS5oYXNPd25Qcm9wZXJ0eSxuPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2w/U3ltYm9sOnt9LG89bi5pdGVyYXRvcnx8IkBAaXRlcmF0b3IiLGk9bi5hc3luY0l0ZXJhdG9yfHwiQEBhc3luY0l0ZXJhdG9yIixhPW4udG9TdHJpbmdUYWd8fCJAQHRvU3RyaW5nVGFnIjtmdW5jdGlvbiBjKHQsZSxyKXtyZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsZSx7dmFsdWU6cixlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMH0pLHRbZV19dHJ5e2Moe30sIiIpfWNhdGNoKE8pe2M9ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0W2VdPXJ9fWZ1bmN0aW9uIHUodCxlLHIsbil7dmFyIG89ZSYmZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBsP2U6bCxpPU9iamVjdC5jcmVhdGUoby5wcm90b3R5cGUpLGE9bmV3IGsobnx8W10pO3JldHVybiBpLl9pbnZva2U9ZnVuY3Rpb24odCxlLHIpe3ZhciBuPSJzdXNwZW5kZWRTdGFydCI7cmV0dXJuIGZ1bmN0aW9uKG8saSl7aWYoImV4ZWN1dGluZyI9PT1uKXRocm93IG5ldyBFcnJvcigiR2VuZXJhdG9yIGlzIGFscmVhZHkgcnVubmluZyIpO2lmKCJjb21wbGV0ZWQiPT09bil7aWYoInRocm93Ij09PW8pdGhyb3cgaTtyZXR1cm4gXygpfWZvcihyLm1ldGhvZD1vLHIuYXJnPWk7Oyl7dmFyIGE9ci5kZWxlZ2F0ZTtpZihhKXt2YXIgYz1iKGEscik7aWYoYyl7aWYoYz09PWYpY29udGludWU7cmV0dXJuIGN9fWlmKCJuZXh0Ij09PXIubWV0aG9kKXIuc2VudD1yLl9zZW50PXIuYXJnO2Vsc2UgaWYoInRocm93Ij09PXIubWV0aG9kKXtpZigic3VzcGVuZGVkU3RhcnQiPT09bil0aHJvdyBuPSJjb21wbGV0ZWQiLHIuYXJnO3IuZGlzcGF0Y2hFeGNlcHRpb24oci5hcmcpfWVsc2UicmV0dXJuIj09PXIubWV0aG9kJiZyLmFicnVwdCgicmV0dXJuIixyLmFyZyk7bj0iZXhlY3V0aW5nIjt2YXIgdT1zKHQsZSxyKTtpZigibm9ybWFsIj09PXUudHlwZSl7aWYobj1yLmRvbmU/ImNvbXBsZXRlZCI6InN1c3BlbmRlZFlpZWxkIix1LmFyZz09PWYpY29udGludWU7cmV0dXJue3ZhbHVlOnUuYXJnLGRvbmU6ci5kb25lfX0idGhyb3ciPT09dS50eXBlJiYobj0iY29tcGxldGVkIixyLm1ldGhvZD0idGhyb3ciLHIuYXJnPXUuYXJnKX19fSh0LHIsYSksaX1mdW5jdGlvbiBzKHQsZSxyKXt0cnl7cmV0dXJue3R5cGU6Im5vcm1hbCIsYXJnOnQuY2FsbChlLHIpfX1jYXRjaChPKXtyZXR1cm57dHlwZToidGhyb3ciLGFyZzpPfX19dC53cmFwPXU7dmFyIGY9e307ZnVuY3Rpb24gbCgpe31mdW5jdGlvbiBoKCl7fWZ1bmN0aW9uIHAoKXt9dmFyIHk9e307Yyh5LG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSk7dmFyIHY9T2JqZWN0LmdldFByb3RvdHlwZU9mLGQ9diYmdih2KEUoW10pKSk7ZCYmZCE9PWUmJnIuY2FsbChkLG8pJiYoeT1kKTt2YXIgbT1wLnByb3RvdHlwZT1sLnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKHkpO2Z1bmN0aW9uIGcodCl7WyJuZXh0IiwidGhyb3ciLCJyZXR1cm4iXS5mb3JFYWNoKChmdW5jdGlvbihlKXtjKHQsZSwoZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2ludm9rZShlLHQpfSkpfSkpfWZ1bmN0aW9uIHcodCxlKXtmdW5jdGlvbiBuKG8saSxhLGMpe3ZhciB1PXModFtvXSx0LGkpO2lmKCJ0aHJvdyIhPT11LnR5cGUpe3ZhciBmPXUuYXJnLGw9Zi52YWx1ZTtyZXR1cm4gbCYmIm9iamVjdCI9PUsobCkmJnIuY2FsbChsLCJfX2F3YWl0Iik/ZS5yZXNvbHZlKGwuX19hd2FpdCkudGhlbigoZnVuY3Rpb24odCl7bigibmV4dCIsdCxhLGMpfSksKGZ1bmN0aW9uKHQpe24oInRocm93Iix0LGEsYyl9KSk6ZS5yZXNvbHZlKGwpLnRoZW4oKGZ1bmN0aW9uKHQpe2YudmFsdWU9dCxhKGYpfSksKGZ1bmN0aW9uKHQpe3JldHVybiBuKCJ0aHJvdyIsdCxhLGMpfSkpfWModS5hcmcpfXZhciBvO3RoaXMuX2ludm9rZT1mdW5jdGlvbih0LHIpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gbmV3IGUoKGZ1bmN0aW9uKGUsbyl7bih0LHIsZSxvKX0pKX1yZXR1cm4gbz1vP28udGhlbihpLGkpOmkoKX19ZnVuY3Rpb24gYih0LGUpe3ZhciByPXQuaXRlcmF0b3JbZS5tZXRob2RdO2lmKHZvaWQgMD09PXIpe2lmKGUuZGVsZWdhdGU9bnVsbCwidGhyb3ciPT09ZS5tZXRob2Qpe2lmKHQuaXRlcmF0b3IucmV0dXJuJiYoZS5tZXRob2Q9InJldHVybiIsZS5hcmc9dm9pZCAwLGIodCxlKSwidGhyb3ciPT09ZS5tZXRob2QpKXJldHVybiBmO2UubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAndGhyb3cnIG1ldGhvZCIpfXJldHVybiBmfXZhciBuPXMocix0Lml0ZXJhdG9yLGUuYXJnKTtpZigidGhyb3ciPT09bi50eXBlKXJldHVybiBlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW4uYXJnLGUuZGVsZWdhdGU9bnVsbCxmO3ZhciBvPW4uYXJnO3JldHVybiBvP28uZG9uZT8oZVt0LnJlc3VsdE5hbWVdPW8udmFsdWUsZS5uZXh0PXQubmV4dExvYywicmV0dXJuIiE9PWUubWV0aG9kJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksZS5kZWxlZ2F0ZT1udWxsLGYpOm86KGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiaXRlcmF0b3IgcmVzdWx0IGlzIG5vdCBhbiBvYmplY3QiKSxlLmRlbGVnYXRlPW51bGwsZil9ZnVuY3Rpb24geCh0KXt2YXIgZT17dHJ5TG9jOnRbMF19OzEgaW4gdCYmKGUuY2F0Y2hMb2M9dFsxXSksMiBpbiB0JiYoZS5maW5hbGx5TG9jPXRbMl0sZS5hZnRlckxvYz10WzNdKSx0aGlzLnRyeUVudHJpZXMucHVzaChlKX1mdW5jdGlvbiBMKHQpe3ZhciBlPXQuY29tcGxldGlvbnx8e307ZS50eXBlPSJub3JtYWwiLGRlbGV0ZSBlLmFyZyx0LmNvbXBsZXRpb249ZX1mdW5jdGlvbiBrKHQpe3RoaXMudHJ5RW50cmllcz1be3RyeUxvYzoicm9vdCJ9XSx0LmZvckVhY2goeCx0aGlzKSx0aGlzLnJlc2V0KCEwKX1mdW5jdGlvbiBFKHQpe2lmKHQpe3ZhciBlPXRbb107aWYoZSlyZXR1cm4gZS5jYWxsKHQpO2lmKCJmdW5jdGlvbiI9PXR5cGVvZiB0Lm5leHQpcmV0dXJuIHQ7aWYoIWlzTmFOKHQubGVuZ3RoKSl7dmFyIG49LTEsaT1mdW5jdGlvbiBlKCl7Zm9yKDsrK248dC5sZW5ndGg7KWlmKHIuY2FsbCh0LG4pKXJldHVybiBlLnZhbHVlPXRbbl0sZS5kb25lPSExLGU7cmV0dXJuIGUudmFsdWU9dm9pZCAwLGUuZG9uZT0hMCxlfTtyZXR1cm4gaS5uZXh0PWl9fXJldHVybntuZXh0Ol99fWZ1bmN0aW9uIF8oKXtyZXR1cm57dmFsdWU6dm9pZCAwLGRvbmU6ITB9fXJldHVybiBoLnByb3RvdHlwZT1wLGMobSwiY29uc3RydWN0b3IiLHApLGMocCwiY29uc3RydWN0b3IiLGgpLGguZGlzcGxheU5hbWU9YyhwLGEsIkdlbmVyYXRvckZ1bmN0aW9uIiksdC5pc0dlbmVyYXRvckZ1bmN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBlPSJmdW5jdGlvbiI9PXR5cGVvZiB0JiZ0LmNvbnN0cnVjdG9yO3JldHVybiEhZSYmKGU9PT1ofHwiR2VuZXJhdG9yRnVuY3Rpb24iPT09KGUuZGlzcGxheU5hbWV8fGUubmFtZSkpfSx0Lm1hcms9ZnVuY3Rpb24odCl7cmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YodCxwKToodC5fX3Byb3RvX189cCxjKHQsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSksdC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShtKSx0fSx0LmF3cmFwPWZ1bmN0aW9uKHQpe3JldHVybntfX2F3YWl0OnR9fSxnKHcucHJvdG90eXBlKSxjKHcucHJvdG90eXBlLGksKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksdC5Bc3luY0l0ZXJhdG9yPXcsdC5hc3luYz1mdW5jdGlvbihlLHIsbixvLGkpe3ZvaWQgMD09PWkmJihpPVByb21pc2UpO3ZhciBhPW5ldyB3KHUoZSxyLG4sbyksaSk7cmV0dXJuIHQuaXNHZW5lcmF0b3JGdW5jdGlvbihyKT9hOmEubmV4dCgpLnRoZW4oKGZ1bmN0aW9uKHQpe3JldHVybiB0LmRvbmU/dC52YWx1ZTphLm5leHQoKX0pKX0sZyhtKSxjKG0sYSwiR2VuZXJhdG9yIiksYyhtLG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksYyhtLCJ0b1N0cmluZyIsKGZ1bmN0aW9uKCl7cmV0dXJuIltvYmplY3QgR2VuZXJhdG9yXSJ9KSksdC5rZXlzPWZ1bmN0aW9uKHQpe3ZhciBlPVtdO2Zvcih2YXIgciBpbiB0KWUucHVzaChyKTtyZXR1cm4gZS5yZXZlcnNlKCksZnVuY3Rpb24gcigpe2Zvcig7ZS5sZW5ndGg7KXt2YXIgbj1lLnBvcCgpO2lmKG4gaW4gdClyZXR1cm4gci52YWx1ZT1uLHIuZG9uZT0hMSxyfXJldHVybiByLmRvbmU9ITAscn19LHQudmFsdWVzPUUsay5wcm90b3R5cGU9e2NvbnN0cnVjdG9yOmsscmVzZXQ6ZnVuY3Rpb24odCl7aWYodGhpcy5wcmV2PTAsdGhpcy5uZXh0PTAsdGhpcy5zZW50PXRoaXMuX3NlbnQ9dm9pZCAwLHRoaXMuZG9uZT0hMSx0aGlzLmRlbGVnYXRlPW51bGwsdGhpcy5tZXRob2Q9Im5leHQiLHRoaXMuYXJnPXZvaWQgMCx0aGlzLnRyeUVudHJpZXMuZm9yRWFjaChMKSwhdClmb3IodmFyIGUgaW4gdGhpcykidCI9PT1lLmNoYXJBdCgwKSYmci5jYWxsKHRoaXMsZSkmJiFpc05hTigrZS5zbGljZSgxKSkmJih0aGlzW2VdPXZvaWQgMCl9LHN0b3A6ZnVuY3Rpb24oKXt0aGlzLmRvbmU9ITA7dmFyIHQ9dGhpcy50cnlFbnRyaWVzWzBdLmNvbXBsZXRpb247aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4gdGhpcy5ydmFsfSxkaXNwYXRjaEV4Y2VwdGlvbjpmdW5jdGlvbih0KXtpZih0aGlzLmRvbmUpdGhyb3cgdDt2YXIgZT10aGlzO2Z1bmN0aW9uIG4ocixuKXtyZXR1cm4gYS50eXBlPSJ0aHJvdyIsYS5hcmc9dCxlLm5leHQ9cixuJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksISFufWZvcih2YXIgbz10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bz49MDstLW8pe3ZhciBpPXRoaXMudHJ5RW50cmllc1tvXSxhPWkuY29tcGxldGlvbjtpZigicm9vdCI9PT1pLnRyeUxvYylyZXR1cm4gbigiZW5kIik7aWYoaS50cnlMb2M8PXRoaXMucHJldil7dmFyIGM9ci5jYWxsKGksImNhdGNoTG9jIiksdT1yLmNhbGwoaSwiZmluYWxseUxvYyIpO2lmKGMmJnUpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX1lbHNlIGlmKGMpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApfWVsc2V7aWYoIXUpdGhyb3cgbmV3IEVycm9yKCJ0cnkgc3RhdGVtZW50IHdpdGhvdXQgY2F0Y2ggb3IgZmluYWxseSIpO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX19fX0sYWJydXB0OmZ1bmN0aW9uKHQsZSl7Zm9yKHZhciBuPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtuPj0wOy0tbil7dmFyIG89dGhpcy50cnlFbnRyaWVzW25dO2lmKG8udHJ5TG9jPD10aGlzLnByZXYmJnIuY2FsbChvLCJmaW5hbGx5TG9jIikmJnRoaXMucHJldjxvLmZpbmFsbHlMb2Mpe3ZhciBpPW87YnJlYWt9fWkmJigiYnJlYWsiPT09dHx8ImNvbnRpbnVlIj09PXQpJiZpLnRyeUxvYzw9ZSYmZTw9aS5maW5hbGx5TG9jJiYoaT1udWxsKTt2YXIgYT1pP2kuY29tcGxldGlvbjp7fTtyZXR1cm4gYS50eXBlPXQsYS5hcmc9ZSxpPyh0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5uZXh0PWkuZmluYWxseUxvYyxmKTp0aGlzLmNvbXBsZXRlKGEpfSxjb21wbGV0ZTpmdW5jdGlvbih0LGUpe2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuImJyZWFrIj09PXQudHlwZXx8ImNvbnRpbnVlIj09PXQudHlwZT90aGlzLm5leHQ9dC5hcmc6InJldHVybiI9PT10LnR5cGU/KHRoaXMucnZhbD10aGlzLmFyZz10LmFyZyx0aGlzLm1ldGhvZD0icmV0dXJuIix0aGlzLm5leHQ9ImVuZCIpOiJub3JtYWwiPT09dC50eXBlJiZlJiYodGhpcy5uZXh0PWUpLGZ9LGZpbmlzaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci5maW5hbGx5TG9jPT09dClyZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sci5hZnRlckxvYyksTChyKSxmfX0sY2F0Y2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIudHJ5TG9jPT09dCl7dmFyIG49ci5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT1uLnR5cGUpe3ZhciBvPW4uYXJnO0wocil9cmV0dXJuIG99fXRocm93IG5ldyBFcnJvcigiaWxsZWdhbCBjYXRjaCBhdHRlbXB0Iil9LGRlbGVnYXRlWWllbGQ6ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0aGlzLmRlbGVnYXRlPXtpdGVyYXRvcjpFKHQpLHJlc3VsdE5hbWU6ZSxuZXh0TG9jOnJ9LCJuZXh0Ij09PXRoaXMubWV0aG9kJiYodGhpcy5hcmc9dm9pZCAwKSxmfX0sdH1mdW5jdGlvbiBZKHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9Vyh0KSl8fGUmJnQmJiJudW1iZXIiPT09dHlwZW9mIHQubGVuZ3RoKXtyJiYodD1yKTt2YXIgbj0wLG89ZnVuY3Rpb24oKXt9O3JldHVybntzOm8sbjpmdW5jdGlvbigpe3JldHVybiBuPj10Lmxlbmd0aD97ZG9uZTohMH06e2RvbmU6ITEsdmFsdWU6dFtuKytdfX0sZTpmdW5jdGlvbih0KXt0aHJvdyB0fSxmOm99fXRocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBpdGVyYXRlIG5vbi1pdGVyYWJsZSBpbnN0YW5jZS5cbkluIG9yZGVyIHRvIGJlIGl0ZXJhYmxlLCBub24tYXJyYXkgb2JqZWN0cyBtdXN0IGhhdmUgYSBbU3ltYm9sLml0ZXJhdG9yXSgpIG1ldGhvZC4iKX12YXIgaSxhPSEwLGM9ITE7cmV0dXJue3M6ZnVuY3Rpb24oKXtyPXIuY2FsbCh0KX0sbjpmdW5jdGlvbigpe3ZhciB0PXIubmV4dCgpO3JldHVybiBhPXQuZG9uZSx0fSxlOmZ1bmN0aW9uKHQpe2M9ITAsaT10fSxmOmZ1bmN0aW9uKCl7dHJ5e2F8fG51bGw9PXIucmV0dXJufHxyLnJldHVybigpfWZpbmFsbHl7aWYoYyl0aHJvdyBpfX19fWZ1bmN0aW9uIFcodCxlKXtpZih0KXtpZigic3RyaW5nIj09PXR5cGVvZiB0KXJldHVybiBEKHQsZSk7dmFyIHI9T2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHQpLnNsaWNlKDgsLTEpO3JldHVybiJPYmplY3QiPT09ciYmdC5jb25zdHJ1Y3RvciYmKHI9dC5jb25zdHJ1Y3Rvci5uYW1lKSwiTWFwIj09PXJ8fCJTZXQiPT09cj9BcnJheS5mcm9tKHQpOiJBcmd1bWVudHMiPT09cnx8L14oPzpVaXxJKW50KD86OHwxNnwzMikoPzpDbGFtcGVkKT9BcnJheSQvLnRlc3Qocik/RCh0LGUpOnZvaWQgMH19ZnVuY3Rpb24gRCh0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIEIodCxlLHIsbixvLGksYSl7dHJ5e3ZhciBjPXRbaV0oYSksdT1jLnZhbHVlfWNhdGNoKHMpe3JldHVybiB2b2lkIHIocyl9Yy5kb25lP2UodSk6UHJvbWlzZS5yZXNvbHZlKHUpLnRoZW4obixvKX1mdW5jdGlvbiBIKHQpe3JldHVybiBmdW5jdGlvbigpe3ZhciBlPXRoaXMscj1hcmd1bWVudHM7cmV0dXJuIG5ldyBQcm9taXNlKChmdW5jdGlvbihuLG8pe3ZhciBpPXQuYXBwbHkoZSxyKTtmdW5jdGlvbiBhKHQpe0IoaSxuLG8sYSxjLCJuZXh0Iix0KX1mdW5jdGlvbiBjKHQpe0IoaSxuLG8sYSxjLCJ0aHJvdyIsdCl9YSh2b2lkIDApfSkpfX1mdW5jdGlvbiAkKCl7cmV0dXJuIFYuYXBwbHkodGhpcyxhcmd1bWVudHMpfWZ1bmN0aW9uIFYoKXtyZXR1cm4gVj1IKE0oKS5tYXJrKChmdW5jdGlvbiB0KCl7dmFyIGUscixuO3JldHVybiBNKCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOjAsZT1ZKHcpLHQucHJldj0yLGUucygpO2Nhc2UgNDppZigocj1lLm4oKSkuZG9uZSl7dC5uZXh0PTExO2JyZWFrfXJldHVybiBuPXIudmFsdWUsdC5uZXh0PTgsbigpO2Nhc2UgODowO2Nhc2UgOTp0Lm5leHQ9NDticmVhaztjYXNlIDExOnQubmV4dD0xNjticmVhaztjYXNlIDEzOnQucHJldj0xMyx0LnQwPXRbImNhdGNoIl0oMiksZS5lKHQudDApO2Nhc2UgMTY6cmV0dXJuIHQucHJldj0xNixlLmYoKSx0LmZpbmlzaCgxNik7Y2FzZSAxOTowO2Nhc2UgMjA6Y2FzZSJlbmQiOnJldHVybiB0LnN0b3AoKX19KSx0LG51bGwsW1syLDEzLDE2LDE5XV0pfSkpKSxWLmFwcGx5KHRoaXMsYXJndW1lbnRzKX12YXIgej1mdW5jdGlvbih0KXt2YXIgZT1uZXcgVVJMKFN0cmluZyh0KSxsb2NhdGlvbi5ocmVmKTtyZXR1cm4gZS5ocmVmLnJlcGxhY2UobmV3IFJlZ0V4cCgiXiIuY29uY2F0KGxvY2F0aW9uLm9yaWdpbikpLCIiKX07ZnVuY3Rpb24gSih0KXtyZXR1cm4gbmV3IFByb21pc2UoKGZ1bmN0aW9uKGUpe3JldHVybiBzZXRUaW1lb3V0KGUsdCl9KSl9ZnVuY3Rpb24gUSh0LGUpe3ZhciByPWUoKTtyZXR1cm4gdC53YWl0VW50aWwocikscn1mdW5jdGlvbiBYKHQpe3JldHVybiBYPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSxYKHQpfWZ1bmN0aW9uIFooKXsvKiEgcmVnZW5lcmF0b3ItcnVudGltZSAtLSBDb3B5cmlnaHQgKGMpIDIwMTQtcHJlc2VudCwgRmFjZWJvb2ssIEluYy4gLS0gbGljZW5zZSAoTUlUKTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlZ2VuZXJhdG9yL2Jsb2IvbWFpbi9MSUNFTlNFICovWj1mdW5jdGlvbigpe3JldHVybiB0fTt2YXIgdD17fSxlPU9iamVjdC5wcm90b3R5cGUscj1lLmhhc093blByb3BlcnR5LG49ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbD9TeW1ib2w6e30sbz1uLml0ZXJhdG9yfHwiQEBpdGVyYXRvciIsaT1uLmFzeW5jSXRlcmF0b3J8fCJAQGFzeW5jSXRlcmF0b3IiLGE9bi50b1N0cmluZ1RhZ3x8IkBAdG9TdHJpbmdUYWciO2Z1bmN0aW9uIGModCxlLHIpe3JldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkodCxlLHt2YWx1ZTpyLGVudW1lcmFibGU6ITAsY29uZmlndXJhYmxlOiEwLHdyaXRhYmxlOiEwfSksdFtlXX10cnl7Yyh7fSwiIil9Y2F0Y2goTyl7Yz1mdW5jdGlvbih0LGUscil7cmV0dXJuIHRbZV09cn19ZnVuY3Rpb24gdSh0LGUscixuKXt2YXIgbz1lJiZlLnByb3RvdHlwZSBpbnN0YW5jZW9mIGw/ZTpsLGk9T2JqZWN0LmNyZWF0ZShvLnByb3RvdHlwZSksYT1uZXcgayhufHxbXSk7cmV0dXJuIGkuX2ludm9rZT1mdW5jdGlvbih0LGUscil7dmFyIG49InN1c3BlbmRlZFN0YXJ0IjtyZXR1cm4gZnVuY3Rpb24obyxpKXtpZigiZXhlY3V0aW5nIj09PW4pdGhyb3cgbmV3IEVycm9yKCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBydW5uaW5nIik7aWYoImNvbXBsZXRlZCI9PT1uKXtpZigidGhyb3ciPT09byl0aHJvdyBpO3JldHVybiBfKCl9Zm9yKHIubWV0aG9kPW8sci5hcmc9aTs7KXt2YXIgYT1yLmRlbGVnYXRlO2lmKGEpe3ZhciBjPWIoYSxyKTtpZihjKXtpZihjPT09Ziljb250aW51ZTtyZXR1cm4gY319aWYoIm5leHQiPT09ci5tZXRob2Qpci5zZW50PXIuX3NlbnQ9ci5hcmc7ZWxzZSBpZigidGhyb3ciPT09ci5tZXRob2Qpe2lmKCJzdXNwZW5kZWRTdGFydCI9PT1uKXRocm93IG49ImNvbXBsZXRlZCIsci5hcmc7ci5kaXNwYXRjaEV4Y2VwdGlvbihyLmFyZyl9ZWxzZSJyZXR1cm4iPT09ci5tZXRob2QmJnIuYWJydXB0KCJyZXR1cm4iLHIuYXJnKTtuPSJleGVjdXRpbmciO3ZhciB1PXModCxlLHIpO2lmKCJub3JtYWwiPT09dS50eXBlKXtpZihuPXIuZG9uZT8iY29tcGxldGVkIjoic3VzcGVuZGVkWWllbGQiLHUuYXJnPT09Ziljb250aW51ZTtyZXR1cm57dmFsdWU6dS5hcmcsZG9uZTpyLmRvbmV9fSJ0aHJvdyI9PT11LnR5cGUmJihuPSJjb21wbGV0ZWQiLHIubWV0aG9kPSJ0aHJvdyIsci5hcmc9dS5hcmcpfX19KHQscixhKSxpfWZ1bmN0aW9uIHModCxlLHIpe3RyeXtyZXR1cm57dHlwZToibm9ybWFsIixhcmc6dC5jYWxsKGUscil9fWNhdGNoKE8pe3JldHVybnt0eXBlOiJ0aHJvdyIsYXJnOk99fX10LndyYXA9dTt2YXIgZj17fTtmdW5jdGlvbiBsKCl7fWZ1bmN0aW9uIGgoKXt9ZnVuY3Rpb24gcCgpe312YXIgeT17fTtjKHksbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKTt2YXIgdj1PYmplY3QuZ2V0UHJvdG90eXBlT2YsZD12JiZ2KHYoRShbXSkpKTtkJiZkIT09ZSYmci5jYWxsKGQsbykmJih5PWQpO3ZhciBtPXAucHJvdG90eXBlPWwucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoeSk7ZnVuY3Rpb24gZyh0KXtbIm5leHQiLCJ0aHJvdyIsInJldHVybiJdLmZvckVhY2goKGZ1bmN0aW9uKGUpe2ModCxlLChmdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5faW52b2tlKGUsdCl9KSl9KSl9ZnVuY3Rpb24gdyh0LGUpe2Z1bmN0aW9uIG4obyxpLGEsYyl7dmFyIHU9cyh0W29dLHQsaSk7aWYoInRocm93IiE9PXUudHlwZSl7dmFyIGY9dS5hcmcsbD1mLnZhbHVlO3JldHVybiBsJiYib2JqZWN0Ij09WChsKSYmci5jYWxsKGwsIl9fYXdhaXQiKT9lLnJlc29sdmUobC5fX2F3YWl0KS50aGVuKChmdW5jdGlvbih0KXtuKCJuZXh0Iix0LGEsYyl9KSwoZnVuY3Rpb24odCl7bigidGhyb3ciLHQsYSxjKX0pKTplLnJlc29sdmUobCkudGhlbigoZnVuY3Rpb24odCl7Zi52YWx1ZT10LGEoZil9KSwoZnVuY3Rpb24odCl7cmV0dXJuIG4oInRocm93Iix0LGEsYyl9KSl9Yyh1LmFyZyl9dmFyIG87dGhpcy5faW52b2tlPWZ1bmN0aW9uKHQscil7ZnVuY3Rpb24gaSgpe3JldHVybiBuZXcgZSgoZnVuY3Rpb24oZSxvKXtuKHQscixlLG8pfSkpfXJldHVybiBvPW8/by50aGVuKGksaSk6aSgpfX1mdW5jdGlvbiBiKHQsZSl7dmFyIHI9dC5pdGVyYXRvcltlLm1ldGhvZF07aWYodm9pZCAwPT09cil7aWYoZS5kZWxlZ2F0ZT1udWxsLCJ0aHJvdyI9PT1lLm1ldGhvZCl7aWYodC5pdGVyYXRvci5yZXR1cm4mJihlLm1ldGhvZD0icmV0dXJuIixlLmFyZz12b2lkIDAsYih0LGUpLCJ0aHJvdyI9PT1lLm1ldGhvZCkpcmV0dXJuIGY7ZS5tZXRob2Q9InRocm93IixlLmFyZz1uZXcgVHlwZUVycm9yKCJUaGUgaXRlcmF0b3IgZG9lcyBub3QgcHJvdmlkZSBhICd0aHJvdycgbWV0aG9kIil9cmV0dXJuIGZ9dmFyIG49cyhyLHQuaXRlcmF0b3IsZS5hcmcpO2lmKCJ0aHJvdyI9PT1uLnR5cGUpcmV0dXJuIGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bi5hcmcsZS5kZWxlZ2F0ZT1udWxsLGY7dmFyIG89bi5hcmc7cmV0dXJuIG8/by5kb25lPyhlW3QucmVzdWx0TmFtZV09by52YWx1ZSxlLm5leHQ9dC5uZXh0TG9jLCJyZXR1cm4iIT09ZS5tZXRob2QmJihlLm1ldGhvZD0ibmV4dCIsZS5hcmc9dm9pZCAwKSxlLmRlbGVnYXRlPW51bGwsZik6bzooZS5tZXRob2Q9InRocm93IixlLmFyZz1uZXcgVHlwZUVycm9yKCJpdGVyYXRvciByZXN1bHQgaXMgbm90IGFuIG9iamVjdCIpLGUuZGVsZWdhdGU9bnVsbCxmKX1mdW5jdGlvbiB4KHQpe3ZhciBlPXt0cnlMb2M6dFswXX07MSBpbiB0JiYoZS5jYXRjaExvYz10WzFdKSwyIGluIHQmJihlLmZpbmFsbHlMb2M9dFsyXSxlLmFmdGVyTG9jPXRbM10pLHRoaXMudHJ5RW50cmllcy5wdXNoKGUpfWZ1bmN0aW9uIEwodCl7dmFyIGU9dC5jb21wbGV0aW9ufHx7fTtlLnR5cGU9Im5vcm1hbCIsZGVsZXRlIGUuYXJnLHQuY29tcGxldGlvbj1lfWZ1bmN0aW9uIGsodCl7dGhpcy50cnlFbnRyaWVzPVt7dHJ5TG9jOiJyb290In1dLHQuZm9yRWFjaCh4LHRoaXMpLHRoaXMucmVzZXQoITApfWZ1bmN0aW9uIEUodCl7aWYodCl7dmFyIGU9dFtvXTtpZihlKXJldHVybiBlLmNhbGwodCk7aWYoImZ1bmN0aW9uIj09dHlwZW9mIHQubmV4dClyZXR1cm4gdDtpZighaXNOYU4odC5sZW5ndGgpKXt2YXIgbj0tMSxpPWZ1bmN0aW9uIGUoKXtmb3IoOysrbjx0Lmxlbmd0aDspaWYoci5jYWxsKHQsbikpcmV0dXJuIGUudmFsdWU9dFtuXSxlLmRvbmU9ITEsZTtyZXR1cm4gZS52YWx1ZT12b2lkIDAsZS5kb25lPSEwLGV9O3JldHVybiBpLm5leHQ9aX19cmV0dXJue25leHQ6X319ZnVuY3Rpb24gXygpe3JldHVybnt2YWx1ZTp2b2lkIDAsZG9uZTohMH19cmV0dXJuIGgucHJvdG90eXBlPXAsYyhtLCJjb25zdHJ1Y3RvciIscCksYyhwLCJjb25zdHJ1Y3RvciIsaCksaC5kaXNwbGF5TmFtZT1jKHAsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSx0LmlzR2VuZXJhdG9yRnVuY3Rpb249ZnVuY3Rpb24odCl7dmFyIGU9ImZ1bmN0aW9uIj09dHlwZW9mIHQmJnQuY29uc3RydWN0b3I7cmV0dXJuISFlJiYoZT09PWh8fCJHZW5lcmF0b3JGdW5jdGlvbiI9PT0oZS5kaXNwbGF5TmFtZXx8ZS5uYW1lKSl9LHQubWFyaz1mdW5jdGlvbih0KXtyZXR1cm4gT2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5zZXRQcm90b3R5cGVPZih0LHApOih0Ll9fcHJvdG9fXz1wLGModCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpKSx0LnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKG0pLHR9LHQuYXdyYXA9ZnVuY3Rpb24odCl7cmV0dXJue19fYXdhaXQ6dH19LGcody5wcm90b3R5cGUpLGMody5wcm90b3R5cGUsaSwoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKSx0LkFzeW5jSXRlcmF0b3I9dyx0LmFzeW5jPWZ1bmN0aW9uKGUscixuLG8saSl7dm9pZCAwPT09aSYmKGk9UHJvbWlzZSk7dmFyIGE9bmV3IHcodShlLHIsbixvKSxpKTtyZXR1cm4gdC5pc0dlbmVyYXRvckZ1bmN0aW9uKHIpP2E6YS5uZXh0KCkudGhlbigoZnVuY3Rpb24odCl7cmV0dXJuIHQuZG9uZT90LnZhbHVlOmEubmV4dCgpfSkpfSxnKG0pLGMobSxhLCJHZW5lcmF0b3IiKSxjKG0sbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKSxjKG0sInRvU3RyaW5nIiwoZnVuY3Rpb24oKXtyZXR1cm4iW29iamVjdCBHZW5lcmF0b3JdIn0pKSx0LmtleXM9ZnVuY3Rpb24odCl7dmFyIGU9W107Zm9yKHZhciByIGluIHQpZS5wdXNoKHIpO3JldHVybiBlLnJldmVyc2UoKSxmdW5jdGlvbiByKCl7Zm9yKDtlLmxlbmd0aDspe3ZhciBuPWUucG9wKCk7aWYobiBpbiB0KXJldHVybiByLnZhbHVlPW4sci5kb25lPSExLHJ9cmV0dXJuIHIuZG9uZT0hMCxyfX0sdC52YWx1ZXM9RSxrLnByb3RvdHlwZT17Y29uc3RydWN0b3I6ayxyZXNldDpmdW5jdGlvbih0KXtpZih0aGlzLnByZXY9MCx0aGlzLm5leHQ9MCx0aGlzLnNlbnQ9dGhpcy5fc2VudD12b2lkIDAsdGhpcy5kb25lPSExLHRoaXMuZGVsZWdhdGU9bnVsbCx0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5hcmc9dm9pZCAwLHRoaXMudHJ5RW50cmllcy5mb3JFYWNoKEwpLCF0KWZvcih2YXIgZSBpbiB0aGlzKSJ0Ij09PWUuY2hhckF0KDApJiZyLmNhbGwodGhpcyxlKSYmIWlzTmFOKCtlLnNsaWNlKDEpKSYmKHRoaXNbZV09dm9pZCAwKX0sc3RvcDpmdW5jdGlvbigpe3RoaXMuZG9uZT0hMDt2YXIgdD10aGlzLnRyeUVudHJpZXNbMF0uY29tcGxldGlvbjtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiB0aGlzLnJ2YWx9LGRpc3BhdGNoRXhjZXB0aW9uOmZ1bmN0aW9uKHQpe2lmKHRoaXMuZG9uZSl0aHJvdyB0O3ZhciBlPXRoaXM7ZnVuY3Rpb24gbihyLG4pe3JldHVybiBhLnR5cGU9InRocm93IixhLmFyZz10LGUubmV4dD1yLG4mJihlLm1ldGhvZD0ibmV4dCIsZS5hcmc9dm9pZCAwKSwhIW59Zm9yKHZhciBvPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtvPj0wOy0tbyl7dmFyIGk9dGhpcy50cnlFbnRyaWVzW29dLGE9aS5jb21wbGV0aW9uO2lmKCJyb290Ij09PWkudHJ5TG9jKXJldHVybiBuKCJlbmQiKTtpZihpLnRyeUxvYzw9dGhpcy5wcmV2KXt2YXIgYz1yLmNhbGwoaSwiY2F0Y2hMb2MiKSx1PXIuY2FsbChpLCJmaW5hbGx5TG9jIik7aWYoYyYmdSl7aWYodGhpcy5wcmV2PGkuY2F0Y2hMb2MpcmV0dXJuIG4oaS5jYXRjaExvYywhMCk7aWYodGhpcy5wcmV2PGkuZmluYWxseUxvYylyZXR1cm4gbihpLmZpbmFsbHlMb2MpfWVsc2UgaWYoYyl7aWYodGhpcy5wcmV2PGkuY2F0Y2hMb2MpcmV0dXJuIG4oaS5jYXRjaExvYywhMCl9ZWxzZXtpZighdSl0aHJvdyBuZXcgRXJyb3IoInRyeSBzdGF0ZW1lbnQgd2l0aG91dCBjYXRjaCBvciBmaW5hbGx5Iik7aWYodGhpcy5wcmV2PGkuZmluYWxseUxvYylyZXR1cm4gbihpLmZpbmFsbHlMb2MpfX19fSxhYnJ1cHQ6ZnVuY3Rpb24odCxlKXtmb3IodmFyIG49dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO24+PTA7LS1uKXt2YXIgbz10aGlzLnRyeUVudHJpZXNbbl07aWYoby50cnlMb2M8PXRoaXMucHJldiYmci5jYWxsKG8sImZpbmFsbHlMb2MiKSYmdGhpcy5wcmV2PG8uZmluYWxseUxvYyl7dmFyIGk9bzticmVha319aSYmKCJicmVhayI9PT10fHwiY29udGludWUiPT09dCkmJmkudHJ5TG9jPD1lJiZlPD1pLmZpbmFsbHlMb2MmJihpPW51bGwpO3ZhciBhPWk/aS5jb21wbGV0aW9uOnt9O3JldHVybiBhLnR5cGU9dCxhLmFyZz1lLGk/KHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLm5leHQ9aS5maW5hbGx5TG9jLGYpOnRoaXMuY29tcGxldGUoYSl9LGNvbXBsZXRlOmZ1bmN0aW9uKHQsZSl7aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4iYnJlYWsiPT09dC50eXBlfHwiY29udGludWUiPT09dC50eXBlP3RoaXMubmV4dD10LmFyZzoicmV0dXJuIj09PXQudHlwZT8odGhpcy5ydmFsPXRoaXMuYXJnPXQuYXJnLHRoaXMubWV0aG9kPSJyZXR1cm4iLHRoaXMubmV4dD0iZW5kIik6Im5vcm1hbCI9PT10LnR5cGUmJmUmJih0aGlzLm5leHQ9ZSksZn0sZmluaXNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLmZpbmFsbHlMb2M9PT10KXJldHVybiB0aGlzLmNvbXBsZXRlKHIuY29tcGxldGlvbixyLmFmdGVyTG9jKSxMKHIpLGZ9fSxjYXRjaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci50cnlMb2M9PT10KXt2YXIgbj1yLmNvbXBsZXRpb247aWYoInRocm93Ij09PW4udHlwZSl7dmFyIG89bi5hcmc7TChyKX1yZXR1cm4gb319dGhyb3cgbmV3IEVycm9yKCJpbGxlZ2FsIGNhdGNoIGF0dGVtcHQiKX0sZGVsZWdhdGVZaWVsZDpmdW5jdGlvbih0LGUscil7cmV0dXJuIHRoaXMuZGVsZWdhdGU9e2l0ZXJhdG9yOkUodCkscmVzdWx0TmFtZTplLG5leHRMb2M6cn0sIm5leHQiPT09dGhpcy5tZXRob2QmJih0aGlzLmFyZz12b2lkIDApLGZ9fSx0fWZ1bmN0aW9uIHR0KHQsZSxyLG4sbyxpLGEpe3RyeXt2YXIgYz10W2ldKGEpLHU9Yy52YWx1ZX1jYXRjaChzKXtyZXR1cm4gdm9pZCByKHMpfWMuZG9uZT9lKHUpOlByb21pc2UucmVzb2x2ZSh1KS50aGVuKG4sbyl9ZnVuY3Rpb24gZXQodCl7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIGU9dGhpcyxyPWFyZ3VtZW50cztyZXR1cm4gbmV3IFByb21pc2UoKGZ1bmN0aW9uKG4sbyl7dmFyIGk9dC5hcHBseShlLHIpO2Z1bmN0aW9uIGEodCl7dHQoaSxuLG8sYSxjLCJuZXh0Iix0KX1mdW5jdGlvbiBjKHQpe3R0KGksbixvLGEsYywidGhyb3ciLHQpfWEodm9pZCAwKX0pKX19ZnVuY3Rpb24gcnQodCxlKXtyZXR1cm4gbnQuYXBwbHkodGhpcyxhcmd1bWVudHMpfWZ1bmN0aW9uIG50KCl7cmV0dXJuIG50PWV0KFooKS5tYXJrKChmdW5jdGlvbiB0KGUscil7dmFyIG4sbyxpLGEsYyx1O3JldHVybiBaKCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOmlmKG49bnVsbCxlLnVybCYmKG89bmV3IFVSTChlLnVybCksbj1vLm9yaWdpbiksbj09PXNlbGYubG9jYXRpb24ub3JpZ2luKXt0Lm5leHQ9NDticmVha310aHJvdyBuZXcgZygiY3Jvc3Mtb3JpZ2luLWNvcHktcmVzcG9uc2UiLHtvcmlnaW46bn0pO2Nhc2UgNDppZihpPWUuY2xvbmUoKSxhPXtoZWFkZXJzOm5ldyBIZWFkZXJzKGkuaGVhZGVycyksc3RhdHVzOmkuc3RhdHVzLHN0YXR1c1RleHQ6aS5zdGF0dXNUZXh0fSxjPXI/cihhKTphLCFJKCkpe3QubmV4dD0xMTticmVha310LnQwPWkuYm9keSx0Lm5leHQ9MTQ7YnJlYWs7Y2FzZSAxMTpyZXR1cm4gdC5uZXh0PTEzLGkuYmxvYigpO2Nhc2UgMTM6dC50MD10LnNlbnQ7Y2FzZSAxNDpyZXR1cm4gdT10LnQwLHQuYWJydXB0KCJyZXR1cm4iLG5ldyBSZXNwb25zZSh1LGMpKTtjYXNlIDE2OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCl9KSkpLG50LmFwcGx5KHRoaXMsYXJndW1lbnRzKX1mdW5jdGlvbiBvdCgpe3NlbGYuYWRkRXZlbnRMaXN0ZW5lcigiYWN0aXZhdGUiLChmdW5jdGlvbigpe3JldHVybiBzZWxmLmNsaWVudHMuY2xhaW0oKX0pKX1yKDQwOCk7dmFyIGl0PSJfX1dCX1JFVklTSU9OX18iO2Z1bmN0aW9uIGF0KHQpe2lmKCF0KXRocm93IG5ldyBnKCJhZGQtdG8tY2FjaGUtbGlzdC11bmV4cGVjdGVkLXR5cGUiLHtlbnRyeTp0fSk7aWYoInN0cmluZyI9PT10eXBlb2YgdCl7dmFyIGU9bmV3IFVSTCh0LGxvY2F0aW9uLmhyZWYpO3JldHVybntjYWNoZUtleTplLmhyZWYsdXJsOmUuaHJlZn19dmFyIHI9dC5yZXZpc2lvbixuPXQudXJsO2lmKCFuKXRocm93IG5ldyBnKCJhZGQtdG8tY2FjaGUtbGlzdC11bmV4cGVjdGVkLXR5cGUiLHtlbnRyeTp0fSk7aWYoIXIpe2U9bmV3IFVSTChuLGxvY2F0aW9uLmhyZWYpO3JldHVybntjYWNoZUtleTplLmhyZWYsdXJsOmUuaHJlZn19dmFyIG89bmV3IFVSTChuLGxvY2F0aW9uLmhyZWYpLGk9bmV3IFVSTChuLGxvY2F0aW9uLmhyZWYpO3JldHVybiBvLnNlYXJjaFBhcmFtcy5zZXQoaXQscikse2NhY2hlS2V5Om8uaHJlZix1cmw6aS5ocmVmfX1mdW5jdGlvbiBjdCh0KXtyZXR1cm4gY3Q9ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/ZnVuY3Rpb24odCl7cmV0dXJuIHR5cGVvZiB0fTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmdC5jb25zdHJ1Y3Rvcj09PVN5bWJvbCYmdCE9PVN5bWJvbC5wcm90b3R5cGU/InN5bWJvbCI6dHlwZW9mIHR9LGN0KHQpfWZ1bmN0aW9uIHV0KCl7LyohIHJlZ2VuZXJhdG9yLXJ1bnRpbWUgLS0gQ29weXJpZ2h0IChjKSAyMDE0LXByZXNlbnQsIEZhY2Vib29rLCBJbmMuIC0tIGxpY2Vuc2UgKE1JVCk6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWdlbmVyYXRvci9ibG9iL21haW4vTElDRU5TRSAqL3V0PWZ1bmN0aW9uKCl7cmV0dXJuIHR9O3ZhciB0PXt9LGU9T2JqZWN0LnByb3RvdHlwZSxyPWUuaGFzT3duUHJvcGVydHksbj0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sP1N5bWJvbDp7fSxvPW4uaXRlcmF0b3J8fCJAQGl0ZXJhdG9yIixpPW4uYXN5bmNJdGVyYXRvcnx8IkBAYXN5bmNJdGVyYXRvciIsYT1uLnRvU3RyaW5nVGFnfHwiQEB0b1N0cmluZ1RhZyI7ZnVuY3Rpb24gYyh0LGUscil7cmV0dXJuIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LGUse3ZhbHVlOnIsZW51bWVyYWJsZTohMCxjb25maWd1cmFibGU6ITAsd3JpdGFibGU6ITB9KSx0W2VdfXRyeXtjKHt9LCIiKX1jYXRjaChPKXtjPWZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdFtlXT1yfX1mdW5jdGlvbiB1KHQsZSxyLG4pe3ZhciBvPWUmJmUucHJvdG90eXBlIGluc3RhbmNlb2YgbD9lOmwsaT1PYmplY3QuY3JlYXRlKG8ucHJvdG90eXBlKSxhPW5ldyBrKG58fFtdKTtyZXR1cm4gaS5faW52b2tlPWZ1bmN0aW9uKHQsZSxyKXt2YXIgbj0ic3VzcGVuZGVkU3RhcnQiO3JldHVybiBmdW5jdGlvbihvLGkpe2lmKCJleGVjdXRpbmciPT09bil0aHJvdyBuZXcgRXJyb3IoIkdlbmVyYXRvciBpcyBhbHJlYWR5IHJ1bm5pbmciKTtpZigiY29tcGxldGVkIj09PW4pe2lmKCJ0aHJvdyI9PT1vKXRocm93IGk7cmV0dXJuIF8oKX1mb3Ioci5tZXRob2Q9byxyLmFyZz1pOzspe3ZhciBhPXIuZGVsZWdhdGU7aWYoYSl7dmFyIGM9YihhLHIpO2lmKGMpe2lmKGM9PT1mKWNvbnRpbnVlO3JldHVybiBjfX1pZigibmV4dCI9PT1yLm1ldGhvZClyLnNlbnQ9ci5fc2VudD1yLmFyZztlbHNlIGlmKCJ0aHJvdyI9PT1yLm1ldGhvZCl7aWYoInN1c3BlbmRlZFN0YXJ0Ij09PW4pdGhyb3cgbj0iY29tcGxldGVkIixyLmFyZztyLmRpc3BhdGNoRXhjZXB0aW9uKHIuYXJnKX1lbHNlInJldHVybiI9PT1yLm1ldGhvZCYmci5hYnJ1cHQoInJldHVybiIsci5hcmcpO249ImV4ZWN1dGluZyI7dmFyIHU9cyh0LGUscik7aWYoIm5vcm1hbCI9PT11LnR5cGUpe2lmKG49ci5kb25lPyJjb21wbGV0ZWQiOiJzdXNwZW5kZWRZaWVsZCIsdS5hcmc9PT1mKWNvbnRpbnVlO3JldHVybnt2YWx1ZTp1LmFyZyxkb25lOnIuZG9uZX19InRocm93Ij09PXUudHlwZSYmKG49ImNvbXBsZXRlZCIsci5tZXRob2Q9InRocm93IixyLmFyZz11LmFyZyl9fX0odCxyLGEpLGl9ZnVuY3Rpb24gcyh0LGUscil7dHJ5e3JldHVybnt0eXBlOiJub3JtYWwiLGFyZzp0LmNhbGwoZSxyKX19Y2F0Y2goTyl7cmV0dXJue3R5cGU6InRocm93Iixhcmc6T319fXQud3JhcD11O3ZhciBmPXt9O2Z1bmN0aW9uIGwoKXt9ZnVuY3Rpb24gaCgpe31mdW5jdGlvbiBwKCl7fXZhciB5PXt9O2MoeSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpO3ZhciB2PU9iamVjdC5nZXRQcm90b3R5cGVPZixkPXYmJnYodihFKFtdKSkpO2QmJmQhPT1lJiZyLmNhbGwoZCxvKSYmKHk9ZCk7dmFyIG09cC5wcm90b3R5cGU9bC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZSh5KTtmdW5jdGlvbiBnKHQpe1sibmV4dCIsInRocm93IiwicmV0dXJuIl0uZm9yRWFjaCgoZnVuY3Rpb24oZSl7Yyh0LGUsKGZ1bmN0aW9uKHQpe3JldHVybiB0aGlzLl9pbnZva2UoZSx0KX0pKX0pKX1mdW5jdGlvbiB3KHQsZSl7ZnVuY3Rpb24gbihvLGksYSxjKXt2YXIgdT1zKHRbb10sdCxpKTtpZigidGhyb3ciIT09dS50eXBlKXt2YXIgZj11LmFyZyxsPWYudmFsdWU7cmV0dXJuIGwmJiJvYmplY3QiPT1jdChsKSYmci5jYWxsKGwsIl9fYXdhaXQiKT9lLnJlc29sdmUobC5fX2F3YWl0KS50aGVuKChmdW5jdGlvbih0KXtuKCJuZXh0Iix0LGEsYyl9KSwoZnVuY3Rpb24odCl7bigidGhyb3ciLHQsYSxjKX0pKTplLnJlc29sdmUobCkudGhlbigoZnVuY3Rpb24odCl7Zi52YWx1ZT10LGEoZil9KSwoZnVuY3Rpb24odCl7cmV0dXJuIG4oInRocm93Iix0LGEsYyl9KSl9Yyh1LmFyZyl9dmFyIG87dGhpcy5faW52b2tlPWZ1bmN0aW9uKHQscil7ZnVuY3Rpb24gaSgpe3JldHVybiBuZXcgZSgoZnVuY3Rpb24oZSxvKXtuKHQscixlLG8pfSkpfXJldHVybiBvPW8/by50aGVuKGksaSk6aSgpfX1mdW5jdGlvbiBiKHQsZSl7dmFyIHI9dC5pdGVyYXRvcltlLm1ldGhvZF07aWYodm9pZCAwPT09cil7aWYoZS5kZWxlZ2F0ZT1udWxsLCJ0aHJvdyI9PT1lLm1ldGhvZCl7aWYodC5pdGVyYXRvci5yZXR1cm4mJihlLm1ldGhvZD0icmV0dXJuIixlLmFyZz12b2lkIDAsYih0LGUpLCJ0aHJvdyI9PT1lLm1ldGhvZCkpcmV0dXJuIGY7ZS5tZXRob2Q9InRocm93IixlLmFyZz1uZXcgVHlwZUVycm9yKCJUaGUgaXRlcmF0b3IgZG9lcyBub3QgcHJvdmlkZSBhICd0aHJvdycgbWV0aG9kIil9cmV0dXJuIGZ9dmFyIG49cyhyLHQuaXRlcmF0b3IsZS5hcmcpO2lmKCJ0aHJvdyI9PT1uLnR5cGUpcmV0dXJuIGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bi5hcmcsZS5kZWxlZ2F0ZT1udWxsLGY7dmFyIG89bi5hcmc7cmV0dXJuIG8/by5kb25lPyhlW3QucmVzdWx0TmFtZV09by52YWx1ZSxlLm5leHQ9dC5uZXh0TG9jLCJyZXR1cm4iIT09ZS5tZXRob2QmJihlLm1ldGhvZD0ibmV4dCIsZS5hcmc9dm9pZCAwKSxlLmRlbGVnYXRlPW51bGwsZik6bzooZS5tZXRob2Q9InRocm93IixlLmFyZz1uZXcgVHlwZUVycm9yKCJpdGVyYXRvciByZXN1bHQgaXMgbm90IGFuIG9iamVjdCIpLGUuZGVsZWdhdGU9bnVsbCxmKX1mdW5jdGlvbiB4KHQpe3ZhciBlPXt0cnlMb2M6dFswXX07MSBpbiB0JiYoZS5jYXRjaExvYz10WzFdKSwyIGluIHQmJihlLmZpbmFsbHlMb2M9dFsyXSxlLmFmdGVyTG9jPXRbM10pLHRoaXMudHJ5RW50cmllcy5wdXNoKGUpfWZ1bmN0aW9uIEwodCl7dmFyIGU9dC5jb21wbGV0aW9ufHx7fTtlLnR5cGU9Im5vcm1hbCIsZGVsZXRlIGUuYXJnLHQuY29tcGxldGlvbj1lfWZ1bmN0aW9uIGsodCl7dGhpcy50cnlFbnRyaWVzPVt7dHJ5TG9jOiJyb290In1dLHQuZm9yRWFjaCh4LHRoaXMpLHRoaXMucmVzZXQoITApfWZ1bmN0aW9uIEUodCl7aWYodCl7dmFyIGU9dFtvXTtpZihlKXJldHVybiBlLmNhbGwodCk7aWYoImZ1bmN0aW9uIj09dHlwZW9mIHQubmV4dClyZXR1cm4gdDtpZighaXNOYU4odC5sZW5ndGgpKXt2YXIgbj0tMSxpPWZ1bmN0aW9uIGUoKXtmb3IoOysrbjx0Lmxlbmd0aDspaWYoci5jYWxsKHQsbikpcmV0dXJuIGUudmFsdWU9dFtuXSxlLmRvbmU9ITEsZTtyZXR1cm4gZS52YWx1ZT12b2lkIDAsZS5kb25lPSEwLGV9O3JldHVybiBpLm5leHQ9aX19cmV0dXJue25leHQ6X319ZnVuY3Rpb24gXygpe3JldHVybnt2YWx1ZTp2b2lkIDAsZG9uZTohMH19cmV0dXJuIGgucHJvdG90eXBlPXAsYyhtLCJjb25zdHJ1Y3RvciIscCksYyhwLCJjb25zdHJ1Y3RvciIsaCksaC5kaXNwbGF5TmFtZT1jKHAsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSx0LmlzR2VuZXJhdG9yRnVuY3Rpb249ZnVuY3Rpb24odCl7dmFyIGU9ImZ1bmN0aW9uIj09dHlwZW9mIHQmJnQuY29uc3RydWN0b3I7cmV0dXJuISFlJiYoZT09PWh8fCJHZW5lcmF0b3JGdW5jdGlvbiI9PT0oZS5kaXNwbGF5TmFtZXx8ZS5uYW1lKSl9LHQubWFyaz1mdW5jdGlvbih0KXtyZXR1cm4gT2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5zZXRQcm90b3R5cGVPZih0LHApOih0Ll9fcHJvdG9fXz1wLGModCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpKSx0LnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKG0pLHR9LHQuYXdyYXA9ZnVuY3Rpb24odCl7cmV0dXJue19fYXdhaXQ6dH19LGcody5wcm90b3R5cGUpLGMody5wcm90b3R5cGUsaSwoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKSx0LkFzeW5jSXRlcmF0b3I9dyx0LmFzeW5jPWZ1bmN0aW9uKGUscixuLG8saSl7dm9pZCAwPT09aSYmKGk9UHJvbWlzZSk7dmFyIGE9bmV3IHcodShlLHIsbixvKSxpKTtyZXR1cm4gdC5pc0dlbmVyYXRvckZ1bmN0aW9uKHIpP2E6YS5uZXh0KCkudGhlbigoZnVuY3Rpb24odCl7cmV0dXJuIHQuZG9uZT90LnZhbHVlOmEubmV4dCgpfSkpfSxnKG0pLGMobSxhLCJHZW5lcmF0b3IiKSxjKG0sbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKSxjKG0sInRvU3RyaW5nIiwoZnVuY3Rpb24oKXtyZXR1cm4iW29iamVjdCBHZW5lcmF0b3JdIn0pKSx0LmtleXM9ZnVuY3Rpb24odCl7dmFyIGU9W107Zm9yKHZhciByIGluIHQpZS5wdXNoKHIpO3JldHVybiBlLnJldmVyc2UoKSxmdW5jdGlvbiByKCl7Zm9yKDtlLmxlbmd0aDspe3ZhciBuPWUucG9wKCk7aWYobiBpbiB0KXJldHVybiByLnZhbHVlPW4sci5kb25lPSExLHJ9cmV0dXJuIHIuZG9uZT0hMCxyfX0sdC52YWx1ZXM9RSxrLnByb3RvdHlwZT17Y29uc3RydWN0b3I6ayxyZXNldDpmdW5jdGlvbih0KXtpZih0aGlzLnByZXY9MCx0aGlzLm5leHQ9MCx0aGlzLnNlbnQ9dGhpcy5fc2VudD12b2lkIDAsdGhpcy5kb25lPSExLHRoaXMuZGVsZWdhdGU9bnVsbCx0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5hcmc9dm9pZCAwLHRoaXMudHJ5RW50cmllcy5mb3JFYWNoKEwpLCF0KWZvcih2YXIgZSBpbiB0aGlzKSJ0Ij09PWUuY2hhckF0KDApJiZyLmNhbGwodGhpcyxlKSYmIWlzTmFOKCtlLnNsaWNlKDEpKSYmKHRoaXNbZV09dm9pZCAwKX0sc3RvcDpmdW5jdGlvbigpe3RoaXMuZG9uZT0hMDt2YXIgdD10aGlzLnRyeUVudHJpZXNbMF0uY29tcGxldGlvbjtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiB0aGlzLnJ2YWx9LGRpc3BhdGNoRXhjZXB0aW9uOmZ1bmN0aW9uKHQpe2lmKHRoaXMuZG9uZSl0aHJvdyB0O3ZhciBlPXRoaXM7ZnVuY3Rpb24gbihyLG4pe3JldHVybiBhLnR5cGU9InRocm93IixhLmFyZz10LGUubmV4dD1yLG4mJihlLm1ldGhvZD0ibmV4dCIsZS5hcmc9dm9pZCAwKSwhIW59Zm9yKHZhciBvPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtvPj0wOy0tbyl7dmFyIGk9dGhpcy50cnlFbnRyaWVzW29dLGE9aS5jb21wbGV0aW9uO2lmKCJyb290Ij09PWkudHJ5TG9jKXJldHVybiBuKCJlbmQiKTtpZihpLnRyeUxvYzw9dGhpcy5wcmV2KXt2YXIgYz1yLmNhbGwoaSwiY2F0Y2hMb2MiKSx1PXIuY2FsbChpLCJmaW5hbGx5TG9jIik7aWYoYyYmdSl7aWYodGhpcy5wcmV2PGkuY2F0Y2hMb2MpcmV0dXJuIG4oaS5jYXRjaExvYywhMCk7aWYodGhpcy5wcmV2PGkuZmluYWxseUxvYylyZXR1cm4gbihpLmZpbmFsbHlMb2MpfWVsc2UgaWYoYyl7aWYodGhpcy5wcmV2PGkuY2F0Y2hMb2MpcmV0dXJuIG4oaS5jYXRjaExvYywhMCl9ZWxzZXtpZighdSl0aHJvdyBuZXcgRXJyb3IoInRyeSBzdGF0ZW1lbnQgd2l0aG91dCBjYXRjaCBvciBmaW5hbGx5Iik7aWYodGhpcy5wcmV2PGkuZmluYWxseUxvYylyZXR1cm4gbihpLmZpbmFsbHlMb2MpfX19fSxhYnJ1cHQ6ZnVuY3Rpb24odCxlKXtmb3IodmFyIG49dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO24+PTA7LS1uKXt2YXIgbz10aGlzLnRyeUVudHJpZXNbbl07aWYoby50cnlMb2M8PXRoaXMucHJldiYmci5jYWxsKG8sImZpbmFsbHlMb2MiKSYmdGhpcy5wcmV2PG8uZmluYWxseUxvYyl7dmFyIGk9bzticmVha319aSYmKCJicmVhayI9PT10fHwiY29udGludWUiPT09dCkmJmkudHJ5TG9jPD1lJiZlPD1pLmZpbmFsbHlMb2MmJihpPW51bGwpO3ZhciBhPWk/aS5jb21wbGV0aW9uOnt9O3JldHVybiBhLnR5cGU9dCxhLmFyZz1lLGk/KHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLm5leHQ9aS5maW5hbGx5TG9jLGYpOnRoaXMuY29tcGxldGUoYSl9LGNvbXBsZXRlOmZ1bmN0aW9uKHQsZSl7aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4iYnJlYWsiPT09dC50eXBlfHwiY29udGludWUiPT09dC50eXBlP3RoaXMubmV4dD10LmFyZzoicmV0dXJuIj09PXQudHlwZT8odGhpcy5ydmFsPXRoaXMuYXJnPXQuYXJnLHRoaXMubWV0aG9kPSJyZXR1cm4iLHRoaXMubmV4dD0iZW5kIik6Im5vcm1hbCI9PT10LnR5cGUmJmUmJih0aGlzLm5leHQ9ZSksZn0sZmluaXNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLmZpbmFsbHlMb2M9PT10KXJldHVybiB0aGlzLmNvbXBsZXRlKHIuY29tcGxldGlvbixyLmFmdGVyTG9jKSxMKHIpLGZ9fSxjYXRjaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci50cnlMb2M9PT10KXt2YXIgbj1yLmNvbXBsZXRpb247aWYoInRocm93Ij09PW4udHlwZSl7dmFyIG89bi5hcmc7TChyKX1yZXR1cm4gb319dGhyb3cgbmV3IEVycm9yKCJpbGxlZ2FsIGNhdGNoIGF0dGVtcHQiKX0sZGVsZWdhdGVZaWVsZDpmdW5jdGlvbih0LGUscil7cmV0dXJuIHRoaXMuZGVsZWdhdGU9e2l0ZXJhdG9yOkUodCkscmVzdWx0TmFtZTplLG5leHRMb2M6cn0sIm5leHQiPT09dGhpcy5tZXRob2QmJih0aGlzLmFyZz12b2lkIDApLGZ9fSx0fWZ1bmN0aW9uIHN0KHQsZSxyLG4sbyxpLGEpe3RyeXt2YXIgYz10W2ldKGEpLHU9Yy52YWx1ZX1jYXRjaChzKXtyZXR1cm4gdm9pZCByKHMpfWMuZG9uZT9lKHUpOlByb21pc2UucmVzb2x2ZSh1KS50aGVuKG4sbyl9ZnVuY3Rpb24gZnQodCl7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIGU9dGhpcyxyPWFyZ3VtZW50cztyZXR1cm4gbmV3IFByb21pc2UoKGZ1bmN0aW9uKG4sbyl7dmFyIGk9dC5hcHBseShlLHIpO2Z1bmN0aW9uIGEodCl7c3QoaSxuLG8sYSxjLCJuZXh0Iix0KX1mdW5jdGlvbiBjKHQpe3N0KGksbixvLGEsYywidGhyb3ciLHQpfWEodm9pZCAwKX0pKX19ZnVuY3Rpb24gbHQodCxlKXtmb3IodmFyIHI9MDtyPGUubGVuZ3RoO3IrKyl7dmFyIG49ZVtyXTtuLmVudW1lcmFibGU9bi5lbnVtZXJhYmxlfHwhMSxuLmNvbmZpZ3VyYWJsZT0hMCwidmFsdWUiaW4gbiYmKG4ud3JpdGFibGU9ITApLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LG4ua2V5LG4pfX1mdW5jdGlvbiBodCh0LGUscil7cmV0dXJuIGUmJmx0KHQucHJvdG90eXBlLGUpLHImJmx0KHQsciksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsInByb3RvdHlwZSIse3dyaXRhYmxlOiExfSksdH1mdW5jdGlvbiBwdCh0LGUpe2lmKCEodCBpbnN0YW5jZW9mIGUpKXRocm93IG5ldyBUeXBlRXJyb3IoIkNhbm5vdCBjYWxsIGEgY2xhc3MgYXMgYSBmdW5jdGlvbiIpfXZhciB5dD1odCgoZnVuY3Rpb24gdCgpe3ZhciBlPXRoaXM7cHQodGhpcyx0KSx0aGlzLnVwZGF0ZWRVUkxzPVtdLHRoaXMubm90VXBkYXRlZFVSTHM9W10sdGhpcy5oYW5kbGVyV2lsbFN0YXJ0PWZ1bmN0aW9uKCl7dmFyIHQ9ZnQodXQoKS5tYXJrKChmdW5jdGlvbiB0KGUpe3ZhciByLG47cmV0dXJuIHV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnI9ZS5yZXF1ZXN0LG49ZS5zdGF0ZSxuJiYobi5vcmlnaW5hbFJlcXVlc3Q9cik7Y2FzZSAyOmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCl9KSkpO3JldHVybiBmdW5jdGlvbihlKXtyZXR1cm4gdC5hcHBseSh0aGlzLGFyZ3VtZW50cyl9fSgpLHRoaXMuY2FjaGVkUmVzcG9uc2VXaWxsQmVVc2VkPWZ1bmN0aW9uKCl7dmFyIHQ9ZnQodXQoKS5tYXJrKChmdW5jdGlvbiB0KHIpe3ZhciBuLG8saSxhO3JldHVybiB1dCgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDpyZXR1cm4gbj1yLmV2ZW50LG89ci5zdGF0ZSxpPXIuY2FjaGVkUmVzcG9uc2UsImluc3RhbGwiPT09bi50eXBlJiZvJiZvLm9yaWdpbmFsUmVxdWVzdCYmby5vcmlnaW5hbFJlcXVlc3QgaW5zdGFuY2VvZiBSZXF1ZXN0JiYoYT1vLm9yaWdpbmFsUmVxdWVzdC51cmwsaT9lLm5vdFVwZGF0ZWRVUkxzLnB1c2goYSk6ZS51cGRhdGVkVVJMcy5wdXNoKGEpKSx0LmFicnVwdCgicmV0dXJuIixpKTtjYXNlIDM6Y2FzZSJlbmQiOnJldHVybiB0LnN0b3AoKX19KSx0KX0pKSk7cmV0dXJuIGZ1bmN0aW9uKGUpe3JldHVybiB0LmFwcGx5KHRoaXMsYXJndW1lbnRzKX19KCl9KSk7ZnVuY3Rpb24gdnQodCl7cmV0dXJuIHZ0PSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSx2dCh0KX1mdW5jdGlvbiBkdCgpey8qISByZWdlbmVyYXRvci1ydW50aW1lIC0tIENvcHlyaWdodCAoYykgMjAxNC1wcmVzZW50LCBGYWNlYm9vaywgSW5jLiAtLSBsaWNlbnNlIChNSVQpOiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVnZW5lcmF0b3IvYmxvYi9tYWluL0xJQ0VOU0UgKi9kdD1mdW5jdGlvbigpe3JldHVybiB0fTt2YXIgdD17fSxlPU9iamVjdC5wcm90b3R5cGUscj1lLmhhc093blByb3BlcnR5LG49ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbD9TeW1ib2w6e30sbz1uLml0ZXJhdG9yfHwiQEBpdGVyYXRvciIsaT1uLmFzeW5jSXRlcmF0b3J8fCJAQGFzeW5jSXRlcmF0b3IiLGE9bi50b1N0cmluZ1RhZ3x8IkBAdG9TdHJpbmdUYWciO2Z1bmN0aW9uIGModCxlLHIpe3JldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkodCxlLHt2YWx1ZTpyLGVudW1lcmFibGU6ITAsY29uZmlndXJhYmxlOiEwLHdyaXRhYmxlOiEwfSksdFtlXX10cnl7Yyh7fSwiIil9Y2F0Y2goTyl7Yz1mdW5jdGlvbih0LGUscil7cmV0dXJuIHRbZV09cn19ZnVuY3Rpb24gdSh0LGUscixuKXt2YXIgbz1lJiZlLnByb3RvdHlwZSBpbnN0YW5jZW9mIGw/ZTpsLGk9T2JqZWN0LmNyZWF0ZShvLnByb3RvdHlwZSksYT1uZXcgayhufHxbXSk7cmV0dXJuIGkuX2ludm9rZT1mdW5jdGlvbih0LGUscil7dmFyIG49InN1c3BlbmRlZFN0YXJ0IjtyZXR1cm4gZnVuY3Rpb24obyxpKXtpZigiZXhlY3V0aW5nIj09PW4pdGhyb3cgbmV3IEVycm9yKCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBydW5uaW5nIik7aWYoImNvbXBsZXRlZCI9PT1uKXtpZigidGhyb3ciPT09byl0aHJvdyBpO3JldHVybiBfKCl9Zm9yKHIubWV0aG9kPW8sci5hcmc9aTs7KXt2YXIgYT1yLmRlbGVnYXRlO2lmKGEpe3ZhciBjPWIoYSxyKTtpZihjKXtpZihjPT09Ziljb250aW51ZTtyZXR1cm4gY319aWYoIm5leHQiPT09ci5tZXRob2Qpci5zZW50PXIuX3NlbnQ9ci5hcmc7ZWxzZSBpZigidGhyb3ciPT09ci5tZXRob2Qpe2lmKCJzdXNwZW5kZWRTdGFydCI9PT1uKXRocm93IG49ImNvbXBsZXRlZCIsci5hcmc7ci5kaXNwYXRjaEV4Y2VwdGlvbihyLmFyZyl9ZWxzZSJyZXR1cm4iPT09ci5tZXRob2QmJnIuYWJydXB0KCJyZXR1cm4iLHIuYXJnKTtuPSJleGVjdXRpbmciO3ZhciB1PXModCxlLHIpO2lmKCJub3JtYWwiPT09dS50eXBlKXtpZihuPXIuZG9uZT8iY29tcGxldGVkIjoic3VzcGVuZGVkWWllbGQiLHUuYXJnPT09Ziljb250aW51ZTtyZXR1cm57dmFsdWU6dS5hcmcsZG9uZTpyLmRvbmV9fSJ0aHJvdyI9PT11LnR5cGUmJihuPSJjb21wbGV0ZWQiLHIubWV0aG9kPSJ0aHJvdyIsci5hcmc9dS5hcmcpfX19KHQscixhKSxpfWZ1bmN0aW9uIHModCxlLHIpe3RyeXtyZXR1cm57dHlwZToibm9ybWFsIixhcmc6dC5jYWxsKGUscil9fWNhdGNoKE8pe3JldHVybnt0eXBlOiJ0aHJvdyIsYXJnOk99fX10LndyYXA9dTt2YXIgZj17fTtmdW5jdGlvbiBsKCl7fWZ1bmN0aW9uIGgoKXt9ZnVuY3Rpb24gcCgpe312YXIgeT17fTtjKHksbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKTt2YXIgdj1PYmplY3QuZ2V0UHJvdG90eXBlT2YsZD12JiZ2KHYoRShbXSkpKTtkJiZkIT09ZSYmci5jYWxsKGQsbykmJih5PWQpO3ZhciBtPXAucHJvdG90eXBlPWwucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoeSk7ZnVuY3Rpb24gZyh0KXtbIm5leHQiLCJ0aHJvdyIsInJldHVybiJdLmZvckVhY2goKGZ1bmN0aW9uKGUpe2ModCxlLChmdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5faW52b2tlKGUsdCl9KSl9KSl9ZnVuY3Rpb24gdyh0LGUpe2Z1bmN0aW9uIG4obyxpLGEsYyl7dmFyIHU9cyh0W29dLHQsaSk7aWYoInRocm93IiE9PXUudHlwZSl7dmFyIGY9dS5hcmcsbD1mLnZhbHVlO3JldHVybiBsJiYib2JqZWN0Ij09dnQobCkmJnIuY2FsbChsLCJfX2F3YWl0Iik/ZS5yZXNvbHZlKGwuX19hd2FpdCkudGhlbigoZnVuY3Rpb24odCl7bigibmV4dCIsdCxhLGMpfSksKGZ1bmN0aW9uKHQpe24oInRocm93Iix0LGEsYyl9KSk6ZS5yZXNvbHZlKGwpLnRoZW4oKGZ1bmN0aW9uKHQpe2YudmFsdWU9dCxhKGYpfSksKGZ1bmN0aW9uKHQpe3JldHVybiBuKCJ0aHJvdyIsdCxhLGMpfSkpfWModS5hcmcpfXZhciBvO3RoaXMuX2ludm9rZT1mdW5jdGlvbih0LHIpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gbmV3IGUoKGZ1bmN0aW9uKGUsbyl7bih0LHIsZSxvKX0pKX1yZXR1cm4gbz1vP28udGhlbihpLGkpOmkoKX19ZnVuY3Rpb24gYih0LGUpe3ZhciByPXQuaXRlcmF0b3JbZS5tZXRob2RdO2lmKHZvaWQgMD09PXIpe2lmKGUuZGVsZWdhdGU9bnVsbCwidGhyb3ciPT09ZS5tZXRob2Qpe2lmKHQuaXRlcmF0b3IucmV0dXJuJiYoZS5tZXRob2Q9InJldHVybiIsZS5hcmc9dm9pZCAwLGIodCxlKSwidGhyb3ciPT09ZS5tZXRob2QpKXJldHVybiBmO2UubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAndGhyb3cnIG1ldGhvZCIpfXJldHVybiBmfXZhciBuPXMocix0Lml0ZXJhdG9yLGUuYXJnKTtpZigidGhyb3ciPT09bi50eXBlKXJldHVybiBlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW4uYXJnLGUuZGVsZWdhdGU9bnVsbCxmO3ZhciBvPW4uYXJnO3JldHVybiBvP28uZG9uZT8oZVt0LnJlc3VsdE5hbWVdPW8udmFsdWUsZS5uZXh0PXQubmV4dExvYywicmV0dXJuIiE9PWUubWV0aG9kJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksZS5kZWxlZ2F0ZT1udWxsLGYpOm86KGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiaXRlcmF0b3IgcmVzdWx0IGlzIG5vdCBhbiBvYmplY3QiKSxlLmRlbGVnYXRlPW51bGwsZil9ZnVuY3Rpb24geCh0KXt2YXIgZT17dHJ5TG9jOnRbMF19OzEgaW4gdCYmKGUuY2F0Y2hMb2M9dFsxXSksMiBpbiB0JiYoZS5maW5hbGx5TG9jPXRbMl0sZS5hZnRlckxvYz10WzNdKSx0aGlzLnRyeUVudHJpZXMucHVzaChlKX1mdW5jdGlvbiBMKHQpe3ZhciBlPXQuY29tcGxldGlvbnx8e307ZS50eXBlPSJub3JtYWwiLGRlbGV0ZSBlLmFyZyx0LmNvbXBsZXRpb249ZX1mdW5jdGlvbiBrKHQpe3RoaXMudHJ5RW50cmllcz1be3RyeUxvYzoicm9vdCJ9XSx0LmZvckVhY2goeCx0aGlzKSx0aGlzLnJlc2V0KCEwKX1mdW5jdGlvbiBFKHQpe2lmKHQpe3ZhciBlPXRbb107aWYoZSlyZXR1cm4gZS5jYWxsKHQpO2lmKCJmdW5jdGlvbiI9PXR5cGVvZiB0Lm5leHQpcmV0dXJuIHQ7aWYoIWlzTmFOKHQubGVuZ3RoKSl7dmFyIG49LTEsaT1mdW5jdGlvbiBlKCl7Zm9yKDsrK248dC5sZW5ndGg7KWlmKHIuY2FsbCh0LG4pKXJldHVybiBlLnZhbHVlPXRbbl0sZS5kb25lPSExLGU7cmV0dXJuIGUudmFsdWU9dm9pZCAwLGUuZG9uZT0hMCxlfTtyZXR1cm4gaS5uZXh0PWl9fXJldHVybntuZXh0Ol99fWZ1bmN0aW9uIF8oKXtyZXR1cm57dmFsdWU6dm9pZCAwLGRvbmU6ITB9fXJldHVybiBoLnByb3RvdHlwZT1wLGMobSwiY29uc3RydWN0b3IiLHApLGMocCwiY29uc3RydWN0b3IiLGgpLGguZGlzcGxheU5hbWU9YyhwLGEsIkdlbmVyYXRvckZ1bmN0aW9uIiksdC5pc0dlbmVyYXRvckZ1bmN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBlPSJmdW5jdGlvbiI9PXR5cGVvZiB0JiZ0LmNvbnN0cnVjdG9yO3JldHVybiEhZSYmKGU9PT1ofHwiR2VuZXJhdG9yRnVuY3Rpb24iPT09KGUuZGlzcGxheU5hbWV8fGUubmFtZSkpfSx0Lm1hcms9ZnVuY3Rpb24odCl7cmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YodCxwKToodC5fX3Byb3RvX189cCxjKHQsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSksdC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShtKSx0fSx0LmF3cmFwPWZ1bmN0aW9uKHQpe3JldHVybntfX2F3YWl0OnR9fSxnKHcucHJvdG90eXBlKSxjKHcucHJvdG90eXBlLGksKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksdC5Bc3luY0l0ZXJhdG9yPXcsdC5hc3luYz1mdW5jdGlvbihlLHIsbixvLGkpe3ZvaWQgMD09PWkmJihpPVByb21pc2UpO3ZhciBhPW5ldyB3KHUoZSxyLG4sbyksaSk7cmV0dXJuIHQuaXNHZW5lcmF0b3JGdW5jdGlvbihyKT9hOmEubmV4dCgpLnRoZW4oKGZ1bmN0aW9uKHQpe3JldHVybiB0LmRvbmU/dC52YWx1ZTphLm5leHQoKX0pKX0sZyhtKSxjKG0sYSwiR2VuZXJhdG9yIiksYyhtLG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksYyhtLCJ0b1N0cmluZyIsKGZ1bmN0aW9uKCl7cmV0dXJuIltvYmplY3QgR2VuZXJhdG9yXSJ9KSksdC5rZXlzPWZ1bmN0aW9uKHQpe3ZhciBlPVtdO2Zvcih2YXIgciBpbiB0KWUucHVzaChyKTtyZXR1cm4gZS5yZXZlcnNlKCksZnVuY3Rpb24gcigpe2Zvcig7ZS5sZW5ndGg7KXt2YXIgbj1lLnBvcCgpO2lmKG4gaW4gdClyZXR1cm4gci52YWx1ZT1uLHIuZG9uZT0hMSxyfXJldHVybiByLmRvbmU9ITAscn19LHQudmFsdWVzPUUsay5wcm90b3R5cGU9e2NvbnN0cnVjdG9yOmsscmVzZXQ6ZnVuY3Rpb24odCl7aWYodGhpcy5wcmV2PTAsdGhpcy5uZXh0PTAsdGhpcy5zZW50PXRoaXMuX3NlbnQ9dm9pZCAwLHRoaXMuZG9uZT0hMSx0aGlzLmRlbGVnYXRlPW51bGwsdGhpcy5tZXRob2Q9Im5leHQiLHRoaXMuYXJnPXZvaWQgMCx0aGlzLnRyeUVudHJpZXMuZm9yRWFjaChMKSwhdClmb3IodmFyIGUgaW4gdGhpcykidCI9PT1lLmNoYXJBdCgwKSYmci5jYWxsKHRoaXMsZSkmJiFpc05hTigrZS5zbGljZSgxKSkmJih0aGlzW2VdPXZvaWQgMCl9LHN0b3A6ZnVuY3Rpb24oKXt0aGlzLmRvbmU9ITA7dmFyIHQ9dGhpcy50cnlFbnRyaWVzWzBdLmNvbXBsZXRpb247aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4gdGhpcy5ydmFsfSxkaXNwYXRjaEV4Y2VwdGlvbjpmdW5jdGlvbih0KXtpZih0aGlzLmRvbmUpdGhyb3cgdDt2YXIgZT10aGlzO2Z1bmN0aW9uIG4ocixuKXtyZXR1cm4gYS50eXBlPSJ0aHJvdyIsYS5hcmc9dCxlLm5leHQ9cixuJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksISFufWZvcih2YXIgbz10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bz49MDstLW8pe3ZhciBpPXRoaXMudHJ5RW50cmllc1tvXSxhPWkuY29tcGxldGlvbjtpZigicm9vdCI9PT1pLnRyeUxvYylyZXR1cm4gbigiZW5kIik7aWYoaS50cnlMb2M8PXRoaXMucHJldil7dmFyIGM9ci5jYWxsKGksImNhdGNoTG9jIiksdT1yLmNhbGwoaSwiZmluYWxseUxvYyIpO2lmKGMmJnUpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX1lbHNlIGlmKGMpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApfWVsc2V7aWYoIXUpdGhyb3cgbmV3IEVycm9yKCJ0cnkgc3RhdGVtZW50IHdpdGhvdXQgY2F0Y2ggb3IgZmluYWxseSIpO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX19fX0sYWJydXB0OmZ1bmN0aW9uKHQsZSl7Zm9yKHZhciBuPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtuPj0wOy0tbil7dmFyIG89dGhpcy50cnlFbnRyaWVzW25dO2lmKG8udHJ5TG9jPD10aGlzLnByZXYmJnIuY2FsbChvLCJmaW5hbGx5TG9jIikmJnRoaXMucHJldjxvLmZpbmFsbHlMb2Mpe3ZhciBpPW87YnJlYWt9fWkmJigiYnJlYWsiPT09dHx8ImNvbnRpbnVlIj09PXQpJiZpLnRyeUxvYzw9ZSYmZTw9aS5maW5hbGx5TG9jJiYoaT1udWxsKTt2YXIgYT1pP2kuY29tcGxldGlvbjp7fTtyZXR1cm4gYS50eXBlPXQsYS5hcmc9ZSxpPyh0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5uZXh0PWkuZmluYWxseUxvYyxmKTp0aGlzLmNvbXBsZXRlKGEpfSxjb21wbGV0ZTpmdW5jdGlvbih0LGUpe2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuImJyZWFrIj09PXQudHlwZXx8ImNvbnRpbnVlIj09PXQudHlwZT90aGlzLm5leHQ9dC5hcmc6InJldHVybiI9PT10LnR5cGU/KHRoaXMucnZhbD10aGlzLmFyZz10LmFyZyx0aGlzLm1ldGhvZD0icmV0dXJuIix0aGlzLm5leHQ9ImVuZCIpOiJub3JtYWwiPT09dC50eXBlJiZlJiYodGhpcy5uZXh0PWUpLGZ9LGZpbmlzaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci5maW5hbGx5TG9jPT09dClyZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sci5hZnRlckxvYyksTChyKSxmfX0sY2F0Y2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIudHJ5TG9jPT09dCl7dmFyIG49ci5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT1uLnR5cGUpe3ZhciBvPW4uYXJnO0wocil9cmV0dXJuIG99fXRocm93IG5ldyBFcnJvcigiaWxsZWdhbCBjYXRjaCBhdHRlbXB0Iil9LGRlbGVnYXRlWWllbGQ6ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0aGlzLmRlbGVnYXRlPXtpdGVyYXRvcjpFKHQpLHJlc3VsdE5hbWU6ZSxuZXh0TG9jOnJ9LCJuZXh0Ij09PXRoaXMubWV0aG9kJiYodGhpcy5hcmc9dm9pZCAwKSxmfX0sdH1mdW5jdGlvbiBtdCh0LGUscixuLG8saSxhKXt0cnl7dmFyIGM9dFtpXShhKSx1PWMudmFsdWV9Y2F0Y2gocyl7cmV0dXJuIHZvaWQgcihzKX1jLmRvbmU/ZSh1KTpQcm9taXNlLnJlc29sdmUodSkudGhlbihuLG8pfWZ1bmN0aW9uIGd0KHQpe3JldHVybiBmdW5jdGlvbigpe3ZhciBlPXRoaXMscj1hcmd1bWVudHM7cmV0dXJuIG5ldyBQcm9taXNlKChmdW5jdGlvbihuLG8pe3ZhciBpPXQuYXBwbHkoZSxyKTtmdW5jdGlvbiBhKHQpe210KGksbixvLGEsYywibmV4dCIsdCl9ZnVuY3Rpb24gYyh0KXttdChpLG4sbyxhLGMsInRocm93Iix0KX1hKHZvaWQgMCl9KSl9fWZ1bmN0aW9uIHd0KHQsZSl7Zm9yKHZhciByPTA7cjxlLmxlbmd0aDtyKyspe3ZhciBuPWVbcl07bi5lbnVtZXJhYmxlPW4uZW51bWVyYWJsZXx8ITEsbi5jb25maWd1cmFibGU9ITAsInZhbHVlImluIG4mJihuLndyaXRhYmxlPSEwKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCxuLmtleSxuKX19ZnVuY3Rpb24gYnQodCxlLHIpe3JldHVybiBlJiZ3dCh0LnByb3RvdHlwZSxlKSxyJiZ3dCh0LHIpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LCJwcm90b3R5cGUiLHt3cml0YWJsZTohMX0pLHR9ZnVuY3Rpb24geHQodCxlKXtpZighKHQgaW5zdGFuY2VvZiBlKSl0aHJvdyBuZXcgVHlwZUVycm9yKCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb24iKX12YXIgTHQ9YnQoKGZ1bmN0aW9uIHQoZSl7dmFyIHI9dGhpcyxuPWUucHJlY2FjaGVDb250cm9sbGVyO3h0KHRoaXMsdCksdGhpcy5jYWNoZUtleVdpbGxCZVVzZWQ9ZnVuY3Rpb24oKXt2YXIgdD1ndChkdCgpLm1hcmsoKGZ1bmN0aW9uIHQoZSl7dmFyIG4sbyxpO3JldHVybiBkdCgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDpyZXR1cm4gbj1lLnJlcXVlc3Qsbz1lLnBhcmFtcyxpPShudWxsPT09b3x8dm9pZCAwPT09bz92b2lkIDA6by5jYWNoZUtleSl8fHIuX3ByZWNhY2hlQ29udHJvbGxlci5nZXRDYWNoZUtleUZvclVSTChuLnVybCksdC5hYnJ1cHQoInJldHVybiIsaT9uZXcgUmVxdWVzdChpLHtoZWFkZXJzOm4uaGVhZGVyc30pOm4pO2Nhc2UgMzpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQpfSkpKTtyZXR1cm4gZnVuY3Rpb24oZSl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfX0oKSx0aGlzLl9wcmVjYWNoZUNvbnRyb2xsZXI9bn0pKTtyKDU3Nyk7ZnVuY3Rpb24ga3QodCl7cmV0dXJuIGt0PSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSxrdCh0KX1mdW5jdGlvbiBFdCgpey8qISByZWdlbmVyYXRvci1ydW50aW1lIC0tIENvcHlyaWdodCAoYykgMjAxNC1wcmVzZW50LCBGYWNlYm9vaywgSW5jLiAtLSBsaWNlbnNlIChNSVQpOiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVnZW5lcmF0b3IvYmxvYi9tYWluL0xJQ0VOU0UgKi9FdD1mdW5jdGlvbigpe3JldHVybiB0fTt2YXIgdD17fSxlPU9iamVjdC5wcm90b3R5cGUscj1lLmhhc093blByb3BlcnR5LG49ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbD9TeW1ib2w6e30sbz1uLml0ZXJhdG9yfHwiQEBpdGVyYXRvciIsaT1uLmFzeW5jSXRlcmF0b3J8fCJAQGFzeW5jSXRlcmF0b3IiLGE9bi50b1N0cmluZ1RhZ3x8IkBAdG9TdHJpbmdUYWciO2Z1bmN0aW9uIGModCxlLHIpe3JldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkodCxlLHt2YWx1ZTpyLGVudW1lcmFibGU6ITAsY29uZmlndXJhYmxlOiEwLHdyaXRhYmxlOiEwfSksdFtlXX10cnl7Yyh7fSwiIil9Y2F0Y2goTyl7Yz1mdW5jdGlvbih0LGUscil7cmV0dXJuIHRbZV09cn19ZnVuY3Rpb24gdSh0LGUscixuKXt2YXIgbz1lJiZlLnByb3RvdHlwZSBpbnN0YW5jZW9mIGw/ZTpsLGk9T2JqZWN0LmNyZWF0ZShvLnByb3RvdHlwZSksYT1uZXcgayhufHxbXSk7cmV0dXJuIGkuX2ludm9rZT1mdW5jdGlvbih0LGUscil7dmFyIG49InN1c3BlbmRlZFN0YXJ0IjtyZXR1cm4gZnVuY3Rpb24obyxpKXtpZigiZXhlY3V0aW5nIj09PW4pdGhyb3cgbmV3IEVycm9yKCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBydW5uaW5nIik7aWYoImNvbXBsZXRlZCI9PT1uKXtpZigidGhyb3ciPT09byl0aHJvdyBpO3JldHVybiBfKCl9Zm9yKHIubWV0aG9kPW8sci5hcmc9aTs7KXt2YXIgYT1yLmRlbGVnYXRlO2lmKGEpe3ZhciBjPWIoYSxyKTtpZihjKXtpZihjPT09Ziljb250aW51ZTtyZXR1cm4gY319aWYoIm5leHQiPT09ci5tZXRob2Qpci5zZW50PXIuX3NlbnQ9ci5hcmc7ZWxzZSBpZigidGhyb3ciPT09ci5tZXRob2Qpe2lmKCJzdXNwZW5kZWRTdGFydCI9PT1uKXRocm93IG49ImNvbXBsZXRlZCIsci5hcmc7ci5kaXNwYXRjaEV4Y2VwdGlvbihyLmFyZyl9ZWxzZSJyZXR1cm4iPT09ci5tZXRob2QmJnIuYWJydXB0KCJyZXR1cm4iLHIuYXJnKTtuPSJleGVjdXRpbmciO3ZhciB1PXModCxlLHIpO2lmKCJub3JtYWwiPT09dS50eXBlKXtpZihuPXIuZG9uZT8iY29tcGxldGVkIjoic3VzcGVuZGVkWWllbGQiLHUuYXJnPT09Ziljb250aW51ZTtyZXR1cm57dmFsdWU6dS5hcmcsZG9uZTpyLmRvbmV9fSJ0aHJvdyI9PT11LnR5cGUmJihuPSJjb21wbGV0ZWQiLHIubWV0aG9kPSJ0aHJvdyIsci5hcmc9dS5hcmcpfX19KHQscixhKSxpfWZ1bmN0aW9uIHModCxlLHIpe3RyeXtyZXR1cm57dHlwZToibm9ybWFsIixhcmc6dC5jYWxsKGUscil9fWNhdGNoKE8pe3JldHVybnt0eXBlOiJ0aHJvdyIsYXJnOk99fX10LndyYXA9dTt2YXIgZj17fTtmdW5jdGlvbiBsKCl7fWZ1bmN0aW9uIGgoKXt9ZnVuY3Rpb24gcCgpe312YXIgeT17fTtjKHksbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKTt2YXIgdj1PYmplY3QuZ2V0UHJvdG90eXBlT2YsZD12JiZ2KHYoRShbXSkpKTtkJiZkIT09ZSYmci5jYWxsKGQsbykmJih5PWQpO3ZhciBtPXAucHJvdG90eXBlPWwucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoeSk7ZnVuY3Rpb24gZyh0KXtbIm5leHQiLCJ0aHJvdyIsInJldHVybiJdLmZvckVhY2goKGZ1bmN0aW9uKGUpe2ModCxlLChmdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5faW52b2tlKGUsdCl9KSl9KSl9ZnVuY3Rpb24gdyh0LGUpe2Z1bmN0aW9uIG4obyxpLGEsYyl7dmFyIHU9cyh0W29dLHQsaSk7aWYoInRocm93IiE9PXUudHlwZSl7dmFyIGY9dS5hcmcsbD1mLnZhbHVlO3JldHVybiBsJiYib2JqZWN0Ij09a3QobCkmJnIuY2FsbChsLCJfX2F3YWl0Iik/ZS5yZXNvbHZlKGwuX19hd2FpdCkudGhlbigoZnVuY3Rpb24odCl7bigibmV4dCIsdCxhLGMpfSksKGZ1bmN0aW9uKHQpe24oInRocm93Iix0LGEsYyl9KSk6ZS5yZXNvbHZlKGwpLnRoZW4oKGZ1bmN0aW9uKHQpe2YudmFsdWU9dCxhKGYpfSksKGZ1bmN0aW9uKHQpe3JldHVybiBuKCJ0aHJvdyIsdCxhLGMpfSkpfWModS5hcmcpfXZhciBvO3RoaXMuX2ludm9rZT1mdW5jdGlvbih0LHIpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gbmV3IGUoKGZ1bmN0aW9uKGUsbyl7bih0LHIsZSxvKX0pKX1yZXR1cm4gbz1vP28udGhlbihpLGkpOmkoKX19ZnVuY3Rpb24gYih0LGUpe3ZhciByPXQuaXRlcmF0b3JbZS5tZXRob2RdO2lmKHZvaWQgMD09PXIpe2lmKGUuZGVsZWdhdGU9bnVsbCwidGhyb3ciPT09ZS5tZXRob2Qpe2lmKHQuaXRlcmF0b3IucmV0dXJuJiYoZS5tZXRob2Q9InJldHVybiIsZS5hcmc9dm9pZCAwLGIodCxlKSwidGhyb3ciPT09ZS5tZXRob2QpKXJldHVybiBmO2UubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAndGhyb3cnIG1ldGhvZCIpfXJldHVybiBmfXZhciBuPXMocix0Lml0ZXJhdG9yLGUuYXJnKTtpZigidGhyb3ciPT09bi50eXBlKXJldHVybiBlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW4uYXJnLGUuZGVsZWdhdGU9bnVsbCxmO3ZhciBvPW4uYXJnO3JldHVybiBvP28uZG9uZT8oZVt0LnJlc3VsdE5hbWVdPW8udmFsdWUsZS5uZXh0PXQubmV4dExvYywicmV0dXJuIiE9PWUubWV0aG9kJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksZS5kZWxlZ2F0ZT1udWxsLGYpOm86KGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiaXRlcmF0b3IgcmVzdWx0IGlzIG5vdCBhbiBvYmplY3QiKSxlLmRlbGVnYXRlPW51bGwsZil9ZnVuY3Rpb24geCh0KXt2YXIgZT17dHJ5TG9jOnRbMF19OzEgaW4gdCYmKGUuY2F0Y2hMb2M9dFsxXSksMiBpbiB0JiYoZS5maW5hbGx5TG9jPXRbMl0sZS5hZnRlckxvYz10WzNdKSx0aGlzLnRyeUVudHJpZXMucHVzaChlKX1mdW5jdGlvbiBMKHQpe3ZhciBlPXQuY29tcGxldGlvbnx8e307ZS50eXBlPSJub3JtYWwiLGRlbGV0ZSBlLmFyZyx0LmNvbXBsZXRpb249ZX1mdW5jdGlvbiBrKHQpe3RoaXMudHJ5RW50cmllcz1be3RyeUxvYzoicm9vdCJ9XSx0LmZvckVhY2goeCx0aGlzKSx0aGlzLnJlc2V0KCEwKX1mdW5jdGlvbiBFKHQpe2lmKHQpe3ZhciBlPXRbb107aWYoZSlyZXR1cm4gZS5jYWxsKHQpO2lmKCJmdW5jdGlvbiI9PXR5cGVvZiB0Lm5leHQpcmV0dXJuIHQ7aWYoIWlzTmFOKHQubGVuZ3RoKSl7dmFyIG49LTEsaT1mdW5jdGlvbiBlKCl7Zm9yKDsrK248dC5sZW5ndGg7KWlmKHIuY2FsbCh0LG4pKXJldHVybiBlLnZhbHVlPXRbbl0sZS5kb25lPSExLGU7cmV0dXJuIGUudmFsdWU9dm9pZCAwLGUuZG9uZT0hMCxlfTtyZXR1cm4gaS5uZXh0PWl9fXJldHVybntuZXh0Ol99fWZ1bmN0aW9uIF8oKXtyZXR1cm57dmFsdWU6dm9pZCAwLGRvbmU6ITB9fXJldHVybiBoLnByb3RvdHlwZT1wLGMobSwiY29uc3RydWN0b3IiLHApLGMocCwiY29uc3RydWN0b3IiLGgpLGguZGlzcGxheU5hbWU9YyhwLGEsIkdlbmVyYXRvckZ1bmN0aW9uIiksdC5pc0dlbmVyYXRvckZ1bmN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBlPSJmdW5jdGlvbiI9PXR5cGVvZiB0JiZ0LmNvbnN0cnVjdG9yO3JldHVybiEhZSYmKGU9PT1ofHwiR2VuZXJhdG9yRnVuY3Rpb24iPT09KGUuZGlzcGxheU5hbWV8fGUubmFtZSkpfSx0Lm1hcms9ZnVuY3Rpb24odCl7cmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YodCxwKToodC5fX3Byb3RvX189cCxjKHQsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSksdC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShtKSx0fSx0LmF3cmFwPWZ1bmN0aW9uKHQpe3JldHVybntfX2F3YWl0OnR9fSxnKHcucHJvdG90eXBlKSxjKHcucHJvdG90eXBlLGksKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksdC5Bc3luY0l0ZXJhdG9yPXcsdC5hc3luYz1mdW5jdGlvbihlLHIsbixvLGkpe3ZvaWQgMD09PWkmJihpPVByb21pc2UpO3ZhciBhPW5ldyB3KHUoZSxyLG4sbyksaSk7cmV0dXJuIHQuaXNHZW5lcmF0b3JGdW5jdGlvbihyKT9hOmEubmV4dCgpLnRoZW4oKGZ1bmN0aW9uKHQpe3JldHVybiB0LmRvbmU/dC52YWx1ZTphLm5leHQoKX0pKX0sZyhtKSxjKG0sYSwiR2VuZXJhdG9yIiksYyhtLG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksYyhtLCJ0b1N0cmluZyIsKGZ1bmN0aW9uKCl7cmV0dXJuIltvYmplY3QgR2VuZXJhdG9yXSJ9KSksdC5rZXlzPWZ1bmN0aW9uKHQpe3ZhciBlPVtdO2Zvcih2YXIgciBpbiB0KWUucHVzaChyKTtyZXR1cm4gZS5yZXZlcnNlKCksZnVuY3Rpb24gcigpe2Zvcig7ZS5sZW5ndGg7KXt2YXIgbj1lLnBvcCgpO2lmKG4gaW4gdClyZXR1cm4gci52YWx1ZT1uLHIuZG9uZT0hMSxyfXJldHVybiByLmRvbmU9ITAscn19LHQudmFsdWVzPUUsay5wcm90b3R5cGU9e2NvbnN0cnVjdG9yOmsscmVzZXQ6ZnVuY3Rpb24odCl7aWYodGhpcy5wcmV2PTAsdGhpcy5uZXh0PTAsdGhpcy5zZW50PXRoaXMuX3NlbnQ9dm9pZCAwLHRoaXMuZG9uZT0hMSx0aGlzLmRlbGVnYXRlPW51bGwsdGhpcy5tZXRob2Q9Im5leHQiLHRoaXMuYXJnPXZvaWQgMCx0aGlzLnRyeUVudHJpZXMuZm9yRWFjaChMKSwhdClmb3IodmFyIGUgaW4gdGhpcykidCI9PT1lLmNoYXJBdCgwKSYmci5jYWxsKHRoaXMsZSkmJiFpc05hTigrZS5zbGljZSgxKSkmJih0aGlzW2VdPXZvaWQgMCl9LHN0b3A6ZnVuY3Rpb24oKXt0aGlzLmRvbmU9ITA7dmFyIHQ9dGhpcy50cnlFbnRyaWVzWzBdLmNvbXBsZXRpb247aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4gdGhpcy5ydmFsfSxkaXNwYXRjaEV4Y2VwdGlvbjpmdW5jdGlvbih0KXtpZih0aGlzLmRvbmUpdGhyb3cgdDt2YXIgZT10aGlzO2Z1bmN0aW9uIG4ocixuKXtyZXR1cm4gYS50eXBlPSJ0aHJvdyIsYS5hcmc9dCxlLm5leHQ9cixuJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksISFufWZvcih2YXIgbz10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bz49MDstLW8pe3ZhciBpPXRoaXMudHJ5RW50cmllc1tvXSxhPWkuY29tcGxldGlvbjtpZigicm9vdCI9PT1pLnRyeUxvYylyZXR1cm4gbigiZW5kIik7aWYoaS50cnlMb2M8PXRoaXMucHJldil7dmFyIGM9ci5jYWxsKGksImNhdGNoTG9jIiksdT1yLmNhbGwoaSwiZmluYWxseUxvYyIpO2lmKGMmJnUpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX1lbHNlIGlmKGMpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApfWVsc2V7aWYoIXUpdGhyb3cgbmV3IEVycm9yKCJ0cnkgc3RhdGVtZW50IHdpdGhvdXQgY2F0Y2ggb3IgZmluYWxseSIpO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX19fX0sYWJydXB0OmZ1bmN0aW9uKHQsZSl7Zm9yKHZhciBuPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtuPj0wOy0tbil7dmFyIG89dGhpcy50cnlFbnRyaWVzW25dO2lmKG8udHJ5TG9jPD10aGlzLnByZXYmJnIuY2FsbChvLCJmaW5hbGx5TG9jIikmJnRoaXMucHJldjxvLmZpbmFsbHlMb2Mpe3ZhciBpPW87YnJlYWt9fWkmJigiYnJlYWsiPT09dHx8ImNvbnRpbnVlIj09PXQpJiZpLnRyeUxvYzw9ZSYmZTw9aS5maW5hbGx5TG9jJiYoaT1udWxsKTt2YXIgYT1pP2kuY29tcGxldGlvbjp7fTtyZXR1cm4gYS50eXBlPXQsYS5hcmc9ZSxpPyh0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5uZXh0PWkuZmluYWxseUxvYyxmKTp0aGlzLmNvbXBsZXRlKGEpfSxjb21wbGV0ZTpmdW5jdGlvbih0LGUpe2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuImJyZWFrIj09PXQudHlwZXx8ImNvbnRpbnVlIj09PXQudHlwZT90aGlzLm5leHQ9dC5hcmc6InJldHVybiI9PT10LnR5cGU/KHRoaXMucnZhbD10aGlzLmFyZz10LmFyZyx0aGlzLm1ldGhvZD0icmV0dXJuIix0aGlzLm5leHQ9ImVuZCIpOiJub3JtYWwiPT09dC50eXBlJiZlJiYodGhpcy5uZXh0PWUpLGZ9LGZpbmlzaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci5maW5hbGx5TG9jPT09dClyZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sci5hZnRlckxvYyksTChyKSxmfX0sY2F0Y2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIudHJ5TG9jPT09dCl7dmFyIG49ci5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT1uLnR5cGUpe3ZhciBvPW4uYXJnO0wocil9cmV0dXJuIG99fXRocm93IG5ldyBFcnJvcigiaWxsZWdhbCBjYXRjaCBhdHRlbXB0Iil9LGRlbGVnYXRlWWllbGQ6ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0aGlzLmRlbGVnYXRlPXtpdGVyYXRvcjpFKHQpLHJlc3VsdE5hbWU6ZSxuZXh0TG9jOnJ9LCJuZXh0Ij09PXRoaXMubWV0aG9kJiYodGhpcy5hcmc9dm9pZCAwKSxmfX0sdH1mdW5jdGlvbiBfdCh0LGUscixuLG8saSxhKXt0cnl7dmFyIGM9dFtpXShhKSx1PWMudmFsdWV9Y2F0Y2gocyl7cmV0dXJuIHZvaWQgcihzKX1jLmRvbmU/ZSh1KTpQcm9taXNlLnJlc29sdmUodSkudGhlbihuLG8pfWZ1bmN0aW9uIE90KHQpe3JldHVybiBmdW5jdGlvbigpe3ZhciBlPXRoaXMscj1hcmd1bWVudHM7cmV0dXJuIG5ldyBQcm9taXNlKChmdW5jdGlvbihuLG8pe3ZhciBpPXQuYXBwbHkoZSxyKTtmdW5jdGlvbiBhKHQpe190KGksbixvLGEsYywibmV4dCIsdCl9ZnVuY3Rpb24gYyh0KXtfdChpLG4sbyxhLGMsInRocm93Iix0KX1hKHZvaWQgMCl9KSl9fWZ1bmN0aW9uIFN0KHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9VHQodCkpfHxlJiZ0JiYibnVtYmVyIj09PXR5cGVvZiB0Lmxlbmd0aCl7ciYmKHQ9cik7dmFyIG49MCxvPWZ1bmN0aW9uKCl7fTtyZXR1cm57czpvLG46ZnVuY3Rpb24oKXtyZXR1cm4gbj49dC5sZW5ndGg/e2RvbmU6ITB9Ontkb25lOiExLHZhbHVlOnRbbisrXX19LGU6ZnVuY3Rpb24odCl7dGhyb3cgdH0sZjpvfX10aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gaXRlcmF0ZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9dmFyIGksYT0hMCxjPSExO3JldHVybntzOmZ1bmN0aW9uKCl7cj1yLmNhbGwodCl9LG46ZnVuY3Rpb24oKXt2YXIgdD1yLm5leHQoKTtyZXR1cm4gYT10LmRvbmUsdH0sZTpmdW5jdGlvbih0KXtjPSEwLGk9dH0sZjpmdW5jdGlvbigpe3RyeXthfHxudWxsPT1yLnJldHVybnx8ci5yZXR1cm4oKX1maW5hbGx5e2lmKGMpdGhyb3cgaX19fX1mdW5jdGlvbiBqdCh0KXtyZXR1cm4gUnQodCl8fE50KHQpfHxUdCh0KXx8UHQoKX1mdW5jdGlvbiBQdCgpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBzcHJlYWQgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfWZ1bmN0aW9uIFR0KHQsZSl7aWYodCl7aWYoInN0cmluZyI9PT10eXBlb2YgdClyZXR1cm4gQXQodCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT9BdCh0LGUpOnZvaWQgMH19ZnVuY3Rpb24gTnQodCl7aWYoInVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZudWxsIT10W1N5bWJvbC5pdGVyYXRvcl18fG51bGwhPXRbIkBAaXRlcmF0b3IiXSlyZXR1cm4gQXJyYXkuZnJvbSh0KX1mdW5jdGlvbiBSdCh0KXtpZihBcnJheS5pc0FycmF5KHQpKXJldHVybiBBdCh0KX1mdW5jdGlvbiBBdCh0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIEN0KHQsZSl7aWYoISh0IGluc3RhbmNlb2YgZSkpdGhyb3cgbmV3IFR5cGVFcnJvcigiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uIil9ZnVuY3Rpb24gSXQodCxlKXtmb3IodmFyIHI9MDtyPGUubGVuZ3RoO3IrKyl7dmFyIG49ZVtyXTtuLmVudW1lcmFibGU9bi5lbnVtZXJhYmxlfHwhMSxuLmNvbmZpZ3VyYWJsZT0hMCwidmFsdWUiaW4gbiYmKG4ud3JpdGFibGU9ITApLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LG4ua2V5LG4pfX1mdW5jdGlvbiBHdCh0LGUscil7cmV0dXJuIGUmJkl0KHQucHJvdG90eXBlLGUpLHImJkl0KHQsciksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsInByb3RvdHlwZSIse3dyaXRhYmxlOiExfSksdH1mdW5jdGlvbiBGdCh0KXtyZXR1cm4ic3RyaW5nIj09PXR5cGVvZiB0P25ldyBSZXF1ZXN0KHQpOnR9dmFyIFV0PWZ1bmN0aW9uKCl7ZnVuY3Rpb24gZSh0LHIpe0N0KHRoaXMsZSksdGhpcy5fY2FjaGVLZXlzPXt9LE9iamVjdC5hc3NpZ24odGhpcyxyKSx0aGlzLmV2ZW50PXIuZXZlbnQsdGhpcy5fc3RyYXRlZ3k9dCx0aGlzLl9oYW5kbGVyRGVmZXJyZWQ9bmV3IHEsdGhpcy5fZXh0ZW5kTGlmZXRpbWVQcm9taXNlcz1bXSx0aGlzLl9wbHVnaW5zPWp0KHQucGx1Z2lucyksdGhpcy5fcGx1Z2luU3RhdGVNYXA9bmV3IE1hcDt2YXIgbixvPVN0KHRoaXMuX3BsdWdpbnMpO3RyeXtmb3Ioby5zKCk7IShuPW8ubigpKS5kb25lOyl7dmFyIGk9bi52YWx1ZTt0aGlzLl9wbHVnaW5TdGF0ZU1hcC5zZXQoaSx7fSl9fWNhdGNoKGEpe28uZShhKX1maW5hbGx5e28uZigpfXRoaXMuZXZlbnQud2FpdFVudGlsKHRoaXMuX2hhbmRsZXJEZWZlcnJlZC5wcm9taXNlKX1yZXR1cm4gR3QoZSxbe2tleToiZmV0Y2giLHZhbHVlOmZ1bmN0aW9uKHQpe2Z1bmN0aW9uIGUoZSl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiBlLnRvU3RyaW5nPWZ1bmN0aW9uKCl7cmV0dXJuIHQudG9TdHJpbmcoKX0sZX0oZnVuY3Rpb24oKXt2YXIgdD1PdChFdCgpLm1hcmsoKGZ1bmN0aW9uIHQoZSl7dmFyIHIsbixvLGksYSxjLHUscyxmLGwsaCxwO3JldHVybiBFdCgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDppZihyPXRoaXMuZXZlbnQsbj1GdChlKSwhKCJuYXZpZ2F0ZSI9PT1uLm1vZGUmJnIgaW5zdGFuY2VvZiBGZXRjaEV2ZW50JiZyLnByZWxvYWRSZXNwb25zZSkpe3QubmV4dD05O2JyZWFrfXJldHVybiB0Lm5leHQ9NSxyLnByZWxvYWRSZXNwb25zZTtjYXNlIDU6aWYobz10LnNlbnQsIW8pe3QubmV4dD05O2JyZWFrfXJldHVybiB0LmFicnVwdCgicmV0dXJuIixvKTtjYXNlIDk6aT10aGlzLmhhc0NhbGxiYWNrKCJmZXRjaERpZEZhaWwiKT9uLmNsb25lKCk6bnVsbCx0LnByZXY9MTAsYT1TdCh0aGlzLml0ZXJhdGVDYWxsYmFja3MoInJlcXVlc3RXaWxsRmV0Y2giKSksdC5wcmV2PTEyLGEucygpO2Nhc2UgMTQ6aWYoKGM9YS5uKCkpLmRvbmUpe3QubmV4dD0yMTticmVha31yZXR1cm4gdT1jLnZhbHVlLHQubmV4dD0xOCx1KHtyZXF1ZXN0Om4uY2xvbmUoKSxldmVudDpyfSk7Y2FzZSAxODpuPXQuc2VudDtjYXNlIDE5OnQubmV4dD0xNDticmVhaztjYXNlIDIxOnQubmV4dD0yNjticmVhaztjYXNlIDIzOnQucHJldj0yMyx0LnQwPXRbImNhdGNoIl0oMTIpLGEuZSh0LnQwKTtjYXNlIDI2OnJldHVybiB0LnByZXY9MjYsYS5mKCksdC5maW5pc2goMjYpO2Nhc2UgMjk6dC5uZXh0PTM1O2JyZWFrO2Nhc2UgMzE6aWYodC5wcmV2PTMxLHQudDE9dFsiY2F0Y2giXSgxMCksISh0LnQxIGluc3RhbmNlb2YgRXJyb3IpKXt0Lm5leHQ9MzU7YnJlYWt9dGhyb3cgbmV3IGcoInBsdWdpbi1lcnJvci1yZXF1ZXN0LXdpbGwtZmV0Y2giLHt0aHJvd25FcnJvck1lc3NhZ2U6dC50MS5tZXNzYWdlfSk7Y2FzZSAzNTpyZXR1cm4gcz1uLmNsb25lKCksdC5wcmV2PTM2LHQubmV4dD0zOSxmZXRjaChuLCJuYXZpZ2F0ZSI9PT1uLm1vZGU/dm9pZCAwOnRoaXMuX3N0cmF0ZWd5LmZldGNoT3B0aW9ucyk7Y2FzZSAzOTpmPXQuc2VudCxsPVN0KHRoaXMuaXRlcmF0ZUNhbGxiYWNrcygiZmV0Y2hEaWRTdWNjZWVkIikpLHQucHJldj00MixsLnMoKTtjYXNlIDQ0OmlmKChoPWwubigpKS5kb25lKXt0Lm5leHQ9NTE7YnJlYWt9cmV0dXJuIHA9aC52YWx1ZSx0Lm5leHQ9NDgscCh7ZXZlbnQ6cixyZXF1ZXN0OnMscmVzcG9uc2U6Zn0pO2Nhc2UgNDg6Zj10LnNlbnQ7Y2FzZSA0OTp0Lm5leHQ9NDQ7YnJlYWs7Y2FzZSA1MTp0Lm5leHQ9NTY7YnJlYWs7Y2FzZSA1Mzp0LnByZXY9NTMsdC50Mj10WyJjYXRjaCJdKDQyKSxsLmUodC50Mik7Y2FzZSA1NjpyZXR1cm4gdC5wcmV2PTU2LGwuZigpLHQuZmluaXNoKDU2KTtjYXNlIDU5OnJldHVybiB0LmFicnVwdCgicmV0dXJuIixmKTtjYXNlIDYyOmlmKHQucHJldj02Mix0LnQzPXRbImNhdGNoIl0oMzYpLCFpKXt0Lm5leHQ9Njg7YnJlYWt9cmV0dXJuIHQubmV4dD02OCx0aGlzLnJ1bkNhbGxiYWNrcygiZmV0Y2hEaWRGYWlsIix7ZXJyb3I6dC50MyxldmVudDpyLG9yaWdpbmFsUmVxdWVzdDppLmNsb25lKCkscmVxdWVzdDpzLmNsb25lKCl9KTtjYXNlIDY4OnRocm93IHQudDM7Y2FzZSA2OTpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsdGhpcyxbWzEwLDMxXSxbMTIsMjMsMjYsMjldLFszNiw2Ml0sWzQyLDUzLDU2LDU5XV0pfSkpKTtyZXR1cm4gZnVuY3Rpb24oZSl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfX0oKSl9LHtrZXk6ImZldGNoQW5kQ2FjaGVQdXQiLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIHQ9T3QoRXQoKS5tYXJrKChmdW5jdGlvbiB0KGUpe3ZhciByLG47cmV0dXJuIEV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnJldHVybiB0Lm5leHQ9Mix0aGlzLmZldGNoKGUpO2Nhc2UgMjpyZXR1cm4gcj10LnNlbnQsbj1yLmNsb25lKCksdGhpcy53YWl0VW50aWwodGhpcy5jYWNoZVB1dChlLG4pKSx0LmFicnVwdCgicmV0dXJuIixyKTtjYXNlIDY6Y2FzZSJlbmQiOnJldHVybiB0LnN0b3AoKX19KSx0LHRoaXMpfSkpKTtmdW5jdGlvbiBlKGUpe3JldHVybiB0LmFwcGx5KHRoaXMsYXJndW1lbnRzKX1yZXR1cm4gZX0oKX0se2tleToiY2FjaGVNYXRjaCIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgdD1PdChFdCgpLm1hcmsoKGZ1bmN0aW9uIHQoZSl7dmFyIHIsbixvLGksYSxjLHUscyxmLGw7cmV0dXJuIEV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnJldHVybiByPUZ0KGUpLG89dGhpcy5fc3RyYXRlZ3ksaT1vLmNhY2hlTmFtZSxhPW8ubWF0Y2hPcHRpb25zLHQubmV4dD00LHRoaXMuZ2V0Q2FjaGVLZXkociwicmVhZCIpO2Nhc2UgNDpyZXR1cm4gYz10LnNlbnQsdT1PYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sYSkse2NhY2hlTmFtZTppfSksdC5uZXh0PTgsY2FjaGVzLm1hdGNoKGMsdSk7Y2FzZSA4Om49dC5zZW50LHM9U3QodGhpcy5pdGVyYXRlQ2FsbGJhY2tzKCJjYWNoZWRSZXNwb25zZVdpbGxCZVVzZWQiKSksdC5wcmV2PTExLHMucygpO2Nhc2UgMTM6aWYoKGY9cy5uKCkpLmRvbmUpe3QubmV4dD0yMzticmVha31yZXR1cm4gbD1mLnZhbHVlLHQubmV4dD0xNyxsKHtjYWNoZU5hbWU6aSxtYXRjaE9wdGlvbnM6YSxjYWNoZWRSZXNwb25zZTpuLHJlcXVlc3Q6YyxldmVudDp0aGlzLmV2ZW50fSk7Y2FzZSAxNzppZih0LnQwPXQuc2VudCx0LnQwKXt0Lm5leHQ9MjA7YnJlYWt9dC50MD12b2lkIDA7Y2FzZSAyMDpuPXQudDA7Y2FzZSAyMTp0Lm5leHQ9MTM7YnJlYWs7Y2FzZSAyMzp0Lm5leHQ9Mjg7YnJlYWs7Y2FzZSAyNTp0LnByZXY9MjUsdC50MT10WyJjYXRjaCJdKDExKSxzLmUodC50MSk7Y2FzZSAyODpyZXR1cm4gdC5wcmV2PTI4LHMuZigpLHQuZmluaXNoKDI4KTtjYXNlIDMxOnJldHVybiB0LmFicnVwdCgicmV0dXJuIixuKTtjYXNlIDMyOmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCx0aGlzLFtbMTEsMjUsMjgsMzFdXSl9KSkpO2Z1bmN0aW9uIGUoZSl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiBlfSgpfSx7a2V5OiJjYWNoZVB1dCIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgZT1PdChFdCgpLm1hcmsoKGZ1bmN0aW9uIGUocixuKXt2YXIgbyxpLGEsYyx1LHMsZixsLGgscCx5LHYsZDtyZXR1cm4gRXQoKS53cmFwKChmdW5jdGlvbihlKXt3aGlsZSgxKXN3aXRjaChlLnByZXY9ZS5uZXh0KXtjYXNlIDA6cmV0dXJuIG89RnQociksZS5uZXh0PTMsSigwKTtjYXNlIDM6cmV0dXJuIGUubmV4dD01LHRoaXMuZ2V0Q2FjaGVLZXkobywid3JpdGUiKTtjYXNlIDU6aT1lLnNlbnQsZS5uZXh0PTExO2JyZWFrO2Nhc2UgOTphPW4uaGVhZGVycy5nZXQoIlZhcnkiKSxhJiZ0LmRlYnVnKCJUaGUgcmVzcG9uc2UgZm9yICIuY29uY2F0KHooaS51cmwpLCIgIikrImhhcyBhICdWYXJ5OiAiLmNvbmNhdChhLCInIGhlYWRlci4gIikrIkNvbnNpZGVyIHNldHRpbmcgdGhlIHtpZ25vcmVWYXJ5OiB0cnVlfSBvcHRpb24gb24geW91ciBzdHJhdGVneSB0byBlbnN1cmUgY2FjaGUgbWF0Y2hpbmcgYW5kIGRlbGV0aW9uIHdvcmtzIGFzIGV4cGVjdGVkLiIpO2Nhc2UgMTE6aWYobil7ZS5uZXh0PTE0O2JyZWFrfXRocm93IG5ldyBnKCJjYWNoZS1wdXQtd2l0aC1uby1yZXNwb25zZSIse3VybDp6KGkudXJsKX0pO2Nhc2UgMTQ6cmV0dXJuIGUubmV4dD0xNix0aGlzLl9lbnN1cmVSZXNwb25zZVNhZmVUb0NhY2hlKG4pO2Nhc2UgMTY6aWYoYz1lLnNlbnQsYyl7ZS5uZXh0PTIwO2JyZWFrfXJldHVybiBlLmFicnVwdCgicmV0dXJuIiwhMSk7Y2FzZSAyMDpyZXR1cm4gdT10aGlzLl9zdHJhdGVneSxzPXUuY2FjaGVOYW1lLGY9dS5tYXRjaE9wdGlvbnMsZS5uZXh0PTIzLHNlbGYuY2FjaGVzLm9wZW4ocyk7Y2FzZSAyMzppZihsPWUuc2VudCxoPXRoaXMuaGFzQ2FsbGJhY2soImNhY2hlRGlkVXBkYXRlIiksIWgpe2UubmV4dD0zMTticmVha31yZXR1cm4gZS5uZXh0PTI4LEEobCxpLmNsb25lKCksWyJfX1dCX1JFVklTSU9OX18iXSxmKTtjYXNlIDI4OmUudDA9ZS5zZW50LGUubmV4dD0zMjticmVhaztjYXNlIDMxOmUudDA9bnVsbDtjYXNlIDMyOnJldHVybiBwPWUudDAsZS5wcmV2PTM0LGUubmV4dD0zNyxsLnB1dChpLGg/Yy5jbG9uZSgpOmMpO2Nhc2UgMzc6ZS5uZXh0PTQ2O2JyZWFrO2Nhc2UgMzk6aWYoZS5wcmV2PTM5LGUudDE9ZVsiY2F0Y2giXSgzNCksIShlLnQxIGluc3RhbmNlb2YgRXJyb3IpKXtlLm5leHQ9NDY7YnJlYWt9aWYoIlF1b3RhRXhjZWVkZWRFcnJvciIhPT1lLnQxLm5hbWUpe2UubmV4dD00NTticmVha31yZXR1cm4gZS5uZXh0PTQ1LCQoKTtjYXNlIDQ1OnRocm93IGUudDE7Y2FzZSA0Njp5PVN0KHRoaXMuaXRlcmF0ZUNhbGxiYWNrcygiY2FjaGVEaWRVcGRhdGUiKSksZS5wcmV2PTQ3LHkucygpO2Nhc2UgNDk6aWYoKHY9eS5uKCkpLmRvbmUpe2UubmV4dD01NTticmVha31yZXR1cm4gZD12LnZhbHVlLGUubmV4dD01MyxkKHtjYWNoZU5hbWU6cyxvbGRSZXNwb25zZTpwLG5ld1Jlc3BvbnNlOmMuY2xvbmUoKSxyZXF1ZXN0OmksZXZlbnQ6dGhpcy5ldmVudH0pO2Nhc2UgNTM6ZS5uZXh0PTQ5O2JyZWFrO2Nhc2UgNTU6ZS5uZXh0PTYwO2JyZWFrO2Nhc2UgNTc6ZS5wcmV2PTU3LGUudDI9ZVsiY2F0Y2giXSg0NykseS5lKGUudDIpO2Nhc2UgNjA6cmV0dXJuIGUucHJldj02MCx5LmYoKSxlLmZpbmlzaCg2MCk7Y2FzZSA2MzpyZXR1cm4gZS5hYnJ1cHQoInJldHVybiIsITApO2Nhc2UgNjQ6Y2FzZSJlbmQiOnJldHVybiBlLnN0b3AoKX19KSxlLHRoaXMsW1szNCwzOV0sWzQ3LDU3LDYwLDYzXV0pfSkpKTtmdW5jdGlvbiByKHQscil7cmV0dXJuIGUuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiByfSgpfSx7a2V5OiJnZXRDYWNoZUtleSIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgdD1PdChFdCgpLm1hcmsoKGZ1bmN0aW9uIHQoZSxyKXt2YXIgbixvLGksYSxjO3JldHVybiBFdCgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDppZihuPSIiLmNvbmNhdChlLnVybCwiIHwgIikuY29uY2F0KHIpLHRoaXMuX2NhY2hlS2V5c1tuXSl7dC5uZXh0PTI0O2JyZWFrfW89ZSxpPVN0KHRoaXMuaXRlcmF0ZUNhbGxiYWNrcygiY2FjaGVLZXlXaWxsQmVVc2VkIikpLHQucHJldj00LGkucygpO2Nhc2UgNjppZigoYT1pLm4oKSkuZG9uZSl7dC5uZXh0PTE1O2JyZWFrfXJldHVybiBjPWEudmFsdWUsdC50MD1GdCx0Lm5leHQ9MTEsYyh7bW9kZTpyLHJlcXVlc3Q6byxldmVudDp0aGlzLmV2ZW50LHBhcmFtczp0aGlzLnBhcmFtc30pO2Nhc2UgMTE6dC50MT10LnNlbnQsbz0oMCx0LnQwKSh0LnQxKTtjYXNlIDEzOnQubmV4dD02O2JyZWFrO2Nhc2UgMTU6dC5uZXh0PTIwO2JyZWFrO2Nhc2UgMTc6dC5wcmV2PTE3LHQudDI9dFsiY2F0Y2giXSg0KSxpLmUodC50Mik7Y2FzZSAyMDpyZXR1cm4gdC5wcmV2PTIwLGkuZigpLHQuZmluaXNoKDIwKTtjYXNlIDIzOnRoaXMuX2NhY2hlS2V5c1tuXT1vO2Nhc2UgMjQ6cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iLHRoaXMuX2NhY2hlS2V5c1tuXSk7Y2FzZSAyNTpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsdGhpcyxbWzQsMTcsMjAsMjNdXSl9KSkpO2Z1bmN0aW9uIGUoZSxyKXtyZXR1cm4gdC5hcHBseSh0aGlzLGFyZ3VtZW50cyl9cmV0dXJuIGV9KCl9LHtrZXk6Imhhc0NhbGxiYWNrIix2YWx1ZTpmdW5jdGlvbih0KXt2YXIgZSxyPVN0KHRoaXMuX3N0cmF0ZWd5LnBsdWdpbnMpO3RyeXtmb3Ioci5zKCk7IShlPXIubigpKS5kb25lOyl7dmFyIG49ZS52YWx1ZTtpZih0IGluIG4pcmV0dXJuITB9fWNhdGNoKG8pe3IuZShvKX1maW5hbGx5e3IuZigpfXJldHVybiExfX0se2tleToicnVuQ2FsbGJhY2tzIix2YWx1ZTpmdW5jdGlvbigpe3ZhciB0PU90KEV0KCkubWFyaygoZnVuY3Rpb24gdChlLHIpe3ZhciBuLG8saTtyZXR1cm4gRXQoKS53cmFwKChmdW5jdGlvbih0KXt3aGlsZSgxKXN3aXRjaCh0LnByZXY9dC5uZXh0KXtjYXNlIDA6bj1TdCh0aGlzLml0ZXJhdGVDYWxsYmFja3MoZSkpLHQucHJldj0xLG4ucygpO2Nhc2UgMzppZigobz1uLm4oKSkuZG9uZSl7dC5uZXh0PTk7YnJlYWt9cmV0dXJuIGk9by52YWx1ZSx0Lm5leHQ9NyxpKHIpO2Nhc2UgNzp0Lm5leHQ9MzticmVhaztjYXNlIDk6dC5uZXh0PTE0O2JyZWFrO2Nhc2UgMTE6dC5wcmV2PTExLHQudDA9dFsiY2F0Y2giXSgxKSxuLmUodC50MCk7Y2FzZSAxNDpyZXR1cm4gdC5wcmV2PTE0LG4uZigpLHQuZmluaXNoKDE0KTtjYXNlIDE3OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCx0aGlzLFtbMSwxMSwxNCwxN11dKX0pKSk7ZnVuY3Rpb24gZShlLHIpe3JldHVybiB0LmFwcGx5KHRoaXMsYXJndW1lbnRzKX1yZXR1cm4gZX0oKX0se2tleToiaXRlcmF0ZUNhbGxiYWNrcyIsdmFsdWU6RXQoKS5tYXJrKChmdW5jdGlvbiB0KGUpe3ZhciByLG4sbyxpPXRoaXM7cmV0dXJuIEV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnI9U3QodGhpcy5fc3RyYXRlZ3kucGx1Z2lucyksdC5wcmV2PTEsbz1FdCgpLm1hcmsoKGZ1bmN0aW9uIHQoKXt2YXIgcixvLGE7cmV0dXJuIEV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOmlmKHI9bi52YWx1ZSwiZnVuY3Rpb24iIT09dHlwZW9mIHJbZV0pe3QubmV4dD02O2JyZWFrfXJldHVybiBvPWkuX3BsdWdpblN0YXRlTWFwLmdldChyKSxhPWZ1bmN0aW9uKHQpe3ZhciBuPU9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSx0KSx7c3RhdGU6b30pO3JldHVybiByW2VdKG4pfSx0Lm5leHQ9NixhO2Nhc2UgNjpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQpfSkpLHIucygpO2Nhc2UgNDppZigobj1yLm4oKSkuZG9uZSl7dC5uZXh0PTg7YnJlYWt9cmV0dXJuIHQuZGVsZWdhdGVZaWVsZChvKCksInQwIiw2KTtjYXNlIDY6dC5uZXh0PTQ7YnJlYWs7Y2FzZSA4OnQubmV4dD0xMzticmVhaztjYXNlIDEwOnQucHJldj0xMCx0LnQxPXRbImNhdGNoIl0oMSksci5lKHQudDEpO2Nhc2UgMTM6cmV0dXJuIHQucHJldj0xMyxyLmYoKSx0LmZpbmlzaCgxMyk7Y2FzZSAxNjpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsdGhpcyxbWzEsMTAsMTMsMTZdXSl9KSl9LHtrZXk6IndhaXRVbnRpbCIsdmFsdWU6ZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2V4dGVuZExpZmV0aW1lUHJvbWlzZXMucHVzaCh0KSx0fX0se2tleToiZG9uZVdhaXRpbmciLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIHQ9T3QoRXQoKS5tYXJrKChmdW5jdGlvbiB0KCl7dmFyIGU7cmV0dXJuIEV0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOmlmKCEoZT10aGlzLl9leHRlbmRMaWZldGltZVByb21pc2VzLnNoaWZ0KCkpKXt0Lm5leHQ9NTticmVha31yZXR1cm4gdC5uZXh0PTMsZTtjYXNlIDM6dC5uZXh0PTA7YnJlYWs7Y2FzZSA1OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCx0aGlzKX0pKSk7ZnVuY3Rpb24gZSgpe3JldHVybiB0LmFwcGx5KHRoaXMsYXJndW1lbnRzKX1yZXR1cm4gZX0oKX0se2tleToiZGVzdHJveSIsdmFsdWU6ZnVuY3Rpb24oKXt0aGlzLl9oYW5kbGVyRGVmZXJyZWQucmVzb2x2ZShudWxsKX19LHtrZXk6Il9lbnN1cmVSZXNwb25zZVNhZmVUb0NhY2hlIix2YWx1ZTpmdW5jdGlvbigpe3ZhciB0PU90KEV0KCkubWFyaygoZnVuY3Rpb24gdChlKXt2YXIgcixuLG8saSxhO3JldHVybiBFdCgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDpyPWUsbj0hMSxvPVN0KHRoaXMuaXRlcmF0ZUNhbGxiYWNrcygiY2FjaGVXaWxsVXBkYXRlIikpLHQucHJldj0zLG8ucygpO2Nhc2UgNTppZigoaT1vLm4oKSkuZG9uZSl7dC5uZXh0PTE4O2JyZWFrfXJldHVybiBhPWkudmFsdWUsdC5uZXh0PTksYSh7cmVxdWVzdDp0aGlzLnJlcXVlc3QscmVzcG9uc2U6cixldmVudDp0aGlzLmV2ZW50fSk7Y2FzZSA5OmlmKHQudDA9dC5zZW50LHQudDApe3QubmV4dD0xMjticmVha310LnQwPXZvaWQgMDtjYXNlIDEyOmlmKHI9dC50MCxuPSEwLHIpe3QubmV4dD0xNjticmVha31yZXR1cm4gdC5hYnJ1cHQoImJyZWFrIiwxOCk7Y2FzZSAxNjp0Lm5leHQ9NTticmVhaztjYXNlIDE4OnQubmV4dD0yMzticmVhaztjYXNlIDIwOnQucHJldj0yMCx0LnQxPXRbImNhdGNoIl0oMyksby5lKHQudDEpO2Nhc2UgMjM6cmV0dXJuIHQucHJldj0yMyxvLmYoKSx0LmZpbmlzaCgyMyk7Y2FzZSAyNjpyZXR1cm4gbnx8ciYmMjAwIT09ci5zdGF0dXMmJihyPXZvaWQgMCksdC5hYnJ1cHQoInJldHVybiIscik7Y2FzZSAyODpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsdGhpcyxbWzMsMjAsMjMsMjZdXSl9KSkpO2Z1bmN0aW9uIGUoZSl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiBlfSgpfV0pLGV9KCk7ZnVuY3Rpb24gcXQodCl7cmV0dXJuIHF0PSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSxxdCh0KX1mdW5jdGlvbiBLdCgpey8qISByZWdlbmVyYXRvci1ydW50aW1lIC0tIENvcHlyaWdodCAoYykgMjAxNC1wcmVzZW50LCBGYWNlYm9vaywgSW5jLiAtLSBsaWNlbnNlIChNSVQpOiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVnZW5lcmF0b3IvYmxvYi9tYWluL0xJQ0VOU0UgKi9LdD1mdW5jdGlvbigpe3JldHVybiB0fTt2YXIgdD17fSxlPU9iamVjdC5wcm90b3R5cGUscj1lLmhhc093blByb3BlcnR5LG49ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbD9TeW1ib2w6e30sbz1uLml0ZXJhdG9yfHwiQEBpdGVyYXRvciIsaT1uLmFzeW5jSXRlcmF0b3J8fCJAQGFzeW5jSXRlcmF0b3IiLGE9bi50b1N0cmluZ1RhZ3x8IkBAdG9TdHJpbmdUYWciO2Z1bmN0aW9uIGModCxlLHIpe3JldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkodCxlLHt2YWx1ZTpyLGVudW1lcmFibGU6ITAsY29uZmlndXJhYmxlOiEwLHdyaXRhYmxlOiEwfSksdFtlXX10cnl7Yyh7fSwiIil9Y2F0Y2goTyl7Yz1mdW5jdGlvbih0LGUscil7cmV0dXJuIHRbZV09cn19ZnVuY3Rpb24gdSh0LGUscixuKXt2YXIgbz1lJiZlLnByb3RvdHlwZSBpbnN0YW5jZW9mIGw/ZTpsLGk9T2JqZWN0LmNyZWF0ZShvLnByb3RvdHlwZSksYT1uZXcgayhufHxbXSk7cmV0dXJuIGkuX2ludm9rZT1mdW5jdGlvbih0LGUscil7dmFyIG49InN1c3BlbmRlZFN0YXJ0IjtyZXR1cm4gZnVuY3Rpb24obyxpKXtpZigiZXhlY3V0aW5nIj09PW4pdGhyb3cgbmV3IEVycm9yKCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBydW5uaW5nIik7aWYoImNvbXBsZXRlZCI9PT1uKXtpZigidGhyb3ciPT09byl0aHJvdyBpO3JldHVybiBfKCl9Zm9yKHIubWV0aG9kPW8sci5hcmc9aTs7KXt2YXIgYT1yLmRlbGVnYXRlO2lmKGEpe3ZhciBjPWIoYSxyKTtpZihjKXtpZihjPT09Ziljb250aW51ZTtyZXR1cm4gY319aWYoIm5leHQiPT09ci5tZXRob2Qpci5zZW50PXIuX3NlbnQ9ci5hcmc7ZWxzZSBpZigidGhyb3ciPT09ci5tZXRob2Qpe2lmKCJzdXNwZW5kZWRTdGFydCI9PT1uKXRocm93IG49ImNvbXBsZXRlZCIsci5hcmc7ci5kaXNwYXRjaEV4Y2VwdGlvbihyLmFyZyl9ZWxzZSJyZXR1cm4iPT09ci5tZXRob2QmJnIuYWJydXB0KCJyZXR1cm4iLHIuYXJnKTtuPSJleGVjdXRpbmciO3ZhciB1PXModCxlLHIpO2lmKCJub3JtYWwiPT09dS50eXBlKXtpZihuPXIuZG9uZT8iY29tcGxldGVkIjoic3VzcGVuZGVkWWllbGQiLHUuYXJnPT09Ziljb250aW51ZTtyZXR1cm57dmFsdWU6dS5hcmcsZG9uZTpyLmRvbmV9fSJ0aHJvdyI9PT11LnR5cGUmJihuPSJjb21wbGV0ZWQiLHIubWV0aG9kPSJ0aHJvdyIsci5hcmc9dS5hcmcpfX19KHQscixhKSxpfWZ1bmN0aW9uIHModCxlLHIpe3RyeXtyZXR1cm57dHlwZToibm9ybWFsIixhcmc6dC5jYWxsKGUscil9fWNhdGNoKE8pe3JldHVybnt0eXBlOiJ0aHJvdyIsYXJnOk99fX10LndyYXA9dTt2YXIgZj17fTtmdW5jdGlvbiBsKCl7fWZ1bmN0aW9uIGgoKXt9ZnVuY3Rpb24gcCgpe312YXIgeT17fTtjKHksbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKTt2YXIgdj1PYmplY3QuZ2V0UHJvdG90eXBlT2YsZD12JiZ2KHYoRShbXSkpKTtkJiZkIT09ZSYmci5jYWxsKGQsbykmJih5PWQpO3ZhciBtPXAucHJvdG90eXBlPWwucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoeSk7ZnVuY3Rpb24gZyh0KXtbIm5leHQiLCJ0aHJvdyIsInJldHVybiJdLmZvckVhY2goKGZ1bmN0aW9uKGUpe2ModCxlLChmdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5faW52b2tlKGUsdCl9KSl9KSl9ZnVuY3Rpb24gdyh0LGUpe2Z1bmN0aW9uIG4obyxpLGEsYyl7dmFyIHU9cyh0W29dLHQsaSk7aWYoInRocm93IiE9PXUudHlwZSl7dmFyIGY9dS5hcmcsbD1mLnZhbHVlO3JldHVybiBsJiYib2JqZWN0Ij09cXQobCkmJnIuY2FsbChsLCJfX2F3YWl0Iik/ZS5yZXNvbHZlKGwuX19hd2FpdCkudGhlbigoZnVuY3Rpb24odCl7bigibmV4dCIsdCxhLGMpfSksKGZ1bmN0aW9uKHQpe24oInRocm93Iix0LGEsYyl9KSk6ZS5yZXNvbHZlKGwpLnRoZW4oKGZ1bmN0aW9uKHQpe2YudmFsdWU9dCxhKGYpfSksKGZ1bmN0aW9uKHQpe3JldHVybiBuKCJ0aHJvdyIsdCxhLGMpfSkpfWModS5hcmcpfXZhciBvO3RoaXMuX2ludm9rZT1mdW5jdGlvbih0LHIpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gbmV3IGUoKGZ1bmN0aW9uKGUsbyl7bih0LHIsZSxvKX0pKX1yZXR1cm4gbz1vP28udGhlbihpLGkpOmkoKX19ZnVuY3Rpb24gYih0LGUpe3ZhciByPXQuaXRlcmF0b3JbZS5tZXRob2RdO2lmKHZvaWQgMD09PXIpe2lmKGUuZGVsZWdhdGU9bnVsbCwidGhyb3ciPT09ZS5tZXRob2Qpe2lmKHQuaXRlcmF0b3IucmV0dXJuJiYoZS5tZXRob2Q9InJldHVybiIsZS5hcmc9dm9pZCAwLGIodCxlKSwidGhyb3ciPT09ZS5tZXRob2QpKXJldHVybiBmO2UubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAndGhyb3cnIG1ldGhvZCIpfXJldHVybiBmfXZhciBuPXMocix0Lml0ZXJhdG9yLGUuYXJnKTtpZigidGhyb3ciPT09bi50eXBlKXJldHVybiBlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW4uYXJnLGUuZGVsZWdhdGU9bnVsbCxmO3ZhciBvPW4uYXJnO3JldHVybiBvP28uZG9uZT8oZVt0LnJlc3VsdE5hbWVdPW8udmFsdWUsZS5uZXh0PXQubmV4dExvYywicmV0dXJuIiE9PWUubWV0aG9kJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksZS5kZWxlZ2F0ZT1udWxsLGYpOm86KGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiaXRlcmF0b3IgcmVzdWx0IGlzIG5vdCBhbiBvYmplY3QiKSxlLmRlbGVnYXRlPW51bGwsZil9ZnVuY3Rpb24geCh0KXt2YXIgZT17dHJ5TG9jOnRbMF19OzEgaW4gdCYmKGUuY2F0Y2hMb2M9dFsxXSksMiBpbiB0JiYoZS5maW5hbGx5TG9jPXRbMl0sZS5hZnRlckxvYz10WzNdKSx0aGlzLnRyeUVudHJpZXMucHVzaChlKX1mdW5jdGlvbiBMKHQpe3ZhciBlPXQuY29tcGxldGlvbnx8e307ZS50eXBlPSJub3JtYWwiLGRlbGV0ZSBlLmFyZyx0LmNvbXBsZXRpb249ZX1mdW5jdGlvbiBrKHQpe3RoaXMudHJ5RW50cmllcz1be3RyeUxvYzoicm9vdCJ9XSx0LmZvckVhY2goeCx0aGlzKSx0aGlzLnJlc2V0KCEwKX1mdW5jdGlvbiBFKHQpe2lmKHQpe3ZhciBlPXRbb107aWYoZSlyZXR1cm4gZS5jYWxsKHQpO2lmKCJmdW5jdGlvbiI9PXR5cGVvZiB0Lm5leHQpcmV0dXJuIHQ7aWYoIWlzTmFOKHQubGVuZ3RoKSl7dmFyIG49LTEsaT1mdW5jdGlvbiBlKCl7Zm9yKDsrK248dC5sZW5ndGg7KWlmKHIuY2FsbCh0LG4pKXJldHVybiBlLnZhbHVlPXRbbl0sZS5kb25lPSExLGU7cmV0dXJuIGUudmFsdWU9dm9pZCAwLGUuZG9uZT0hMCxlfTtyZXR1cm4gaS5uZXh0PWl9fXJldHVybntuZXh0Ol99fWZ1bmN0aW9uIF8oKXtyZXR1cm57dmFsdWU6dm9pZCAwLGRvbmU6ITB9fXJldHVybiBoLnByb3RvdHlwZT1wLGMobSwiY29uc3RydWN0b3IiLHApLGMocCwiY29uc3RydWN0b3IiLGgpLGguZGlzcGxheU5hbWU9YyhwLGEsIkdlbmVyYXRvckZ1bmN0aW9uIiksdC5pc0dlbmVyYXRvckZ1bmN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBlPSJmdW5jdGlvbiI9PXR5cGVvZiB0JiZ0LmNvbnN0cnVjdG9yO3JldHVybiEhZSYmKGU9PT1ofHwiR2VuZXJhdG9yRnVuY3Rpb24iPT09KGUuZGlzcGxheU5hbWV8fGUubmFtZSkpfSx0Lm1hcms9ZnVuY3Rpb24odCl7cmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YodCxwKToodC5fX3Byb3RvX189cCxjKHQsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSksdC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShtKSx0fSx0LmF3cmFwPWZ1bmN0aW9uKHQpe3JldHVybntfX2F3YWl0OnR9fSxnKHcucHJvdG90eXBlKSxjKHcucHJvdG90eXBlLGksKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksdC5Bc3luY0l0ZXJhdG9yPXcsdC5hc3luYz1mdW5jdGlvbihlLHIsbixvLGkpe3ZvaWQgMD09PWkmJihpPVByb21pc2UpO3ZhciBhPW5ldyB3KHUoZSxyLG4sbyksaSk7cmV0dXJuIHQuaXNHZW5lcmF0b3JGdW5jdGlvbihyKT9hOmEubmV4dCgpLnRoZW4oKGZ1bmN0aW9uKHQpe3JldHVybiB0LmRvbmU/dC52YWx1ZTphLm5leHQoKX0pKX0sZyhtKSxjKG0sYSwiR2VuZXJhdG9yIiksYyhtLG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksYyhtLCJ0b1N0cmluZyIsKGZ1bmN0aW9uKCl7cmV0dXJuIltvYmplY3QgR2VuZXJhdG9yXSJ9KSksdC5rZXlzPWZ1bmN0aW9uKHQpe3ZhciBlPVtdO2Zvcih2YXIgciBpbiB0KWUucHVzaChyKTtyZXR1cm4gZS5yZXZlcnNlKCksZnVuY3Rpb24gcigpe2Zvcig7ZS5sZW5ndGg7KXt2YXIgbj1lLnBvcCgpO2lmKG4gaW4gdClyZXR1cm4gci52YWx1ZT1uLHIuZG9uZT0hMSxyfXJldHVybiByLmRvbmU9ITAscn19LHQudmFsdWVzPUUsay5wcm90b3R5cGU9e2NvbnN0cnVjdG9yOmsscmVzZXQ6ZnVuY3Rpb24odCl7aWYodGhpcy5wcmV2PTAsdGhpcy5uZXh0PTAsdGhpcy5zZW50PXRoaXMuX3NlbnQ9dm9pZCAwLHRoaXMuZG9uZT0hMSx0aGlzLmRlbGVnYXRlPW51bGwsdGhpcy5tZXRob2Q9Im5leHQiLHRoaXMuYXJnPXZvaWQgMCx0aGlzLnRyeUVudHJpZXMuZm9yRWFjaChMKSwhdClmb3IodmFyIGUgaW4gdGhpcykidCI9PT1lLmNoYXJBdCgwKSYmci5jYWxsKHRoaXMsZSkmJiFpc05hTigrZS5zbGljZSgxKSkmJih0aGlzW2VdPXZvaWQgMCl9LHN0b3A6ZnVuY3Rpb24oKXt0aGlzLmRvbmU9ITA7dmFyIHQ9dGhpcy50cnlFbnRyaWVzWzBdLmNvbXBsZXRpb247aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4gdGhpcy5ydmFsfSxkaXNwYXRjaEV4Y2VwdGlvbjpmdW5jdGlvbih0KXtpZih0aGlzLmRvbmUpdGhyb3cgdDt2YXIgZT10aGlzO2Z1bmN0aW9uIG4ocixuKXtyZXR1cm4gYS50eXBlPSJ0aHJvdyIsYS5hcmc9dCxlLm5leHQ9cixuJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksISFufWZvcih2YXIgbz10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bz49MDstLW8pe3ZhciBpPXRoaXMudHJ5RW50cmllc1tvXSxhPWkuY29tcGxldGlvbjtpZigicm9vdCI9PT1pLnRyeUxvYylyZXR1cm4gbigiZW5kIik7aWYoaS50cnlMb2M8PXRoaXMucHJldil7dmFyIGM9ci5jYWxsKGksImNhdGNoTG9jIiksdT1yLmNhbGwoaSwiZmluYWxseUxvYyIpO2lmKGMmJnUpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX1lbHNlIGlmKGMpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApfWVsc2V7aWYoIXUpdGhyb3cgbmV3IEVycm9yKCJ0cnkgc3RhdGVtZW50IHdpdGhvdXQgY2F0Y2ggb3IgZmluYWxseSIpO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX19fX0sYWJydXB0OmZ1bmN0aW9uKHQsZSl7Zm9yKHZhciBuPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtuPj0wOy0tbil7dmFyIG89dGhpcy50cnlFbnRyaWVzW25dO2lmKG8udHJ5TG9jPD10aGlzLnByZXYmJnIuY2FsbChvLCJmaW5hbGx5TG9jIikmJnRoaXMucHJldjxvLmZpbmFsbHlMb2Mpe3ZhciBpPW87YnJlYWt9fWkmJigiYnJlYWsiPT09dHx8ImNvbnRpbnVlIj09PXQpJiZpLnRyeUxvYzw9ZSYmZTw9aS5maW5hbGx5TG9jJiYoaT1udWxsKTt2YXIgYT1pP2kuY29tcGxldGlvbjp7fTtyZXR1cm4gYS50eXBlPXQsYS5hcmc9ZSxpPyh0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5uZXh0PWkuZmluYWxseUxvYyxmKTp0aGlzLmNvbXBsZXRlKGEpfSxjb21wbGV0ZTpmdW5jdGlvbih0LGUpe2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuImJyZWFrIj09PXQudHlwZXx8ImNvbnRpbnVlIj09PXQudHlwZT90aGlzLm5leHQ9dC5hcmc6InJldHVybiI9PT10LnR5cGU/KHRoaXMucnZhbD10aGlzLmFyZz10LmFyZyx0aGlzLm1ldGhvZD0icmV0dXJuIix0aGlzLm5leHQ9ImVuZCIpOiJub3JtYWwiPT09dC50eXBlJiZlJiYodGhpcy5uZXh0PWUpLGZ9LGZpbmlzaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci5maW5hbGx5TG9jPT09dClyZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sci5hZnRlckxvYyksTChyKSxmfX0sY2F0Y2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIudHJ5TG9jPT09dCl7dmFyIG49ci5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT1uLnR5cGUpe3ZhciBvPW4uYXJnO0wocil9cmV0dXJuIG99fXRocm93IG5ldyBFcnJvcigiaWxsZWdhbCBjYXRjaCBhdHRlbXB0Iil9LGRlbGVnYXRlWWllbGQ6ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0aGlzLmRlbGVnYXRlPXtpdGVyYXRvcjpFKHQpLHJlc3VsdE5hbWU6ZSxuZXh0TG9jOnJ9LCJuZXh0Ij09PXRoaXMubWV0aG9kJiYodGhpcy5hcmc9dm9pZCAwKSxmfX0sdH1mdW5jdGlvbiBNdCh0LGUpe3ZhciByPSJ1bmRlZmluZWQiIT09dHlwZW9mIFN5bWJvbCYmdFtTeW1ib2wuaXRlcmF0b3JdfHx0WyJAQGl0ZXJhdG9yIl07aWYoIXIpe2lmKEFycmF5LmlzQXJyYXkodCl8fChyPUh0KHQpKXx8ZSYmdCYmIm51bWJlciI9PT10eXBlb2YgdC5sZW5ndGgpe3ImJih0PXIpO3ZhciBuPTAsbz1mdW5jdGlvbigpe307cmV0dXJue3M6byxuOmZ1bmN0aW9uKCl7cmV0dXJuIG4+PXQubGVuZ3RoP3tkb25lOiEwfTp7ZG9uZTohMSx2YWx1ZTp0W24rK119fSxlOmZ1bmN0aW9uKHQpe3Rocm93IHR9LGY6b319dGhyb3cgbmV3IFR5cGVFcnJvcigiSW52YWxpZCBhdHRlbXB0IHRvIGl0ZXJhdGUgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfXZhciBpLGE9ITAsYz0hMTtyZXR1cm57czpmdW5jdGlvbigpe3I9ci5jYWxsKHQpfSxuOmZ1bmN0aW9uKCl7dmFyIHQ9ci5uZXh0KCk7cmV0dXJuIGE9dC5kb25lLHR9LGU6ZnVuY3Rpb24odCl7Yz0hMCxpPXR9LGY6ZnVuY3Rpb24oKXt0cnl7YXx8bnVsbD09ci5yZXR1cm58fHIucmV0dXJuKCl9ZmluYWxseXtpZihjKXRocm93IGl9fX19ZnVuY3Rpb24gWXQodCxlLHIsbixvLGksYSl7dHJ5e3ZhciBjPXRbaV0oYSksdT1jLnZhbHVlfWNhdGNoKHMpe3JldHVybiB2b2lkIHIocyl9Yy5kb25lP2UodSk6UHJvbWlzZS5yZXNvbHZlKHUpLnRoZW4obixvKX1mdW5jdGlvbiBXdCh0KXtyZXR1cm4gZnVuY3Rpb24oKXt2YXIgZT10aGlzLHI9YXJndW1lbnRzO3JldHVybiBuZXcgUHJvbWlzZSgoZnVuY3Rpb24obixvKXt2YXIgaT10LmFwcGx5KGUscik7ZnVuY3Rpb24gYSh0KXtZdChpLG4sbyxhLGMsIm5leHQiLHQpfWZ1bmN0aW9uIGModCl7WXQoaSxuLG8sYSxjLCJ0aHJvdyIsdCl9YSh2b2lkIDApfSkpfX1mdW5jdGlvbiBEdCh0LGUpe3JldHVybiB6dCh0KXx8VnQodCxlKXx8SHQodCxlKXx8QnQoKX1mdW5jdGlvbiBCdCgpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBkZXN0cnVjdHVyZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9ZnVuY3Rpb24gSHQodCxlKXtpZih0KXtpZigic3RyaW5nIj09PXR5cGVvZiB0KXJldHVybiAkdCh0LGUpO3ZhciByPU9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbCh0KS5zbGljZSg4LC0xKTtyZXR1cm4iT2JqZWN0Ij09PXImJnQuY29uc3RydWN0b3ImJihyPXQuY29uc3RydWN0b3IubmFtZSksIk1hcCI9PT1yfHwiU2V0Ij09PXI/QXJyYXkuZnJvbSh0KToiQXJndW1lbnRzIj09PXJ8fC9eKD86VWl8SSludCg/Ojh8MTZ8MzIpKD86Q2xhbXBlZCk/QXJyYXkkLy50ZXN0KHIpPyR0KHQsZSk6dm9pZCAwfX1mdW5jdGlvbiAkdCh0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIFZ0KHQsZSl7dmFyIHI9bnVsbD09dD9udWxsOiJ1bmRlZmluZWQiIT09dHlwZW9mIFN5bWJvbCYmdFtTeW1ib2wuaXRlcmF0b3JdfHx0WyJAQGl0ZXJhdG9yIl07aWYobnVsbCE9cil7dmFyIG4sbyxpPVtdLGE9ITAsYz0hMTt0cnl7Zm9yKHI9ci5jYWxsKHQpOyEoYT0obj1yLm5leHQoKSkuZG9uZSk7YT0hMClpZihpLnB1c2gobi52YWx1ZSksZSYmaS5sZW5ndGg9PT1lKWJyZWFrfWNhdGNoKHUpe2M9ITAsbz11fWZpbmFsbHl7dHJ5e2F8fG51bGw9PXJbInJldHVybiJdfHxyWyJyZXR1cm4iXSgpfWZpbmFsbHl7aWYoYyl0aHJvdyBvfX1yZXR1cm4gaX19ZnVuY3Rpb24genQodCl7aWYoQXJyYXkuaXNBcnJheSh0KSlyZXR1cm4gdH1mdW5jdGlvbiBKdCh0LGUpe2lmKCEodCBpbnN0YW5jZW9mIGUpKXRocm93IG5ldyBUeXBlRXJyb3IoIkNhbm5vdCBjYWxsIGEgY2xhc3MgYXMgYSBmdW5jdGlvbiIpfWZ1bmN0aW9uIFF0KHQsZSl7Zm9yKHZhciByPTA7cjxlLmxlbmd0aDtyKyspe3ZhciBuPWVbcl07bi5lbnVtZXJhYmxlPW4uZW51bWVyYWJsZXx8ITEsbi5jb25maWd1cmFibGU9ITAsInZhbHVlImluIG4mJihuLndyaXRhYmxlPSEwKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCxuLmtleSxuKX19ZnVuY3Rpb24gWHQodCxlLHIpe3JldHVybiBlJiZRdCh0LnByb3RvdHlwZSxlKSxyJiZRdCh0LHIpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LCJwcm90b3R5cGUiLHt3cml0YWJsZTohMX0pLHR9dmFyIFp0PWZ1bmN0aW9uKCl7ZnVuY3Rpb24gdCgpe3ZhciBlPWFyZ3VtZW50cy5sZW5ndGg+MCYmdm9pZCAwIT09YXJndW1lbnRzWzBdP2FyZ3VtZW50c1swXTp7fTtKdCh0aGlzLHQpLHRoaXMuY2FjaGVOYW1lPUUuZ2V0UnVudGltZU5hbWUoZS5jYWNoZU5hbWUpLHRoaXMucGx1Z2lucz1lLnBsdWdpbnN8fFtdLHRoaXMuZmV0Y2hPcHRpb25zPWUuZmV0Y2hPcHRpb25zLHRoaXMubWF0Y2hPcHRpb25zPWUubWF0Y2hPcHRpb25zfXJldHVybiBYdCh0LFt7a2V5OiJoYW5kbGUiLHZhbHVlOmZ1bmN0aW9uKHQpe3ZhciBlPXRoaXMuaGFuZGxlQWxsKHQpLHI9RHQoZSwxKSxuPXJbMF07cmV0dXJuIG59fSx7a2V5OiJoYW5kbGVBbGwiLHZhbHVlOmZ1bmN0aW9uKHQpe3QgaW5zdGFuY2VvZiBGZXRjaEV2ZW50JiYodD17ZXZlbnQ6dCxyZXF1ZXN0OnQucmVxdWVzdH0pO3ZhciBlPXQuZXZlbnQscj0ic3RyaW5nIj09PXR5cGVvZiB0LnJlcXVlc3Q/bmV3IFJlcXVlc3QodC5yZXF1ZXN0KTp0LnJlcXVlc3Qsbj0icGFyYW1zImluIHQ/dC5wYXJhbXM6dm9pZCAwLG89bmV3IFV0KHRoaXMse2V2ZW50OmUscmVxdWVzdDpyLHBhcmFtczpufSksaT10aGlzLl9nZXRSZXNwb25zZShvLHIsZSksYT10aGlzLl9hd2FpdENvbXBsZXRlKGksbyxyLGUpO3JldHVybltpLGFdfX0se2tleToiX2dldFJlc3BvbnNlIix2YWx1ZTpmdW5jdGlvbigpe3ZhciB0PVd0KEt0KCkubWFyaygoZnVuY3Rpb24gdChlLHIsbil7dmFyIG8saSxhLGMsdSxzLGY7cmV0dXJuIEt0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnJldHVybiB0Lm5leHQ9MixlLnJ1bkNhbGxiYWNrcygiaGFuZGxlcldpbGxTdGFydCIse2V2ZW50Om4scmVxdWVzdDpyfSk7Y2FzZSAyOnJldHVybiBvPXZvaWQgMCx0LnByZXY9Myx0Lm5leHQ9Nix0aGlzLl9oYW5kbGUocixlKTtjYXNlIDY6aWYobz10LnNlbnQsbyYmImVycm9yIiE9PW8udHlwZSl7dC5uZXh0PTk7YnJlYWt9dGhyb3cgbmV3IGcoIm5vLXJlc3BvbnNlIix7dXJsOnIudXJsfSk7Y2FzZSA5OnQubmV4dD0zOTticmVhaztjYXNlIDExOmlmKHQucHJldj0xMSx0LnQwPXRbImNhdGNoIl0oMyksISh0LnQwIGluc3RhbmNlb2YgRXJyb3IpKXt0Lm5leHQ9MzQ7YnJlYWt9aT1NdChlLml0ZXJhdGVDYWxsYmFja3MoImhhbmRsZXJEaWRFcnJvciIpKSx0LnByZXY9MTUsaS5zKCk7Y2FzZSAxNzppZigoYT1pLm4oKSkuZG9uZSl7dC5uZXh0PTI2O2JyZWFrfXJldHVybiBjPWEudmFsdWUsdC5uZXh0PTIxLGMoe2Vycm9yOnQudDAsZXZlbnQ6bixyZXF1ZXN0OnJ9KTtjYXNlIDIxOmlmKG89dC5zZW50LCFvKXt0Lm5leHQ9MjQ7YnJlYWt9cmV0dXJuIHQuYWJydXB0KCJicmVhayIsMjYpO2Nhc2UgMjQ6dC5uZXh0PTE3O2JyZWFrO2Nhc2UgMjY6dC5uZXh0PTMxO2JyZWFrO2Nhc2UgMjg6dC5wcmV2PTI4LHQudDE9dFsiY2F0Y2giXSgxNSksaS5lKHQudDEpO2Nhc2UgMzE6cmV0dXJuIHQucHJldj0zMSxpLmYoKSx0LmZpbmlzaCgzMSk7Y2FzZSAzNDppZihvKXt0Lm5leHQ9Mzg7YnJlYWt9dGhyb3cgdC50MDtjYXNlIDM4OjA7Y2FzZSAzOTp1PU10KGUuaXRlcmF0ZUNhbGxiYWNrcygiaGFuZGxlcldpbGxSZXNwb25kIikpLHQucHJldj00MCx1LnMoKTtjYXNlIDQyOmlmKChzPXUubigpKS5kb25lKXt0Lm5leHQ9NDk7YnJlYWt9cmV0dXJuIGY9cy52YWx1ZSx0Lm5leHQ9NDYsZih7ZXZlbnQ6bixyZXF1ZXN0OnIscmVzcG9uc2U6b30pO2Nhc2UgNDY6bz10LnNlbnQ7Y2FzZSA0Nzp0Lm5leHQ9NDI7YnJlYWs7Y2FzZSA0OTp0Lm5leHQ9NTQ7YnJlYWs7Y2FzZSA1MTp0LnByZXY9NTEsdC50Mj10WyJjYXRjaCJdKDQwKSx1LmUodC50Mik7Y2FzZSA1NDpyZXR1cm4gdC5wcmV2PTU0LHUuZigpLHQuZmluaXNoKDU0KTtjYXNlIDU3OnJldHVybiB0LmFicnVwdCgicmV0dXJuIixvKTtjYXNlIDU4OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCx0aGlzLFtbMywxMV0sWzE1LDI4LDMxLDM0XSxbNDAsNTEsNTQsNTddXSl9KSkpO2Z1bmN0aW9uIGUoZSxyLG4pe3JldHVybiB0LmFwcGx5KHRoaXMsYXJndW1lbnRzKX1yZXR1cm4gZX0oKX0se2tleToiX2F3YWl0Q29tcGxldGUiLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIHQ9V3QoS3QoKS5tYXJrKChmdW5jdGlvbiB0KGUscixuLG8pe3ZhciBpLGE7cmV0dXJuIEt0KCkud3JhcCgoZnVuY3Rpb24odCl7d2hpbGUoMSlzd2l0Y2godC5wcmV2PXQubmV4dCl7Y2FzZSAwOnJldHVybiB0LnByZXY9MCx0Lm5leHQ9MyxlO2Nhc2UgMzppPXQuc2VudCx0Lm5leHQ9ODticmVhaztjYXNlIDY6dC5wcmV2PTYsdC50MD10WyJjYXRjaCJdKDApO2Nhc2UgODpyZXR1cm4gdC5wcmV2PTgsdC5uZXh0PTExLHIucnVuQ2FsbGJhY2tzKCJoYW5kbGVyRGlkUmVzcG9uZCIse2V2ZW50Om8scmVxdWVzdDpuLHJlc3BvbnNlOml9KTtjYXNlIDExOnJldHVybiB0Lm5leHQ9MTMsci5kb25lV2FpdGluZygpO2Nhc2UgMTM6dC5uZXh0PTE4O2JyZWFrO2Nhc2UgMTU6dC5wcmV2PTE1LHQudDE9dFsiY2F0Y2giXSg4KSx0LnQxIGluc3RhbmNlb2YgRXJyb3ImJihhPXQudDEpO2Nhc2UgMTg6cmV0dXJuIHQubmV4dD0yMCxyLnJ1bkNhbGxiYWNrcygiaGFuZGxlckRpZENvbXBsZXRlIix7ZXZlbnQ6byxyZXF1ZXN0Om4scmVzcG9uc2U6aSxlcnJvcjphfSk7Y2FzZSAyMDppZihyLmRlc3Ryb3koKSwhYSl7dC5uZXh0PTIzO2JyZWFrfXRocm93IGE7Y2FzZSAyMzpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsbnVsbCxbWzAsNl0sWzgsMTVdXSl9KSkpO2Z1bmN0aW9uIGUoZSxyLG4sbyl7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiBlfSgpfV0pLHR9KCk7ZnVuY3Rpb24gdGUodCl7cmV0dXJuIHRlPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSx0ZSh0KX1mdW5jdGlvbiBlZSh0LGUpe3JldHVybiBvZSh0KXx8bmUodCxlKXx8YWUodCxlKXx8cmUoKX1mdW5jdGlvbiByZSgpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBkZXN0cnVjdHVyZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9ZnVuY3Rpb24gbmUodCxlKXt2YXIgcj1udWxsPT10P251bGw6InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZihudWxsIT1yKXt2YXIgbixvLGk9W10sYT0hMCxjPSExO3RyeXtmb3Iocj1yLmNhbGwodCk7IShhPShuPXIubmV4dCgpKS5kb25lKTthPSEwKWlmKGkucHVzaChuLnZhbHVlKSxlJiZpLmxlbmd0aD09PWUpYnJlYWt9Y2F0Y2godSl7Yz0hMCxvPXV9ZmluYWxseXt0cnl7YXx8bnVsbD09clsicmV0dXJuIl18fHJbInJldHVybiJdKCl9ZmluYWxseXtpZihjKXRocm93IG99fXJldHVybiBpfX1mdW5jdGlvbiBvZSh0KXtpZihBcnJheS5pc0FycmF5KHQpKXJldHVybiB0fWZ1bmN0aW9uIGllKHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9YWUodCkpfHxlJiZ0JiYibnVtYmVyIj09PXR5cGVvZiB0Lmxlbmd0aCl7ciYmKHQ9cik7dmFyIG49MCxvPWZ1bmN0aW9uKCl7fTtyZXR1cm57czpvLG46ZnVuY3Rpb24oKXtyZXR1cm4gbj49dC5sZW5ndGg/e2RvbmU6ITB9Ontkb25lOiExLHZhbHVlOnRbbisrXX19LGU6ZnVuY3Rpb24odCl7dGhyb3cgdH0sZjpvfX10aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gaXRlcmF0ZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9dmFyIGksYT0hMCxjPSExO3JldHVybntzOmZ1bmN0aW9uKCl7cj1yLmNhbGwodCl9LG46ZnVuY3Rpb24oKXt2YXIgdD1yLm5leHQoKTtyZXR1cm4gYT10LmRvbmUsdH0sZTpmdW5jdGlvbih0KXtjPSEwLGk9dH0sZjpmdW5jdGlvbigpe3RyeXthfHxudWxsPT1yLnJldHVybnx8ci5yZXR1cm4oKX1maW5hbGx5e2lmKGMpdGhyb3cgaX19fX1mdW5jdGlvbiBhZSh0LGUpe2lmKHQpe2lmKCJzdHJpbmciPT09dHlwZW9mIHQpcmV0dXJuIGNlKHQsZSk7dmFyIHI9T2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHQpLnNsaWNlKDgsLTEpO3JldHVybiJPYmplY3QiPT09ciYmdC5jb25zdHJ1Y3RvciYmKHI9dC5jb25zdHJ1Y3Rvci5uYW1lKSwiTWFwIj09PXJ8fCJTZXQiPT09cj9BcnJheS5mcm9tKHQpOiJBcmd1bWVudHMiPT09cnx8L14oPzpVaXxJKW50KD86OHwxNnwzMikoPzpDbGFtcGVkKT9BcnJheSQvLnRlc3Qocik/Y2UodCxlKTp2b2lkIDB9fWZ1bmN0aW9uIGNlKHQsZSl7KG51bGw9PWV8fGU+dC5sZW5ndGgpJiYoZT10Lmxlbmd0aCk7Zm9yKHZhciByPTAsbj1uZXcgQXJyYXkoZSk7cjxlO3IrKyluW3JdPXRbcl07cmV0dXJuIG59ZnVuY3Rpb24gdWUoKXsvKiEgcmVnZW5lcmF0b3ItcnVudGltZSAtLSBDb3B5cmlnaHQgKGMpIDIwMTQtcHJlc2VudCwgRmFjZWJvb2ssIEluYy4gLS0gbGljZW5zZSAoTUlUKTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlZ2VuZXJhdG9yL2Jsb2IvbWFpbi9MSUNFTlNFICovdWU9ZnVuY3Rpb24oKXtyZXR1cm4gdH07dmFyIHQ9e30sZT1PYmplY3QucHJvdG90eXBlLHI9ZS5oYXNPd25Qcm9wZXJ0eSxuPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2w/U3ltYm9sOnt9LG89bi5pdGVyYXRvcnx8IkBAaXRlcmF0b3IiLGk9bi5hc3luY0l0ZXJhdG9yfHwiQEBhc3luY0l0ZXJhdG9yIixhPW4udG9TdHJpbmdUYWd8fCJAQHRvU3RyaW5nVGFnIjtmdW5jdGlvbiBjKHQsZSxyKXtyZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsZSx7dmFsdWU6cixlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMH0pLHRbZV19dHJ5e2Moe30sIiIpfWNhdGNoKE8pe2M9ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0W2VdPXJ9fWZ1bmN0aW9uIHUodCxlLHIsbil7dmFyIG89ZSYmZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBsP2U6bCxpPU9iamVjdC5jcmVhdGUoby5wcm90b3R5cGUpLGE9bmV3IGsobnx8W10pO3JldHVybiBpLl9pbnZva2U9ZnVuY3Rpb24odCxlLHIpe3ZhciBuPSJzdXNwZW5kZWRTdGFydCI7cmV0dXJuIGZ1bmN0aW9uKG8saSl7aWYoImV4ZWN1dGluZyI9PT1uKXRocm93IG5ldyBFcnJvcigiR2VuZXJhdG9yIGlzIGFscmVhZHkgcnVubmluZyIpO2lmKCJjb21wbGV0ZWQiPT09bil7aWYoInRocm93Ij09PW8pdGhyb3cgaTtyZXR1cm4gXygpfWZvcihyLm1ldGhvZD1vLHIuYXJnPWk7Oyl7dmFyIGE9ci5kZWxlZ2F0ZTtpZihhKXt2YXIgYz1iKGEscik7aWYoYyl7aWYoYz09PWYpY29udGludWU7cmV0dXJuIGN9fWlmKCJuZXh0Ij09PXIubWV0aG9kKXIuc2VudD1yLl9zZW50PXIuYXJnO2Vsc2UgaWYoInRocm93Ij09PXIubWV0aG9kKXtpZigic3VzcGVuZGVkU3RhcnQiPT09bil0aHJvdyBuPSJjb21wbGV0ZWQiLHIuYXJnO3IuZGlzcGF0Y2hFeGNlcHRpb24oci5hcmcpfWVsc2UicmV0dXJuIj09PXIubWV0aG9kJiZyLmFicnVwdCgicmV0dXJuIixyLmFyZyk7bj0iZXhlY3V0aW5nIjt2YXIgdT1zKHQsZSxyKTtpZigibm9ybWFsIj09PXUudHlwZSl7aWYobj1yLmRvbmU/ImNvbXBsZXRlZCI6InN1c3BlbmRlZFlpZWxkIix1LmFyZz09PWYpY29udGludWU7cmV0dXJue3ZhbHVlOnUuYXJnLGRvbmU6ci5kb25lfX0idGhyb3ciPT09dS50eXBlJiYobj0iY29tcGxldGVkIixyLm1ldGhvZD0idGhyb3ciLHIuYXJnPXUuYXJnKX19fSh0LHIsYSksaX1mdW5jdGlvbiBzKHQsZSxyKXt0cnl7cmV0dXJue3R5cGU6Im5vcm1hbCIsYXJnOnQuY2FsbChlLHIpfX1jYXRjaChPKXtyZXR1cm57dHlwZToidGhyb3ciLGFyZzpPfX19dC53cmFwPXU7dmFyIGY9e307ZnVuY3Rpb24gbCgpe31mdW5jdGlvbiBoKCl7fWZ1bmN0aW9uIHAoKXt9dmFyIHk9e307Yyh5LG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSk7dmFyIHY9T2JqZWN0LmdldFByb3RvdHlwZU9mLGQ9diYmdih2KEUoW10pKSk7ZCYmZCE9PWUmJnIuY2FsbChkLG8pJiYoeT1kKTt2YXIgbT1wLnByb3RvdHlwZT1sLnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKHkpO2Z1bmN0aW9uIGcodCl7WyJuZXh0IiwidGhyb3ciLCJyZXR1cm4iXS5mb3JFYWNoKChmdW5jdGlvbihlKXtjKHQsZSwoZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2ludm9rZShlLHQpfSkpfSkpfWZ1bmN0aW9uIHcodCxlKXtmdW5jdGlvbiBuKG8saSxhLGMpe3ZhciB1PXModFtvXSx0LGkpO2lmKCJ0aHJvdyIhPT11LnR5cGUpe3ZhciBmPXUuYXJnLGw9Zi52YWx1ZTtyZXR1cm4gbCYmIm9iamVjdCI9PXRlKGwpJiZyLmNhbGwobCwiX19hd2FpdCIpP2UucmVzb2x2ZShsLl9fYXdhaXQpLnRoZW4oKGZ1bmN0aW9uKHQpe24oIm5leHQiLHQsYSxjKX0pLChmdW5jdGlvbih0KXtuKCJ0aHJvdyIsdCxhLGMpfSkpOmUucmVzb2x2ZShsKS50aGVuKChmdW5jdGlvbih0KXtmLnZhbHVlPXQsYShmKX0pLChmdW5jdGlvbih0KXtyZXR1cm4gbigidGhyb3ciLHQsYSxjKX0pKX1jKHUuYXJnKX12YXIgbzt0aGlzLl9pbnZva2U9ZnVuY3Rpb24odCxyKXtmdW5jdGlvbiBpKCl7cmV0dXJuIG5ldyBlKChmdW5jdGlvbihlLG8pe24odCxyLGUsbyl9KSl9cmV0dXJuIG89bz9vLnRoZW4oaSxpKTppKCl9fWZ1bmN0aW9uIGIodCxlKXt2YXIgcj10Lml0ZXJhdG9yW2UubWV0aG9kXTtpZih2b2lkIDA9PT1yKXtpZihlLmRlbGVnYXRlPW51bGwsInRocm93Ij09PWUubWV0aG9kKXtpZih0Lml0ZXJhdG9yLnJldHVybiYmKGUubWV0aG9kPSJyZXR1cm4iLGUuYXJnPXZvaWQgMCxiKHQsZSksInRocm93Ij09PWUubWV0aG9kKSlyZXR1cm4gZjtlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIlRoZSBpdGVyYXRvciBkb2VzIG5vdCBwcm92aWRlIGEgJ3Rocm93JyBtZXRob2QiKX1yZXR1cm4gZn12YXIgbj1zKHIsdC5pdGVyYXRvcixlLmFyZyk7aWYoInRocm93Ij09PW4udHlwZSlyZXR1cm4gZS5tZXRob2Q9InRocm93IixlLmFyZz1uLmFyZyxlLmRlbGVnYXRlPW51bGwsZjt2YXIgbz1uLmFyZztyZXR1cm4gbz9vLmRvbmU/KGVbdC5yZXN1bHROYW1lXT1vLnZhbHVlLGUubmV4dD10Lm5leHRMb2MsInJldHVybiIhPT1lLm1ldGhvZCYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLGUuZGVsZWdhdGU9bnVsbCxmKTpvOihlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIml0ZXJhdG9yIHJlc3VsdCBpcyBub3QgYW4gb2JqZWN0IiksZS5kZWxlZ2F0ZT1udWxsLGYpfWZ1bmN0aW9uIHgodCl7dmFyIGU9e3RyeUxvYzp0WzBdfTsxIGluIHQmJihlLmNhdGNoTG9jPXRbMV0pLDIgaW4gdCYmKGUuZmluYWxseUxvYz10WzJdLGUuYWZ0ZXJMb2M9dFszXSksdGhpcy50cnlFbnRyaWVzLnB1c2goZSl9ZnVuY3Rpb24gTCh0KXt2YXIgZT10LmNvbXBsZXRpb258fHt9O2UudHlwZT0ibm9ybWFsIixkZWxldGUgZS5hcmcsdC5jb21wbGV0aW9uPWV9ZnVuY3Rpb24gayh0KXt0aGlzLnRyeUVudHJpZXM9W3t0cnlMb2M6InJvb3QifV0sdC5mb3JFYWNoKHgsdGhpcyksdGhpcy5yZXNldCghMCl9ZnVuY3Rpb24gRSh0KXtpZih0KXt2YXIgZT10W29dO2lmKGUpcmV0dXJuIGUuY2FsbCh0KTtpZigiZnVuY3Rpb24iPT10eXBlb2YgdC5uZXh0KXJldHVybiB0O2lmKCFpc05hTih0Lmxlbmd0aCkpe3ZhciBuPS0xLGk9ZnVuY3Rpb24gZSgpe2Zvcig7KytuPHQubGVuZ3RoOylpZihyLmNhbGwodCxuKSlyZXR1cm4gZS52YWx1ZT10W25dLGUuZG9uZT0hMSxlO3JldHVybiBlLnZhbHVlPXZvaWQgMCxlLmRvbmU9ITAsZX07cmV0dXJuIGkubmV4dD1pfX1yZXR1cm57bmV4dDpffX1mdW5jdGlvbiBfKCl7cmV0dXJue3ZhbHVlOnZvaWQgMCxkb25lOiEwfX1yZXR1cm4gaC5wcm90b3R5cGU9cCxjKG0sImNvbnN0cnVjdG9yIixwKSxjKHAsImNvbnN0cnVjdG9yIixoKSxoLmRpc3BsYXlOYW1lPWMocCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpLHQuaXNHZW5lcmF0b3JGdW5jdGlvbj1mdW5jdGlvbih0KXt2YXIgZT0iZnVuY3Rpb24iPT10eXBlb2YgdCYmdC5jb25zdHJ1Y3RvcjtyZXR1cm4hIWUmJihlPT09aHx8IkdlbmVyYXRvckZ1bmN0aW9uIj09PShlLmRpc3BsYXlOYW1lfHxlLm5hbWUpKX0sdC5tYXJrPWZ1bmN0aW9uKHQpe3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2Y/T2JqZWN0LnNldFByb3RvdHlwZU9mKHQscCk6KHQuX19wcm90b19fPXAsYyh0LGEsIkdlbmVyYXRvckZ1bmN0aW9uIikpLHQucHJvdG90eXBlPU9iamVjdC5jcmVhdGUobSksdH0sdC5hd3JhcD1mdW5jdGlvbih0KXtyZXR1cm57X19hd2FpdDp0fX0sZyh3LnByb3RvdHlwZSksYyh3LnByb3RvdHlwZSxpLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLHQuQXN5bmNJdGVyYXRvcj13LHQuYXN5bmM9ZnVuY3Rpb24oZSxyLG4sbyxpKXt2b2lkIDA9PT1pJiYoaT1Qcm9taXNlKTt2YXIgYT1uZXcgdyh1KGUscixuLG8pLGkpO3JldHVybiB0LmlzR2VuZXJhdG9yRnVuY3Rpb24ocik/YTphLm5leHQoKS50aGVuKChmdW5jdGlvbih0KXtyZXR1cm4gdC5kb25lP3QudmFsdWU6YS5uZXh0KCl9KSl9LGcobSksYyhtLGEsIkdlbmVyYXRvciIpLGMobSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLGMobSwidG9TdHJpbmciLChmdW5jdGlvbigpe3JldHVybiJbb2JqZWN0IEdlbmVyYXRvcl0ifSkpLHQua2V5cz1mdW5jdGlvbih0KXt2YXIgZT1bXTtmb3IodmFyIHIgaW4gdCllLnB1c2gocik7cmV0dXJuIGUucmV2ZXJzZSgpLGZ1bmN0aW9uIHIoKXtmb3IoO2UubGVuZ3RoOyl7dmFyIG49ZS5wb3AoKTtpZihuIGluIHQpcmV0dXJuIHIudmFsdWU9bixyLmRvbmU9ITEscn1yZXR1cm4gci5kb25lPSEwLHJ9fSx0LnZhbHVlcz1FLGsucHJvdG90eXBlPXtjb25zdHJ1Y3RvcjprLHJlc2V0OmZ1bmN0aW9uKHQpe2lmKHRoaXMucHJldj0wLHRoaXMubmV4dD0wLHRoaXMuc2VudD10aGlzLl9zZW50PXZvaWQgMCx0aGlzLmRvbmU9ITEsdGhpcy5kZWxlZ2F0ZT1udWxsLHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLmFyZz12b2lkIDAsdGhpcy50cnlFbnRyaWVzLmZvckVhY2goTCksIXQpZm9yKHZhciBlIGluIHRoaXMpInQiPT09ZS5jaGFyQXQoMCkmJnIuY2FsbCh0aGlzLGUpJiYhaXNOYU4oK2Uuc2xpY2UoMSkpJiYodGhpc1tlXT12b2lkIDApfSxzdG9wOmZ1bmN0aW9uKCl7dGhpcy5kb25lPSEwO3ZhciB0PXRoaXMudHJ5RW50cmllc1swXS5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuIHRoaXMucnZhbH0sZGlzcGF0Y2hFeGNlcHRpb246ZnVuY3Rpb24odCl7aWYodGhpcy5kb25lKXRocm93IHQ7dmFyIGU9dGhpcztmdW5jdGlvbiBuKHIsbil7cmV0dXJuIGEudHlwZT0idGhyb3ciLGEuYXJnPXQsZS5uZXh0PXIsbiYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLCEhbn1mb3IodmFyIG89dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO28+PTA7LS1vKXt2YXIgaT10aGlzLnRyeUVudHJpZXNbb10sYT1pLmNvbXBsZXRpb247aWYoInJvb3QiPT09aS50cnlMb2MpcmV0dXJuIG4oImVuZCIpO2lmKGkudHJ5TG9jPD10aGlzLnByZXYpe3ZhciBjPXIuY2FsbChpLCJjYXRjaExvYyIpLHU9ci5jYWxsKGksImZpbmFsbHlMb2MiKTtpZihjJiZ1KXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9ZWxzZSBpZihjKXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKX1lbHNle2lmKCF1KXRocm93IG5ldyBFcnJvcigidHJ5IHN0YXRlbWVudCB3aXRob3V0IGNhdGNoIG9yIGZpbmFsbHkiKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9fX19LGFicnVwdDpmdW5jdGlvbih0LGUpe2Zvcih2YXIgbj10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bj49MDstLW4pe3ZhciBvPXRoaXMudHJ5RW50cmllc1tuXTtpZihvLnRyeUxvYzw9dGhpcy5wcmV2JiZyLmNhbGwobywiZmluYWxseUxvYyIpJiZ0aGlzLnByZXY8by5maW5hbGx5TG9jKXt2YXIgaT1vO2JyZWFrfX1pJiYoImJyZWFrIj09PXR8fCJjb250aW51ZSI9PT10KSYmaS50cnlMb2M8PWUmJmU8PWkuZmluYWxseUxvYyYmKGk9bnVsbCk7dmFyIGE9aT9pLmNvbXBsZXRpb246e307cmV0dXJuIGEudHlwZT10LGEuYXJnPWUsaT8odGhpcy5tZXRob2Q9Im5leHQiLHRoaXMubmV4dD1pLmZpbmFsbHlMb2MsZik6dGhpcy5jb21wbGV0ZShhKX0sY29tcGxldGU6ZnVuY3Rpb24odCxlKXtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiJicmVhayI9PT10LnR5cGV8fCJjb250aW51ZSI9PT10LnR5cGU/dGhpcy5uZXh0PXQuYXJnOiJyZXR1cm4iPT09dC50eXBlPyh0aGlzLnJ2YWw9dGhpcy5hcmc9dC5hcmcsdGhpcy5tZXRob2Q9InJldHVybiIsdGhpcy5uZXh0PSJlbmQiKToibm9ybWFsIj09PXQudHlwZSYmZSYmKHRoaXMubmV4dD1lKSxmfSxmaW5pc2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIuZmluYWxseUxvYz09PXQpcmV0dXJuIHRoaXMuY29tcGxldGUoci5jb21wbGV0aW9uLHIuYWZ0ZXJMb2MpLEwociksZn19LGNhdGNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLnRyeUxvYz09PXQpe3ZhciBuPXIuY29tcGxldGlvbjtpZigidGhyb3ciPT09bi50eXBlKXt2YXIgbz1uLmFyZztMKHIpfXJldHVybiBvfX10aHJvdyBuZXcgRXJyb3IoImlsbGVnYWwgY2F0Y2ggYXR0ZW1wdCIpfSxkZWxlZ2F0ZVlpZWxkOmZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdGhpcy5kZWxlZ2F0ZT17aXRlcmF0b3I6RSh0KSxyZXN1bHROYW1lOmUsbmV4dExvYzpyfSwibmV4dCI9PT10aGlzLm1ldGhvZCYmKHRoaXMuYXJnPXZvaWQgMCksZn19LHR9ZnVuY3Rpb24gc2UodCxlLHIsbixvLGksYSl7dHJ5e3ZhciBjPXRbaV0oYSksdT1jLnZhbHVlfWNhdGNoKHMpe3JldHVybiB2b2lkIHIocyl9Yy5kb25lP2UodSk6UHJvbWlzZS5yZXNvbHZlKHUpLnRoZW4obixvKX1mdW5jdGlvbiBmZSh0KXtyZXR1cm4gZnVuY3Rpb24oKXt2YXIgZT10aGlzLHI9YXJndW1lbnRzO3JldHVybiBuZXcgUHJvbWlzZSgoZnVuY3Rpb24obixvKXt2YXIgaT10LmFwcGx5KGUscik7ZnVuY3Rpb24gYSh0KXtzZShpLG4sbyxhLGMsIm5leHQiLHQpfWZ1bmN0aW9uIGModCl7c2UoaSxuLG8sYSxjLCJ0aHJvdyIsdCl9YSh2b2lkIDApfSkpfX1mdW5jdGlvbiBsZSh0LGUpe2lmKCEodCBpbnN0YW5jZW9mIGUpKXRocm93IG5ldyBUeXBlRXJyb3IoIkNhbm5vdCBjYWxsIGEgY2xhc3MgYXMgYSBmdW5jdGlvbiIpfWZ1bmN0aW9uIGhlKHQsZSl7Zm9yKHZhciByPTA7cjxlLmxlbmd0aDtyKyspe3ZhciBuPWVbcl07bi5lbnVtZXJhYmxlPW4uZW51bWVyYWJsZXx8ITEsbi5jb25maWd1cmFibGU9ITAsInZhbHVlImluIG4mJihuLndyaXRhYmxlPSEwKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCxuLmtleSxuKX19ZnVuY3Rpb24gcGUodCxlLHIpe3JldHVybiBlJiZoZSh0LnByb3RvdHlwZSxlKSxyJiZoZSh0LHIpLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LCJwcm90b3R5cGUiLHt3cml0YWJsZTohMX0pLHR9ZnVuY3Rpb24geWUodCxlKXtpZigiZnVuY3Rpb24iIT09dHlwZW9mIGUmJm51bGwhPT1lKXRocm93IG5ldyBUeXBlRXJyb3IoIlN1cGVyIGV4cHJlc3Npb24gbXVzdCBlaXRoZXIgYmUgbnVsbCBvciBhIGZ1bmN0aW9uIik7dC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShlJiZlLnByb3RvdHlwZSx7Y29uc3RydWN0b3I6e3ZhbHVlOnQsd3JpdGFibGU6ITAsY29uZmlndXJhYmxlOiEwfX0pLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LCJwcm90b3R5cGUiLHt3cml0YWJsZTohMX0pLGUmJnZlKHQsZSl9ZnVuY3Rpb24gdmUodCxlKXtyZXR1cm4gdmU9T2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5zZXRQcm90b3R5cGVPZi5iaW5kKCk6ZnVuY3Rpb24odCxlKXtyZXR1cm4gdC5fX3Byb3RvX189ZSx0fSx2ZSh0LGUpfWZ1bmN0aW9uIGRlKHQpe3ZhciBlPXdlKCk7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIHIsbj1iZSh0KTtpZihlKXt2YXIgbz1iZSh0aGlzKS5jb25zdHJ1Y3RvcjtyPVJlZmxlY3QuY29uc3RydWN0KG4sYXJndW1lbnRzLG8pfWVsc2Ugcj1uLmFwcGx5KHRoaXMsYXJndW1lbnRzKTtyZXR1cm4gbWUodGhpcyxyKX19ZnVuY3Rpb24gbWUodCxlKXtpZihlJiYoIm9iamVjdCI9PT10ZShlKXx8ImZ1bmN0aW9uIj09PXR5cGVvZiBlKSlyZXR1cm4gZTtpZih2b2lkIDAhPT1lKXRocm93IG5ldyBUeXBlRXJyb3IoIkRlcml2ZWQgY29uc3RydWN0b3JzIG1heSBvbmx5IHJldHVybiBvYmplY3Qgb3IgdW5kZWZpbmVkIik7cmV0dXJuIGdlKHQpfWZ1bmN0aW9uIGdlKHQpe2lmKHZvaWQgMD09PXQpdGhyb3cgbmV3IFJlZmVyZW5jZUVycm9yKCJ0aGlzIGhhc24ndCBiZWVuIGluaXRpYWxpc2VkIC0gc3VwZXIoKSBoYXNuJ3QgYmVlbiBjYWxsZWQiKTtyZXR1cm4gdH1mdW5jdGlvbiB3ZSgpe2lmKCJ1bmRlZmluZWQiPT09dHlwZW9mIFJlZmxlY3R8fCFSZWZsZWN0LmNvbnN0cnVjdClyZXR1cm4hMTtpZihSZWZsZWN0LmNvbnN0cnVjdC5zaGFtKXJldHVybiExO2lmKCJmdW5jdGlvbiI9PT10eXBlb2YgUHJveHkpcmV0dXJuITA7dHJ5e3JldHVybiBCb29sZWFuLnByb3RvdHlwZS52YWx1ZU9mLmNhbGwoUmVmbGVjdC5jb25zdHJ1Y3QoQm9vbGVhbixbXSwoZnVuY3Rpb24oKXt9KSkpLCEwfWNhdGNoKHQpe3JldHVybiExfX1mdW5jdGlvbiBiZSh0KXtyZXR1cm4gYmU9T2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5nZXRQcm90b3R5cGVPZi5iaW5kKCk6ZnVuY3Rpb24odCl7cmV0dXJuIHQuX19wcm90b19ffHxPYmplY3QuZ2V0UHJvdG90eXBlT2YodCl9LGJlKHQpfXZhciB4ZT1mdW5jdGlvbihlKXt5ZShuLGUpO3ZhciByPWRlKG4pO2Z1bmN0aW9uIG4oKXt2YXIgdCxlPWFyZ3VtZW50cy5sZW5ndGg+MCYmdm9pZCAwIT09YXJndW1lbnRzWzBdP2FyZ3VtZW50c1swXTp7fTtyZXR1cm4gbGUodGhpcyxuKSxlLmNhY2hlTmFtZT1FLmdldFByZWNhY2hlTmFtZShlLmNhY2hlTmFtZSksdD1yLmNhbGwodGhpcyxlKSx0Ll9mYWxsYmFja1RvTmV0d29yaz0hMSE9PWUuZmFsbGJhY2tUb05ldHdvcmssdC5wbHVnaW5zLnB1c2gobi5jb3B5UmVkaXJlY3RlZENhY2hlYWJsZVJlc3BvbnNlc1BsdWdpbiksdH1yZXR1cm4gcGUobixbe2tleToiX2hhbmRsZSIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgdD1mZSh1ZSgpLm1hcmsoKGZ1bmN0aW9uIHQoZSxyKXt2YXIgbjtyZXR1cm4gdWUoKS53cmFwKChmdW5jdGlvbih0KXt3aGlsZSgxKXN3aXRjaCh0LnByZXY9dC5uZXh0KXtjYXNlIDA6cmV0dXJuIHQubmV4dD0yLHIuY2FjaGVNYXRjaChlKTtjYXNlIDI6aWYobj10LnNlbnQsIW4pe3QubmV4dD01O2JyZWFrfXJldHVybiB0LmFicnVwdCgicmV0dXJuIixuKTtjYXNlIDU6aWYoIXIuZXZlbnR8fCJpbnN0YWxsIiE9PXIuZXZlbnQudHlwZSl7dC5uZXh0PTk7YnJlYWt9cmV0dXJuIHQubmV4dD04LHRoaXMuX2hhbmRsZUluc3RhbGwoZSxyKTtjYXNlIDg6cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iLHQuc2VudCk7Y2FzZSA5OnJldHVybiB0Lm5leHQ9MTEsdGhpcy5faGFuZGxlRmV0Y2goZSxyKTtjYXNlIDExOnJldHVybiB0LmFicnVwdCgicmV0dXJuIix0LnNlbnQpO2Nhc2UgMTI6Y2FzZSJlbmQiOnJldHVybiB0LnN0b3AoKX19KSx0LHRoaXMpfSkpKTtmdW5jdGlvbiBlKGUscil7cmV0dXJuIHQuYXBwbHkodGhpcyxhcmd1bWVudHMpfXJldHVybiBlfSgpfSx7a2V5OiJfaGFuZGxlRmV0Y2giLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIGU9ZmUodWUoKS5tYXJrKChmdW5jdGlvbiBlKHIsbil7dmFyIG8saSxhLGMsdSxzO3JldHVybiB1ZSgpLndyYXAoKGZ1bmN0aW9uKGUpe3doaWxlKDEpc3dpdGNoKGUucHJldj1lLm5leHQpe2Nhc2UgMDppZihpPW4ucGFyYW1zfHx7fSwhdGhpcy5fZmFsbGJhY2tUb05ldHdvcmspe2UubmV4dD0xNzticmVha31yZXR1cm4gYT1pLmludGVncml0eSxjPXIuaW50ZWdyaXR5LHU9IWN8fGM9PT1hLGUubmV4dD04LG4uZmV0Y2gobmV3IFJlcXVlc3Qocix7aW50ZWdyaXR5OiJuby1jb3JzIiE9PXIubW9kZT9jfHxhOnZvaWQgMH0pKTtjYXNlIDg6aWYobz1lLnNlbnQsIWF8fCF1fHwibm8tY29ycyI9PT1yLm1vZGUpe2UubmV4dD0xNTticmVha31yZXR1cm4gdGhpcy5fdXNlRGVmYXVsdENhY2hlYWJpbGl0eVBsdWdpbklmTmVlZGVkKCksZS5uZXh0PTEzLG4uY2FjaGVQdXQocixvLmNsb25lKCkpO2Nhc2UgMTM6ZS5zZW50O2Nhc2UgMTU6ZS5uZXh0PTE4O2JyZWFrO2Nhc2UgMTc6dGhyb3cgbmV3IGcoIm1pc3NpbmctcHJlY2FjaGUtZW50cnkiLHtjYWNoZU5hbWU6dGhpcy5jYWNoZU5hbWUsdXJsOnIudXJsfSk7Y2FzZSAxODplLm5leHQ9MzQ7YnJlYWs7Y2FzZSAyMzplLnQwPWUuc2VudDtjYXNlIDI0OnM9ZS50MCx0Lmdyb3VwQ29sbGFwc2VkKCJQcmVjYWNoaW5nIGlzIHJlc3BvbmRpbmcgdG86ICIreihyLnVybCkpLHQubG9nKCJTZXJ2aW5nIHRoZSBwcmVjYWNoZWQgdXJsOiAiLmNvbmNhdCh6KHMgaW5zdGFuY2VvZiBSZXF1ZXN0P3MudXJsOnMpKSksdC5ncm91cENvbGxhcHNlZCgiVmlldyByZXF1ZXN0IGRldGFpbHMgaGVyZS4iKSx0LmxvZyhyKSx0Lmdyb3VwRW5kKCksdC5ncm91cENvbGxhcHNlZCgiVmlldyByZXNwb25zZSBkZXRhaWxzIGhlcmUuIiksdC5sb2cobyksdC5ncm91cEVuZCgpLHQuZ3JvdXBFbmQoKTtjYXNlIDM0OnJldHVybiBlLmFicnVwdCgicmV0dXJuIixvKTtjYXNlIDM1OmNhc2UiZW5kIjpyZXR1cm4gZS5zdG9wKCl9fSksZSx0aGlzKX0pKSk7ZnVuY3Rpb24gcih0LHIpe3JldHVybiBlLmFwcGx5KHRoaXMsYXJndW1lbnRzKX1yZXR1cm4gcn0oKX0se2tleToiX2hhbmRsZUluc3RhbGwiLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIHQ9ZmUodWUoKS5tYXJrKChmdW5jdGlvbiB0KGUscil7dmFyIG4sbztyZXR1cm4gdWUoKS53cmFwKChmdW5jdGlvbih0KXt3aGlsZSgxKXN3aXRjaCh0LnByZXY9dC5uZXh0KXtjYXNlIDA6cmV0dXJuIHRoaXMuX3VzZURlZmF1bHRDYWNoZWFiaWxpdHlQbHVnaW5JZk5lZWRlZCgpLHQubmV4dD0zLHIuZmV0Y2goZSk7Y2FzZSAzOnJldHVybiBuPXQuc2VudCx0Lm5leHQ9NixyLmNhY2hlUHV0KGUsbi5jbG9uZSgpKTtjYXNlIDY6aWYobz10LnNlbnQsbyl7dC5uZXh0PTk7YnJlYWt9dGhyb3cgbmV3IGcoImJhZC1wcmVjYWNoaW5nLXJlc3BvbnNlIix7dXJsOmUudXJsLHN0YXR1czpuLnN0YXR1c30pO2Nhc2UgOTpyZXR1cm4gdC5hYnJ1cHQoInJldHVybiIsbik7Y2FzZSAxMDpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsdGhpcyl9KSkpO2Z1bmN0aW9uIGUoZSxyKXtyZXR1cm4gdC5hcHBseSh0aGlzLGFyZ3VtZW50cyl9cmV0dXJuIGV9KCl9LHtrZXk6Il91c2VEZWZhdWx0Q2FjaGVhYmlsaXR5UGx1Z2luSWZOZWVkZWQiLHZhbHVlOmZ1bmN0aW9uKCl7dmFyIHQsZT1udWxsLHI9MCxvPWllKHRoaXMucGx1Z2lucy5lbnRyaWVzKCkpO3RyeXtmb3Ioby5zKCk7ISh0PW8ubigpKS5kb25lOyl7dmFyIGk9ZWUodC52YWx1ZSwyKSxhPWlbMF0sYz1pWzFdO2MhPT1uLmNvcHlSZWRpcmVjdGVkQ2FjaGVhYmxlUmVzcG9uc2VzUGx1Z2luJiYoYz09PW4uZGVmYXVsdFByZWNhY2hlQ2FjaGVhYmlsaXR5UGx1Z2luJiYoZT1hKSxjLmNhY2hlV2lsbFVwZGF0ZSYmcisrKX19Y2F0Y2godSl7by5lKHUpfWZpbmFsbHl7by5mKCl9MD09PXI/dGhpcy5wbHVnaW5zLnB1c2gobi5kZWZhdWx0UHJlY2FjaGVDYWNoZWFiaWxpdHlQbHVnaW4pOnI+MSYmbnVsbCE9PWUmJnRoaXMucGx1Z2lucy5zcGxpY2UoZSwxKX19XSksbn0oWnQpO2Z1bmN0aW9uIExlKHQpe3JldHVybiBMZT0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiYic3ltYm9sIj09dHlwZW9mIFN5bWJvbC5pdGVyYXRvcj9mdW5jdGlvbih0KXtyZXR1cm4gdHlwZW9mIHR9OmZ1bmN0aW9uKHQpe3JldHVybiB0JiYiZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiZ0LmNvbnN0cnVjdG9yPT09U3ltYm9sJiZ0IT09U3ltYm9sLnByb3RvdHlwZT8ic3ltYm9sIjp0eXBlb2YgdH0sTGUodCl9ZnVuY3Rpb24ga2UoKXsvKiEgcmVnZW5lcmF0b3ItcnVudGltZSAtLSBDb3B5cmlnaHQgKGMpIDIwMTQtcHJlc2VudCwgRmFjZWJvb2ssIEluYy4gLS0gbGljZW5zZSAoTUlUKTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlZ2VuZXJhdG9yL2Jsb2IvbWFpbi9MSUNFTlNFICova2U9ZnVuY3Rpb24oKXtyZXR1cm4gdH07dmFyIHQ9e30sZT1PYmplY3QucHJvdG90eXBlLHI9ZS5oYXNPd25Qcm9wZXJ0eSxuPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2w/U3ltYm9sOnt9LG89bi5pdGVyYXRvcnx8IkBAaXRlcmF0b3IiLGk9bi5hc3luY0l0ZXJhdG9yfHwiQEBhc3luY0l0ZXJhdG9yIixhPW4udG9TdHJpbmdUYWd8fCJAQHRvU3RyaW5nVGFnIjtmdW5jdGlvbiBjKHQsZSxyKXtyZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsZSx7dmFsdWU6cixlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMH0pLHRbZV19dHJ5e2Moe30sIiIpfWNhdGNoKE8pe2M9ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0W2VdPXJ9fWZ1bmN0aW9uIHUodCxlLHIsbil7dmFyIG89ZSYmZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBsP2U6bCxpPU9iamVjdC5jcmVhdGUoby5wcm90b3R5cGUpLGE9bmV3IGsobnx8W10pO3JldHVybiBpLl9pbnZva2U9ZnVuY3Rpb24odCxlLHIpe3ZhciBuPSJzdXNwZW5kZWRTdGFydCI7cmV0dXJuIGZ1bmN0aW9uKG8saSl7aWYoImV4ZWN1dGluZyI9PT1uKXRocm93IG5ldyBFcnJvcigiR2VuZXJhdG9yIGlzIGFscmVhZHkgcnVubmluZyIpO2lmKCJjb21wbGV0ZWQiPT09bil7aWYoInRocm93Ij09PW8pdGhyb3cgaTtyZXR1cm4gXygpfWZvcihyLm1ldGhvZD1vLHIuYXJnPWk7Oyl7dmFyIGE9ci5kZWxlZ2F0ZTtpZihhKXt2YXIgYz1iKGEscik7aWYoYyl7aWYoYz09PWYpY29udGludWU7cmV0dXJuIGN9fWlmKCJuZXh0Ij09PXIubWV0aG9kKXIuc2VudD1yLl9zZW50PXIuYXJnO2Vsc2UgaWYoInRocm93Ij09PXIubWV0aG9kKXtpZigic3VzcGVuZGVkU3RhcnQiPT09bil0aHJvdyBuPSJjb21wbGV0ZWQiLHIuYXJnO3IuZGlzcGF0Y2hFeGNlcHRpb24oci5hcmcpfWVsc2UicmV0dXJuIj09PXIubWV0aG9kJiZyLmFicnVwdCgicmV0dXJuIixyLmFyZyk7bj0iZXhlY3V0aW5nIjt2YXIgdT1zKHQsZSxyKTtpZigibm9ybWFsIj09PXUudHlwZSl7aWYobj1yLmRvbmU/ImNvbXBsZXRlZCI6InN1c3BlbmRlZFlpZWxkIix1LmFyZz09PWYpY29udGludWU7cmV0dXJue3ZhbHVlOnUuYXJnLGRvbmU6ci5kb25lfX0idGhyb3ciPT09dS50eXBlJiYobj0iY29tcGxldGVkIixyLm1ldGhvZD0idGhyb3ciLHIuYXJnPXUuYXJnKX19fSh0LHIsYSksaX1mdW5jdGlvbiBzKHQsZSxyKXt0cnl7cmV0dXJue3R5cGU6Im5vcm1hbCIsYXJnOnQuY2FsbChlLHIpfX1jYXRjaChPKXtyZXR1cm57dHlwZToidGhyb3ciLGFyZzpPfX19dC53cmFwPXU7dmFyIGY9e307ZnVuY3Rpb24gbCgpe31mdW5jdGlvbiBoKCl7fWZ1bmN0aW9uIHAoKXt9dmFyIHk9e307Yyh5LG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSk7dmFyIHY9T2JqZWN0LmdldFByb3RvdHlwZU9mLGQ9diYmdih2KEUoW10pKSk7ZCYmZCE9PWUmJnIuY2FsbChkLG8pJiYoeT1kKTt2YXIgbT1wLnByb3RvdHlwZT1sLnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKHkpO2Z1bmN0aW9uIGcodCl7WyJuZXh0IiwidGhyb3ciLCJyZXR1cm4iXS5mb3JFYWNoKChmdW5jdGlvbihlKXtjKHQsZSwoZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2ludm9rZShlLHQpfSkpfSkpfWZ1bmN0aW9uIHcodCxlKXtmdW5jdGlvbiBuKG8saSxhLGMpe3ZhciB1PXModFtvXSx0LGkpO2lmKCJ0aHJvdyIhPT11LnR5cGUpe3ZhciBmPXUuYXJnLGw9Zi52YWx1ZTtyZXR1cm4gbCYmIm9iamVjdCI9PUxlKGwpJiZyLmNhbGwobCwiX19hd2FpdCIpP2UucmVzb2x2ZShsLl9fYXdhaXQpLnRoZW4oKGZ1bmN0aW9uKHQpe24oIm5leHQiLHQsYSxjKX0pLChmdW5jdGlvbih0KXtuKCJ0aHJvdyIsdCxhLGMpfSkpOmUucmVzb2x2ZShsKS50aGVuKChmdW5jdGlvbih0KXtmLnZhbHVlPXQsYShmKX0pLChmdW5jdGlvbih0KXtyZXR1cm4gbigidGhyb3ciLHQsYSxjKX0pKX1jKHUuYXJnKX12YXIgbzt0aGlzLl9pbnZva2U9ZnVuY3Rpb24odCxyKXtmdW5jdGlvbiBpKCl7cmV0dXJuIG5ldyBlKChmdW5jdGlvbihlLG8pe24odCxyLGUsbyl9KSl9cmV0dXJuIG89bz9vLnRoZW4oaSxpKTppKCl9fWZ1bmN0aW9uIGIodCxlKXt2YXIgcj10Lml0ZXJhdG9yW2UubWV0aG9kXTtpZih2b2lkIDA9PT1yKXtpZihlLmRlbGVnYXRlPW51bGwsInRocm93Ij09PWUubWV0aG9kKXtpZih0Lml0ZXJhdG9yLnJldHVybiYmKGUubWV0aG9kPSJyZXR1cm4iLGUuYXJnPXZvaWQgMCxiKHQsZSksInRocm93Ij09PWUubWV0aG9kKSlyZXR1cm4gZjtlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIlRoZSBpdGVyYXRvciBkb2VzIG5vdCBwcm92aWRlIGEgJ3Rocm93JyBtZXRob2QiKX1yZXR1cm4gZn12YXIgbj1zKHIsdC5pdGVyYXRvcixlLmFyZyk7aWYoInRocm93Ij09PW4udHlwZSlyZXR1cm4gZS5tZXRob2Q9InRocm93IixlLmFyZz1uLmFyZyxlLmRlbGVnYXRlPW51bGwsZjt2YXIgbz1uLmFyZztyZXR1cm4gbz9vLmRvbmU/KGVbdC5yZXN1bHROYW1lXT1vLnZhbHVlLGUubmV4dD10Lm5leHRMb2MsInJldHVybiIhPT1lLm1ldGhvZCYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLGUuZGVsZWdhdGU9bnVsbCxmKTpvOihlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIml0ZXJhdG9yIHJlc3VsdCBpcyBub3QgYW4gb2JqZWN0IiksZS5kZWxlZ2F0ZT1udWxsLGYpfWZ1bmN0aW9uIHgodCl7dmFyIGU9e3RyeUxvYzp0WzBdfTsxIGluIHQmJihlLmNhdGNoTG9jPXRbMV0pLDIgaW4gdCYmKGUuZmluYWxseUxvYz10WzJdLGUuYWZ0ZXJMb2M9dFszXSksdGhpcy50cnlFbnRyaWVzLnB1c2goZSl9ZnVuY3Rpb24gTCh0KXt2YXIgZT10LmNvbXBsZXRpb258fHt9O2UudHlwZT0ibm9ybWFsIixkZWxldGUgZS5hcmcsdC5jb21wbGV0aW9uPWV9ZnVuY3Rpb24gayh0KXt0aGlzLnRyeUVudHJpZXM9W3t0cnlMb2M6InJvb3QifV0sdC5mb3JFYWNoKHgsdGhpcyksdGhpcy5yZXNldCghMCl9ZnVuY3Rpb24gRSh0KXtpZih0KXt2YXIgZT10W29dO2lmKGUpcmV0dXJuIGUuY2FsbCh0KTtpZigiZnVuY3Rpb24iPT10eXBlb2YgdC5uZXh0KXJldHVybiB0O2lmKCFpc05hTih0Lmxlbmd0aCkpe3ZhciBuPS0xLGk9ZnVuY3Rpb24gZSgpe2Zvcig7KytuPHQubGVuZ3RoOylpZihyLmNhbGwodCxuKSlyZXR1cm4gZS52YWx1ZT10W25dLGUuZG9uZT0hMSxlO3JldHVybiBlLnZhbHVlPXZvaWQgMCxlLmRvbmU9ITAsZX07cmV0dXJuIGkubmV4dD1pfX1yZXR1cm57bmV4dDpffX1mdW5jdGlvbiBfKCl7cmV0dXJue3ZhbHVlOnZvaWQgMCxkb25lOiEwfX1yZXR1cm4gaC5wcm90b3R5cGU9cCxjKG0sImNvbnN0cnVjdG9yIixwKSxjKHAsImNvbnN0cnVjdG9yIixoKSxoLmRpc3BsYXlOYW1lPWMocCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpLHQuaXNHZW5lcmF0b3JGdW5jdGlvbj1mdW5jdGlvbih0KXt2YXIgZT0iZnVuY3Rpb24iPT10eXBlb2YgdCYmdC5jb25zdHJ1Y3RvcjtyZXR1cm4hIWUmJihlPT09aHx8IkdlbmVyYXRvckZ1bmN0aW9uIj09PShlLmRpc3BsYXlOYW1lfHxlLm5hbWUpKX0sdC5tYXJrPWZ1bmN0aW9uKHQpe3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2Y/T2JqZWN0LnNldFByb3RvdHlwZU9mKHQscCk6KHQuX19wcm90b19fPXAsYyh0LGEsIkdlbmVyYXRvckZ1bmN0aW9uIikpLHQucHJvdG90eXBlPU9iamVjdC5jcmVhdGUobSksdH0sdC5hd3JhcD1mdW5jdGlvbih0KXtyZXR1cm57X19hd2FpdDp0fX0sZyh3LnByb3RvdHlwZSksYyh3LnByb3RvdHlwZSxpLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLHQuQXN5bmNJdGVyYXRvcj13LHQuYXN5bmM9ZnVuY3Rpb24oZSxyLG4sbyxpKXt2b2lkIDA9PT1pJiYoaT1Qcm9taXNlKTt2YXIgYT1uZXcgdyh1KGUscixuLG8pLGkpO3JldHVybiB0LmlzR2VuZXJhdG9yRnVuY3Rpb24ocik/YTphLm5leHQoKS50aGVuKChmdW5jdGlvbih0KXtyZXR1cm4gdC5kb25lP3QudmFsdWU6YS5uZXh0KCl9KSl9LGcobSksYyhtLGEsIkdlbmVyYXRvciIpLGMobSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLGMobSwidG9TdHJpbmciLChmdW5jdGlvbigpe3JldHVybiJbb2JqZWN0IEdlbmVyYXRvcl0ifSkpLHQua2V5cz1mdW5jdGlvbih0KXt2YXIgZT1bXTtmb3IodmFyIHIgaW4gdCllLnB1c2gocik7cmV0dXJuIGUucmV2ZXJzZSgpLGZ1bmN0aW9uIHIoKXtmb3IoO2UubGVuZ3RoOyl7dmFyIG49ZS5wb3AoKTtpZihuIGluIHQpcmV0dXJuIHIudmFsdWU9bixyLmRvbmU9ITEscn1yZXR1cm4gci5kb25lPSEwLHJ9fSx0LnZhbHVlcz1FLGsucHJvdG90eXBlPXtjb25zdHJ1Y3RvcjprLHJlc2V0OmZ1bmN0aW9uKHQpe2lmKHRoaXMucHJldj0wLHRoaXMubmV4dD0wLHRoaXMuc2VudD10aGlzLl9zZW50PXZvaWQgMCx0aGlzLmRvbmU9ITEsdGhpcy5kZWxlZ2F0ZT1udWxsLHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLmFyZz12b2lkIDAsdGhpcy50cnlFbnRyaWVzLmZvckVhY2goTCksIXQpZm9yKHZhciBlIGluIHRoaXMpInQiPT09ZS5jaGFyQXQoMCkmJnIuY2FsbCh0aGlzLGUpJiYhaXNOYU4oK2Uuc2xpY2UoMSkpJiYodGhpc1tlXT12b2lkIDApfSxzdG9wOmZ1bmN0aW9uKCl7dGhpcy5kb25lPSEwO3ZhciB0PXRoaXMudHJ5RW50cmllc1swXS5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuIHRoaXMucnZhbH0sZGlzcGF0Y2hFeGNlcHRpb246ZnVuY3Rpb24odCl7aWYodGhpcy5kb25lKXRocm93IHQ7dmFyIGU9dGhpcztmdW5jdGlvbiBuKHIsbil7cmV0dXJuIGEudHlwZT0idGhyb3ciLGEuYXJnPXQsZS5uZXh0PXIsbiYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLCEhbn1mb3IodmFyIG89dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO28+PTA7LS1vKXt2YXIgaT10aGlzLnRyeUVudHJpZXNbb10sYT1pLmNvbXBsZXRpb247aWYoInJvb3QiPT09aS50cnlMb2MpcmV0dXJuIG4oImVuZCIpO2lmKGkudHJ5TG9jPD10aGlzLnByZXYpe3ZhciBjPXIuY2FsbChpLCJjYXRjaExvYyIpLHU9ci5jYWxsKGksImZpbmFsbHlMb2MiKTtpZihjJiZ1KXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9ZWxzZSBpZihjKXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKX1lbHNle2lmKCF1KXRocm93IG5ldyBFcnJvcigidHJ5IHN0YXRlbWVudCB3aXRob3V0IGNhdGNoIG9yIGZpbmFsbHkiKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9fX19LGFicnVwdDpmdW5jdGlvbih0LGUpe2Zvcih2YXIgbj10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bj49MDstLW4pe3ZhciBvPXRoaXMudHJ5RW50cmllc1tuXTtpZihvLnRyeUxvYzw9dGhpcy5wcmV2JiZyLmNhbGwobywiZmluYWxseUxvYyIpJiZ0aGlzLnByZXY8by5maW5hbGx5TG9jKXt2YXIgaT1vO2JyZWFrfX1pJiYoImJyZWFrIj09PXR8fCJjb250aW51ZSI9PT10KSYmaS50cnlMb2M8PWUmJmU8PWkuZmluYWxseUxvYyYmKGk9bnVsbCk7dmFyIGE9aT9pLmNvbXBsZXRpb246e307cmV0dXJuIGEudHlwZT10LGEuYXJnPWUsaT8odGhpcy5tZXRob2Q9Im5leHQiLHRoaXMubmV4dD1pLmZpbmFsbHlMb2MsZik6dGhpcy5jb21wbGV0ZShhKX0sY29tcGxldGU6ZnVuY3Rpb24odCxlKXtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiJicmVhayI9PT10LnR5cGV8fCJjb250aW51ZSI9PT10LnR5cGU/dGhpcy5uZXh0PXQuYXJnOiJyZXR1cm4iPT09dC50eXBlPyh0aGlzLnJ2YWw9dGhpcy5hcmc9dC5hcmcsdGhpcy5tZXRob2Q9InJldHVybiIsdGhpcy5uZXh0PSJlbmQiKToibm9ybWFsIj09PXQudHlwZSYmZSYmKHRoaXMubmV4dD1lKSxmfSxmaW5pc2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIuZmluYWxseUxvYz09PXQpcmV0dXJuIHRoaXMuY29tcGxldGUoci5jb21wbGV0aW9uLHIuYWZ0ZXJMb2MpLEwociksZn19LGNhdGNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLnRyeUxvYz09PXQpe3ZhciBuPXIuY29tcGxldGlvbjtpZigidGhyb3ciPT09bi50eXBlKXt2YXIgbz1uLmFyZztMKHIpfXJldHVybiBvfX10aHJvdyBuZXcgRXJyb3IoImlsbGVnYWwgY2F0Y2ggYXR0ZW1wdCIpfSxkZWxlZ2F0ZVlpZWxkOmZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdGhpcy5kZWxlZ2F0ZT17aXRlcmF0b3I6RSh0KSxyZXN1bHROYW1lOmUsbmV4dExvYzpyfSwibmV4dCI9PT10aGlzLm1ldGhvZCYmKHRoaXMuYXJnPXZvaWQgMCksZn19LHR9ZnVuY3Rpb24gRWUodCxlKXtyZXR1cm4gU2UodCl8fE9lKHQsZSl8fEFlKHQsZSl8fF9lKCl9ZnVuY3Rpb24gX2UoKXt0aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gZGVzdHJ1Y3R1cmUgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfWZ1bmN0aW9uIE9lKHQsZSl7dmFyIHI9bnVsbD09dD9udWxsOiJ1bmRlZmluZWQiIT09dHlwZW9mIFN5bWJvbCYmdFtTeW1ib2wuaXRlcmF0b3JdfHx0WyJAQGl0ZXJhdG9yIl07aWYobnVsbCE9cil7dmFyIG4sbyxpPVtdLGE9ITAsYz0hMTt0cnl7Zm9yKHI9ci5jYWxsKHQpOyEoYT0obj1yLm5leHQoKSkuZG9uZSk7YT0hMClpZihpLnB1c2gobi52YWx1ZSksZSYmaS5sZW5ndGg9PT1lKWJyZWFrfWNhdGNoKHUpe2M9ITAsbz11fWZpbmFsbHl7dHJ5e2F8fG51bGw9PXJbInJldHVybiJdfHxyWyJyZXR1cm4iXSgpfWZpbmFsbHl7aWYoYyl0aHJvdyBvfX1yZXR1cm4gaX19ZnVuY3Rpb24gU2UodCl7aWYoQXJyYXkuaXNBcnJheSh0KSlyZXR1cm4gdH1mdW5jdGlvbiBqZSh0LGUscixuLG8saSxhKXt0cnl7dmFyIGM9dFtpXShhKSx1PWMudmFsdWV9Y2F0Y2gocyl7cmV0dXJuIHZvaWQgcihzKX1jLmRvbmU/ZSh1KTpQcm9taXNlLnJlc29sdmUodSkudGhlbihuLG8pfWZ1bmN0aW9uIFBlKHQpe3JldHVybiBmdW5jdGlvbigpe3ZhciBlPXRoaXMscj1hcmd1bWVudHM7cmV0dXJuIG5ldyBQcm9taXNlKChmdW5jdGlvbihuLG8pe3ZhciBpPXQuYXBwbHkoZSxyKTtmdW5jdGlvbiBhKHQpe2plKGksbixvLGEsYywibmV4dCIsdCl9ZnVuY3Rpb24gYyh0KXtqZShpLG4sbyxhLGMsInRocm93Iix0KX1hKHZvaWQgMCl9KSl9fWZ1bmN0aW9uIFRlKHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9QWUodCkpfHxlJiZ0JiYibnVtYmVyIj09PXR5cGVvZiB0Lmxlbmd0aCl7ciYmKHQ9cik7dmFyIG49MCxvPWZ1bmN0aW9uKCl7fTtyZXR1cm57czpvLG46ZnVuY3Rpb24oKXtyZXR1cm4gbj49dC5sZW5ndGg/e2RvbmU6ITB9Ontkb25lOiExLHZhbHVlOnRbbisrXX19LGU6ZnVuY3Rpb24odCl7dGhyb3cgdH0sZjpvfX10aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gaXRlcmF0ZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9dmFyIGksYT0hMCxjPSExO3JldHVybntzOmZ1bmN0aW9uKCl7cj1yLmNhbGwodCl9LG46ZnVuY3Rpb24oKXt2YXIgdD1yLm5leHQoKTtyZXR1cm4gYT10LmRvbmUsdH0sZTpmdW5jdGlvbih0KXtjPSEwLGk9dH0sZjpmdW5jdGlvbigpe3RyeXthfHxudWxsPT1yLnJldHVybnx8ci5yZXR1cm4oKX1maW5hbGx5e2lmKGMpdGhyb3cgaX19fX1mdW5jdGlvbiBOZSh0KXtyZXR1cm4gSWUodCl8fENlKHQpfHxBZSh0KXx8UmUoKX1mdW5jdGlvbiBSZSgpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBzcHJlYWQgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfWZ1bmN0aW9uIEFlKHQsZSl7aWYodCl7aWYoInN0cmluZyI9PT10eXBlb2YgdClyZXR1cm4gR2UodCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT9HZSh0LGUpOnZvaWQgMH19ZnVuY3Rpb24gQ2UodCl7aWYoInVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZudWxsIT10W1N5bWJvbC5pdGVyYXRvcl18fG51bGwhPXRbIkBAaXRlcmF0b3IiXSlyZXR1cm4gQXJyYXkuZnJvbSh0KX1mdW5jdGlvbiBJZSh0KXtpZihBcnJheS5pc0FycmF5KHQpKXJldHVybiBHZSh0KX1mdW5jdGlvbiBHZSh0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIEZlKHQsZSl7aWYoISh0IGluc3RhbmNlb2YgZSkpdGhyb3cgbmV3IFR5cGVFcnJvcigiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uIil9ZnVuY3Rpb24gVWUodCxlKXtmb3IodmFyIHI9MDtyPGUubGVuZ3RoO3IrKyl7dmFyIG49ZVtyXTtuLmVudW1lcmFibGU9bi5lbnVtZXJhYmxlfHwhMSxuLmNvbmZpZ3VyYWJsZT0hMCwidmFsdWUiaW4gbiYmKG4ud3JpdGFibGU9ITApLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LG4ua2V5LG4pfX1mdW5jdGlvbiBxZSh0LGUscil7cmV0dXJuIGUmJlVlKHQucHJvdG90eXBlLGUpLHImJlVlKHQsciksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsInByb3RvdHlwZSIse3dyaXRhYmxlOiExfSksdH14ZS5kZWZhdWx0UHJlY2FjaGVDYWNoZWFiaWxpdHlQbHVnaW49e2NhY2hlV2lsbFVwZGF0ZTpmdW5jdGlvbih0KXtyZXR1cm4gZmUodWUoKS5tYXJrKChmdW5jdGlvbiBlKCl7dmFyIHI7cmV0dXJuIHVlKCkud3JhcCgoZnVuY3Rpb24oZSl7d2hpbGUoMSlzd2l0Y2goZS5wcmV2PWUubmV4dCl7Y2FzZSAwOmlmKHI9dC5yZXNwb25zZSxyJiYhKHIuc3RhdHVzPj00MDApKXtlLm5leHQ9MzticmVha31yZXR1cm4gZS5hYnJ1cHQoInJldHVybiIsbnVsbCk7Y2FzZSAzOnJldHVybiBlLmFicnVwdCgicmV0dXJuIixyKTtjYXNlIDQ6Y2FzZSJlbmQiOnJldHVybiBlLnN0b3AoKX19KSxlKX0pKSkoKX19LHhlLmNvcHlSZWRpcmVjdGVkQ2FjaGVhYmxlUmVzcG9uc2VzUGx1Z2luPXtjYWNoZVdpbGxVcGRhdGU6ZnVuY3Rpb24odCl7cmV0dXJuIGZlKHVlKCkubWFyaygoZnVuY3Rpb24gZSgpe3ZhciByO3JldHVybiB1ZSgpLndyYXAoKGZ1bmN0aW9uKGUpe3doaWxlKDEpc3dpdGNoKGUucHJldj1lLm5leHQpe2Nhc2UgMDppZihyPXQucmVzcG9uc2UsIXIucmVkaXJlY3RlZCl7ZS5uZXh0PTc7YnJlYWt9cmV0dXJuIGUubmV4dD00LHJ0KHIpO2Nhc2UgNDplLnQwPWUuc2VudCxlLm5leHQ9ODticmVhaztjYXNlIDc6ZS50MD1yO2Nhc2UgODpyZXR1cm4gZS5hYnJ1cHQoInJldHVybiIsZS50MCk7Y2FzZSA5OmNhc2UiZW5kIjpyZXR1cm4gZS5zdG9wKCl9fSksZSl9KSkpKCl9fTt2YXIgS2UsTWU9ZnVuY3Rpb24oKXtmdW5jdGlvbiB0KCl7dmFyIGU9YXJndW1lbnRzLmxlbmd0aD4wJiZ2b2lkIDAhPT1hcmd1bWVudHNbMF0/YXJndW1lbnRzWzBdOnt9LHI9ZS5jYWNoZU5hbWUsbj1lLnBsdWdpbnMsbz12b2lkIDA9PT1uP1tdOm4saT1lLmZhbGxiYWNrVG9OZXR3b3JrLGE9dm9pZCAwPT09aXx8aTtGZSh0aGlzLHQpLHRoaXMuX3VybHNUb0NhY2hlS2V5cz1uZXcgTWFwLHRoaXMuX3VybHNUb0NhY2hlTW9kZXM9bmV3IE1hcCx0aGlzLl9jYWNoZUtleXNUb0ludGVncml0aWVzPW5ldyBNYXAsdGhpcy5fc3RyYXRlZ3k9bmV3IHhlKHtjYWNoZU5hbWU6RS5nZXRQcmVjYWNoZU5hbWUocikscGx1Z2luczpbXS5jb25jYXQoTmUobyksW25ldyBMdCh7cHJlY2FjaGVDb250cm9sbGVyOnRoaXN9KV0pLGZhbGxiYWNrVG9OZXR3b3JrOmF9KSx0aGlzLmluc3RhbGw9dGhpcy5pbnN0YWxsLmJpbmQodGhpcyksdGhpcy5hY3RpdmF0ZT10aGlzLmFjdGl2YXRlLmJpbmQodGhpcyl9cmV0dXJuIHFlKHQsW3trZXk6InN0cmF0ZWd5IixnZXQ6ZnVuY3Rpb24oKXtyZXR1cm4gdGhpcy5fc3RyYXRlZ3l9fSx7a2V5OiJwcmVjYWNoZSIsdmFsdWU6ZnVuY3Rpb24odCl7dGhpcy5hZGRUb0NhY2hlTGlzdCh0KSx0aGlzLl9pbnN0YWxsQW5kQWN0aXZlTGlzdGVuZXJzQWRkZWR8fChzZWxmLmFkZEV2ZW50TGlzdGVuZXIoImluc3RhbGwiLHRoaXMuaW5zdGFsbCksc2VsZi5hZGRFdmVudExpc3RlbmVyKCJhY3RpdmF0ZSIsdGhpcy5hY3RpdmF0ZSksdGhpcy5faW5zdGFsbEFuZEFjdGl2ZUxpc3RlbmVyc0FkZGVkPSEwKX19LHtrZXk6ImFkZFRvQ2FjaGVMaXN0Iix2YWx1ZTpmdW5jdGlvbih0KXt2YXIgZSxyPVtdLG49VGUodCk7dHJ5e2ZvcihuLnMoKTshKGU9bi5uKCkpLmRvbmU7KXt2YXIgbz1lLnZhbHVlOyJzdHJpbmciPT09dHlwZW9mIG8/ci5wdXNoKG8pOm8mJnZvaWQgMD09PW8ucmV2aXNpb24mJnIucHVzaChvLnVybCk7dmFyIGk9YXQobyksYT1pLmNhY2hlS2V5LGM9aS51cmwsdT0ic3RyaW5nIiE9PXR5cGVvZiBvJiZvLnJldmlzaW9uPyJyZWxvYWQiOiJkZWZhdWx0IjtpZih0aGlzLl91cmxzVG9DYWNoZUtleXMuaGFzKGMpJiZ0aGlzLl91cmxzVG9DYWNoZUtleXMuZ2V0KGMpIT09YSl0aHJvdyBuZXcgZygiYWRkLXRvLWNhY2hlLWxpc3QtY29uZmxpY3RpbmctZW50cmllcyIse2ZpcnN0RW50cnk6dGhpcy5fdXJsc1RvQ2FjaGVLZXlzLmdldChjKSxzZWNvbmRFbnRyeTphfSk7aWYoInN0cmluZyIhPT10eXBlb2YgbyYmby5pbnRlZ3JpdHkpe2lmKHRoaXMuX2NhY2hlS2V5c1RvSW50ZWdyaXRpZXMuaGFzKGEpJiZ0aGlzLl9jYWNoZUtleXNUb0ludGVncml0aWVzLmdldChhKSE9PW8uaW50ZWdyaXR5KXRocm93IG5ldyBnKCJhZGQtdG8tY2FjaGUtbGlzdC1jb25mbGljdGluZy1pbnRlZ3JpdGllcyIse3VybDpjfSk7dGhpcy5fY2FjaGVLZXlzVG9JbnRlZ3JpdGllcy5zZXQoYSxvLmludGVncml0eSl9aWYodGhpcy5fdXJsc1RvQ2FjaGVLZXlzLnNldChjLGEpLHRoaXMuX3VybHNUb0NhY2hlTW9kZXMuc2V0KGMsdSksci5sZW5ndGg+MCl7dmFyIHM9Ildvcmtib3ggaXMgcHJlY2FjaGluZyBVUkxzIHdpdGhvdXQgcmV2aXNpb24gIisiaW5mbzogIi5jb25jYXQoci5qb2luKCIsICIpLCJcblRoaXMgaXMgZ2VuZXJhbGx5IE5PVCBzYWZlLiAiKSsiTGVhcm4gbW9yZSBhdCBodHRwczovL2JpdC5seS93Yi1wcmVjYWNoZSI7Y29uc29sZS53YXJuKHMpfX19Y2F0Y2goZil7bi5lKGYpfWZpbmFsbHl7bi5mKCl9fX0se2tleToiaW5zdGFsbCIsdmFsdWU6ZnVuY3Rpb24odCl7dmFyIGU9dGhpcztyZXR1cm4gUSh0LFBlKGtlKCkubWFyaygoZnVuY3Rpb24gcigpe3ZhciBuLG8saSxhLGMsdSxzLGYsbCxoLHA7cmV0dXJuIGtlKCkud3JhcCgoZnVuY3Rpb24ocil7d2hpbGUoMSlzd2l0Y2goci5wcmV2PXIubmV4dCl7Y2FzZSAwOm49bmV3IHl0LGUuc3RyYXRlZ3kucGx1Z2lucy5wdXNoKG4pLG89VGUoZS5fdXJsc1RvQ2FjaGVLZXlzKSxyLnByZXY9MyxvLnMoKTtjYXNlIDU6aWYoKGk9by5uKCkpLmRvbmUpe3IubmV4dD0xNDticmVha31yZXR1cm4gYT1FZShpLnZhbHVlLDIpLGM9YVswXSx1PWFbMV0scz1lLl9jYWNoZUtleXNUb0ludGVncml0aWVzLmdldCh1KSxmPWUuX3VybHNUb0NhY2hlTW9kZXMuZ2V0KGMpLGw9bmV3IFJlcXVlc3QoYyx7aW50ZWdyaXR5OnMsY2FjaGU6ZixjcmVkZW50aWFsczoic2FtZS1vcmlnaW4ifSksci5uZXh0PTEyLFByb21pc2UuYWxsKGUuc3RyYXRlZ3kuaGFuZGxlQWxsKHtwYXJhbXM6e2NhY2hlS2V5OnV9LHJlcXVlc3Q6bCxldmVudDp0fSkpO2Nhc2UgMTI6ci5uZXh0PTU7YnJlYWs7Y2FzZSAxNDpyLm5leHQ9MTk7YnJlYWs7Y2FzZSAxNjpyLnByZXY9MTYsci50MD1yWyJjYXRjaCJdKDMpLG8uZShyLnQwKTtjYXNlIDE5OnJldHVybiByLnByZXY9MTksby5mKCksci5maW5pc2goMTkpO2Nhc2UgMjI6cmV0dXJuIGg9bi51cGRhdGVkVVJMcyxwPW4ubm90VXBkYXRlZFVSTHMsci5hYnJ1cHQoInJldHVybiIse3VwZGF0ZWRVUkxzOmgsbm90VXBkYXRlZFVSTHM6cH0pO2Nhc2UgMjU6Y2FzZSJlbmQiOnJldHVybiByLnN0b3AoKX19KSxyLG51bGwsW1szLDE2LDE5LDIyXV0pfSkpKSl9fSx7a2V5OiJhY3RpdmF0ZSIsdmFsdWU6ZnVuY3Rpb24odCl7dmFyIGU9dGhpcztyZXR1cm4gUSh0LFBlKGtlKCkubWFyaygoZnVuY3Rpb24gdCgpe3ZhciByLG4sbyxpLGEsYyx1O3JldHVybiBrZSgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDpyZXR1cm4gdC5uZXh0PTIsc2VsZi5jYWNoZXMub3BlbihlLnN0cmF0ZWd5LmNhY2hlTmFtZSk7Y2FzZSAyOnJldHVybiByPXQuc2VudCx0Lm5leHQ9NSxyLmtleXMoKTtjYXNlIDU6bj10LnNlbnQsbz1uZXcgU2V0KGUuX3VybHNUb0NhY2hlS2V5cy52YWx1ZXMoKSksaT1bXSxhPVRlKG4pLHQucHJldj05LGEucygpO2Nhc2UgMTE6aWYoKGM9YS5uKCkpLmRvbmUpe3QubmV4dD0xOTticmVha31pZih1PWMudmFsdWUsby5oYXModS51cmwpKXt0Lm5leHQ9MTc7YnJlYWt9cmV0dXJuIHQubmV4dD0xNixyLmRlbGV0ZSh1KTtjYXNlIDE2OmkucHVzaCh1LnVybCk7Y2FzZSAxNzp0Lm5leHQ9MTE7YnJlYWs7Y2FzZSAxOTp0Lm5leHQ9MjQ7YnJlYWs7Y2FzZSAyMTp0LnByZXY9MjEsdC50MD10WyJjYXRjaCJdKDkpLGEuZSh0LnQwKTtjYXNlIDI0OnJldHVybiB0LnByZXY9MjQsYS5mKCksdC5maW5pc2goMjQpO2Nhc2UgMjc6cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iLHtkZWxldGVkVVJMczppfSk7Y2FzZSAyOTpjYXNlImVuZCI6cmV0dXJuIHQuc3RvcCgpfX0pLHQsbnVsbCxbWzksMjEsMjQsMjddXSl9KSkpKX19LHtrZXk6ImdldFVSTHNUb0NhY2hlS2V5cyIsdmFsdWU6ZnVuY3Rpb24oKXtyZXR1cm4gdGhpcy5fdXJsc1RvQ2FjaGVLZXlzfX0se2tleToiZ2V0Q2FjaGVkVVJMcyIsdmFsdWU6ZnVuY3Rpb24oKXtyZXR1cm4gTmUodGhpcy5fdXJsc1RvQ2FjaGVLZXlzLmtleXMoKSl9fSx7a2V5OiJnZXRDYWNoZUtleUZvclVSTCIsdmFsdWU6ZnVuY3Rpb24odCl7dmFyIGU9bmV3IFVSTCh0LGxvY2F0aW9uLmhyZWYpO3JldHVybiB0aGlzLl91cmxzVG9DYWNoZUtleXMuZ2V0KGUuaHJlZil9fSx7a2V5OiJnZXRJbnRlZ3JpdHlGb3JDYWNoZUtleSIsdmFsdWU6ZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2NhY2hlS2V5c1RvSW50ZWdyaXRpZXMuZ2V0KHQpfX0se2tleToibWF0Y2hQcmVjYWNoZSIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgdD1QZShrZSgpLm1hcmsoKGZ1bmN0aW9uIHQoZSl7dmFyIHIsbixvO3JldHVybiBrZSgpLndyYXAoKGZ1bmN0aW9uKHQpe3doaWxlKDEpc3dpdGNoKHQucHJldj10Lm5leHQpe2Nhc2UgMDppZihyPWUgaW5zdGFuY2VvZiBSZXF1ZXN0P2UudXJsOmUsbj10aGlzLmdldENhY2hlS2V5Rm9yVVJMKHIpLCFuKXt0Lm5leHQ9NzticmVha31yZXR1cm4gdC5uZXh0PTUsc2VsZi5jYWNoZXMub3Blbih0aGlzLnN0cmF0ZWd5LmNhY2hlTmFtZSk7Y2FzZSA1OnJldHVybiBvPXQuc2VudCx0LmFicnVwdCgicmV0dXJuIixvLm1hdGNoKG4pKTtjYXNlIDc6cmV0dXJuIHQuYWJydXB0KCJyZXR1cm4iLHZvaWQgMCk7Y2FzZSA4OmNhc2UiZW5kIjpyZXR1cm4gdC5zdG9wKCl9fSksdCx0aGlzKX0pKSk7ZnVuY3Rpb24gZShlKXtyZXR1cm4gdC5hcHBseSh0aGlzLGFyZ3VtZW50cyl9cmV0dXJuIGV9KCl9LHtrZXk6ImNyZWF0ZUhhbmRsZXJCb3VuZFRvVVJMIix2YWx1ZTpmdW5jdGlvbih0KXt2YXIgZT10aGlzLHI9dGhpcy5nZXRDYWNoZUtleUZvclVSTCh0KTtpZighcil0aHJvdyBuZXcgZygibm9uLXByZWNhY2hlZC11cmwiLHt1cmw6dH0pO3JldHVybiBmdW5jdGlvbihuKXtyZXR1cm4gbi5yZXF1ZXN0PW5ldyBSZXF1ZXN0KHQpLG4ucGFyYW1zPU9iamVjdC5hc3NpZ24oe2NhY2hlS2V5OnJ9LG4ucGFyYW1zKSxlLnN0cmF0ZWd5LmhhbmRsZShuKX19fV0pLHR9KCksWWU9ZnVuY3Rpb24oKXtyZXR1cm4gS2V8fChLZT1uZXcgTWUpLEtlfTtyKDE4OCk7dmFyIFdlPSJHRVQiO2Z1bmN0aW9uIERlKHQpe3JldHVybiBEZT0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiYic3ltYm9sIj09dHlwZW9mIFN5bWJvbC5pdGVyYXRvcj9mdW5jdGlvbih0KXtyZXR1cm4gdHlwZW9mIHR9OmZ1bmN0aW9uKHQpe3JldHVybiB0JiYiZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiZ0LmNvbnN0cnVjdG9yPT09U3ltYm9sJiZ0IT09U3ltYm9sLnByb3RvdHlwZT8ic3ltYm9sIjp0eXBlb2YgdH0sRGUodCl9dmFyIEJlPWZ1bmN0aW9uKHQpe3JldHVybiB0JiYib2JqZWN0Ij09PURlKHQpP3Q6e2hhbmRsZTp0fX07ZnVuY3Rpb24gSGUodCxlKXtpZighKHQgaW5zdGFuY2VvZiBlKSl0aHJvdyBuZXcgVHlwZUVycm9yKCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb24iKX1mdW5jdGlvbiAkZSh0LGUpe2Zvcih2YXIgcj0wO3I8ZS5sZW5ndGg7cisrKXt2YXIgbj1lW3JdO24uZW51bWVyYWJsZT1uLmVudW1lcmFibGV8fCExLG4uY29uZmlndXJhYmxlPSEwLCJ2YWx1ZSJpbiBuJiYobi53cml0YWJsZT0hMCksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsbi5rZXksbil9fWZ1bmN0aW9uIFZlKHQsZSxyKXtyZXR1cm4gZSYmJGUodC5wcm90b3R5cGUsZSksciYmJGUodCxyKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSx0fXZhciB6ZT1mdW5jdGlvbigpe2Z1bmN0aW9uIHQoZSxyKXt2YXIgbj1hcmd1bWVudHMubGVuZ3RoPjImJnZvaWQgMCE9PWFyZ3VtZW50c1syXT9hcmd1bWVudHNbMl06V2U7SGUodGhpcyx0KSx0aGlzLmhhbmRsZXI9QmUociksdGhpcy5tYXRjaD1lLHRoaXMubWV0aG9kPW59cmV0dXJuIFZlKHQsW3trZXk6InNldENhdGNoSGFuZGxlciIsdmFsdWU6ZnVuY3Rpb24odCl7dGhpcy5jYXRjaEhhbmRsZXI9QmUodCl9fV0pLHR9KCk7ZnVuY3Rpb24gSmUodCl7cmV0dXJuIEplPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJiJzeW1ib2wiPT10eXBlb2YgU3ltYm9sLml0ZXJhdG9yP2Z1bmN0aW9uKHQpe3JldHVybiB0eXBlb2YgdH06ZnVuY3Rpb24odCl7cmV0dXJuIHQmJiJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2wmJnQuY29uc3RydWN0b3I9PT1TeW1ib2wmJnQhPT1TeW1ib2wucHJvdG90eXBlPyJzeW1ib2wiOnR5cGVvZiB0fSxKZSh0KX1mdW5jdGlvbiBRZSh0LGUpe2Zvcih2YXIgcj0wO3I8ZS5sZW5ndGg7cisrKXt2YXIgbj1lW3JdO24uZW51bWVyYWJsZT1uLmVudW1lcmFibGV8fCExLG4uY29uZmlndXJhYmxlPSEwLCJ2YWx1ZSJpbiBuJiYobi53cml0YWJsZT0hMCksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsbi5rZXksbil9fWZ1bmN0aW9uIFhlKHQsZSxyKXtyZXR1cm4gZSYmUWUodC5wcm90b3R5cGUsZSksciYmUWUodCxyKSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSx0fWZ1bmN0aW9uIFplKHQsZSl7aWYoISh0IGluc3RhbmNlb2YgZSkpdGhyb3cgbmV3IFR5cGVFcnJvcigiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uIil9ZnVuY3Rpb24gdHIodCxlKXtpZigiZnVuY3Rpb24iIT09dHlwZW9mIGUmJm51bGwhPT1lKXRocm93IG5ldyBUeXBlRXJyb3IoIlN1cGVyIGV4cHJlc3Npb24gbXVzdCBlaXRoZXIgYmUgbnVsbCBvciBhIGZ1bmN0aW9uIik7dC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShlJiZlLnByb3RvdHlwZSx7Y29uc3RydWN0b3I6e3ZhbHVlOnQsd3JpdGFibGU6ITAsY29uZmlndXJhYmxlOiEwfX0pLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LCJwcm90b3R5cGUiLHt3cml0YWJsZTohMX0pLGUmJmVyKHQsZSl9ZnVuY3Rpb24gZXIodCxlKXtyZXR1cm4gZXI9T2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5zZXRQcm90b3R5cGVPZi5iaW5kKCk6ZnVuY3Rpb24odCxlKXtyZXR1cm4gdC5fX3Byb3RvX189ZSx0fSxlcih0LGUpfWZ1bmN0aW9uIHJyKHQpe3ZhciBlPWlyKCk7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIHIsbj1hcih0KTtpZihlKXt2YXIgbz1hcih0aGlzKS5jb25zdHJ1Y3RvcjtyPVJlZmxlY3QuY29uc3RydWN0KG4sYXJndW1lbnRzLG8pfWVsc2Ugcj1uLmFwcGx5KHRoaXMsYXJndW1lbnRzKTtyZXR1cm4gbnIodGhpcyxyKX19ZnVuY3Rpb24gbnIodCxlKXtpZihlJiYoIm9iamVjdCI9PT1KZShlKXx8ImZ1bmN0aW9uIj09PXR5cGVvZiBlKSlyZXR1cm4gZTtpZih2b2lkIDAhPT1lKXRocm93IG5ldyBUeXBlRXJyb3IoIkRlcml2ZWQgY29uc3RydWN0b3JzIG1heSBvbmx5IHJldHVybiBvYmplY3Qgb3IgdW5kZWZpbmVkIik7cmV0dXJuIG9yKHQpfWZ1bmN0aW9uIG9yKHQpe2lmKHZvaWQgMD09PXQpdGhyb3cgbmV3IFJlZmVyZW5jZUVycm9yKCJ0aGlzIGhhc24ndCBiZWVuIGluaXRpYWxpc2VkIC0gc3VwZXIoKSBoYXNuJ3QgYmVlbiBjYWxsZWQiKTtyZXR1cm4gdH1mdW5jdGlvbiBpcigpe2lmKCJ1bmRlZmluZWQiPT09dHlwZW9mIFJlZmxlY3R8fCFSZWZsZWN0LmNvbnN0cnVjdClyZXR1cm4hMTtpZihSZWZsZWN0LmNvbnN0cnVjdC5zaGFtKXJldHVybiExO2lmKCJmdW5jdGlvbiI9PT10eXBlb2YgUHJveHkpcmV0dXJuITA7dHJ5e3JldHVybiBCb29sZWFuLnByb3RvdHlwZS52YWx1ZU9mLmNhbGwoUmVmbGVjdC5jb25zdHJ1Y3QoQm9vbGVhbixbXSwoZnVuY3Rpb24oKXt9KSkpLCEwfWNhdGNoKHQpe3JldHVybiExfX1mdW5jdGlvbiBhcih0KXtyZXR1cm4gYXI9T2JqZWN0LnNldFByb3RvdHlwZU9mP09iamVjdC5nZXRQcm90b3R5cGVPZi5iaW5kKCk6ZnVuY3Rpb24odCl7cmV0dXJuIHQuX19wcm90b19ffHxPYmplY3QuZ2V0UHJvdG90eXBlT2YodCl9LGFyKHQpfXZhciBjcj1mdW5jdGlvbih0KXt0cihyLHQpO3ZhciBlPXJyKHIpO2Z1bmN0aW9uIHIodCxuLG8pe1plKHRoaXMscik7dmFyIGk9ZnVuY3Rpb24oZSl7dmFyIHI9ZS51cmwsbj10LmV4ZWMoci5ocmVmKTtpZihuJiYoci5vcmlnaW49PT1sb2NhdGlvbi5vcmlnaW58fDA9PT1uLmluZGV4KSlyZXR1cm4gbi5zbGljZSgxKX07cmV0dXJuIGUuY2FsbCh0aGlzLGksbixvKX1yZXR1cm4gWGUocil9KHplKTtmdW5jdGlvbiB1cih0KXtyZXR1cm4gdXI9ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/ZnVuY3Rpb24odCl7cmV0dXJuIHR5cGVvZiB0fTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmdC5jb25zdHJ1Y3Rvcj09PVN5bWJvbCYmdCE9PVN5bWJvbC5wcm90b3R5cGU/InN5bWJvbCI6dHlwZW9mIHR9LHVyKHQpfWZ1bmN0aW9uIHNyKHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9Z3IodCkpfHxlJiZ0JiYibnVtYmVyIj09PXR5cGVvZiB0Lmxlbmd0aCl7ciYmKHQ9cik7dmFyIG49MCxvPWZ1bmN0aW9uKCl7fTtyZXR1cm57czpvLG46ZnVuY3Rpb24oKXtyZXR1cm4gbj49dC5sZW5ndGg/e2RvbmU6ITB9Ontkb25lOiExLHZhbHVlOnRbbisrXX19LGU6ZnVuY3Rpb24odCl7dGhyb3cgdH0sZjpvfX10aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gaXRlcmF0ZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9dmFyIGksYT0hMCxjPSExO3JldHVybntzOmZ1bmN0aW9uKCl7cj1yLmNhbGwodCl9LG46ZnVuY3Rpb24oKXt2YXIgdD1yLm5leHQoKTtyZXR1cm4gYT10LmRvbmUsdH0sZTpmdW5jdGlvbih0KXtjPSEwLGk9dH0sZjpmdW5jdGlvbigpe3RyeXthfHxudWxsPT1yLnJldHVybnx8ci5yZXR1cm4oKX1maW5hbGx5e2lmKGMpdGhyb3cgaX19fX1mdW5jdGlvbiBmcigpey8qISByZWdlbmVyYXRvci1ydW50aW1lIC0tIENvcHlyaWdodCAoYykgMjAxNC1wcmVzZW50LCBGYWNlYm9vaywgSW5jLiAtLSBsaWNlbnNlIChNSVQpOiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVnZW5lcmF0b3IvYmxvYi9tYWluL0xJQ0VOU0UgKi9mcj1mdW5jdGlvbigpe3JldHVybiB0fTt2YXIgdD17fSxlPU9iamVjdC5wcm90b3R5cGUscj1lLmhhc093blByb3BlcnR5LG49ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbD9TeW1ib2w6e30sbz1uLml0ZXJhdG9yfHwiQEBpdGVyYXRvciIsaT1uLmFzeW5jSXRlcmF0b3J8fCJAQGFzeW5jSXRlcmF0b3IiLGE9bi50b1N0cmluZ1RhZ3x8IkBAdG9TdHJpbmdUYWciO2Z1bmN0aW9uIGModCxlLHIpe3JldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkodCxlLHt2YWx1ZTpyLGVudW1lcmFibGU6ITAsY29uZmlndXJhYmxlOiEwLHdyaXRhYmxlOiEwfSksdFtlXX10cnl7Yyh7fSwiIil9Y2F0Y2goTyl7Yz1mdW5jdGlvbih0LGUscil7cmV0dXJuIHRbZV09cn19ZnVuY3Rpb24gdSh0LGUscixuKXt2YXIgbz1lJiZlLnByb3RvdHlwZSBpbnN0YW5jZW9mIGw/ZTpsLGk9T2JqZWN0LmNyZWF0ZShvLnByb3RvdHlwZSksYT1uZXcgayhufHxbXSk7cmV0dXJuIGkuX2ludm9rZT1mdW5jdGlvbih0LGUscil7dmFyIG49InN1c3BlbmRlZFN0YXJ0IjtyZXR1cm4gZnVuY3Rpb24obyxpKXtpZigiZXhlY3V0aW5nIj09PW4pdGhyb3cgbmV3IEVycm9yKCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBydW5uaW5nIik7aWYoImNvbXBsZXRlZCI9PT1uKXtpZigidGhyb3ciPT09byl0aHJvdyBpO3JldHVybiBfKCl9Zm9yKHIubWV0aG9kPW8sci5hcmc9aTs7KXt2YXIgYT1yLmRlbGVnYXRlO2lmKGEpe3ZhciBjPWIoYSxyKTtpZihjKXtpZihjPT09Ziljb250aW51ZTtyZXR1cm4gY319aWYoIm5leHQiPT09ci5tZXRob2Qpci5zZW50PXIuX3NlbnQ9ci5hcmc7ZWxzZSBpZigidGhyb3ciPT09ci5tZXRob2Qpe2lmKCJzdXNwZW5kZWRTdGFydCI9PT1uKXRocm93IG49ImNvbXBsZXRlZCIsci5hcmc7ci5kaXNwYXRjaEV4Y2VwdGlvbihyLmFyZyl9ZWxzZSJyZXR1cm4iPT09ci5tZXRob2QmJnIuYWJydXB0KCJyZXR1cm4iLHIuYXJnKTtuPSJleGVjdXRpbmciO3ZhciB1PXModCxlLHIpO2lmKCJub3JtYWwiPT09dS50eXBlKXtpZihuPXIuZG9uZT8iY29tcGxldGVkIjoic3VzcGVuZGVkWWllbGQiLHUuYXJnPT09Ziljb250aW51ZTtyZXR1cm57dmFsdWU6dS5hcmcsZG9uZTpyLmRvbmV9fSJ0aHJvdyI9PT11LnR5cGUmJihuPSJjb21wbGV0ZWQiLHIubWV0aG9kPSJ0aHJvdyIsci5hcmc9dS5hcmcpfX19KHQscixhKSxpfWZ1bmN0aW9uIHModCxlLHIpe3RyeXtyZXR1cm57dHlwZToibm9ybWFsIixhcmc6dC5jYWxsKGUscil9fWNhdGNoKE8pe3JldHVybnt0eXBlOiJ0aHJvdyIsYXJnOk99fX10LndyYXA9dTt2YXIgZj17fTtmdW5jdGlvbiBsKCl7fWZ1bmN0aW9uIGgoKXt9ZnVuY3Rpb24gcCgpe312YXIgeT17fTtjKHksbywoZnVuY3Rpb24oKXtyZXR1cm4gdGhpc30pKTt2YXIgdj1PYmplY3QuZ2V0UHJvdG90eXBlT2YsZD12JiZ2KHYoRShbXSkpKTtkJiZkIT09ZSYmci5jYWxsKGQsbykmJih5PWQpO3ZhciBtPXAucHJvdG90eXBlPWwucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoeSk7ZnVuY3Rpb24gZyh0KXtbIm5leHQiLCJ0aHJvdyIsInJldHVybiJdLmZvckVhY2goKGZ1bmN0aW9uKGUpe2ModCxlLChmdW5jdGlvbih0KXtyZXR1cm4gdGhpcy5faW52b2tlKGUsdCl9KSl9KSl9ZnVuY3Rpb24gdyh0LGUpe2Z1bmN0aW9uIG4obyxpLGEsYyl7dmFyIHU9cyh0W29dLHQsaSk7aWYoInRocm93IiE9PXUudHlwZSl7dmFyIGY9dS5hcmcsbD1mLnZhbHVlO3JldHVybiBsJiYib2JqZWN0Ij09dXIobCkmJnIuY2FsbChsLCJfX2F3YWl0Iik/ZS5yZXNvbHZlKGwuX19hd2FpdCkudGhlbigoZnVuY3Rpb24odCl7bigibmV4dCIsdCxhLGMpfSksKGZ1bmN0aW9uKHQpe24oInRocm93Iix0LGEsYyl9KSk6ZS5yZXNvbHZlKGwpLnRoZW4oKGZ1bmN0aW9uKHQpe2YudmFsdWU9dCxhKGYpfSksKGZ1bmN0aW9uKHQpe3JldHVybiBuKCJ0aHJvdyIsdCxhLGMpfSkpfWModS5hcmcpfXZhciBvO3RoaXMuX2ludm9rZT1mdW5jdGlvbih0LHIpe2Z1bmN0aW9uIGkoKXtyZXR1cm4gbmV3IGUoKGZ1bmN0aW9uKGUsbyl7bih0LHIsZSxvKX0pKX1yZXR1cm4gbz1vP28udGhlbihpLGkpOmkoKX19ZnVuY3Rpb24gYih0LGUpe3ZhciByPXQuaXRlcmF0b3JbZS5tZXRob2RdO2lmKHZvaWQgMD09PXIpe2lmKGUuZGVsZWdhdGU9bnVsbCwidGhyb3ciPT09ZS5tZXRob2Qpe2lmKHQuaXRlcmF0b3IucmV0dXJuJiYoZS5tZXRob2Q9InJldHVybiIsZS5hcmc9dm9pZCAwLGIodCxlKSwidGhyb3ciPT09ZS5tZXRob2QpKXJldHVybiBmO2UubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiVGhlIGl0ZXJhdG9yIGRvZXMgbm90IHByb3ZpZGUgYSAndGhyb3cnIG1ldGhvZCIpfXJldHVybiBmfXZhciBuPXMocix0Lml0ZXJhdG9yLGUuYXJnKTtpZigidGhyb3ciPT09bi50eXBlKXJldHVybiBlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW4uYXJnLGUuZGVsZWdhdGU9bnVsbCxmO3ZhciBvPW4uYXJnO3JldHVybiBvP28uZG9uZT8oZVt0LnJlc3VsdE5hbWVdPW8udmFsdWUsZS5uZXh0PXQubmV4dExvYywicmV0dXJuIiE9PWUubWV0aG9kJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksZS5kZWxlZ2F0ZT1udWxsLGYpOm86KGUubWV0aG9kPSJ0aHJvdyIsZS5hcmc9bmV3IFR5cGVFcnJvcigiaXRlcmF0b3IgcmVzdWx0IGlzIG5vdCBhbiBvYmplY3QiKSxlLmRlbGVnYXRlPW51bGwsZil9ZnVuY3Rpb24geCh0KXt2YXIgZT17dHJ5TG9jOnRbMF19OzEgaW4gdCYmKGUuY2F0Y2hMb2M9dFsxXSksMiBpbiB0JiYoZS5maW5hbGx5TG9jPXRbMl0sZS5hZnRlckxvYz10WzNdKSx0aGlzLnRyeUVudHJpZXMucHVzaChlKX1mdW5jdGlvbiBMKHQpe3ZhciBlPXQuY29tcGxldGlvbnx8e307ZS50eXBlPSJub3JtYWwiLGRlbGV0ZSBlLmFyZyx0LmNvbXBsZXRpb249ZX1mdW5jdGlvbiBrKHQpe3RoaXMudHJ5RW50cmllcz1be3RyeUxvYzoicm9vdCJ9XSx0LmZvckVhY2goeCx0aGlzKSx0aGlzLnJlc2V0KCEwKX1mdW5jdGlvbiBFKHQpe2lmKHQpe3ZhciBlPXRbb107aWYoZSlyZXR1cm4gZS5jYWxsKHQpO2lmKCJmdW5jdGlvbiI9PXR5cGVvZiB0Lm5leHQpcmV0dXJuIHQ7aWYoIWlzTmFOKHQubGVuZ3RoKSl7dmFyIG49LTEsaT1mdW5jdGlvbiBlKCl7Zm9yKDsrK248dC5sZW5ndGg7KWlmKHIuY2FsbCh0LG4pKXJldHVybiBlLnZhbHVlPXRbbl0sZS5kb25lPSExLGU7cmV0dXJuIGUudmFsdWU9dm9pZCAwLGUuZG9uZT0hMCxlfTtyZXR1cm4gaS5uZXh0PWl9fXJldHVybntuZXh0Ol99fWZ1bmN0aW9uIF8oKXtyZXR1cm57dmFsdWU6dm9pZCAwLGRvbmU6ITB9fXJldHVybiBoLnByb3RvdHlwZT1wLGMobSwiY29uc3RydWN0b3IiLHApLGMocCwiY29uc3RydWN0b3IiLGgpLGguZGlzcGxheU5hbWU9YyhwLGEsIkdlbmVyYXRvckZ1bmN0aW9uIiksdC5pc0dlbmVyYXRvckZ1bmN0aW9uPWZ1bmN0aW9uKHQpe3ZhciBlPSJmdW5jdGlvbiI9PXR5cGVvZiB0JiZ0LmNvbnN0cnVjdG9yO3JldHVybiEhZSYmKGU9PT1ofHwiR2VuZXJhdG9yRnVuY3Rpb24iPT09KGUuZGlzcGxheU5hbWV8fGUubmFtZSkpfSx0Lm1hcms9ZnVuY3Rpb24odCl7cmV0dXJuIE9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YodCxwKToodC5fX3Byb3RvX189cCxjKHQsYSwiR2VuZXJhdG9yRnVuY3Rpb24iKSksdC5wcm90b3R5cGU9T2JqZWN0LmNyZWF0ZShtKSx0fSx0LmF3cmFwPWZ1bmN0aW9uKHQpe3JldHVybntfX2F3YWl0OnR9fSxnKHcucHJvdG90eXBlKSxjKHcucHJvdG90eXBlLGksKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksdC5Bc3luY0l0ZXJhdG9yPXcsdC5hc3luYz1mdW5jdGlvbihlLHIsbixvLGkpe3ZvaWQgMD09PWkmJihpPVByb21pc2UpO3ZhciBhPW5ldyB3KHUoZSxyLG4sbyksaSk7cmV0dXJuIHQuaXNHZW5lcmF0b3JGdW5jdGlvbihyKT9hOmEubmV4dCgpLnRoZW4oKGZ1bmN0aW9uKHQpe3JldHVybiB0LmRvbmU/dC52YWx1ZTphLm5leHQoKX0pKX0sZyhtKSxjKG0sYSwiR2VuZXJhdG9yIiksYyhtLG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSksYyhtLCJ0b1N0cmluZyIsKGZ1bmN0aW9uKCl7cmV0dXJuIltvYmplY3QgR2VuZXJhdG9yXSJ9KSksdC5rZXlzPWZ1bmN0aW9uKHQpe3ZhciBlPVtdO2Zvcih2YXIgciBpbiB0KWUucHVzaChyKTtyZXR1cm4gZS5yZXZlcnNlKCksZnVuY3Rpb24gcigpe2Zvcig7ZS5sZW5ndGg7KXt2YXIgbj1lLnBvcCgpO2lmKG4gaW4gdClyZXR1cm4gci52YWx1ZT1uLHIuZG9uZT0hMSxyfXJldHVybiByLmRvbmU9ITAscn19LHQudmFsdWVzPUUsay5wcm90b3R5cGU9e2NvbnN0cnVjdG9yOmsscmVzZXQ6ZnVuY3Rpb24odCl7aWYodGhpcy5wcmV2PTAsdGhpcy5uZXh0PTAsdGhpcy5zZW50PXRoaXMuX3NlbnQ9dm9pZCAwLHRoaXMuZG9uZT0hMSx0aGlzLmRlbGVnYXRlPW51bGwsdGhpcy5tZXRob2Q9Im5leHQiLHRoaXMuYXJnPXZvaWQgMCx0aGlzLnRyeUVudHJpZXMuZm9yRWFjaChMKSwhdClmb3IodmFyIGUgaW4gdGhpcykidCI9PT1lLmNoYXJBdCgwKSYmci5jYWxsKHRoaXMsZSkmJiFpc05hTigrZS5zbGljZSgxKSkmJih0aGlzW2VdPXZvaWQgMCl9LHN0b3A6ZnVuY3Rpb24oKXt0aGlzLmRvbmU9ITA7dmFyIHQ9dGhpcy50cnlFbnRyaWVzWzBdLmNvbXBsZXRpb247aWYoInRocm93Ij09PXQudHlwZSl0aHJvdyB0LmFyZztyZXR1cm4gdGhpcy5ydmFsfSxkaXNwYXRjaEV4Y2VwdGlvbjpmdW5jdGlvbih0KXtpZih0aGlzLmRvbmUpdGhyb3cgdDt2YXIgZT10aGlzO2Z1bmN0aW9uIG4ocixuKXtyZXR1cm4gYS50eXBlPSJ0aHJvdyIsYS5hcmc9dCxlLm5leHQ9cixuJiYoZS5tZXRob2Q9Im5leHQiLGUuYXJnPXZvaWQgMCksISFufWZvcih2YXIgbz10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bz49MDstLW8pe3ZhciBpPXRoaXMudHJ5RW50cmllc1tvXSxhPWkuY29tcGxldGlvbjtpZigicm9vdCI9PT1pLnRyeUxvYylyZXR1cm4gbigiZW5kIik7aWYoaS50cnlMb2M8PXRoaXMucHJldil7dmFyIGM9ci5jYWxsKGksImNhdGNoTG9jIiksdT1yLmNhbGwoaSwiZmluYWxseUxvYyIpO2lmKGMmJnUpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX1lbHNlIGlmKGMpe2lmKHRoaXMucHJldjxpLmNhdGNoTG9jKXJldHVybiBuKGkuY2F0Y2hMb2MsITApfWVsc2V7aWYoIXUpdGhyb3cgbmV3IEVycm9yKCJ0cnkgc3RhdGVtZW50IHdpdGhvdXQgY2F0Y2ggb3IgZmluYWxseSIpO2lmKHRoaXMucHJldjxpLmZpbmFsbHlMb2MpcmV0dXJuIG4oaS5maW5hbGx5TG9jKX19fX0sYWJydXB0OmZ1bmN0aW9uKHQsZSl7Zm9yKHZhciBuPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtuPj0wOy0tbil7dmFyIG89dGhpcy50cnlFbnRyaWVzW25dO2lmKG8udHJ5TG9jPD10aGlzLnByZXYmJnIuY2FsbChvLCJmaW5hbGx5TG9jIikmJnRoaXMucHJldjxvLmZpbmFsbHlMb2Mpe3ZhciBpPW87YnJlYWt9fWkmJigiYnJlYWsiPT09dHx8ImNvbnRpbnVlIj09PXQpJiZpLnRyeUxvYzw9ZSYmZTw9aS5maW5hbGx5TG9jJiYoaT1udWxsKTt2YXIgYT1pP2kuY29tcGxldGlvbjp7fTtyZXR1cm4gYS50eXBlPXQsYS5hcmc9ZSxpPyh0aGlzLm1ldGhvZD0ibmV4dCIsdGhpcy5uZXh0PWkuZmluYWxseUxvYyxmKTp0aGlzLmNvbXBsZXRlKGEpfSxjb21wbGV0ZTpmdW5jdGlvbih0LGUpe2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuImJyZWFrIj09PXQudHlwZXx8ImNvbnRpbnVlIj09PXQudHlwZT90aGlzLm5leHQ9dC5hcmc6InJldHVybiI9PT10LnR5cGU/KHRoaXMucnZhbD10aGlzLmFyZz10LmFyZyx0aGlzLm1ldGhvZD0icmV0dXJuIix0aGlzLm5leHQ9ImVuZCIpOiJub3JtYWwiPT09dC50eXBlJiZlJiYodGhpcy5uZXh0PWUpLGZ9LGZpbmlzaDpmdW5jdGlvbih0KXtmb3IodmFyIGU9dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO2U+PTA7LS1lKXt2YXIgcj10aGlzLnRyeUVudHJpZXNbZV07aWYoci5maW5hbGx5TG9jPT09dClyZXR1cm4gdGhpcy5jb21wbGV0ZShyLmNvbXBsZXRpb24sci5hZnRlckxvYyksTChyKSxmfX0sY2F0Y2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIudHJ5TG9jPT09dCl7dmFyIG49ci5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT1uLnR5cGUpe3ZhciBvPW4uYXJnO0wocil9cmV0dXJuIG99fXRocm93IG5ldyBFcnJvcigiaWxsZWdhbCBjYXRjaCBhdHRlbXB0Iil9LGRlbGVnYXRlWWllbGQ6ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0aGlzLmRlbGVnYXRlPXtpdGVyYXRvcjpFKHQpLHJlc3VsdE5hbWU6ZSxuZXh0TG9jOnJ9LCJuZXh0Ij09PXRoaXMubWV0aG9kJiYodGhpcy5hcmc9dm9pZCAwKSxmfX0sdH1mdW5jdGlvbiBscih0LGUscixuLG8saSxhKXt0cnl7dmFyIGM9dFtpXShhKSx1PWMudmFsdWV9Y2F0Y2gocyl7cmV0dXJuIHZvaWQgcihzKX1jLmRvbmU/ZSh1KTpQcm9taXNlLnJlc29sdmUodSkudGhlbihuLG8pfWZ1bmN0aW9uIGhyKHQpe3JldHVybiBmdW5jdGlvbigpe3ZhciBlPXRoaXMscj1hcmd1bWVudHM7cmV0dXJuIG5ldyBQcm9taXNlKChmdW5jdGlvbihuLG8pe3ZhciBpPXQuYXBwbHkoZSxyKTtmdW5jdGlvbiBhKHQpe2xyKGksbixvLGEsYywibmV4dCIsdCl9ZnVuY3Rpb24gYyh0KXtscihpLG4sbyxhLGMsInRocm93Iix0KX1hKHZvaWQgMCl9KSl9fWZ1bmN0aW9uIHByKHQsZSxyKXtyZXR1cm4gcHI9eXIoKT9SZWZsZWN0LmNvbnN0cnVjdC5iaW5kKCk6ZnVuY3Rpb24odCxlLHIpe3ZhciBuPVtudWxsXTtuLnB1c2guYXBwbHkobixlKTt2YXIgbz1GdW5jdGlvbi5iaW5kLmFwcGx5KHQsbiksaT1uZXcgbztyZXR1cm4gciYmdnIoaSxyLnByb3RvdHlwZSksaX0scHIuYXBwbHkobnVsbCxhcmd1bWVudHMpfWZ1bmN0aW9uIHlyKCl7aWYoInVuZGVmaW5lZCI9PT10eXBlb2YgUmVmbGVjdHx8IVJlZmxlY3QuY29uc3RydWN0KXJldHVybiExO2lmKFJlZmxlY3QuY29uc3RydWN0LnNoYW0pcmV0dXJuITE7aWYoImZ1bmN0aW9uIj09PXR5cGVvZiBQcm94eSlyZXR1cm4hMDt0cnl7cmV0dXJuIEJvb2xlYW4ucHJvdG90eXBlLnZhbHVlT2YuY2FsbChSZWZsZWN0LmNvbnN0cnVjdChCb29sZWFuLFtdLChmdW5jdGlvbigpe30pKSksITB9Y2F0Y2godCl7cmV0dXJuITF9fWZ1bmN0aW9uIHZyKHQsZSl7cmV0dXJuIHZyPU9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YuYmluZCgpOmZ1bmN0aW9uKHQsZSl7cmV0dXJuIHQuX19wcm90b19fPWUsdH0sdnIodCxlKX1mdW5jdGlvbiBkcih0KXtyZXR1cm4gYnIodCl8fHdyKHQpfHxncih0KXx8bXIoKX1mdW5jdGlvbiBtcigpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBzcHJlYWQgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfWZ1bmN0aW9uIGdyKHQsZSl7aWYodCl7aWYoInN0cmluZyI9PT10eXBlb2YgdClyZXR1cm4geHIodCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT94cih0LGUpOnZvaWQgMH19ZnVuY3Rpb24gd3IodCl7aWYoInVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZudWxsIT10W1N5bWJvbC5pdGVyYXRvcl18fG51bGwhPXRbIkBAaXRlcmF0b3IiXSlyZXR1cm4gQXJyYXkuZnJvbSh0KX1mdW5jdGlvbiBicih0KXtpZihBcnJheS5pc0FycmF5KHQpKXJldHVybiB4cih0KX1mdW5jdGlvbiB4cih0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIExyKHQsZSl7aWYoISh0IGluc3RhbmNlb2YgZSkpdGhyb3cgbmV3IFR5cGVFcnJvcigiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uIil9ZnVuY3Rpb24ga3IodCxlKXtmb3IodmFyIHI9MDtyPGUubGVuZ3RoO3IrKyl7dmFyIG49ZVtyXTtuLmVudW1lcmFibGU9bi5lbnVtZXJhYmxlfHwhMSxuLmNvbmZpZ3VyYWJsZT0hMCwidmFsdWUiaW4gbiYmKG4ud3JpdGFibGU9ITApLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LG4ua2V5LG4pfX1mdW5jdGlvbiBFcih0LGUscil7cmV0dXJuIGUmJmtyKHQucHJvdG90eXBlLGUpLHImJmtyKHQsciksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsInByb3RvdHlwZSIse3dyaXRhYmxlOiExfSksdH12YXIgX3IsT3I9ZnVuY3Rpb24oKXtmdW5jdGlvbiB0KCl7THIodGhpcyx0KSx0aGlzLl9yb3V0ZXM9bmV3IE1hcCx0aGlzLl9kZWZhdWx0SGFuZGxlck1hcD1uZXcgTWFwfXJldHVybiBFcih0LFt7a2V5OiJyb3V0ZXMiLGdldDpmdW5jdGlvbigpe3JldHVybiB0aGlzLl9yb3V0ZXN9fSx7a2V5OiJhZGRGZXRjaExpc3RlbmVyIix2YWx1ZTpmdW5jdGlvbigpe3ZhciB0PXRoaXM7c2VsZi5hZGRFdmVudExpc3RlbmVyKCJmZXRjaCIsKGZ1bmN0aW9uKGUpe3ZhciByPWUucmVxdWVzdCxuPXQuaGFuZGxlUmVxdWVzdCh7cmVxdWVzdDpyLGV2ZW50OmV9KTtuJiZlLnJlc3BvbmRXaXRoKG4pfSkpfX0se2tleToiYWRkQ2FjaGVMaXN0ZW5lciIsdmFsdWU6ZnVuY3Rpb24oKXt2YXIgdD10aGlzO3NlbGYuYWRkRXZlbnRMaXN0ZW5lcigibWVzc2FnZSIsKGZ1bmN0aW9uKGUpe2lmKGUuZGF0YSYmIkNBQ0hFX1VSTFMiPT09ZS5kYXRhLnR5cGUpe3ZhciByPWUuZGF0YS5wYXlsb2FkOzA7dmFyIG49UHJvbWlzZS5hbGwoci51cmxzVG9DYWNoZS5tYXAoKGZ1bmN0aW9uKHIpeyJzdHJpbmciPT09dHlwZW9mIHImJihyPVtyXSk7dmFyIG49cHIoUmVxdWVzdCxkcihyKSk7cmV0dXJuIHQuaGFuZGxlUmVxdWVzdCh7cmVxdWVzdDpuLGV2ZW50OmV9KX0pKSk7ZS53YWl0VW50aWwobiksZS5wb3J0cyYmZS5wb3J0c1swXSYmbi50aGVuKChmdW5jdGlvbigpe3JldHVybiBlLnBvcnRzWzBdLnBvc3RNZXNzYWdlKCEwKX0pKX19KSl9fSx7a2V5OiJoYW5kbGVSZXF1ZXN0Iix2YWx1ZTpmdW5jdGlvbih0KXt2YXIgZT10aGlzLHI9dC5yZXF1ZXN0LG49dC5ldmVudDt2YXIgbz1uZXcgVVJMKHIudXJsLGxvY2F0aW9uLmhyZWYpO2lmKG8ucHJvdG9jb2wuc3RhcnRzV2l0aCgiaHR0cCIpKXt2YXIgaT1vLm9yaWdpbj09PWxvY2F0aW9uLm9yaWdpbixhPXRoaXMuZmluZE1hdGNoaW5nUm91dGUoe2V2ZW50Om4scmVxdWVzdDpyLHNhbWVPcmlnaW46aSx1cmw6b30pLGM9YS5wYXJhbXMsdT1hLnJvdXRlLHM9dSYmdS5oYW5kbGVyOzA7dmFyIGY9ci5tZXRob2Q7aWYoIXMmJnRoaXMuX2RlZmF1bHRIYW5kbGVyTWFwLmhhcyhmKSYmKHM9dGhpcy5fZGVmYXVsdEhhbmRsZXJNYXAuZ2V0KGYpKSxzKXt2YXIgbDswO3RyeXtsPXMuaGFuZGxlKHt1cmw6byxyZXF1ZXN0OnIsZXZlbnQ6bixwYXJhbXM6Y30pfWNhdGNoKHApe2w9UHJvbWlzZS5yZWplY3QocCl9dmFyIGg9dSYmdS5jYXRjaEhhbmRsZXI7cmV0dXJuIGwgaW5zdGFuY2VvZiBQcm9taXNlJiYodGhpcy5fY2F0Y2hIYW5kbGVyfHxoKSYmKGw9bC5jYXRjaChmdW5jdGlvbigpe3ZhciB0PWhyKGZyKCkubWFyaygoZnVuY3Rpb24gdChpKXtyZXR1cm4gZnIoKS53cmFwKChmdW5jdGlvbih0KXt3aGlsZSgxKXN3aXRjaCh0LnByZXY9dC5uZXh0KXtjYXNlIDA6aWYoIWgpe3QubmV4dD0xMTticmVha31yZXR1cm4gdC5wcmV2PTIsdC5uZXh0PTUsaC5oYW5kbGUoe3VybDpvLHJlcXVlc3Q6cixldmVudDpuLHBhcmFtczpjfSk7Y2FzZSA1OnJldHVybiB0LmFicnVwdCgicmV0dXJuIix0LnNlbnQpO2Nhc2UgODp0LnByZXY9OCx0LnQwPXRbImNhdGNoIl0oMiksdC50MCBpbnN0YW5jZW9mIEVycm9yJiYoaT10LnQwKTtjYXNlIDExOmlmKCFlLl9jYXRjaEhhbmRsZXIpe3QubmV4dD0xNDticmVha31yZXR1cm4gdC5hYnJ1cHQoInJldHVybiIsZS5fY2F0Y2hIYW5kbGVyLmhhbmRsZSh7dXJsOm8scmVxdWVzdDpyLGV2ZW50Om59KSk7Y2FzZSAxNDp0aHJvdyBpO2Nhc2UgMTU6Y2FzZSJlbmQiOnJldHVybiB0LnN0b3AoKX19KSx0LG51bGwsW1syLDhdXSl9KSkpO3JldHVybiBmdW5jdGlvbihlKXtyZXR1cm4gdC5hcHBseSh0aGlzLGFyZ3VtZW50cyl9fSgpKSksbH19fX0se2tleToiZmluZE1hdGNoaW5nUm91dGUiLHZhbHVlOmZ1bmN0aW9uKHQpe3ZhciBlLHI9dC51cmwsbj10LnNhbWVPcmlnaW4sbz10LnJlcXVlc3QsaT10LmV2ZW50LGE9dGhpcy5fcm91dGVzLmdldChvLm1ldGhvZCl8fFtdLGM9c3IoYSk7dHJ5e2ZvcihjLnMoKTshKGU9Yy5uKCkpLmRvbmU7KXt2YXIgdT1lLnZhbHVlLHM9dm9pZCAwLGY9dS5tYXRjaCh7dXJsOnIsc2FtZU9yaWdpbjpuLHJlcXVlc3Q6byxldmVudDppfSk7aWYoZilyZXR1cm4gcz1mLChBcnJheS5pc0FycmF5KHMpJiYwPT09cy5sZW5ndGh8fGYuY29uc3RydWN0b3I9PT1PYmplY3QmJjA9PT1PYmplY3Qua2V5cyhmKS5sZW5ndGh8fCJib29sZWFuIj09PXR5cGVvZiBmKSYmKHM9dm9pZCAwKSx7cm91dGU6dSxwYXJhbXM6c319fWNhdGNoKGwpe2MuZShsKX1maW5hbGx5e2MuZigpfXJldHVybnt9fX0se2tleToic2V0RGVmYXVsdEhhbmRsZXIiLHZhbHVlOmZ1bmN0aW9uKHQpe3ZhciBlPWFyZ3VtZW50cy5sZW5ndGg+MSYmdm9pZCAwIT09YXJndW1lbnRzWzFdP2FyZ3VtZW50c1sxXTpXZTt0aGlzLl9kZWZhdWx0SGFuZGxlck1hcC5zZXQoZSxCZSh0KSl9fSx7a2V5OiJzZXRDYXRjaEhhbmRsZXIiLHZhbHVlOmZ1bmN0aW9uKHQpe3RoaXMuX2NhdGNoSGFuZGxlcj1CZSh0KX19LHtrZXk6InJlZ2lzdGVyUm91dGUiLHZhbHVlOmZ1bmN0aW9uKHQpe3RoaXMuX3JvdXRlcy5oYXModC5tZXRob2QpfHx0aGlzLl9yb3V0ZXMuc2V0KHQubWV0aG9kLFtdKSx0aGlzLl9yb3V0ZXMuZ2V0KHQubWV0aG9kKS5wdXNoKHQpfX0se2tleToidW5yZWdpc3RlclJvdXRlIix2YWx1ZTpmdW5jdGlvbih0KXtpZighdGhpcy5fcm91dGVzLmhhcyh0Lm1ldGhvZCkpdGhyb3cgbmV3IGcoInVucmVnaXN0ZXItcm91dGUtYnV0LW5vdC1mb3VuZC13aXRoLW1ldGhvZCIse21ldGhvZDp0Lm1ldGhvZH0pO3ZhciBlPXRoaXMuX3JvdXRlcy5nZXQodC5tZXRob2QpLmluZGV4T2YodCk7aWYoIShlPi0xKSl0aHJvdyBuZXcgZygidW5yZWdpc3Rlci1yb3V0ZS1yb3V0ZS1ub3QtcmVnaXN0ZXJlZCIpO3RoaXMuX3JvdXRlcy5nZXQodC5tZXRob2QpLnNwbGljZShlLDEpfX1dKSx0fSgpLFNyPWZ1bmN0aW9uKCl7cmV0dXJuIF9yfHwoX3I9bmV3IE9yLF9yLmFkZEZldGNoTGlzdGVuZXIoKSxfci5hZGRDYWNoZUxpc3RlbmVyKCkpLF9yfTtmdW5jdGlvbiBqcih0LGUscil7dmFyIG47aWYoInN0cmluZyI9PT10eXBlb2YgdCl7dmFyIG89bmV3IFVSTCh0LGxvY2F0aW9uLmhyZWYpLGk9ZnVuY3Rpb24odCl7dmFyIGU9dC51cmw7cmV0dXJuIGUuaHJlZj09PW8uaHJlZn07bj1uZXcgemUoaSxlLHIpfWVsc2UgaWYodCBpbnN0YW5jZW9mIFJlZ0V4cCluPW5ldyBjcih0LGUscik7ZWxzZSBpZigiZnVuY3Rpb24iPT09dHlwZW9mIHQpbj1uZXcgemUodCxlLHIpO2Vsc2V7aWYoISh0IGluc3RhbmNlb2YgemUpKXRocm93IG5ldyBnKCJ1bnN1cHBvcnRlZC1yb3V0ZS10eXBlIix7bW9kdWxlTmFtZToid29ya2JveC1yb3V0aW5nIixmdW5jTmFtZToicmVnaXN0ZXJSb3V0ZSIscGFyYW1OYW1lOiJjYXB0dXJlIn0pO249dH12YXIgYT1TcigpO3JldHVybiBhLnJlZ2lzdGVyUm91dGUobiksbn1mdW5jdGlvbiBQcih0KXtyZXR1cm4gQXIodCl8fFJyKHQpfHxOcih0KXx8VHIoKX1mdW5jdGlvbiBUcigpe3Rocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBzcHJlYWQgbm9uLWl0ZXJhYmxlIGluc3RhbmNlLlxuSW4gb3JkZXIgdG8gYmUgaXRlcmFibGUsIG5vbi1hcnJheSBvYmplY3RzIG11c3QgaGF2ZSBhIFtTeW1ib2wuaXRlcmF0b3JdKCkgbWV0aG9kLiIpfWZ1bmN0aW9uIE5yKHQsZSl7aWYodCl7aWYoInN0cmluZyI9PT10eXBlb2YgdClyZXR1cm4gQ3IodCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT9Dcih0LGUpOnZvaWQgMH19ZnVuY3Rpb24gUnIodCl7aWYoInVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZudWxsIT10W1N5bWJvbC5pdGVyYXRvcl18fG51bGwhPXRbIkBAaXRlcmF0b3IiXSlyZXR1cm4gQXJyYXkuZnJvbSh0KX1mdW5jdGlvbiBBcih0KXtpZihBcnJheS5pc0FycmF5KHQpKXJldHVybiBDcih0KX1mdW5jdGlvbiBDcih0LGUpeyhudWxsPT1lfHxlPnQubGVuZ3RoKSYmKGU9dC5sZW5ndGgpO2Zvcih2YXIgcj0wLG49bmV3IEFycmF5KGUpO3I8ZTtyKyspbltyXT10W3JdO3JldHVybiBufWZ1bmN0aW9uIElyKHQpe2Zvcih2YXIgZT1hcmd1bWVudHMubGVuZ3RoPjEmJnZvaWQgMCE9PWFyZ3VtZW50c1sxXT9hcmd1bWVudHNbMV06W10scj1mdW5jdGlvbigpe3ZhciByPW9bbl07ZS5zb21lKChmdW5jdGlvbih0KXtyZXR1cm4gdC50ZXN0KHIpfSkpJiZ0LnNlYXJjaFBhcmFtcy5kZWxldGUocil9LG49MCxvPVByKHQuc2VhcmNoUGFyYW1zLmtleXMoKSk7bjxvLmxlbmd0aDtuKyspcigpO3JldHVybiB0fWZ1bmN0aW9uIEdyKHQpe3JldHVybiBHcj0iZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiYic3ltYm9sIj09dHlwZW9mIFN5bWJvbC5pdGVyYXRvcj9mdW5jdGlvbih0KXtyZXR1cm4gdHlwZW9mIHR9OmZ1bmN0aW9uKHQpe3JldHVybiB0JiYiZnVuY3Rpb24iPT10eXBlb2YgU3ltYm9sJiZ0LmNvbnN0cnVjdG9yPT09U3ltYm9sJiZ0IT09U3ltYm9sLnByb3RvdHlwZT8ic3ltYm9sIjp0eXBlb2YgdH0sR3IodCl9ZnVuY3Rpb24gRnIoKXsvKiEgcmVnZW5lcmF0b3ItcnVudGltZSAtLSBDb3B5cmlnaHQgKGMpIDIwMTQtcHJlc2VudCwgRmFjZWJvb2ssIEluYy4gLS0gbGljZW5zZSAoTUlUKTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlZ2VuZXJhdG9yL2Jsb2IvbWFpbi9MSUNFTlNFICovRnI9ZnVuY3Rpb24oKXtyZXR1cm4gdH07dmFyIHQ9e30sZT1PYmplY3QucHJvdG90eXBlLHI9ZS5oYXNPd25Qcm9wZXJ0eSxuPSJmdW5jdGlvbiI9PXR5cGVvZiBTeW1ib2w/U3ltYm9sOnt9LG89bi5pdGVyYXRvcnx8IkBAaXRlcmF0b3IiLGk9bi5hc3luY0l0ZXJhdG9yfHwiQEBhc3luY0l0ZXJhdG9yIixhPW4udG9TdHJpbmdUYWd8fCJAQHRvU3RyaW5nVGFnIjtmdW5jdGlvbiBjKHQsZSxyKXtyZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsZSx7dmFsdWU6cixlbnVtZXJhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMCx3cml0YWJsZTohMH0pLHRbZV19dHJ5e2Moe30sIiIpfWNhdGNoKE8pe2M9ZnVuY3Rpb24odCxlLHIpe3JldHVybiB0W2VdPXJ9fWZ1bmN0aW9uIHUodCxlLHIsbil7dmFyIG89ZSYmZS5wcm90b3R5cGUgaW5zdGFuY2VvZiBsP2U6bCxpPU9iamVjdC5jcmVhdGUoby5wcm90b3R5cGUpLGE9bmV3IGsobnx8W10pO3JldHVybiBpLl9pbnZva2U9ZnVuY3Rpb24odCxlLHIpe3ZhciBuPSJzdXNwZW5kZWRTdGFydCI7cmV0dXJuIGZ1bmN0aW9uKG8saSl7aWYoImV4ZWN1dGluZyI9PT1uKXRocm93IG5ldyBFcnJvcigiR2VuZXJhdG9yIGlzIGFscmVhZHkgcnVubmluZyIpO2lmKCJjb21wbGV0ZWQiPT09bil7aWYoInRocm93Ij09PW8pdGhyb3cgaTtyZXR1cm4gXygpfWZvcihyLm1ldGhvZD1vLHIuYXJnPWk7Oyl7dmFyIGE9ci5kZWxlZ2F0ZTtpZihhKXt2YXIgYz1iKGEscik7aWYoYyl7aWYoYz09PWYpY29udGludWU7cmV0dXJuIGN9fWlmKCJuZXh0Ij09PXIubWV0aG9kKXIuc2VudD1yLl9zZW50PXIuYXJnO2Vsc2UgaWYoInRocm93Ij09PXIubWV0aG9kKXtpZigic3VzcGVuZGVkU3RhcnQiPT09bil0aHJvdyBuPSJjb21wbGV0ZWQiLHIuYXJnO3IuZGlzcGF0Y2hFeGNlcHRpb24oci5hcmcpfWVsc2UicmV0dXJuIj09PXIubWV0aG9kJiZyLmFicnVwdCgicmV0dXJuIixyLmFyZyk7bj0iZXhlY3V0aW5nIjt2YXIgdT1zKHQsZSxyKTtpZigibm9ybWFsIj09PXUudHlwZSl7aWYobj1yLmRvbmU/ImNvbXBsZXRlZCI6InN1c3BlbmRlZFlpZWxkIix1LmFyZz09PWYpY29udGludWU7cmV0dXJue3ZhbHVlOnUuYXJnLGRvbmU6ci5kb25lfX0idGhyb3ciPT09dS50eXBlJiYobj0iY29tcGxldGVkIixyLm1ldGhvZD0idGhyb3ciLHIuYXJnPXUuYXJnKX19fSh0LHIsYSksaX1mdW5jdGlvbiBzKHQsZSxyKXt0cnl7cmV0dXJue3R5cGU6Im5vcm1hbCIsYXJnOnQuY2FsbChlLHIpfX1jYXRjaChPKXtyZXR1cm57dHlwZToidGhyb3ciLGFyZzpPfX19dC53cmFwPXU7dmFyIGY9e307ZnVuY3Rpb24gbCgpe31mdW5jdGlvbiBoKCl7fWZ1bmN0aW9uIHAoKXt9dmFyIHk9e307Yyh5LG8sKGZ1bmN0aW9uKCl7cmV0dXJuIHRoaXN9KSk7dmFyIHY9T2JqZWN0LmdldFByb3RvdHlwZU9mLGQ9diYmdih2KEUoW10pKSk7ZCYmZCE9PWUmJnIuY2FsbChkLG8pJiYoeT1kKTt2YXIgbT1wLnByb3RvdHlwZT1sLnByb3RvdHlwZT1PYmplY3QuY3JlYXRlKHkpO2Z1bmN0aW9uIGcodCl7WyJuZXh0IiwidGhyb3ciLCJyZXR1cm4iXS5mb3JFYWNoKChmdW5jdGlvbihlKXtjKHQsZSwoZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuX2ludm9rZShlLHQpfSkpfSkpfWZ1bmN0aW9uIHcodCxlKXtmdW5jdGlvbiBuKG8saSxhLGMpe3ZhciB1PXModFtvXSx0LGkpO2lmKCJ0aHJvdyIhPT11LnR5cGUpe3ZhciBmPXUuYXJnLGw9Zi52YWx1ZTtyZXR1cm4gbCYmIm9iamVjdCI9PUdyKGwpJiZyLmNhbGwobCwiX19hd2FpdCIpP2UucmVzb2x2ZShsLl9fYXdhaXQpLnRoZW4oKGZ1bmN0aW9uKHQpe24oIm5leHQiLHQsYSxjKX0pLChmdW5jdGlvbih0KXtuKCJ0aHJvdyIsdCxhLGMpfSkpOmUucmVzb2x2ZShsKS50aGVuKChmdW5jdGlvbih0KXtmLnZhbHVlPXQsYShmKX0pLChmdW5jdGlvbih0KXtyZXR1cm4gbigidGhyb3ciLHQsYSxjKX0pKX1jKHUuYXJnKX12YXIgbzt0aGlzLl9pbnZva2U9ZnVuY3Rpb24odCxyKXtmdW5jdGlvbiBpKCl7cmV0dXJuIG5ldyBlKChmdW5jdGlvbihlLG8pe24odCxyLGUsbyl9KSl9cmV0dXJuIG89bz9vLnRoZW4oaSxpKTppKCl9fWZ1bmN0aW9uIGIodCxlKXt2YXIgcj10Lml0ZXJhdG9yW2UubWV0aG9kXTtpZih2b2lkIDA9PT1yKXtpZihlLmRlbGVnYXRlPW51bGwsInRocm93Ij09PWUubWV0aG9kKXtpZih0Lml0ZXJhdG9yLnJldHVybiYmKGUubWV0aG9kPSJyZXR1cm4iLGUuYXJnPXZvaWQgMCxiKHQsZSksInRocm93Ij09PWUubWV0aG9kKSlyZXR1cm4gZjtlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIlRoZSBpdGVyYXRvciBkb2VzIG5vdCBwcm92aWRlIGEgJ3Rocm93JyBtZXRob2QiKX1yZXR1cm4gZn12YXIgbj1zKHIsdC5pdGVyYXRvcixlLmFyZyk7aWYoInRocm93Ij09PW4udHlwZSlyZXR1cm4gZS5tZXRob2Q9InRocm93IixlLmFyZz1uLmFyZyxlLmRlbGVnYXRlPW51bGwsZjt2YXIgbz1uLmFyZztyZXR1cm4gbz9vLmRvbmU/KGVbdC5yZXN1bHROYW1lXT1vLnZhbHVlLGUubmV4dD10Lm5leHRMb2MsInJldHVybiIhPT1lLm1ldGhvZCYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLGUuZGVsZWdhdGU9bnVsbCxmKTpvOihlLm1ldGhvZD0idGhyb3ciLGUuYXJnPW5ldyBUeXBlRXJyb3IoIml0ZXJhdG9yIHJlc3VsdCBpcyBub3QgYW4gb2JqZWN0IiksZS5kZWxlZ2F0ZT1udWxsLGYpfWZ1bmN0aW9uIHgodCl7dmFyIGU9e3RyeUxvYzp0WzBdfTsxIGluIHQmJihlLmNhdGNoTG9jPXRbMV0pLDIgaW4gdCYmKGUuZmluYWxseUxvYz10WzJdLGUuYWZ0ZXJMb2M9dFszXSksdGhpcy50cnlFbnRyaWVzLnB1c2goZSl9ZnVuY3Rpb24gTCh0KXt2YXIgZT10LmNvbXBsZXRpb258fHt9O2UudHlwZT0ibm9ybWFsIixkZWxldGUgZS5hcmcsdC5jb21wbGV0aW9uPWV9ZnVuY3Rpb24gayh0KXt0aGlzLnRyeUVudHJpZXM9W3t0cnlMb2M6InJvb3QifV0sdC5mb3JFYWNoKHgsdGhpcyksdGhpcy5yZXNldCghMCl9ZnVuY3Rpb24gRSh0KXtpZih0KXt2YXIgZT10W29dO2lmKGUpcmV0dXJuIGUuY2FsbCh0KTtpZigiZnVuY3Rpb24iPT10eXBlb2YgdC5uZXh0KXJldHVybiB0O2lmKCFpc05hTih0Lmxlbmd0aCkpe3ZhciBuPS0xLGk9ZnVuY3Rpb24gZSgpe2Zvcig7KytuPHQubGVuZ3RoOylpZihyLmNhbGwodCxuKSlyZXR1cm4gZS52YWx1ZT10W25dLGUuZG9uZT0hMSxlO3JldHVybiBlLnZhbHVlPXZvaWQgMCxlLmRvbmU9ITAsZX07cmV0dXJuIGkubmV4dD1pfX1yZXR1cm57bmV4dDpffX1mdW5jdGlvbiBfKCl7cmV0dXJue3ZhbHVlOnZvaWQgMCxkb25lOiEwfX1yZXR1cm4gaC5wcm90b3R5cGU9cCxjKG0sImNvbnN0cnVjdG9yIixwKSxjKHAsImNvbnN0cnVjdG9yIixoKSxoLmRpc3BsYXlOYW1lPWMocCxhLCJHZW5lcmF0b3JGdW5jdGlvbiIpLHQuaXNHZW5lcmF0b3JGdW5jdGlvbj1mdW5jdGlvbih0KXt2YXIgZT0iZnVuY3Rpb24iPT10eXBlb2YgdCYmdC5jb25zdHJ1Y3RvcjtyZXR1cm4hIWUmJihlPT09aHx8IkdlbmVyYXRvckZ1bmN0aW9uIj09PShlLmRpc3BsYXlOYW1lfHxlLm5hbWUpKX0sdC5tYXJrPWZ1bmN0aW9uKHQpe3JldHVybiBPYmplY3Quc2V0UHJvdG90eXBlT2Y/T2JqZWN0LnNldFByb3RvdHlwZU9mKHQscCk6KHQuX19wcm90b19fPXAsYyh0LGEsIkdlbmVyYXRvckZ1bmN0aW9uIikpLHQucHJvdG90eXBlPU9iamVjdC5jcmVhdGUobSksdH0sdC5hd3JhcD1mdW5jdGlvbih0KXtyZXR1cm57X19hd2FpdDp0fX0sZyh3LnByb3RvdHlwZSksYyh3LnByb3RvdHlwZSxpLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLHQuQXN5bmNJdGVyYXRvcj13LHQuYXN5bmM9ZnVuY3Rpb24oZSxyLG4sbyxpKXt2b2lkIDA9PT1pJiYoaT1Qcm9taXNlKTt2YXIgYT1uZXcgdyh1KGUscixuLG8pLGkpO3JldHVybiB0LmlzR2VuZXJhdG9yRnVuY3Rpb24ocik/YTphLm5leHQoKS50aGVuKChmdW5jdGlvbih0KXtyZXR1cm4gdC5kb25lP3QudmFsdWU6YS5uZXh0KCl9KSl9LGcobSksYyhtLGEsIkdlbmVyYXRvciIpLGMobSxvLChmdW5jdGlvbigpe3JldHVybiB0aGlzfSkpLGMobSwidG9TdHJpbmciLChmdW5jdGlvbigpe3JldHVybiJbb2JqZWN0IEdlbmVyYXRvcl0ifSkpLHQua2V5cz1mdW5jdGlvbih0KXt2YXIgZT1bXTtmb3IodmFyIHIgaW4gdCllLnB1c2gocik7cmV0dXJuIGUucmV2ZXJzZSgpLGZ1bmN0aW9uIHIoKXtmb3IoO2UubGVuZ3RoOyl7dmFyIG49ZS5wb3AoKTtpZihuIGluIHQpcmV0dXJuIHIudmFsdWU9bixyLmRvbmU9ITEscn1yZXR1cm4gci5kb25lPSEwLHJ9fSx0LnZhbHVlcz1FLGsucHJvdG90eXBlPXtjb25zdHJ1Y3RvcjprLHJlc2V0OmZ1bmN0aW9uKHQpe2lmKHRoaXMucHJldj0wLHRoaXMubmV4dD0wLHRoaXMuc2VudD10aGlzLl9zZW50PXZvaWQgMCx0aGlzLmRvbmU9ITEsdGhpcy5kZWxlZ2F0ZT1udWxsLHRoaXMubWV0aG9kPSJuZXh0Iix0aGlzLmFyZz12b2lkIDAsdGhpcy50cnlFbnRyaWVzLmZvckVhY2goTCksIXQpZm9yKHZhciBlIGluIHRoaXMpInQiPT09ZS5jaGFyQXQoMCkmJnIuY2FsbCh0aGlzLGUpJiYhaXNOYU4oK2Uuc2xpY2UoMSkpJiYodGhpc1tlXT12b2lkIDApfSxzdG9wOmZ1bmN0aW9uKCl7dGhpcy5kb25lPSEwO3ZhciB0PXRoaXMudHJ5RW50cmllc1swXS5jb21wbGV0aW9uO2lmKCJ0aHJvdyI9PT10LnR5cGUpdGhyb3cgdC5hcmc7cmV0dXJuIHRoaXMucnZhbH0sZGlzcGF0Y2hFeGNlcHRpb246ZnVuY3Rpb24odCl7aWYodGhpcy5kb25lKXRocm93IHQ7dmFyIGU9dGhpcztmdW5jdGlvbiBuKHIsbil7cmV0dXJuIGEudHlwZT0idGhyb3ciLGEuYXJnPXQsZS5uZXh0PXIsbiYmKGUubWV0aG9kPSJuZXh0IixlLmFyZz12b2lkIDApLCEhbn1mb3IodmFyIG89dGhpcy50cnlFbnRyaWVzLmxlbmd0aC0xO28+PTA7LS1vKXt2YXIgaT10aGlzLnRyeUVudHJpZXNbb10sYT1pLmNvbXBsZXRpb247aWYoInJvb3QiPT09aS50cnlMb2MpcmV0dXJuIG4oImVuZCIpO2lmKGkudHJ5TG9jPD10aGlzLnByZXYpe3ZhciBjPXIuY2FsbChpLCJjYXRjaExvYyIpLHU9ci5jYWxsKGksImZpbmFsbHlMb2MiKTtpZihjJiZ1KXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9ZWxzZSBpZihjKXtpZih0aGlzLnByZXY8aS5jYXRjaExvYylyZXR1cm4gbihpLmNhdGNoTG9jLCEwKX1lbHNle2lmKCF1KXRocm93IG5ldyBFcnJvcigidHJ5IHN0YXRlbWVudCB3aXRob3V0IGNhdGNoIG9yIGZpbmFsbHkiKTtpZih0aGlzLnByZXY8aS5maW5hbGx5TG9jKXJldHVybiBuKGkuZmluYWxseUxvYyl9fX19LGFicnVwdDpmdW5jdGlvbih0LGUpe2Zvcih2YXIgbj10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7bj49MDstLW4pe3ZhciBvPXRoaXMudHJ5RW50cmllc1tuXTtpZihvLnRyeUxvYzw9dGhpcy5wcmV2JiZyLmNhbGwobywiZmluYWxseUxvYyIpJiZ0aGlzLnByZXY8by5maW5hbGx5TG9jKXt2YXIgaT1vO2JyZWFrfX1pJiYoImJyZWFrIj09PXR8fCJjb250aW51ZSI9PT10KSYmaS50cnlMb2M8PWUmJmU8PWkuZmluYWxseUxvYyYmKGk9bnVsbCk7dmFyIGE9aT9pLmNvbXBsZXRpb246e307cmV0dXJuIGEudHlwZT10LGEuYXJnPWUsaT8odGhpcy5tZXRob2Q9Im5leHQiLHRoaXMubmV4dD1pLmZpbmFsbHlMb2MsZik6dGhpcy5jb21wbGV0ZShhKX0sY29tcGxldGU6ZnVuY3Rpb24odCxlKXtpZigidGhyb3ciPT09dC50eXBlKXRocm93IHQuYXJnO3JldHVybiJicmVhayI9PT10LnR5cGV8fCJjb250aW51ZSI9PT10LnR5cGU/dGhpcy5uZXh0PXQuYXJnOiJyZXR1cm4iPT09dC50eXBlPyh0aGlzLnJ2YWw9dGhpcy5hcmc9dC5hcmcsdGhpcy5tZXRob2Q9InJldHVybiIsdGhpcy5uZXh0PSJlbmQiKToibm9ybWFsIj09PXQudHlwZSYmZSYmKHRoaXMubmV4dD1lKSxmfSxmaW5pc2g6ZnVuY3Rpb24odCl7Zm9yKHZhciBlPXRoaXMudHJ5RW50cmllcy5sZW5ndGgtMTtlPj0wOy0tZSl7dmFyIHI9dGhpcy50cnlFbnRyaWVzW2VdO2lmKHIuZmluYWxseUxvYz09PXQpcmV0dXJuIHRoaXMuY29tcGxldGUoci5jb21wbGV0aW9uLHIuYWZ0ZXJMb2MpLEwociksZn19LGNhdGNoOmZ1bmN0aW9uKHQpe2Zvcih2YXIgZT10aGlzLnRyeUVudHJpZXMubGVuZ3RoLTE7ZT49MDstLWUpe3ZhciByPXRoaXMudHJ5RW50cmllc1tlXTtpZihyLnRyeUxvYz09PXQpe3ZhciBuPXIuY29tcGxldGlvbjtpZigidGhyb3ciPT09bi50eXBlKXt2YXIgbz1uLmFyZztMKHIpfXJldHVybiBvfX10aHJvdyBuZXcgRXJyb3IoImlsbGVnYWwgY2F0Y2ggYXR0ZW1wdCIpfSxkZWxlZ2F0ZVlpZWxkOmZ1bmN0aW9uKHQsZSxyKXtyZXR1cm4gdGhpcy5kZWxlZ2F0ZT17aXRlcmF0b3I6RSh0KSxyZXN1bHROYW1lOmUsbmV4dExvYzpyfSwibmV4dCI9PT10aGlzLm1ldGhvZCYmKHRoaXMuYXJnPXZvaWQgMCksZn19LHR9dmFyIFVyPUZyKCkubWFyayhZcik7ZnVuY3Rpb24gcXIodCxlKXt2YXIgcj0idW5kZWZpbmVkIiE9PXR5cGVvZiBTeW1ib2wmJnRbU3ltYm9sLml0ZXJhdG9yXXx8dFsiQEBpdGVyYXRvciJdO2lmKCFyKXtpZihBcnJheS5pc0FycmF5KHQpfHwocj1Lcih0KSl8fGUmJnQmJiJudW1iZXIiPT09dHlwZW9mIHQubGVuZ3RoKXtyJiYodD1yKTt2YXIgbj0wLG89ZnVuY3Rpb24oKXt9O3JldHVybntzOm8sbjpmdW5jdGlvbigpe3JldHVybiBuPj10Lmxlbmd0aD97ZG9uZTohMH06e2RvbmU6ITEsdmFsdWU6dFtuKytdfX0sZTpmdW5jdGlvbih0KXt0aHJvdyB0fSxmOm99fXRocm93IG5ldyBUeXBlRXJyb3IoIkludmFsaWQgYXR0ZW1wdCB0byBpdGVyYXRlIG5vbi1pdGVyYWJsZSBpbnN0YW5jZS5cbkluIG9yZGVyIHRvIGJlIGl0ZXJhYmxlLCBub24tYXJyYXkgb2JqZWN0cyBtdXN0IGhhdmUgYSBbU3ltYm9sLml0ZXJhdG9yXSgpIG1ldGhvZC4iKX12YXIgaSxhPSEwLGM9ITE7cmV0dXJue3M6ZnVuY3Rpb24oKXtyPXIuY2FsbCh0KX0sbjpmdW5jdGlvbigpe3ZhciB0PXIubmV4dCgpO3JldHVybiBhPXQuZG9uZSx0fSxlOmZ1bmN0aW9uKHQpe2M9ITAsaT10fSxmOmZ1bmN0aW9uKCl7dHJ5e2F8fG51bGw9PXIucmV0dXJufHxyLnJldHVybigpfWZpbmFsbHl7aWYoYyl0aHJvdyBpfX19fWZ1bmN0aW9uIEtyKHQsZSl7aWYodCl7aWYoInN0cmluZyI9PT10eXBlb2YgdClyZXR1cm4gTXIodCxlKTt2YXIgcj1PYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCkuc2xpY2UoOCwtMSk7cmV0dXJuIk9iamVjdCI9PT1yJiZ0LmNvbnN0cnVjdG9yJiYocj10LmNvbnN0cnVjdG9yLm5hbWUpLCJNYXAiPT09cnx8IlNldCI9PT1yP0FycmF5LmZyb20odCk6IkFyZ3VtZW50cyI9PT1yfHwvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChyKT9Ncih0LGUpOnZvaWQgMH19ZnVuY3Rpb24gTXIodCxlKXsobnVsbD09ZXx8ZT50Lmxlbmd0aCkmJihlPXQubGVuZ3RoKTtmb3IodmFyIHI9MCxuPW5ldyBBcnJheShlKTtyPGU7cisrKW5bcl09dFtyXTtyZXR1cm4gbn1mdW5jdGlvbiBZcih0KXt2YXIgZSxyLG4sbyxpLGEsYyx1LHMsZixsLGgscCx5LHYsZCxtPWFyZ3VtZW50cztyZXR1cm4gRnIoKS53cmFwKChmdW5jdGlvbihnKXt3aGlsZSgxKXN3aXRjaChnLnByZXY9Zy5uZXh0KXtjYXNlIDA6cmV0dXJuIGU9bS5sZW5ndGg+MSYmdm9pZCAwIT09bVsxXT9tWzFdOnt9LHI9ZS5pZ25vcmVVUkxQYXJhbWV0ZXJzTWF0Y2hpbmcsbj12b2lkIDA9PT1yP1svXnV0bV8vLC9eZmJjbGlkJC9dOnIsbz1lLmRpcmVjdG9yeUluZGV4LGk9dm9pZCAwPT09bz8iaW5kZXguaHRtbCI6byxhPWUuY2xlYW5VUkxzLGM9dm9pZCAwPT09YXx8YSx1PWUudXJsTWFuaXB1bGF0aW9uLHM9bmV3IFVSTCh0LGxvY2F0aW9uLmhyZWYpLHMuaGFzaD0iIixnLm5leHQ9NSxzLmhyZWY7Y2FzZSA1OnJldHVybiBmPUlyKHMsbiksZy5uZXh0PTgsZi5ocmVmO2Nhc2UgODppZighaXx8IWYucGF0aG5hbWUuZW5kc1dpdGgoIi8iKSl7Zy5uZXh0PTEzO2JyZWFrfXJldHVybiBsPW5ldyBVUkwoZi5ocmVmKSxsLnBhdGhuYW1lKz1pLGcubmV4dD0xMyxsLmhyZWY7Y2FzZSAxMzppZighYyl7Zy5uZXh0PTE4O2JyZWFrfXJldHVybiBoPW5ldyBVUkwoZi5ocmVmKSxoLnBhdGhuYW1lKz0iLmh0bWwiLGcubmV4dD0xOCxoLmhyZWY7Y2FzZSAxODppZighdSl7Zy5uZXh0PTM3O2JyZWFrfXA9dSh7dXJsOnN9KSx5PXFyKHApLGcucHJldj0yMSx5LnMoKTtjYXNlIDIzOmlmKCh2PXkubigpKS5kb25lKXtnLm5leHQ9Mjk7YnJlYWt9cmV0dXJuIGQ9di52YWx1ZSxnLm5leHQ9MjcsZC5ocmVmO2Nhc2UgMjc6Zy5uZXh0PTIzO2JyZWFrO2Nhc2UgMjk6Zy5uZXh0PTM0O2JyZWFrO2Nhc2UgMzE6Zy5wcmV2PTMxLGcudDA9Z1siY2F0Y2giXSgyMSkseS5lKGcudDApO2Nhc2UgMzQ6cmV0dXJuIGcucHJldj0zNCx5LmYoKSxnLmZpbmlzaCgzNCk7Y2FzZSAzNzpjYXNlImVuZCI6cmV0dXJuIGcuc3RvcCgpfX0pLFVyLG51bGwsW1syMSwzMSwzNCwzN11dKX1mdW5jdGlvbiBXcih0KXtyZXR1cm4gV3I9ImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmInN5bWJvbCI9PXR5cGVvZiBTeW1ib2wuaXRlcmF0b3I/ZnVuY3Rpb24odCl7cmV0dXJuIHR5cGVvZiB0fTpmdW5jdGlvbih0KXtyZXR1cm4gdCYmImZ1bmN0aW9uIj09dHlwZW9mIFN5bWJvbCYmdC5jb25zdHJ1Y3Rvcj09PVN5bWJvbCYmdCE9PVN5bWJvbC5wcm90b3R5cGU/InN5bWJvbCI6dHlwZW9mIHR9LFdyKHQpfWZ1bmN0aW9uIERyKHQsZSl7dmFyIHI9InVuZGVmaW5lZCIhPT10eXBlb2YgU3ltYm9sJiZ0W1N5bWJvbC5pdGVyYXRvcl18fHRbIkBAaXRlcmF0b3IiXTtpZighcil7aWYoQXJyYXkuaXNBcnJheSh0KXx8KHI9QnIodCkpfHxlJiZ0JiYibnVtYmVyIj09PXR5cGVvZiB0Lmxlbmd0aCl7ciYmKHQ9cik7dmFyIG49MCxvPWZ1bmN0aW9uKCl7fTtyZXR1cm57czpvLG46ZnVuY3Rpb24oKXtyZXR1cm4gbj49dC5sZW5ndGg/e2RvbmU6ITB9Ontkb25lOiExLHZhbHVlOnRbbisrXX19LGU6ZnVuY3Rpb24odCl7dGhyb3cgdH0sZjpvfX10aHJvdyBuZXcgVHlwZUVycm9yKCJJbnZhbGlkIGF0dGVtcHQgdG8gaXRlcmF0ZSBub24taXRlcmFibGUgaW5zdGFuY2UuXG5JbiBvcmRlciB0byBiZSBpdGVyYWJsZSwgbm9uLWFycmF5IG9iamVjdHMgbXVzdCBoYXZlIGEgW1N5bWJvbC5pdGVyYXRvcl0oKSBtZXRob2QuIil9dmFyIGksYT0hMCxjPSExO3JldHVybntzOmZ1bmN0aW9uKCl7cj1yLmNhbGwodCl9LG46ZnVuY3Rpb24oKXt2YXIgdD1yLm5leHQoKTtyZXR1cm4gYT10LmRvbmUsdH0sZTpmdW5jdGlvbih0KXtjPSEwLGk9dH0sZjpmdW5jdGlvbigpe3RyeXthfHxudWxsPT1yLnJldHVybnx8ci5yZXR1cm4oKX1maW5hbGx5e2lmKGMpdGhyb3cgaX19fX1mdW5jdGlvbiBCcih0LGUpe2lmKHQpe2lmKCJzdHJpbmciPT09dHlwZW9mIHQpcmV0dXJuIEhyKHQsZSk7dmFyIHI9T2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHQpLnNsaWNlKDgsLTEpO3JldHVybiJPYmplY3QiPT09ciYmdC5jb25zdHJ1Y3RvciYmKHI9dC5jb25zdHJ1Y3Rvci5uYW1lKSwiTWFwIj09PXJ8fCJTZXQiPT09cj9BcnJheS5mcm9tKHQpOiJBcmd1bWVudHMiPT09cnx8L14oPzpVaXxJKW50KD86OHwxNnwzMikoPzpDbGFtcGVkKT9BcnJheSQvLnRlc3Qocik/SHIodCxlKTp2b2lkIDB9fWZ1bmN0aW9uIEhyKHQsZSl7KG51bGw9PWV8fGU+dC5sZW5ndGgpJiYoZT10Lmxlbmd0aCk7Zm9yKHZhciByPTAsbj1uZXcgQXJyYXkoZSk7cjxlO3IrKyluW3JdPXRbcl07cmV0dXJuIG59ZnVuY3Rpb24gJHIodCxlKXtmb3IodmFyIHI9MDtyPGUubGVuZ3RoO3IrKyl7dmFyIG49ZVtyXTtuLmVudW1lcmFibGU9bi5lbnVtZXJhYmxlfHwhMSxuLmNvbmZpZ3VyYWJsZT0hMCwidmFsdWUiaW4gbiYmKG4ud3JpdGFibGU9ITApLE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0LG4ua2V5LG4pfX1mdW5jdGlvbiBWcih0LGUscil7cmV0dXJuIGUmJiRyKHQucHJvdG90eXBlLGUpLHImJiRyKHQsciksT2JqZWN0LmRlZmluZVByb3BlcnR5KHQsInByb3RvdHlwZSIse3dyaXRhYmxlOiExfSksdH1mdW5jdGlvbiB6cih0LGUpe2lmKCEodCBpbnN0YW5jZW9mIGUpKXRocm93IG5ldyBUeXBlRXJyb3IoIkNhbm5vdCBjYWxsIGEgY2xhc3MgYXMgYSBmdW5jdGlvbiIpfWZ1bmN0aW9uIEpyKHQsZSl7aWYoImZ1bmN0aW9uIiE9PXR5cGVvZiBlJiZudWxsIT09ZSl0aHJvdyBuZXcgVHlwZUVycm9yKCJTdXBlciBleHByZXNzaW9uIG11c3QgZWl0aGVyIGJlIG51bGwgb3IgYSBmdW5jdGlvbiIpO3QucHJvdG90eXBlPU9iamVjdC5jcmVhdGUoZSYmZS5wcm90b3R5cGUse2NvbnN0cnVjdG9yOnt2YWx1ZTp0LHdyaXRhYmxlOiEwLGNvbmZpZ3VyYWJsZTohMH19KSxPYmplY3QuZGVmaW5lUHJvcGVydHkodCwicHJvdG90eXBlIix7d3JpdGFibGU6ITF9KSxlJiZRcih0LGUpfWZ1bmN0aW9uIFFyKHQsZSl7cmV0dXJuIFFyPU9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3Quc2V0UHJvdG90eXBlT2YuYmluZCgpOmZ1bmN0aW9uKHQsZSl7cmV0dXJuIHQuX19wcm90b19fPWUsdH0sUXIodCxlKX1mdW5jdGlvbiBYcih0KXt2YXIgZT1lbigpO3JldHVybiBmdW5jdGlvbigpe3ZhciByLG49cm4odCk7aWYoZSl7dmFyIG89cm4odGhpcykuY29uc3RydWN0b3I7cj1SZWZsZWN0LmNvbnN0cnVjdChuLGFyZ3VtZW50cyxvKX1lbHNlIHI9bi5hcHBseSh0aGlzLGFyZ3VtZW50cyk7cmV0dXJuIFpyKHRoaXMscil9fWZ1bmN0aW9uIFpyKHQsZSl7aWYoZSYmKCJvYmplY3QiPT09V3IoZSl8fCJmdW5jdGlvbiI9PT10eXBlb2YgZSkpcmV0dXJuIGU7aWYodm9pZCAwIT09ZSl0aHJvdyBuZXcgVHlwZUVycm9yKCJEZXJpdmVkIGNvbnN0cnVjdG9ycyBtYXkgb25seSByZXR1cm4gb2JqZWN0IG9yIHVuZGVmaW5lZCIpO3JldHVybiB0bih0KX1mdW5jdGlvbiB0bih0KXtpZih2b2lkIDA9PT10KXRocm93IG5ldyBSZWZlcmVuY2VFcnJvcigidGhpcyBoYXNuJ3QgYmVlbiBpbml0aWFsaXNlZCAtIHN1cGVyKCkgaGFzbid0IGJlZW4gY2FsbGVkIik7cmV0dXJuIHR9ZnVuY3Rpb24gZW4oKXtpZigidW5kZWZpbmVkIj09PXR5cGVvZiBSZWZsZWN0fHwhUmVmbGVjdC5jb25zdHJ1Y3QpcmV0dXJuITE7aWYoUmVmbGVjdC5jb25zdHJ1Y3Quc2hhbSlyZXR1cm4hMTtpZigiZnVuY3Rpb24iPT09dHlwZW9mIFByb3h5KXJldHVybiEwO3RyeXtyZXR1cm4gQm9vbGVhbi5wcm90b3R5cGUudmFsdWVPZi5jYWxsKFJlZmxlY3QuY29uc3RydWN0KEJvb2xlYW4sW10sKGZ1bmN0aW9uKCl7fSkpKSwhMH1jYXRjaCh0KXtyZXR1cm4hMX19ZnVuY3Rpb24gcm4odCl7cmV0dXJuIHJuPU9iamVjdC5zZXRQcm90b3R5cGVPZj9PYmplY3QuZ2V0UHJvdG90eXBlT2YuYmluZCgpOmZ1bmN0aW9uKHQpe3JldHVybiB0Ll9fcHJvdG9fX3x8T2JqZWN0LmdldFByb3RvdHlwZU9mKHQpfSxybih0KX12YXIgbm49ZnVuY3Rpb24odCl7SnIocix0KTt2YXIgZT1YcihyKTtmdW5jdGlvbiByKHQsbil7enIodGhpcyxyKTt2YXIgbz1mdW5jdGlvbihlKXt2YXIgcixvPWUucmVxdWVzdCxpPXQuZ2V0VVJMc1RvQ2FjaGVLZXlzKCksYT1EcihZcihvLnVybCxuKSk7dHJ5e2ZvcihhLnMoKTshKHI9YS5uKCkpLmRvbmU7KXt2YXIgYz1yLnZhbHVlLHU9aS5nZXQoYyk7aWYodSl7dmFyIHM9dC5nZXRJbnRlZ3JpdHlGb3JDYWNoZUtleSh1KTtyZXR1cm57Y2FjaGVLZXk6dSxpbnRlZ3JpdHk6c319fX1jYXRjaChmKXthLmUoZil9ZmluYWxseXthLmYoKX19O3JldHVybiBlLmNhbGwodGhpcyxvLHQuc3RyYXRlZ3kpfXJldHVybiBWcihyKX0oemUpO2Z1bmN0aW9uIG9uKHQpe3ZhciBlPVllKCkscj1uZXcgbm4oZSx0KTtqcihyKX1mdW5jdGlvbiBhbih0KXt2YXIgZT1ZZSgpO2UucHJlY2FjaGUodCl9ZnVuY3Rpb24gY24odCxlKXthbih0KSxvbihlKX1vdCgpLHNlbGYuc2tpcFdhaXRpbmcoKSxjbihbeydyZXZpc2lvbic6J2VjZjg1ZDk0MDMzMTI4MmM0OTRlMmI1YmM4NWE3NzQ3JywndXJsJzonL21wL29lbS9jc3MvcmVzZXQuY3NzJ30seydyZXZpc2lvbic6bnVsbCwndXJsJzonL21wL29lbS9qcy9zaGVsbC5jYmE1ODMxYS5qcyd9XSksanIoL15odHRwczpcL1wvd3pxLmd1b3Nlbi5jb20uY25cLyQvLChmdW5jdGlvbigpe3JldHVybiBuZXcgUmVzcG9uc2UoIkN1c3RvbWVyIHJlc3BvbnNlIil9KSl9KCl9KSgpOwovLyMgc291cmNlTWFwcGluZ1VSTD1zdy5qcy5tYXA=", "encoding": "base64"}, "redirectURL": null, "headersSize": 0, "bodySize": 22702}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": 2, "connect": 34, "ssl": 17, "send": 1, "wait": 121, "receive": 26}}, {"startedDateTime": "2025-06-06T10:54:00.66+08:00", "time": 153, "request": {"method": "GET", "url": "https://wzq.csc108.com/mp/oem/index.html", "httpVersion": "HTTP/1.1", "cookies": [{"name": "qlappid", "value": "wx9cf8c670ebd68ce4"}, {"name": "qlskey", "value": "110a7528ddc89595101*bH4JbHI44a"}, {"name": "qluin", "value": "os-ppuF7XdUGL-hJG43C1yFNbBm8"}, {"name": "qq_logtype", "value": "16"}, {"name": "qs_openid", "value": "o4NN4jlQfrkl5xecDR9NKp2wFN04"}, {"name": "qs_unionid", "value": "oURPMwqW34OdDH0smHSUJPgLnaAI"}, {"name": "trade_in_cmschina", "value": "1"}, {"name": "trade_in_partner", "value": "1"}, {"name": "wx_session_time", "value": "*************"}, {"name": "wzq_dealer", "value": "11100"}, {"name": "wzq_qlappid", "value": "wx9cf8c670ebd68ce4"}, {"name": "wzq_qlskey", "value": "110a7528ddc89595101*bH4JbHI44a"}, {"name": "wzq_qluin", "value": "os-ppuF7XdUGL-hJG43C1yFNbBm8"}], "headers": [{"name": "Host", "value": "wzq.csc108.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/13639 Flue"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "Sec-Fetch-Site", "value": "none"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "<PERSON><PERSON>", "value": "qlappid=wx9cf8c670ebd68ce4; qlskey=110a7528ddc89595101*bH4JbHI44a; qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8; qq_logtype=16; qs_openid=o4NN4jlQfrkl5xecDR9NKp2wFN04; qs_unionid=oURPMwqW34OdDH0smHSUJPgLnaAI; trade_in_cmschina=1; trade_in_partner=1; wx_session_time=*************; wzq_dealer=11100; wzq_qlappid=wx9cf8c670ebd68ce4; wzq_qlskey=110a7528ddc89595101*bH4JbHI44a; wzq_qluin=os-ppuF7XdUGL-hJG43C1yFNbBm8"}], "queryString": [], "headersSize": 1042, "bodySize": 0}, "response": {"_charlesStatus": "COMPLETE", "status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Fri, 06 Jun 2025 02:54:00 GMT"}, {"name": "Content-Type", "value": "text/html"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "x-via", "value": "1.1 PS-HET-018Sv41:34 (Cdn Cache Server V2.0)"}, {"name": "x-ws-request-id", "value": "68425848_PS-HET-018Sv41_45756-50738"}, {"name": "Connection", "value": "keep-alive"}], "content": {"size": 15195, "compression": 9772, "mimeType": "text/html", "text": "<!doctype html><html lang=\"\"><head><meta charset=\"utf-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"apple-mobile-web-app-capable\" content=\"yes\"/><meta name=\"apple-mobile-web-app-status-bar-style\" content=\"black\"/><meta name=\"format-detection\" content=\"telephone=no,email=no\"/><meta name=\"x5-orientation\" content=\"portrait\"/><meta name=\"x5-cache\" content=\"disable\"><meta name=\"pgv\" content=\"wzq.tenpay.com\"/><meta content=\"light\" name=\"color-scheme\"/><meta name=\"viewport\" content=\"width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1,viewport-fit=cover\"/><title>涓俊寤烘姇璇佸埜</title><link rel=\"stylesheet\" href=\"/mp/oem/css/reset.css\"><style>.p-async-loading {\n        box-sizing: border-box;\n        width: 100%;\n        padding: 50px;\n        text-align: center;\n      }\n      .p-async-loading .p-loading {\n        width: 30px;\n        height: 30px;\n        display: inline-block;\n        vertical-align: middle;\n        -webkit-animation: p-loading 1s steps(12) infinite;\n        animation: p-loading 1s steps(12) infinite;\n        background-size: 100%;\n      }\n      .p-loading :before {\n          background: rgba(0,0,0,0) url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;\n      }\n      @keyframes p-loading {\n        0%{\n            -webkit-transform:rotate(0deg);\n            transform:rotate(0deg)\n        }\n        to{\n            -webkit-transform:rotate(1turn);transform:rotate(1turn)\n        }\n      }\n      #wujie{\n        width: 100%;\n        height: auto;\n      }</style></head><body><noscript><strong>We're sorry but 涓俊寤烘姇璇佸埜 doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id=\"testShadow\" style=\"position:absolute;top:-100px;left:0px;\"></div><div id=\"app\"><div class=\"p-async-loading\"><i class=\"p-loading\"></i></div></div><div id=\"wujie\"></div><div id=\"shell-navbar\"></div><script>if ('serviceWorker' in navigator) {\n        window.addEventListener('load', () => {\n          navigator.serviceWorker\n            .register('/sw.js?v=zhongxinjiantou_202409041640')\n            .then(function (reg) {\n              console.log('Service worker has been registered.')\n            })\n            .catch(function (err) {\n              console.error('Error during service worker registration: ' + err);\n            });\n        });\n      } else {\n        console.log('Service worker is not supported by browser.');\n      }\n\n      if (localStorage.length === 0) {\n        sessionStorage.setItem('isNewUser', '1');\n      }\n      (function() {\n        function createModuleScriptEl(src, ismodule) {\n          var script = document.createElement('script');\n          script.src = src;\n          script.defer = 'defer';\n          script.type = ismodule ? 'module' : 'nomodule';\n          document.head.appendChild(script);\n        }\n        function createProloadResources(resources, type) {\n          for (var index = 0; index < resources.length; index++) {\n            const element = resources[index];\n            var link = document.createElement('link');\n            link.rel = 'prefetch';\n            link.href = element;\n            link.as = type;\n            document.body.appendChild(link);\n          }\n        }\n        var _hash = location.hash;\n        document.getElementById('shell-navbar').style.display = 'none';\n        // 鍏ュ彛js\n        var jsFiles = '/mp/oem/js/chunk-vendors.78b479c8.js,/mp/oem/js/app.57504ec7.js,/mp/oem/js/shell.cba5831a.js'.split(',');\n        // 鍏ュ彛css\n        var cssFiles = '/mp/oem/css/chunk-vendors.2478a1a4.css,/mp/oem/css/app.5d1bc3af.css'.split(',');\n        const { hostname } = location;\n        // 榛樿浣跨敤鏈�灏忓３鍘诲姞杞戒氦鏄撳瓙搴旂敤\n        const blackHostNameList = ['wzq.citics.com'];\n\n        // 棰勮姹傞厤缃�\n        if ('prod' === 'prod') {\n          window.__wujieConfigPending = fetch('https://' + location.host + '/cgi-bin/qry_conf.fcgi', {\n            method: 'post',\n            body: 'action=1&biz_key=zhongxinjiantou_oem.wujie.pagemap',\n            headers: {\n              'content-type': 'application/x-www-form-urlencoded'\n            }\n          }).then(function (res) {\n            return res.json();\n          });\n        } else {\n          window.__wujieConfigPending = fetch(\n            `https://st.gtimg.com/config/microapp/zhongxinjiantou_oem/prod/bundle.json?t=${Date.now()}`\n          ).then(res => res.json());\n        }\n        // 涓俊鐢变簬鏈夋粦鍧楅獙璇佺爜锛屾澶勪氦鏄撴ā鍧椾笉璧拌繘鏈�灏忓３閫昏緫锛屽緟鍚庣画浼樺寲\n        if (\n          (blackHostNameList.indexOf(hostname) === -1\n          && (_hash.indexOf('wj_trade') > -1 && _hash.indexOf('wj_trade/apply/index') === -1))\n        ) {\n          document.getElementById('app').style.display = 'none';\n          for (var index = 0; index < jsFiles.length; index++) {\n            var src = jsFiles[index];\n            if (/\\/shell(.\\w+)?.js/.test(src)) {\n              createModuleScriptEl(src, true);\n            }\n          }\n          (function createCSSVarStyle() {\n            var stylesheet = document.createElement('style');\n            stylesheet.textContent = ':root,body,page{--color-blue:#2577ff;--color-red:#ff4c4c;--color-green:#34bf49;--color-gray:#858c9e;--color-orange:#ff5a00;--color-link:#576b95;--color-heavygray:#0c121e;--color-midgray-1:#262e40;--color-midgray-2:#50596d;--color-lightgray-1:#757e93;--color-lightgray-2:#9aa2b3;--color-primary:#ff5a00;--color-primary-1:#ff5a00;--color-primary-2:#ff5a00;--text-color-0:#000;--text-color-1:var(--color-heavygray);--text-color-2:var(--color-midgray-2);--text-color-3:var(--color-midgray-2);--text-color-4:var(--color-lightgray-2);--text-color-5:var(--color-lightgray-2);--fill-1:#f5f6f9;--fill-2:#fff;--fill-3:#fbfcfd;--fill-4:#fff6f2;--fill-5:#fff6f2;--fill-6:#fff6f2;--border-heavy:#dfe1e6;--border-light:#dfe1e6;--mask-1:rgba(0,0,0,.3);--mask-2:rgba(0,0,0,.6);--color-rise:#ff4c4c;--color-drop:#34bf49;--color-equal:#858c9e;--color-rise-2:#ff8080;--color-drop-2:#68e17a;--color-rise-bg1:#fcd6d6;--color-drop-bg1:#cff1d9;--color-rise-bg2:#fef5f5;--color-drop-bg2:#f3fbf5}[data-rise-drop-mode=green-up-red-down] page,body[data-rise-drop-mode=green-up-red-down]{--color-rise:#34bf49;--color-drop:#ff4c4c;--color-equal:#858c9e;--color-rise-2:#68e17a;--color-drop-2:#ff8080;--color-rise-bg1:#cff1d9;--color-drop-bg1:#fcd6d6;--color-rise-bg2:#f3fbf5;--color-drop-bg2:#fef5f5}';\n            document.head.appendChild(stylesheet);\n            var stockuiStylesheet = document.createElement('style');\n            stockuiStylesheet.textContent = ':root{--color-primary:#3077ec;--color-primary-tap:#006ee6;--color-warning:#ff891e;--color-purple:#d907ff;--color-error:#f4333c;--color-tap:#f5f5f5;--color-divider:#e5e5e5;--color-divider-secondary:#f3f3f3;--color-icon-base:#ccc;--color-text-white:#fff;--color-text-1:#262e40;--color-text-2:#475166;--color-text-3:#606980;--color-text-4:#7a8499;--color-text-5:#98a0b3;--fill-1:#f5f6fa;--fill-2:#ffffff;--fill-3:#ffffff;--border-color-1:#e9ebf0;--border-color-2:#e9ebf0;--color-dark-text-base:#f0f1f5;--color-dark-text-secondary:#9599a1;--color-dark-text-placeholder:#676d79;--color-dark-text-light:#cccccc;--fill-base:#fff;--fill-body:#f5f5f9;--fill-mask:rgba(0,0,0,0.3);--opacity-disabled:0.3;--border-color-base:#e5e5e5;--border-color-dark:#191e27;--fontsize-large-xxxx:30px;--fontsize-large-xxx:24px;--fontsize-large-xx:20px;--fontsize-large-x:18px;--fontsize-large:16px;--fontsize-medium:14px;--fontsize-small:12px;--fontsize-small-s:10px;--radius-size-normal:4px;--radius-size-small:2px;--radius-circle:50%;--line-height-base:1;--line-height-paragraph:1.5;--line-height-large-x:25px;--line-height-large:23px;--line-height-medium:20px;--line-height-small:17px;--line-height-small-s:14px;--button-height:45px;--button-middle-height:36px;--button-samll-height:28px;--button-tiny-height:20px;--button-disabled-fill:#98a0b3;--button-disabled-color:var(--color-text-white);--button-group-border:var(--border-color-base);--cell-color:var(--color-text-1);--cell-value-color:var(--color-text-2);--cell-group-bg:var(--fill-2);--cell-border:var(--border-color-base);--dialog-color:#0a1428;--dialog-bgc:var(--color-text-white);--dialog-icon-color:#4a4c5b;--dialog-icon-bgc:#f3f4f5;--dialog-title-color:#0a1428;--dialog-close-color:#999;--dialog-btn-color:#0a1428;--dialog-btn-bgc:var(--color-text-white);--dialog-btn-active-bgc:rgba(0,0,0,0.04);--dialog-btn-highlight-color:var(--color-primary);--dialog-btn-highlight-active-bgc:rgba(252,145,83,0.04);--dialog-btn-disabled-color:#999;--dialog-btn-disabled-active-bgc:transparent;--dialog-btns-split-color:#edeff3;--divider-border-color:var(--color-divider);--index-list-bgc:var(--color-text-white);--index-list-title-color:#0a1428;--index-list-anchor-color:#999;--index-list-anchor-bgc:#f7f7f7;--index-list-item-color:#0a1428;--index-list-item-active-bgc:rgba(0,0,0,0.04);--index-list-nav-color:#666;--index-list-nav-active-color:var(--color-primary);--input-item-fake-color:var(--color-text-1);--modal-icon-close-color:#dcdfe6;--number-keyboard-background:#fff;--number-keyboard-gray:#f0f1f5;--number-keyboard-border:#efeff4;--number-keyboard-color:#262e40;--number-keyboard-active:#f5f5f5;--password-input-bg:#ffffff;--picker-lr-padding:16px;--picker-gradient-top:linear-gradient(totop,rgba(255,255,255,0.4),rgba(255,255,255,0.8));--picker-gradient-bottom:linear-gradient(tobottom,rgba(255,255,255,0.4),rgba(255,255,255,0.8));--popup-mask-bgc:var(--fill-mask);--segmented-control-color:var(--color-primary);--segmented-control-height:27px;--segmented-control-fill-tap:var(--color-primary);--slide-dot-bgc:#dcdfe6;--slide-dot-active-bgc:#98a0b3;--steps-icon-border-color:#b8becc;--steps-description-color:#b8becc;--steps-tail-color:#b8becc;--steps-process-icon-color:var(--color-primary);--steps-process-title-color:var(--color-text-1);--steps-process-description-color:#b8becc;--steps-process-tail-color:#b8becc;--steps-wait-icon-color:#b8becc;--steps-wait-title-color:#b8becc;--steps-wait-description-color:#b8becc;--steps-wait-tail-color:#b8becc;--steps-finish-icon-color:var(--color-primary);--steps-finish-title-color:var(--color-text-1);--steps-finish-description-color:#b8becc;--steps-finish-tail-color:var(--steps-process-icon-color);--steps-error-icon-color:var(--color-error);--steps-error-title-color:var(--steps-error-icon-color);--steps-error-description-color:var(--color-text-2);--steps-error-tail-color:var(--steps-process-icon-color);--stepper-button-bg:#f5f6fa;--stepper-step-color:#7A8499;--stepper-button-border-color:#dcdfe6;--switch-background:#fff;--switch-border-color:rgba(0,0,0,0.1);--toast-color:var(--color-text-white);--toast-bgc:rgba(0,0,0,0.7);--toast-min-width:120px;--toast-max-width:255px;}';\n            document.head.appendChild(stockuiStylesheet);\n          })();\n        } else {\n          document.getElementById('wujie').style.display = 'none';\n          for (var index = 0; index < jsFiles.length; index++) {\n            var src = jsFiles[index];\n            if (!/\\/shell(.\\w+)?.js/.test(src)) {\n              createModuleScriptEl(src, true);\n            }\n          }\n          for (var index = 0; index < jsFiles.length; index++) {\n            var src = jsFiles[index];\n            var host = null;\n            if (src.indexOf('//') === 0) {\n              // cdn\n              var fullurl = new URL('http:' + src);\n              host = fullurl.host;\n              src = fullurl.pathname\n            }\n            var srcArray = src.split('.');\n            srcArray[0] = srcArray[0] + '-legacy';\n\n            var legacyUrl = host ? '//' + host + srcArray.join('.') : srcArray.join('.');\n            createModuleScriptEl(legacyUrl, false);\n          }\n          for (var index = 0; index < cssFiles.length; index++) {\n            var src = cssFiles[index];\n            var link = document.createElement('link');\n            link.href = src;\n            link.rel = 'stylesheet';\n            document.head.appendChild(link);\n          }\n        }\n      })()</script><style>#shell-navbar{\n        position: fixed !important;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        height: 49px;\n        transform: translateY(0);\n        z-index: 10;\n        background-color: var(--fill-2);\n        padding-bottom: env(safe-area-inset-bottom);\n        display: flex;\n        justify-content: space-between;\n      }\n      #shell-navbar::before {\n        position: absolute;\n        top: 0;\n        left: 0;\n        box-sizing: border-box;\n        transform-origin: 0 top;\n        content: ' ';\n        pointer-events: none;\n        border-top: 1px solid var(--border-light);\n        width: calc(300% - 0 * 0.05rem);\n        transform: scale(0.333) translateZ(0);\n      }\n      #shell-navbar .nav-item {\n        position: relative;\n        height: 100%;\n        font-size: 22px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        flex: 1;\n      }\n      #shell-navbar .icon {\n        font-size: 26px;\n        width: .56rem;\n        height: .56rem;\n        margin-bottom: 4px;\n      }\n      #shell-navbar .name {\n       color: var(--text-color-1);\n       font-size: 10px;\n      }\n      #shell-navbar .matched .name {\n        color: var(--color-red);\n      }</style></body></html>"}, "redirectURL": null, "headersSize": 0, "bodySize": 5423}, "serverIPAddress": "************", "cache": {}, "timings": {"dns": -1, "connect": -1, "ssl": -1, "send": 1, "wait": 149, "receive": 3}}]}}