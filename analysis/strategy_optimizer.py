"""
策略优化器

通过参数优化提升交易策略的性能
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed


class StrategyOptimizer:
    """
    策略优化器
    
    通过参数优化提升交易策略的性能
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化策略优化器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 优化结果
        self.optimization_results = []
        # 最佳参数
        self.best_params = {}
        # 最佳性能指标
        self.best_metrics = {}
        
        self.logger.info("策略优化器初始化完成")
    
    def grid_search(self, 
                   strategy_fn: Callable[[Dict[str, Any]], Dict[str, float]], 
                   param_grid: Dict[str, List[Any]],
                   metric_key: str = 'sharpe_ratio',
                   maximize: bool = True,
                   max_workers: int = None) -> List[Dict[str, Any]]:
        """
        网格搜索优化策略参数
        
        Args:
            strategy_fn: 策略函数，接收参数字典并返回性能指标字典
            param_grid: 参数网格，格式为 {param_name: [param_values]}
            metric_key: 用于评估的性能指标名称
            maximize: 是否最大化指标（True为最大化，False为最小化）
            max_workers: 最大并行工作进程数，None表示使用默认值
            
        Returns:
            List[Dict[str, Any]]: 优化结果列表，每个元素包含参数和性能指标
        """
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        self.logger.info(f"开始网格搜索，共 {len(param_combinations)} 种参数组合")
        
        results = []
        
        # 使用并行处理加速
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_params = {}
            for i, values in enumerate(param_combinations):
                params = dict(zip(param_names, values))
                future = executor.submit(self._evaluate_strategy, strategy_fn, params)
                future_to_params[future] = params
            
            # 收集结果
            for i, future in enumerate(as_completed(future_to_params)):
                params = future_to_params[future]
                try:
                    metrics = future.result()
                    result = {**params, **metrics}
                    results.append(result)
                    
                    # 打印进度
                    if (i + 1) % 10 == 0 or i + 1 == len(param_combinations):
                        self.logger.info(f"进度: {i + 1}/{len(param_combinations)} ({(i + 1) / len(param_combinations) * 100:.1f}%)")
                        
                except Exception as e:
                    self.logger.error(f"参数 {params} 评估失败: {str(e)}")
        
        # 按指定指标排序结果
        if results:
            results.sort(key=lambda x: x.get(metric_key, 0), reverse=maximize)
            
            # 保存最佳参数和指标
            self.best_params = {k: results[0][k] for k in param_names}
            self.best_metrics = {k: results[0][k] for k in results[0] if k not in param_names}
            
            self.logger.info(f"网格搜索完成，最佳参数: {self.best_params}")
            self.logger.info(f"最佳性能指标: {metric_key}={self.best_metrics.get(metric_key)}")
        else:
            self.logger.warning("网格搜索未产生有效结果")
        
        self.optimization_results = results
        return results
    
    def _evaluate_strategy(self, strategy_fn: Callable[[Dict[str, Any]], Dict[str, float]], 
                          params: Dict[str, Any]) -> Dict[str, float]:
        """
        评估给定参数下的策略性能
        
        Args:
            strategy_fn: 策略函数
            params: 策略参数
            
        Returns:
            Dict[str, float]: 性能指标字典
        """
        try:
            metrics = strategy_fn(params)
            return metrics
        except Exception as e:
            self.logger.error(f"策略评估出错: {str(e)}")
            return {}
    
    def random_search(self, 
                     strategy_fn: Callable[[Dict[str, Any]], Dict[str, float]], 
                     param_distributions: Dict[str, Callable[[], Any]],
                     n_iter: int = 100,
                     metric_key: str = 'sharpe_ratio',
                     maximize: bool = True,
                     max_workers: int = None) -> List[Dict[str, Any]]:
        """
        随机搜索优化策略参数
        
        Args:
            strategy_fn: 策略函数，接收参数字典并返回性能指标字典
            param_distributions: 参数分布，格式为 {param_name: distribution_fn}，
                                其中distribution_fn是一个无参数函数，返回一个随机抽样值
            n_iter: 随机搜索迭代次数
            metric_key: 用于评估的性能指标名称
            maximize: 是否最大化指标（True为最大化，False为最小化）
            max_workers: 最大并行工作进程数，None表示使用默认值
            
        Returns:
            List[Dict[str, Any]]: 优化结果列表，每个元素包含参数和性能指标
        """
        param_names = list(param_distributions.keys())
        
        self.logger.info(f"开始随机搜索，共 {n_iter} 次迭代")
        
        results = []
        
        # 使用并行处理加速
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_params = {}
            for i in range(n_iter):
                # 从每个分布中抽样
                params = {name: dist_fn() for name, dist_fn in param_distributions.items()}
                future = executor.submit(self._evaluate_strategy, strategy_fn, params)
                future_to_params[future] = params
            
            # 收集结果
            for i, future in enumerate(as_completed(future_to_params)):
                params = future_to_params[future]
                try:
                    metrics = future.result()
                    result = {**params, **metrics}
                    results.append(result)
                    
                    # 打印进度
                    if (i + 1) % 10 == 0 or i + 1 == n_iter:
                        self.logger.info(f"进度: {i + 1}/{n_iter} ({(i + 1) / n_iter * 100:.1f}%)")
                        
                except Exception as e:
                    self.logger.error(f"参数 {params} 评估失败: {str(e)}")
        
        # 按指定指标排序结果
        if results:
            results.sort(key=lambda x: x.get(metric_key, 0), reverse=maximize)
            
            # 保存最佳参数和指标
            self.best_params = {k: results[0][k] for k in param_names}
            self.best_metrics = {k: results[0][k] for k in results[0] if k not in param_names}
            
            self.logger.info(f"随机搜索完成，最佳参数: {self.best_params}")
            self.logger.info(f"最佳性能指标: {metric_key}={self.best_metrics.get(metric_key)}")
        else:
            self.logger.warning("随机搜索未产生有效结果")
        
        self.optimization_results = results
        return results
    
    def plot_optimization_results(self, param_names: List[str], metric_key: str) -> List[Figure]:
        """
        绘制优化结果图
        
        Args:
            param_names: 要可视化的参数名称列表
            metric_key: 性能指标名称
            
        Returns:
            List[Figure]: Matplotlib图形对象列表
        """
        if not self.optimization_results:
            self.logger.warning("没有优化结果可供可视化")
            return []
        
        figures = []
        
        # 将优化结果转换为DataFrame
        results_df = pd.DataFrame(self.optimization_results)
        
        # 对每个参数绘制散点图
        for param_name in param_names:
            if param_name not in results_df.columns or metric_key not in results_df.columns:
                continue
                
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 绘制散点图
            scatter = ax.scatter(results_df[param_name], results_df[metric_key], 
                               alpha=0.6, edgecolors='w', linewidth=0.5)
            
            # 添加趋势线
            try:
                z = np.polyfit(results_df[param_name], results_df[metric_key], 1)
                p = np.poly1d(z)
                ax.plot(results_df[param_name], p(results_df[param_name]), 
                       "r--", alpha=0.8, linewidth=1)
            except:
                pass
            
            # 标记最佳点
            if self.best_params:
                best_param = self.best_params.get(param_name)
                best_metric = self.best_metrics.get(metric_key)
                if best_param is not None and best_metric is not None:
                    ax.scatter([best_param], [best_metric], color='red', s=100, 
                              marker='*', label='Best')
                    ax.annotate(f'Best: {best_param}', 
                               xy=(best_param, best_metric),
                               xytext=(10, 10),
                               textcoords='offset points',
                               arrowprops=dict(arrowstyle='->', color='red'))
            
            # 设置标题和标签
            ax.set_title(f'Parameter Optimization: {param_name} vs {metric_key}', fontsize=14)
            ax.set_xlabel(param_name, fontsize=12)
            ax.set_ylabel(metric_key, fontsize=12)
            
            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # 添加图例
            ax.legend()
            
            plt.tight_layout()
            figures.append(fig)
        
        # 如果有两个参数，绘制热图
        if len(param_names) >= 2:
            for i, param1 in enumerate(param_names):
                for j, param2 in enumerate(param_names):
                    if i >= j:  # 避免重复
                        continue
                        
                    if param1 not in results_df.columns or param2 not in results_df.columns:
                        continue
                    
                    # 创建热图
                    fig, ax = plt.subplots(figsize=(10, 8))
                    
                    # 准备数据
                    pivot_table = results_df.pivot_table(
                        values=metric_key, 
                        index=param1, 
                        columns=param2, 
                        aggfunc='mean'
                    )
                    
                    # 绘制热图
                    heatmap = ax.pcolor(pivot_table, cmap='viridis')
                    
                    # 设置坐标轴
                    ax.set_xticks(np.arange(pivot_table.shape[1]) + 0.5)
                    ax.set_yticks(np.arange(pivot_table.shape[0]) + 0.5)
                    
                    # 设置坐标轴标签
                    ax.set_xticklabels(pivot_table.columns)
                    ax.set_yticklabels(pivot_table.index)
                    
                    # 添加颜色条
                    cbar = plt.colorbar(heatmap)
                    cbar.set_label(metric_key)
                    
                    # 设置标题
                    ax.set_title(f'Parameter Heatmap: {param1} vs {param2}', fontsize=14)
                    ax.set_xlabel(param2, fontsize=12)
                    ax.set_ylabel(param1, fontsize=12)
                    
                    plt.tight_layout()
                    figures.append(fig)
        
        return figures
    
    def plot_parameter_importance(self, metric_key: str) -> Figure:
        """
        绘制参数重要性图
        
        Args:
            metric_key: 性能指标名称
            
        Returns:
            Figure: Matplotlib图形对象
        """
        if not self.optimization_results:
            self.logger.warning("没有优化结果可供分析")
            return plt.figure()
        
        # 将优化结果转换为DataFrame
        results_df = pd.DataFrame(self.optimization_results)
        
        # 获取参数列表（排除指标列）
        param_names = [col for col in results_df.columns if col != metric_key and 
                      col not in ['annual_return', 'volatility', 'max_drawdown', 'sharpe_ratio', 
                                 'sortino_ratio', 'calmar_ratio', 'win_rate', 'profit_loss_ratio']]
        
        if not param_names:
            self.logger.warning("没有找到参数列")
            return plt.figure()
        
        # 计算每个参数的重要性（使用相关系数的绝对值）
        importance = {}
        for param in param_names:
            if pd.api.types.is_numeric_dtype(results_df[param]):
                corr = results_df[[param, metric_key]].corr().iloc[0, 1]
                importance[param] = abs(corr)
        
        if not importance:
            self.logger.warning("无法计算参数重要性")
            return plt.figure()
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 绘制条形图
        params = list(importance.keys())
        values = list(importance.values())
        
        # 按重要性排序
        sorted_indices = np.argsort(values)
        params = [params[i] for i in sorted_indices]
        values = [values[i] for i in sorted_indices]
        
        ax.barh(params, values, color='skyblue')
        
        # 设置标题和标签
        ax.set_title(f'Parameter Importance for {metric_key}', fontsize=14)
        ax.set_xlabel('Absolute Correlation', fontsize=12)
        ax.set_ylabel('Parameter', fontsize=12)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 添加数值标签
        for i, v in enumerate(values):
            ax.text(v + 0.01, i, f'{v:.2f}', va='center')
        
        plt.tight_layout()
        
        return fig
    
    def save_optimization_results(self, output_dir: str):
        """
        保存优化结果到文件
        
        Args:
            output_dir: 输出目录
        """
        import os
        import json
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存优化结果到CSV文件
        if self.optimization_results:
            results_df = pd.DataFrame(self.optimization_results)
            results_file = os.path.join(output_dir, 'optimization_results.csv')
            results_df.to_csv(results_file, index=False)
            
            # 保存最佳参数到JSON文件
            best_params_file = os.path.join(output_dir, 'best_params.json')
            with open(best_params_file, 'w') as f:
                json.dump(self.best_params, f, indent=4)
            
            # 保存最佳性能指标到JSON文件
            best_metrics_file = os.path.join(output_dir, 'best_metrics.json')
            with open(best_metrics_file, 'w') as f:
                json.dump(self.best_metrics, f, indent=4)
            
            self.logger.info(f"优化结果已保存到目录: {output_dir}")
        else:
            self.logger.warning("没有优化结果可供保存")
    
    def walk_forward_optimization(self, 
                                strategy_fn: Callable[[Dict[str, Any], pd.DataFrame, pd.DataFrame], Dict[str, float]], 
                                data: pd.DataFrame,
                                param_grid: Dict[str, List[Any]],
                                window_size: int,
                                step_size: int,
                                metric_key: str = 'sharpe_ratio',
                                maximize: bool = True) -> List[Dict[str, Any]]:
        """
        执行向前优化
        
        Args:
            strategy_fn: 策略函数，接收参数字典、训练数据和测试数据，返回性能指标字典
            data: 完整的历史数据
            param_grid: 参数网格，格式为 {param_name: [param_values]}
            window_size: 训练窗口大小
            step_size: 测试步长
            metric_key: 用于评估的性能指标名称
            maximize: 是否最大化指标（True为最大化，False为最小化）
            
        Returns:
            List[Dict[str, Any]]: 向前优化结果列表
        """
        if len(data) <= window_size:
            self.logger.error("数据长度不足，无法执行向前优化")
            return []
        
        self.logger.info(f"开始向前优化，窗口大小: {window_size}，步长: {step_size}")
        
        walk_forward_results = []
        
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        # 遍历每个时间窗口
        for start_idx in range(0, len(data) - window_size - step_size, step_size):
            train_end_idx = start_idx + window_size
            test_end_idx = min(train_end_idx + step_size, len(data))
            
            train_data = data.iloc[start_idx:train_end_idx]
            test_data = data.iloc[train_end_idx:test_end_idx]
            
            train_period = f"{train_data.index[0]} to {train_data.index[-1]}"
            test_period = f"{test_data.index[0]} to {test_data.index[-1]}"
            
            self.logger.info(f"优化窗口: 训练期 {train_period}, 测试期 {test_period}")
            
            # 在训练数据上找到最佳参数
            best_params = None
            best_metric_value = float('-inf') if maximize else float('inf')
            
            for params_tuple in param_combinations:
                params = dict(zip(param_names, params_tuple))
                
                try:
                    # 在训练数据上评估策略
                    metrics = strategy_fn(params, train_data, None)
                    
                    # 更新最佳参数
                    metric_value = metrics.get(metric_key, float('-inf') if maximize else float('inf'))
                    if ((maximize and metric_value > best_metric_value) or 
                        (not maximize and metric_value < best_metric_value)):
                        best_metric_value = metric_value
                        best_params = params.copy()
                        
                except Exception as e:
                    self.logger.error(f"参数 {params} 在训练期评估失败: {str(e)}")
            
            if best_params is None:
                self.logger.warning(f"窗口 {train_period} 未找到有效参数")
                continue
                
            # 使用最佳参数在测试数据上评估策略
            try:
                test_metrics = strategy_fn(best_params, None, test_data)
                
                # 记录结果
                result = {
                    'train_start': train_data.index[0],
                    'train_end': train_data.index[-1],
                    'test_start': test_data.index[0],
                    'test_end': test_data.index[-1],
                    **best_params,
                    **test_metrics
                }
                
                walk_forward_results.append(result)
                
                self.logger.info(f"窗口 {test_period} 最佳参数: {best_params}")
                self.logger.info(f"测试期性能: {metric_key}={test_metrics.get(metric_key)}")
                
            except Exception as e:
                self.logger.error(f"最佳参数 {best_params} 在测试期评估失败: {str(e)}")
        
        self.logger.info(f"向前优化完成，共 {len(walk_forward_results)} 个结果")
        
        return walk_forward_results
    
    def plot_walk_forward_results(self, results: List[Dict[str, Any]], metric_key: str) -> Figure:
        """
        绘制向前优化结果图
        
        Args:
            results: 向前优化结果列表
            metric_key: 性能指标名称
            
        Returns:
            Figure: Matplotlib图形对象
        """
        if not results:
            self.logger.warning("没有向前优化结果可供可视化")
            return plt.figure()
        
        # 将结果转换为DataFrame
        results_df = pd.DataFrame(results)
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True, gridspec_kw={'height_ratios': [3, 1]})
        
        # 绘制性能指标曲线
        ax1.plot(results_df['test_start'], results_df[metric_key], 
                marker='o', linestyle='-', color='blue', markersize=6)
        
        # 添加网格线
        ax1.grid(True, linestyle='--', alpha=0.7)
        
        # 设置标题和标签
        ax1.set_title(f'Walk Forward Optimization: {metric_key}', fontsize=14)
        ax1.set_ylabel(metric_key, fontsize=12)
        
        # 获取参数列表（排除指标列和日期列）
        exclude_cols = [metric_key, 'train_start', 'train_end', 'test_start', 'test_end', 
                       'annual_return', 'volatility', 'max_drawdown', 'sharpe_ratio', 
                       'sortino_ratio', 'calmar_ratio', 'win_rate', 'profit_loss_ratio']
        param_names = [col for col in results_df.columns if col not in exclude_cols]
        
        # 绘制参数变化
        colors = plt.cm.tab10(np.linspace(0, 1, len(param_names)))
        
        for i, param in enumerate(param_names):
            # 归一化参数值以便在同一图表上显示
            if pd.api.types.is_numeric_dtype(results_df[param]):
                param_min = results_df[param].min()
                param_max = results_df[param].max()
                if param_max > param_min:
                    normalized = (results_df[param] - param_min) / (param_max - param_min)
                    ax2.plot(results_df['test_start'], normalized, 
                           marker='s', linestyle='-', color=colors[i], markersize=4,
                           label=f'{param} [{param_min:.2f}-{param_max:.2f}]')
        
        # 添加网格线
        ax2.grid(True, linestyle='--', alpha=0.7)
        
        # 设置标签
        ax2.set_xlabel('Test Period Start', fontsize=12)
        ax2.set_ylabel('Normalized Parameter Value', fontsize=12)
        
        # 添加图例
        ax2.legend(loc='upper center', bbox_to_anchor=(0.5, -0.15), 
                  ncol=3, fancybox=True, shadow=True)
        
        # 格式化日期轴
        fig.autofmt_xdate()
        
        plt.tight_layout()
        
        return fig