#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化交易函数使用示例

展示如何使用新的优化交易函数
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime
from colorama import init, Fore, Style

# 导入交易模块
from citic_trader.trade import TradeClient
from citic_trader.utils import setup_logger

# 初始化colorama
init(autoreset=True)

def load_cookies(cookies_file: str = "cookies.json") -> dict:
    """加载cookies"""
    try:
        if not os.path.exists(cookies_file):
            print(Fore.RED + f"Cookies文件不存在: {cookies_file}")
            return {}
            
        with open(cookies_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
            try:
                cookies = json.loads(content)
                print(Fore.GREEN + f"成功加载cookies")
                return cookies
            except json.JSONDecodeError:
                print(Fore.RED + f"无法解析cookies文件")
                return {}
                
    except Exception as e:
        print(Fore.RED + f"加载cookies失败: {e}")
        return {}

def setup_trade_client(cookies_file: str = "cookies.json") -> TradeClient:
    """设置交易客户端"""
    try:
        # 加载cookies
        cookies = load_cookies(cookies_file)
        if not cookies:
            print(Fore.RED + "未找到有效的cookies")
            return None
        
        # 创建会话
        session = requests.Session()
        session.trust_env = False
        session.proxies = {'http': None, 'https': None}
        for key, value in cookies.items():
            session.cookies.set(key, value)
        
        # 用户信息
        user_info = {
            "account_id": "",
            "stockholder_code": "",
            "psw_session": "",
            "app_info": {
                "_appver": "7.0.20",
                "_osVer": "Windows1064",
                "_buildh5ver": "************"
            }
        }
        
        # 创建交易客户端
        logger = setup_logger(log_level=logging.INFO)
        trade_client = TradeClient(session, user_info, logger)
        print(Fore.GREEN + "交易客户端创建成功")
        return trade_client
        
    except Exception as e:
        print(Fore.RED + f"设置交易客户端失败: {e}")
        return None

def demo_optimized_buy():
    """演示优化后的买入函数"""
    print(Fore.CYAN + Style.BRIGHT + "\n" + "=" * 60)
    print(Fore.CYAN + Style.BRIGHT + "优化买入函数演示")
    print(Fore.CYAN + Style.BRIGHT + "=" * 60)
    
    # 设置交易客户端
    trade_client = setup_trade_client()
    if not trade_client:
        return
    
    stock_code = "518880"  # 黄金ETF
    price = 4.500
    
    print(Fore.YELLOW + f"\n演示股票: {stock_code}")
    print(Fore.YELLOW + f"演示价格: {price}")
    
    # 1. 原始买入函数（需要手动获取股票信息）
    print(Fore.GREEN + "\n1. 原始买入函数:")
    print("   需要手动获取股票信息...")
    stock_info = trade_client.get_stock_info(stock_code)
    print("   调用买入函数...")
    result = trade_client.buy(
        stock_code=stock_code,
        price=price,
        volume=100,
        stock_info=stock_info,
        order_type="specified"
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 2. 优化买入函数（自动获取股票信息，并行执行）
    print(Fore.GREEN + "\n2. 优化买入函数:")
    print("   自动获取股票信息，并行执行...")
    result = trade_client.buy_optimized(
        stock_code=stock_code,
        price=price,
        volume=100,
        order_type="specified"
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 3. 超级优化买入函数（最大化并行执行）
    print(Fore.GREEN + "\n3. 超级优化买入函数:")
    print("   最大化并行执行，最小化等待时间...")
    result = trade_client.buy_ultra_optimized(
        stock_code=stock_code,
        price=price,
        volume=100,
        order_type="specified"
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 4. 全仓买入示例
    print(Fore.GREEN + "\n4. 全仓买入示例:")
    print("   自动计算全仓买入数量...")
    result = trade_client.buy_ultra_optimized(
        stock_code=stock_code,
        price=price,
        order_type="full"  # 全仓买入
    )
    print(f"   结果: {result.get('status', 'unknown')}")

def demo_optimized_sell():
    """演示优化后的卖出函数"""
    print(Fore.CYAN + Style.BRIGHT + "\n" + "=" * 60)
    print(Fore.CYAN + Style.BRIGHT + "优化卖出函数演示")
    print(Fore.CYAN + Style.BRIGHT + "=" * 60)
    
    # 设置交易客户端
    trade_client = setup_trade_client()
    if not trade_client:
        return
    
    stock_code = "518880"  # 黄金ETF
    price = 4.500
    
    print(Fore.YELLOW + f"\n演示股票: {stock_code}")
    print(Fore.YELLOW + f"演示价格: {price}")
    
    # 1. 原始卖出函数
    print(Fore.GREEN + "\n1. 原始卖出函数:")
    print("   需要手动获取股票信息...")
    stock_info = trade_client.get_stock_info(stock_code)
    print("   调用卖出函数...")
    result = trade_client.sell(
        stock_code=stock_code,
        price=price,
        volume=100,
        stock_info=stock_info
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 2. 优化卖出函数
    print(Fore.GREEN + "\n2. 优化卖出函数:")
    print("   自动获取股票信息，并行执行...")
    result = trade_client.sell_optimized(
        stock_code=stock_code,
        price=price,
        volume=100,
        order_type="specified"
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 3. 超级优化卖出函数
    print(Fore.GREEN + "\n3. 超级优化卖出函数:")
    print("   最大化并行执行...")
    result = trade_client.sell_ultra_optimized(
        stock_code=stock_code,
        price=price,
        volume=100,
        order_type="specified"
    )
    print(f"   结果: {result.get('status', 'unknown')}")
    
    # 4. 全部卖出示例
    print(Fore.GREEN + "\n4. 全部卖出示例:")
    print("   自动获取持仓数量，全部卖出...")
    result = trade_client.sell_ultra_optimized(
        stock_code=stock_code,
        price=price,
        order_type="all"  # 全部卖出
    )
    print(f"   结果: {result.get('status', 'unknown')}")

def demo_interface_comparison():
    """演示接口对比"""
    print(Fore.MAGENTA + Style.BRIGHT + "\n" + "=" * 60)
    print(Fore.MAGENTA + Style.BRIGHT + "接口对比演示")
    print(Fore.MAGENTA + Style.BRIGHT + "=" * 60)
    
    print(Fore.YELLOW + "\n原始接口调用方式:")
    print(Fore.WHITE + """
# 原始买入 - 需要多个步骤
stock_info = trade_client.get_stock_info("518880")
result = trade_client.buy(
    stock_code="518880",
    price=4.500,
    volume=100,
    stock_info=stock_info,
    order_type="specified"
)
""")
    
    print(Fore.YELLOW + "\n优化后接口调用方式:")
    print(Fore.WHITE + """
# 优化买入 - 简化接口，自动处理
result = trade_client.buy_ultra_optimized(
    stock_code="518880",
    price=4.500,
    volume=100,
    order_type="specified"
)

# 全仓买入 - 更简单
result = trade_client.buy_ultra_optimized(
    stock_code="518880",
    price=4.500,
    order_type="full"
)

# 全部卖出 - 自动获取持仓
result = trade_client.sell_ultra_optimized(
    stock_code="518880",
    price=4.500,
    order_type="all"
)
""")
    
    print(Fore.GREEN + "\n优化特点:")
    print("✓ 简化接口：只需要必要参数")
    print("✓ 智能参数获取：自动获取股票信息、持仓信息等")
    print("✓ 并行执行：trade_prepare、订单号获取、签名生成同时进行")
    print("✓ 性能优化：减少网络请求等待时间")
    print("✓ 兼容性：保持原有接口不变")

def main():
    """主函数"""
    print(Fore.BLUE + Style.BRIGHT + "=" * 80)
    print(Fore.BLUE + Style.BRIGHT + "优化交易函数使用示例")
    print(Fore.BLUE + Style.BRIGHT + "=" * 80)
    
    print(Fore.RED + Style.BRIGHT + "\n注意：这是演示脚本，实际运行可能会执行真实交易！")
    print(Fore.RED + "请确保在测试环境中运行，或修改代码以避免实际交易。")
    
    # 演示接口对比
    demo_interface_comparison()
    
    # 如果用户确认，可以运行实际演示
    user_input = input(Fore.YELLOW + "\n是否运行实际演示？(y/N): ")
    if user_input.lower() == 'y':
        # 演示优化买入
        demo_optimized_buy()
        
        # 演示优化卖出
        demo_optimized_sell()
    else:
        print(Fore.GREEN + "演示结束。")

if __name__ == "__main__":
    main()
