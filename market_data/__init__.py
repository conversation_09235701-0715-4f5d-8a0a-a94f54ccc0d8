"""
行情数据模块

提供实时行情数据获取和处理功能，支持多种数据源
"""

from .data_source import DataSource
from .tdx_data_source import TdxDataSource
from .real_time_data_manager import RealTimeDataManager
import logging
from citic_trader.utils import load_config # 导入 load_config

# 获取日志记录器
logger = logging.getLogger(__name__)

# 加载全局配置
try:
    config = load_config("config.json")
    tdx_config = config.get("tdx_config", {})
except Exception as e:
    logger.critical(f"加载配置文件失败，无法初始化市场数据源: {e}")
    tdx_config = {}

# 初始化主数据源 (通达信)
# 考虑从配置中获取数据源类型和参数，这里为了简化，直接使用TdxDataSource
primary_data_source = TdxDataSource(tdx_config=tdx_config, logger=logger, auto_connect=False) # 暂时不自动连接，手动管理

# 初始化实时数据管理器
real_time_data_manager = RealTimeDataManager(
    primary_data_source=primary_data_source,
    logger=logger
)

def get_real_time_data_manager() -> RealTimeDataManager:
    """
    获取实时数据管理器的单例实例。
    """
    return real_time_data_manager

def connect_market_data_sources() -> bool:
    """
    连接所有市场数据源。
    """
    logger.info("正在连接市场数据源...")
    if real_time_data_manager.primary_data_source.connect():
        logger.info("主数据源连接成功。")
        return True
    else:
        logger.error("主数据源连接失败。")
        return False

def disconnect_market_data_sources() -> bool:
    """
    断开所有市场数据源连接。
    """
    logger.info("正在断开市场数据源连接...")
    if real_time_data_manager.primary_data_source.disconnect():
        logger.info("主数据源断开成功。")
        return True
    else:
        logger.error("主数据源断开失败。")
        return False

__all__ = ['DataSource', 'TdxDataSource', 'RealTimeDataManager'] 