"""
风险检查器

定义风险检查的接口和具体实现
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd


class RiskChecker(ABC):
    """
    风险检查器基类
    
    定义了风险检查的标准接口，所有具体风险检查器都应继承自此类
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化风险检查器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.name = self.__class__.__name__
        self.enabled = True
        
        # 初始化其他成员变量
        self._initialize()
        
        self.logger.info(f"风险检查器 {self.name} 初始化完成")
    
    def _initialize(self):
        """
        初始化其他成员变量
        
        子类可以重写此方法以进行自定义初始化
        """
        pass
    
    @abstractmethod
    def check(self, order: Dict[str, Any]) -> <PERSON><PERSON>[bool, str]:
        """
        检查订单风险
        
        Args:
            order: 订单信息字典，包含股票代码、价格、数量等
            
        Returns:
            Tuple[bool, str]: (是否通过风险检查, 风险提示信息)
        """
        raise NotImplementedError("子类必须实现check方法")
    
    def enable(self):
        """启用风险检查器"""
        self.enabled = True
        self.logger.info(f"启用风险检查器: {self.name}")
    
    def disable(self):
        """禁用风险检查器"""
        self.enabled = False
        self.logger.info(f"禁用风险检查器: {self.name}")
    
    def is_enabled(self) -> bool:
        """
        检查风险检查器是否已启用
        
        Returns:
            bool: 是否已启用
        """
        return self.enabled


class FundingRiskChecker(RiskChecker):
    """
    资金风险检查器
    
    检查订单是否符合资金风险控制规则，包括单笔交易金额限制、日亏损限制和总仓位限制
    """
    
    def __init__(self, 
                max_single_order_amount: float = 100000.0,  # 单笔最大交易金额（元）
                max_daily_loss: float = 10000.0,  # 日最大亏损额（元）
                max_position_ratio: float = 0.5,  # 最大仓位比例（占总资产）
                logger: Optional[logging.Logger] = None):
        """
        初始化资金风险检查器
        
        Args:
            max_single_order_amount: 单笔最大交易金额（元）
            max_daily_loss: 日最大亏损额（元）
            max_position_ratio: 最大仓位比例（占总资产）
            logger: 日志记录器
        """
        self.max_single_order_amount = max_single_order_amount
        self.max_daily_loss = max_daily_loss
        self.max_position_ratio = max_position_ratio
        
        # 当日亏损记录
        self.daily_loss = 0.0
        # 最后重置日期
        self.last_reset_date = datetime.now().date()
        
        super().__init__(logger)
    
    def _initialize(self):
        """初始化资金风险检查器"""
        # 当前持仓信息，格式：{code: {'amount': amount, 'cost': cost}}
        self.positions = {}
        # 总资产（包括现金）
        self.total_assets = 0.0
        # 可用资金
        self.available_cash = 0.0
    
    def update_account_info(self, total_assets: float, available_cash: float, positions: Dict[str, Dict[str, float]]):
        """
        更新账户信息
        
        Args:
            total_assets: 总资产（元）
            available_cash: 可用资金（元）
            positions: 持仓信息，格式：{code: {'amount': amount, 'cost': cost}}
        """
        self.total_assets = total_assets
        self.available_cash = available_cash
        self.positions = positions
        
        # 检查是否需要重置每日亏损
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_loss = 0.0
            self.last_reset_date = current_date
            self.logger.info("重置每日亏损记录")
    
    def record_loss(self, loss: float):
        """
        记录亏损
        
        Args:
            loss: 亏损金额（元），正值表示亏损
        """
        if loss > 0:
            self.daily_loss += loss
            self.logger.info(f"记录亏损: {loss:.2f}元，当日累计亏损: {self.daily_loss:.2f}元")
    
    def check(self, order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查订单是否符合资金风险控制规则
        
        Args:
            order: 订单信息字典，包含股票代码、价格、数量、方向等
            
        Returns:
            Tuple[bool, str]: (是否通过风险检查, 风险提示信息)
        """
        if not self.enabled:
            return True, ""
        
        # 提取订单信息
        code = order.get('code', '')
        price = order.get('price', 0.0)
        amount = order.get('amount', 0)
        direction = order.get('direction', 'buy')  # 'buy' 或 'sell'
        
        # 计算订单金额
        order_amount = price * amount
        
        # 1. 检查单笔交易金额限制
        if order_amount > self.max_single_order_amount:
            message = f"订单金额 {order_amount:.2f} 超过单笔最大交易金额限制 {self.max_single_order_amount:.2f}"
            self.logger.warning(message)
            return False, message
        
        # 2. 检查日亏损限制（仅对卖出订单）
        if direction == 'sell' and code in self.positions:
            position = self.positions[code]
            cost = position.get('cost', 0.0)
            position_amount = position.get('amount', 0)
            
            # 确保不超过持仓数量
            sell_amount = min(amount, position_amount)
            
            # 计算预计亏损
            if cost > 0 and sell_amount > 0:
                potential_loss = (cost - price) * sell_amount
                if potential_loss > 0:  # 如果是亏损
                    total_loss = self.daily_loss + potential_loss
                    if total_loss > self.max_daily_loss:
                        message = f"预计亏损 {potential_loss:.2f} 将导致当日总亏损 {total_loss:.2f} 超过限制 {self.max_daily_loss:.2f}"
                        self.logger.warning(message)
                        return False, message
        
        # 3. 检查总仓位限制（仅对买入订单）
        if direction == 'buy':
            # 计算当前仓位比例
            current_position_ratio = 1.0 - (self.available_cash / self.total_assets) if self.total_assets > 0 else 0.0
            
            # 计算新订单后的仓位比例
            new_position_ratio = (self.total_assets - self.available_cash + order_amount) / self.total_assets if self.total_assets > 0 else 1.0
            
            if new_position_ratio > self.max_position_ratio:
                message = f"订单将使仓位比例从 {current_position_ratio:.2%} 增加到 {new_position_ratio:.2%}，超过最大仓位比例限制 {self.max_position_ratio:.2%}"
                self.logger.warning(message)
                return False, message
            
            # 检查可用资金是否足够
            if order_amount > self.available_cash:
                message = f"订单金额 {order_amount:.2f} 超过可用资金 {self.available_cash:.2f}"
                self.logger.warning(message)
                return False, message
        
        return True, ""


class TradingFrequencyChecker(RiskChecker):
    """
    交易频率风险检查器
    
    检查订单是否符合交易频率控制规则，包括日交易次数限制、最小交易间隔和高频交易检测
    """
    
    def __init__(self, 
                max_daily_trades: int = 20,  # 日最大交易次数
                min_trade_interval: int = 60,  # 最小交易间隔（秒）
                high_freq_threshold: int = 5,  # 高频交易阈值（n次/分钟）
                logger: Optional[logging.Logger] = None):
        """
        初始化交易频率风险检查器
        
        Args:
            max_daily_trades: 日最大交易次数
            min_trade_interval: 最小交易间隔（秒）
            high_freq_threshold: 高频交易阈值（n次/分钟）
            logger: 日志记录器
        """
        self.max_daily_trades = max_daily_trades
        self.min_trade_interval = min_trade_interval
        self.high_freq_threshold = high_freq_threshold
        
        super().__init__(logger)
    
    def _initialize(self):
        """初始化交易频率风险检查器"""
        # 交易记录，格式：[(code, timestamp), ...]
        self.trade_records = []
        # 最后重置日期
        self.last_reset_date = datetime.now().date()
    
    def record_trade(self, code: str):
        """
        记录交易
        
        Args:
            code: 股票代码
        """
        # 检查是否需要重置每日交易记录
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.trade_records = []
            self.last_reset_date = current_date
            self.logger.info("重置每日交易记录")
        
        # 记录交易
        self.trade_records.append((code, datetime.now()))
        self.logger.info(f"记录交易: {code}，当日交易次数: {len(self.trade_records)}")
    
    def check(self, order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查订单是否符合交易频率控制规则
        
        Args:
            order: 订单信息字典，包含股票代码等
            
        Returns:
            Tuple[bool, str]: (是否通过风险检查, 风险提示信息)
        """
        if not self.enabled:
            return True, ""
        
        # 提取订单信息
        code = order.get('code', '')
        
        # 检查是否需要重置每日交易记录
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.trade_records = []
            self.last_reset_date = current_date
            self.logger.info("重置每日交易记录")
        
        # 1. 检查日交易次数限制
        if len(self.trade_records) >= self.max_daily_trades:
            message = f"当日交易次数 {len(self.trade_records)} 已达到最大限制 {self.max_daily_trades}"
            self.logger.warning(message)
            return False, message
        
        # 2. 检查最小交易间隔
        if self.trade_records:
            last_trade_time = self.trade_records[-1][1]
            time_since_last_trade = (datetime.now() - last_trade_time).total_seconds()
            
            if time_since_last_trade < self.min_trade_interval:
                message = f"距离上次交易仅 {time_since_last_trade:.1f} 秒，小于最小交易间隔 {self.min_trade_interval} 秒"
                self.logger.warning(message)
                return False, message
        
        # 3. 检查高频交易
        one_minute_ago = datetime.now() - timedelta(minutes=1)
        recent_trades = [trade for trade in self.trade_records if trade[1] >= one_minute_ago]
        
        if len(recent_trades) >= self.high_freq_threshold:
            message = f"最近1分钟内已有 {len(recent_trades)} 次交易，超过高频交易阈值 {self.high_freq_threshold}"
            self.logger.warning(message)
            return False, message
        
        return True, ""


class PriceRiskChecker(RiskChecker):
    """
    价格风险检查器
    
    检查订单是否符合价格风险控制规则，包括自动止损止盈、波动性监控和价格偏离检测
    """
    
    def __init__(self, 
                stop_loss_ratio: float = 0.05,  # 止损比例
                take_profit_ratio: float = 0.1,  # 止盈比例
                max_price_deviation: float = 0.03,  # 最大价格偏离比例
                volatility_threshold: float = 0.05,  # 波动性阈值
                logger: Optional[logging.Logger] = None):
        """
        初始化价格风险检查器
        
        Args:
            stop_loss_ratio: 止损比例
            take_profit_ratio: 止盈比例
            max_price_deviation: 最大价格偏离比例
            volatility_threshold: 波动性阈值
            logger: 日志记录器
        """
        self.stop_loss_ratio = stop_loss_ratio
        self.take_profit_ratio = take_profit_ratio
        self.max_price_deviation = max_price_deviation
        self.volatility_threshold = volatility_threshold
        
        super().__init__(logger)
    
    def _initialize(self):
        """初始化价格风险检查器"""
        # 股票参考价格，格式：{code: {'price': price, 'timestamp': timestamp}}
        self.reference_prices = {}
        # 持仓信息，格式：{code: {'amount': amount, 'cost': cost}}
        self.positions = {}
    
    def update_reference_price(self, code: str, price: float):
        """
        更新股票参考价格
        
        Args:
            code: 股票代码
            price: 参考价格
        """
        self.reference_prices[code] = {
            'price': price,
            'timestamp': datetime.now()
        }
        self.logger.info(f"更新股票 {code} 参考价格: {price:.4f}")
    
    def update_positions(self, positions: Dict[str, Dict[str, float]]):
        """
        更新持仓信息
        
        Args:
            positions: 持仓信息，格式：{code: {'amount': amount, 'cost': cost}}
        """
        self.positions = positions
    
    def check(self, order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查订单是否符合价格风险控制规则
        
        Args:
            order: 订单信息字典，包含股票代码、价格、方向等
            
        Returns:
            Tuple[bool, str]: (是否通过风险检查, 风险提示信息)
        """
        if not self.enabled:
            return True, ""
        
        # 提取订单信息
        code = order.get('code', '')
        price = order.get('price', 0.0)
        direction = order.get('direction', 'buy')  # 'buy' 或 'sell'
        
        # 1. 检查自动止损止盈（仅对卖出订单）
        if direction == 'sell' and code in self.positions:
            position = self.positions[code]
            cost = position.get('cost', 0.0)
            
            if cost > 0:
                # 计算盈亏比例
                profit_ratio = (price - cost) / cost
                
                # 止损检查
                if profit_ratio < -self.stop_loss_ratio:
                    # 不阻止止损卖出
                    self.logger.info(f"股票 {code} 触发止损卖出，亏损比例: {profit_ratio:.2%}")
                
                # 止盈检查
                elif profit_ratio > self.take_profit_ratio:
                    # 不阻止止盈卖出
                    self.logger.info(f"股票 {code} 触发止盈卖出，盈利比例: {profit_ratio:.2%}")
        
        # 2. 检查价格偏离
        if code in self.reference_prices:
            ref_price = self.reference_prices[code]['price']
            price_deviation = abs(price - ref_price) / ref_price
            
            if price_deviation > self.max_price_deviation:
                message = f"股票 {code} 价格 {price:.4f} 相对于参考价 {ref_price:.4f} 偏离 {price_deviation:.2%}，超过最大偏离限制 {self.max_price_deviation:.2%}"
                self.logger.warning(message)
                return False, message
        
        # 3. 波动性检查（需要历史数据，此处简化实现）
        # 在实际应用中，应该使用市场数据模块获取最近的价格数据计算波动性
        # 这里简化处理，假设已经有了波动性数据
        
        return True, ""


class ExecutionRiskChecker(RiskChecker):
    """
    执行风险检查器
    
    检查订单执行相关的风险，包括交易验证、错误处理机制和紧急停止功能
    """
    
    def __init__(self, 
                max_retry_count: int = 3,  # 最大重试次数
                emergency_stop: bool = False,  # 是否紧急停止
                logger: Optional[logging.Logger] = None):
        """
        初始化执行风险检查器
        
        Args:
            max_retry_count: 最大重试次数
            emergency_stop: 是否紧急停止
            logger: 日志记录器
        """
        self.max_retry_count = max_retry_count
        self.emergency_stop = emergency_stop
        
        super().__init__(logger)
    
    def _initialize(self):
        """初始化执行风险检查器"""
        # 订单重试计数，格式：{order_id: retry_count}
        self.retry_counts = {}
        # 错误订单列表
        self.error_orders = []
    
    def set_emergency_stop(self, stop: bool = True):
        """
        设置紧急停止状态
        
        Args:
            stop: 是否停止
        """
        self.emergency_stop = stop
        status = "启用" if stop else "解除"
        self.logger.warning(f"{status}紧急停止")
    
    def record_retry(self, order_id: str):
        """
        记录订单重试
        
        Args:
            order_id: 订单ID
        """
        if order_id not in self.retry_counts:
            self.retry_counts[order_id] = 1
        else:
            self.retry_counts[order_id] += 1
            
        self.logger.info(f"订单 {order_id} 重试次数: {self.retry_counts[order_id]}/{self.max_retry_count}")
    
    def record_error_order(self, order: Dict[str, Any], error_msg: str):
        """
        记录错误订单
        
        Args:
            order: 订单信息
            error_msg: 错误信息
        """
        order_with_error = order.copy()
        order_with_error['error_msg'] = error_msg
        order_with_error['timestamp'] = datetime.now()
        
        self.error_orders.append(order_with_error)
        self.logger.error(f"记录错误订单: {order.get('code', '')}, 错误: {error_msg}")
    
    def check(self, order: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查订单是否符合执行风险控制规则
        
        Args:
            order: 订单信息字典
            
        Returns:
            Tuple[bool, str]: (是否通过风险检查, 风险提示信息)
        """
        if not self.enabled:
            return True, ""
        
        # 提取订单信息
        order_id = order.get('order_id', '')
        
        # 1. 检查紧急停止状态
        if self.emergency_stop:
            message = "系统处于紧急停止状态，拒绝所有交易"
            self.logger.warning(message)
            return False, message
        
        # 2. 检查重试次数
        if order_id in self.retry_counts and self.retry_counts[order_id] >= self.max_retry_count:
            message = f"订单 {order_id} 重试次数 {self.retry_counts[order_id]} 已达到最大限制 {self.max_retry_count}"
            self.logger.warning(message)
            return False, message
        
        # 3. 交易验证（实际应用中可能需要更复杂的验证逻辑）
        # 这里简化处理，只检查基本字段是否存在
        required_fields = ['code', 'price', 'amount', 'direction']
        for field in required_fields:
            if field not in order or not order[field]:
                message = f"订单缺少必要字段: {field}"
                self.logger.warning(message)
                return False, message
        
        return True, ""