"""
加密模块

处理中信建投微信网厅所需的各种加密算法，包括：
1. 密码加密 (RSA加密)
2. 订单签名 (MD5)
3. 请求数据生成
"""

import base64
import hashlib
import json
import time
import random
import urllib.parse
import binascii
from typing import Dict, Any, Optional
#from Crypto.PublicKey import RSA
#from Crypto.Cipher import PKCS1_v1_5

class TimeSeedHelper:
    """时间种子辅助类，用于密码加密"""
    def __init__(self, time_seed):
        self.time_seed = str(time_seed)  # 确保是字符串
        self.used = False
    
    def get(self):
        self.used = True
        return self.time_seed[:20]  # 取前20个字符
    
    def get_time_code(self):
        """完全模拟JS的getTimeCode方法：计算时间种子的MD5并取前14字符"""
        md5 = hashlib.md5()
        md5.update(self.time_seed.encode('utf-8'))
        md5_hash = md5.hexdigest()
        return md5_hash[:14]

def rsa_custom_encrypt(plaintext: str, pub_key_n: str, pub_key_e: int = 65537) -> str:
    """
    完全模拟JS的RSA加密过程（支持多块分段）
    
    Args:
        plaintext: 待加密字符串
        pub_key_n: RSA公钥模数(16进制字符串)
        pub_key_e: RSA公钥指数(默认65537)
        
    Returns:
        str: 16进制大写加密结果（多块空格分隔）
    """
    # 1. 反转字符串 (模拟JS的_encrypt1)
    reversed_text = plaintext[::-1]
    
    # 2. 转换为字节数组
    byte_array = [ord(c) for c in reversed_text]
    orig_len = len(byte_array)
    
    # 3. 自定义填充 (128字节块大小)
    block_size = 128
    while len(byte_array) % block_size != 0:
        pos = len(byte_array)
        if pos == orig_len or pos % block_size == block_size - 1:
            byte_array.append(0)
        elif pos % block_size == block_size - 2:
            byte_array.append(2)
        else:
            byte_array.append(random.randint(0, 255))
    
    # 4. 分块处理
    n_int = int(pub_key_n, 16)  # 16进制字符串转整数
    encrypted_blocks = []
    
    # 每128字节为一个块
    for i in range(0, len(byte_array), block_size):
        block = byte_array[i:i+block_size]
        
        # 5. 将字节数组转换为大整数 (模拟JS的k类)
        big_int = 0
        for j in range(0, len(block), 2):
            byte1 = block[j]
            byte2 = block[j+1] if (j+1) < len(block) else 0
            value = byte1 | (byte2 << 8)
            big_int |= value << (j * 8)
        
        # 6. RSA加密: cipher = plain^e mod n
        cipher_int = pow(big_int, pub_key_e, n_int)
        
        # 7. 转换为固定长度256字符的16进制大写
        hex_cipher = format(cipher_int, '0256X')
        encrypted_blocks.append(hex_cipher)
    
    # 8. 用空格连接所有加密块
    return " ".join(encrypted_blocks)

def encrypt_password(config: Dict[str, Any], password: str) -> str:
    """
    使用配置信息加密密码

    Args:
        config: 后端返回的配置字典，必须包含timeseed和key字段
        password: 用户密码

    Returns:
        str: 加密后的密码字符串
    """
    # 初始化时间种子
    ts_helper = TimeSeedHelper(config["timeseed"])

    # 构建待加密明文: 时间种子前缀 + 时间码 + 密码
    time_seed_prefix = ts_helper.get()
    time_code = ts_helper.get_time_code()
    plaintext = time_seed_prefix + time_code + password

    # 执行RSA加密
    cipher = rsa_custom_encrypt(plaintext, config["key"])

    # 构建最终结果: 时间码 + 6个0 + 40个0 + 密文
    return f"{time_code}000000{'0'*40}{cipher}"



def format_rsa_key(key: str) -> str:
    """
    格式化RSA公钥
    
    Args:
        key: 原始公钥字符串
        
    Returns:
        str: 格式化后的公钥
    """
    # 通常，RSA公钥需要按照特定格式处理
    # 例如添加头尾标记和换行符
    formatted_key = "-----BEGIN PUBLIC KEY-----\n"
    for i in range(0, len(key), 64):
        formatted_key += key[i:i+64] + "\n"
    formatted_key += "-----END PUBLIC KEY-----"
    return formatted_key

def decrypt_response(response_data: str) -> Dict[str, Any]:
    """
    解密响应数据
    
    Args:
        response_data: 加密的响应数据
        
    Returns:
        Dict: 解密后的数据
    """
    try:
        # 中信建投的响应数据是Base64编码的JSON
        decoded = base64.b64decode(response_data).decode('utf-8')
        return json.loads(decoded)
    except Exception as e:
        # 如果解析失败，尝试直接返回原始数据
        try:
            return json.loads(response_data)
        except:
            return {"retcode": "-1", "retmsg": f"响应解密失败: {str(e)}", "data": response_data}

def generate_request_data(data: Dict[str, Any], method: str = "default") -> Dict[str, Any]:
    """
    生成请求数据
    
    某些请求可能需要添加时间戳、签名等信息，此函数处理这些逻辑。
    
    Args:
        data: 原始请求数据
        method: 处理方法，默认为default
        
    Returns:
        Dict: 处理后的数据
    """
    # 添加通用字段
    result = data.copy()
    
    if method == "trade":
        # 交易请求需要添加通用参数
        result.update({
            "_appver": "7.0.20",
            "_osVer": "Windows1064",
            "_buildh5ver": "202504011653",
            "come_from": "0",
            "gm_flag": "0"
        })
        
        # 如果是买入或卖出请求，添加特定字段
        if "action" in result and (result["action"] == "1" or result["action"] == "2"):
            # 生成随机的entrant_key
            if "entrant_key" not in result:
                result["entrant_key"] = f"{int(time.time() * 1000)}{random.randint(1000, 9999)}"
            
            # 订单类型和风险版本
            if "match_type" not in result:
                result["match_type"] = "0"
            if "risk_ver" not in result:
                result["risk_ver"] = "1"
            
            # 处理股票代码和市场
            if "code" in result and "market" not in result:
                code = result["code"]
                if code.startswith(("6", "5", "7", "9")):
                    result["market"] = "1"  # 上海
                else:
                    result["market"] = "0"  # 深圳
            
            # 确保股票代码不包含市场前缀
            if "code" in result and ("SH" in result["code"] or "SZ" in result["code"]):
                result["code"] = result["code"].replace("SH", "").replace("SZ", "")
            
            # 添加order_sign
            if "code" in result and "price" in result and "amount" in result:
                scode = result.get("code", "")
                price = result.get("price", "")
                quantity = result.get("amount", "")
                result["order_sign"] = generate_order_sign_from_params(scode, price, quantity)
            
            # 如果直接提供了scode, price, quantity
            elif "scode" in result and "price" in result and "quantity" in result:
                scode = result.get("scode", "")
                price = result.get("price", "")
                quantity = result.get("quantity", "")
                result["order_sign"] = generate_order_sign_from_params(scode, price, quantity)
    
    return result

def generate_order_sign(data: Dict[str, Any]) -> str:
    """
    根据订单数据生成订单签名
    
    Args:
        data: 订单数据字典，需要包含scode、price和quantity字段
        
    Returns:
        str: 订单签名
    """
    code = data.get("scode", "") or data.get("code", "")
    price = data.get("price", "")
    quantity = data.get("quantity", "") or data.get("amount", "")
    
    return generate_order_sign_from_params(code, price, quantity)

def generate_order_sign_from_params(code: str, price: str, quantity: str) -> str:
    """
    根据股票代码、价格和数量生成订单签名
    
    根据JS代码，order_sign的生成逻辑是：
    l()(encodeURIComponent("scode="+s+"&price="+c+"&quantity="+p)).toLocaleLowerCase()
    
    使用JavaScript的MD5算法实现，确保与网页端的加密结果一致
    
    Args:
        code: 股票代码
        price: 价格
        quantity: 数量
        
    Returns:
        str: 生成的订单签名
    """
    # 构造参数字符串
    param_str = f"scode={code}&price={price}&quantity={quantity}"
    
    # URL编码
    encoded = urllib.parse.quote(param_str)
    
    # 使用python的MD5算法计算哈希值
    hash_result = hashlib.md5(encoded.encode()).hexdigest()
    
    return hash_result



def generate_entrant_key() -> str:
    """
    生成交易请求的entrant_key参数
    
    基于原始代码中：
    entrant_key: Date.now() + Math.floor(1e4 * Math.random()).toString()
    
    Returns:
        str: 生成的entrant_key
    """
    current_time_ms = int(time.time() * 1000)
    random_num = int(random.random() * 10000)
    return f"{current_time_ms}{random_num}"
