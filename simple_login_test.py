#!/usr/bin/env python3
"""
简单的登录测试脚本

使用方法:
python simple_login_test.py
"""

import json
import sys
import os
from citic_trader.login import login_sync


def load_cookies():
    """加载cookies文件"""
    cookies_file = "cookies.json"
    if os.path.exists(cookies_file):
        try:
            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            print(f"✓ 从cookies.json加载了 {len(cookies)} 个cookies")
            return cookies
        except Exception as e:
            print(f"✗ 加载cookies失败: {e}")
            return {}
    else:
        print("✗ cookies.json文件不存在")
        print("请确保cookies.json文件存在并包含有效的cookies")
        return {}


def save_cookies(cookies):
    """保存cookies到文件"""
    try:
        with open("cookies.json", 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        print("✓ cookies已更新并保存")
    except Exception as e:
        print(f"✗ 保存cookies失败: {e}")


def test_login():
    """测试登录功能"""
    print("中信建投登录测试")
    print("=" * 40)
    
    # 获取密码
    password = input("请输入交易密码: ").strip()
    if not password:
        print("密码不能为空")
        return False
    
    # 加载cookies
    cookies = load_cookies()
    if not cookies:
        print("没有有效的cookies，登录可能会失败")
        choice = input("是否继续尝试? (y/n): ").strip().lower()
        if choice != 'y':
            return False
    
    print("\n开始登录...")
    print("-" * 40)
    
    try:
        # 执行登录
        result, updated_cookies = login_sync(password, cookies)
        
        print("✓ 登录成功!")
        print(f"  返回码: {result.get('retcode')}")
        print(f"  返回消息: {result.get('retmsg')}")
        
        # 显示会话信息
        if 'psw_session' in result:
            session = result['psw_session']
            print(f"  会话信息: {session[:50]}...")
        
        # 保存更新的cookies
        if updated_cookies:
            save_cookies(updated_cookies)
        
        print("\n登录测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ 登录失败: {e}")
        print("\n可能的原因:")
        print("1. 密码错误")
        print("2. cookies过期或无效")
        print("3. 网络连接问题")
        print("4. 服务器暂时不可用")
        return False


def main():
    """主函数"""
    print("简单登录测试工具")
    print("=" * 40)
    print("此工具用于测试中信建投登录功能")
    print("需要准备:")
    print("1. 有效的cookies.json文件")
    print("2. 正确的交易密码")
    print()
    
    # 检查cookies文件
    if not os.path.exists("cookies.json"):
        print("警告: 未找到cookies.json文件")
        print("请从浏览器开发者工具中获取cookies并保存为cookies.json")
        print()
        choice = input("是否继续测试? (y/n): ").strip().lower()
        if choice != 'y':
            print("测试取消")
            return
    
    # 执行测试
    success = test_login()
    
    if success:
        print("\n🎉 登录功能正常!")
        print("你现在可以在其他脚本中使用login_sync函数进行登录")
    else:
        print("\n❌ 登录功能存在问题")
        print("请检查密码和cookies是否正确")


if __name__ == "__main__":
    main()
