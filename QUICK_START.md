# 中信建投登录模块快速开始

## 快速测试

### 1. 测试加密功能
```bash
python test_login.py --encrypt-only
```

### 2. 交互式登录测试
```bash
python simple_login_test.py
```
按提示输入密码即可测试登录功能。

### 3. 完整测试（需要密码参数）
```bash
python test_login.py your_password
```

## 准备工作

### 获取Cookies
1. 打开浏览器，访问中信建投微信网厅
2. 按F12打开开发者工具
3. 在Network标签页中找到任意请求
4. 复制Cookie头部信息
5. 创建cookies.json文件：

```json
{
  "qlappid": "wx9cf8c670ebd68ce4",
  "qlskey": "your_qlskey_value",
  "qluin": "your_qluin_value",
  "wzq_dealer": "11100",
  "trade_in_cmschina": "1",
  "trade_in_partner": "1"
}
```

## 在代码中使用

### 简单登录
```python
from citic_trader.login import login_sync
import json

# 加载cookies
with open("cookies.json", "r") as f:
    cookies = json.load(f)

# 登录
result, updated_cookies = login_sync("your_password", cookies)
print(f"登录成功，会话: {result['psw_session']}")

# 保存更新的cookies
with open("cookies.json", "w") as f:
    json.dump(updated_cookies, f, indent=2)
```

### 集成到交易系统
```python
from citic_trader.login import login_sync

# 登录获取会话
result, cookies = login_sync(password, cookies)
psw_session = result['psw_session']

# 使用会话进行后续操作
# ... 你的交易代码 ...
```

## 常见问题

**Q: 登录失败怎么办？**
A: 检查密码是否正确，cookies是否有效，网络是否正常。

**Q: cookies多久过期？**
A: 通常几小时到一天，需要定期更新。

**Q: 如何获取最新的cookies？**
A: 重新在浏览器中访问网站，从开发者工具获取。

**Q: 可以自动化获取cookies吗？**
A: 可以，但需要额外的浏览器自动化工具如selenium。

## 文件说明

- `citic_trader/login.py` - 主要登录模块
- `citic_trader/encrypt.py` - 密码加密模块
- `test_login.py` - 完整测试脚本
- `simple_login_test.py` - 交互式测试脚本
- `login_example.py` - 使用示例
- `cookies.json` - cookies存储文件（需要自己创建）

## 下一步

登录成功后，你可以：
1. 将psw_session用于后续的交易请求
2. 集成到自动交易系统中
3. 实现定期重新登录以保持会话有效

更多详细信息请参考 `LOGIN_README.md`。
