"""
资金风险控制模块

提供资金风险控制功能，包括单笔交易金额限制、日亏损限制和总持仓限制
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import json
import os

class FundingRiskController:
    """
    资金风险控制器
    
    控制与资金相关的风险，防止过度交易和亏损
    """
    
    def __init__(self, config: Dict[str, Any] = None, logger: Optional[logging.Logger] = None):
        """
        初始化资金风险控制器
        
        Args:
            config: 配置字典，包含风险控制参数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "max_single_trade_amount": 100000,   # 单笔交易最大金额 (元)
            "max_daily_loss": 10000,             # 每日最大亏损额 (元)
            "max_total_position": 500000,        # 最大总仓位 (元)
            "max_position_ratio": 0.5,           # 最大仓位比例 (占总资产)
            "enable_loss_control": True,         # 是否启用亏损控制
            "enable_position_control": True      # 是否启用仓位控制
        }
        
        # 合并配置
        self.config = self.default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化风险控制状态
        self.daily_loss = 0.0                   # 当日亏损
        self.total_position = 0.0               # 当前总仓位
        self.total_assets = 0.0                 # 总资产
        self.available_cash = 0.0               # 可用资金
        self.positions = {}                     # 持仓信息 {代码: {持仓量: 数量, 成本: 成本}}
        self.last_reset_date = datetime.now().date()  # 最后重置日期
        
        # 交易记录
        self.trade_records = []                 # 当日交易记录
        
        self.logger.info(f"资金风险控制器初始化完成, 配置: {json.dumps(self.config, ensure_ascii=False)}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新配置字典
        """
        self.config.update(new_config)
        self.logger.info(f"资金风险控制器配置已更新: {json.dumps(new_config, ensure_ascii=False)}")
    
    def update_account_info(self, total_assets: float, available_cash: float, positions: Dict[str, Dict[str, float]]):
        """
        更新账户信息
        
        Args:
            total_assets: 总资产 (元)
            available_cash: 可用资金 (元)
            positions: 持仓信息，格式: {code: {'amount': amount, 'cost': cost, 'market_value': value}}
        """
        self.total_assets = total_assets
        self.available_cash = available_cash
        self.positions = positions
        
        # 计算当前总仓位
        self.total_position = sum(pos.get('market_value', 0) for pos in positions.values())
        
        # 检查是否需要重置每日亏损
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_loss = 0.0
            self.trade_records = []
            self.last_reset_date = current_date
            self.logger.info("每日亏损和交易记录已重置")
        
        self.logger.info(f"账户信息已更新: 总资产={total_assets:.2f}, 可用资金={available_cash:.2f}, 总仓位={self.total_position:.2f}")
    
    def record_trade(self, trade_info: Dict[str, Any]):
        """
        记录交易
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
        """
        self.trade_records.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "trade_info": trade_info
        })
        self.logger.info(f"已记录交易: {trade_info}")
    
    def record_loss(self, loss: float):
        """
        记录亏损
        
        Args:
            loss: 亏损金额 (元)，正值表示亏损
        """
        if loss > 0:
            self.daily_loss += loss
            self.logger.info(f"记录亏损: {loss:.2f}元，当日累计亏损: {self.daily_loss:.2f}元")
    
    def check_risk(self, trade_info: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        检查资金风险
        
        Args:
            trade_info: 交易信息字典，包含代码、价格、数量、方向等
            
        Returns:
            Tuple[bool, Dict]: (是否通过风险检查, 风险详情)
        """
        # 提取交易信息
        code = trade_info.get('code', '')
        price = float(trade_info.get('price', 0))
        amount = int(trade_info.get('amount', 0))
        direction = trade_info.get('direction', 'buy')  # 'buy' 或 'sell'
        
        # 计算交易金额
        trade_amount = price * amount
        
        # 初始化风险检查结果
        risk_details = {
            "max_single_trade_amount": {
                "passed": True,
                "limit": self.config["max_single_trade_amount"],
                "actual": trade_amount,
                "message": ""
            },
            "max_daily_loss": {
                "passed": True,
                "limit": self.config["max_daily_loss"],
                "actual": self.daily_loss,
                "message": ""
            },
            "max_position": {
                "passed": True,
                "limit": self.config["max_total_position"],
                "actual": self.total_position,
                "message": ""
            },
            "max_position_ratio": {
                "passed": True,
                "limit": self.config["max_position_ratio"],
                "actual": self.total_position / self.total_assets if self.total_assets > 0 else 0,
                "message": ""
            },
            "available_cash": {
                "passed": True,
                "limit": self.available_cash,
                "actual": trade_amount,
                "message": ""
            }
        }
        
        # 1. 检查单笔交易金额限制
        if trade_amount > self.config["max_single_trade_amount"]:
            risk_details["max_single_trade_amount"]["passed"] = False
            risk_details["max_single_trade_amount"]["message"] = f"交易金额 {trade_amount:.2f} 超过单笔最大限制 {self.config['max_single_trade_amount']:.2f}"
            self.logger.warning(risk_details["max_single_trade_amount"]["message"])
        
        # 2. 检查日亏损限制（仅对卖出订单）
        if self.config["enable_loss_control"] and direction == 'sell' and code in self.positions:
            position = self.positions[code]
            cost = position.get('cost', 0.0)
            position_amount = position.get('amount', 0)
            
            # 确保不超过持仓数量
            sell_amount = min(amount, position_amount)
            
            # 计算预计亏损
            if cost > 0 and sell_amount > 0:
                potential_loss = (cost - price) * sell_amount
                if potential_loss > 0:  # 如果是亏损
                    total_loss = self.daily_loss + potential_loss
                    if total_loss > self.config["max_daily_loss"]:
                        risk_details["max_daily_loss"]["passed"] = False
                        risk_details["max_daily_loss"]["actual"] = total_loss
                        risk_details["max_daily_loss"]["message"] = f"预计亏损 {potential_loss:.2f} 将导致当日总亏损 {total_loss:.2f} 超过限制 {self.config['max_daily_loss']:.2f}"
                        self.logger.warning(risk_details["max_daily_loss"]["message"])
        
        # 3. 检查总仓位限制（仅对买入订单）
        if self.config["enable_position_control"] and direction == 'buy':
            # 计算新的总仓位
            new_position = self.total_position + trade_amount
            
            # 检查总仓位是否超过限制
            if new_position > self.config["max_total_position"]:
                risk_details["max_position"]["passed"] = False
                risk_details["max_position"]["actual"] = new_position
                risk_details["max_position"]["message"] = f"新仓位 {new_position:.2f} 将超过总仓位限制 {self.config['max_total_position']:.2f}"
                self.logger.warning(risk_details["max_position"]["message"])
            
            # 检查仓位比例是否超过限制
            new_position_ratio = new_position / self.total_assets if self.total_assets > 0 else 1.0
            if new_position_ratio > self.config["max_position_ratio"]:
                risk_details["max_position_ratio"]["passed"] = False
                risk_details["max_position_ratio"]["actual"] = new_position_ratio
                risk_details["max_position_ratio"]["message"] = f"新仓位比例 {new_position_ratio:.2%} 将超过限制 {self.config['max_position_ratio']:.2%}"
                self.logger.warning(risk_details["max_position_ratio"]["message"])
            
            # 检查可用资金是否足够
            if trade_amount > self.available_cash:
                risk_details["available_cash"]["passed"] = False
                risk_details["available_cash"]["message"] = f"交易金额 {trade_amount:.2f} 超过可用资金 {self.available_cash:.2f}"
                self.logger.warning(risk_details["available_cash"]["message"])
        
        # 综合所有风险检查结果
        all_passed = all(detail["passed"] for detail in risk_details.values())
        
        if all_passed:
            self.logger.info(f"资金风险检查通过: {code}, {direction}, {amount}股, {price}元")
        else:
            self.logger.warning(f"资金风险检查未通过: {code}, {direction}, {amount}股, {price}元")
        
        return all_passed, risk_details
    
    def get_daily_loss(self) -> float:
        """
        获取当日亏损
        
        Returns:
            float: 当日亏损金额 (元)
        """
        return self.daily_loss
    
    def get_position_info(self) -> Dict[str, float]:
        """
        获取仓位信息
        
        Returns:
            Dict: 仓位信息字典
        """
        return {
            "total_assets": self.total_assets,
            "available_cash": self.available_cash,
            "total_position": self.total_position,
            "position_ratio": self.total_position / self.total_assets if self.total_assets > 0 else 0
        }
    
    def save_trade_records(self, file_path: str):
        """
        保存交易记录到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
                
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.trade_records, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"交易记录已保存到 {file_path}")
        except Exception as e:
            self.logger.error(f"保存交易记录失败: {e}") 